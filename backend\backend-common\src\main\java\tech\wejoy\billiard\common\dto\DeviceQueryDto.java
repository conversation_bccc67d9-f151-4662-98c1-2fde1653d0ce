package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.DeviceBrandEnum;
import tech.wejoy.billiard.common.enums.DeviceTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class DeviceQueryDto extends PageRequest {

    @QueryParam("tenantId")
    private Long tenantId;

    @QueryParam("accountId")
    private Long accountId;

    @QueryParam("clubId")
    private Long clubId;

    @QueryParam("brand")
    @Separator(",")
    private List<DeviceBrandEnum> brand;

    @QueryParam("type")
    @Separator(",")
    private List<DeviceTypeEnum> type;

    @QueryParam("online")
    @Separator(",")
    private List<IsEnum> online;

    @QueryParam("open")
    @Separator(",")
    private List<IsEnum> open;

    @QueryParam("linked")
    @Separator(",")
    private List<Boolean> linked;

}
