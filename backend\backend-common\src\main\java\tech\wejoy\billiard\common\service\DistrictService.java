package tech.wejoy.billiard.common.service;

import com.congeer.database.bean.BaseEntity;
import com.fasterxml.jackson.core.type.TypeReference;
import io.quarkus.redis.datasource.RedisDataSource;
import io.quarkus.redis.datasource.hash.HashCommands;
import io.quarkus.redis.datasource.value.ValueCommands;
import jakarta.enterprise.context.ApplicationScoped;
import tech.wejoy.billiard.common.bo.DistrictBo;
import tech.wejoy.billiard.common.entity.DistrictInfo;
import tech.wejoy.billiard.common.entity.DistrictPolyline;
import tech.wejoy.billiard.common.utils.PolygonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
public class DistrictService {

    private final String LIST_KEY = "district:list";
    private final String DETAIL_KEY = "district:detail";
    private final String POLYLINE_KEY = "district:polyline";
    private final String CITY_KEY = "district:city";

    private final HashCommands<String, String, DistrictInfo> valueCommands;

    private final ValueCommands<String, List<DistrictInfo>> districtCommands;

    private final HashCommands<String, String, String> polylineCommands;

    private final ValueCommands<String, List<DistrictPolyline>> cityCommands;

    public DistrictService(RedisDataSource ds) {
        districtCommands = ds.value(new TypeReference<>() {
        }, new TypeReference<>() {
        });
        polylineCommands = ds.hash(String.class, String.class, String.class);
        cityCommands = ds.value(new TypeReference<>() {
        }, new TypeReference<>() {
        });
        valueCommands = ds.hash(String.class, String.class, DistrictInfo.class);
    }

    public List<DistrictInfo> get() {
        return districtCommands.get(LIST_KEY);
    }

    public DistrictBo getTree() {
        List<DistrictInfo> districtInfos = get();
        Map<Long, String> codeMap = districtInfos.stream().collect(Collectors.toMap(BaseEntity::getId, DistrictInfo::getCode));
        codeMap.put(0L, "0");
        Map<String, List<DistrictBo>> parentMap = districtInfos.stream().collect(Collectors.groupingBy(v -> codeMap.get(v.getParentId()), Collectors.mapping(DistrictBo::from, Collectors.toList())));
        Map<String, DistrictBo> boMap = parentMap.values().stream().flatMap(List::stream).collect(Collectors.toMap(DistrictBo::getCode, v -> v));
        boMap.forEach((k, v) -> {
            if (parentMap.containsKey(k)) {
                v.setChildren(parentMap.get(k));
            }
        });
        return parentMap.get("0").getFirst();
    }

    public void setDistrictList(List<DistrictInfo> bo) {
        bo.forEach(v -> {
            v.setCreateAt(null);
            v.setUpdateAt(null);
            valueCommands.hset(DETAIL_KEY, v.getCode(), v);
        });
        districtCommands.set(LIST_KEY, bo);
    }

    public void setPolyline(List<DistrictPolyline> list) {
        list.forEach(v -> {
            polylineCommands.hset(POLYLINE_KEY, v.getCode(), v.getPolyline());
            v.setPolyline(null);
            v.setCreateAt(null);
            v.setUpdateAt(null);
        });
        cityCommands.set(CITY_KEY, list);
    }

    public DistrictBo regeo(float[] location) {
        List<DistrictPolyline> districtPolylines = cityCommands.get(CITY_KEY);
        districtPolylines.sort((v1, v2) -> {
            float distance1 = Math.abs(v1.getLongitude().floatValue() - location[0]) + Math.abs(v1.getLatitude().floatValue() - location[1]);
            float distance2 = Math.abs(v2.getLongitude().floatValue() - location[0]) + Math.abs(v2.getLatitude().floatValue() - location[1]);
            return (int) ((distance1 - distance2) * 10000);
        });
        for (DistrictPolyline districtPolyline : districtPolylines) {
            String polyline = polylineCommands.hget(POLYLINE_KEY, districtPolyline.getCode());
            String[] areas = polyline.split("\\|");
            for (String area : areas) {
                String[] pointsStr = area.split(";");
                List<float[]> points = new ArrayList<>();
                for (String pointStr : pointsStr) {
                    String[] split = pointStr.split(",");
                    points.add(new float[]{Float.parseFloat(split[0]), Float.parseFloat(split[1])});
                }
                if (PolygonUtil.isPointInPolygon(location, points)) {
                    return DistrictBo.from(valueCommands.hget(DETAIL_KEY, districtPolyline.getCode()));
                }
            }
        }
        return null;
    }

}
