package tech.wejoy.billiard.common.manager;

import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.entity.FileInfo;
import tech.wejoy.billiard.common.enums.FileLinkEnum;
import tech.wejoy.billiard.common.enums.FileTypeEnum;
import tech.wejoy.billiard.common.repo.FileInfoRepo;

import java.util.List;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class FileManager {

    private final FileInfoRepo fileInfoRepo;

    public void save(FileInfo FileInfo) {
        fileInfoRepo.save(FileInfo);
    }

    public void delete(FileTypeEnum type, FileLinkEnum link, Long outerId) {
        fileInfoRepo.delete("outerId = ?1 and link = ?2 and type = ?3", outerId, link, type);
    }

    public List<FileInfo> getImageInfoByTypeAndId(FileTypeEnum type, FileLinkEnum link, Long outerId) {
        return fileInfoRepo.find("outerId = ?1 and link = ?2 and type = ?3", Sort.by("seq"), outerId, link, type).list();
    }

    public FileInfo findByUrl(String url) {
        return fileInfoRepo.find("url", url).firstResult();
    }

    public void deleteById(Long id) {
        fileInfoRepo.deleteById(id);
    }

    public FileInfo findById(Long id) {
        return fileInfoRepo.findById(id);
    }

    public List<FileInfo> list(FileLinkEnum fileLinkEnum, Long id) {
        return fileInfoRepo.find("link = ?1 and outerId = ?2", Sort.by("seq"), fileLinkEnum, id).list();
    }

}
