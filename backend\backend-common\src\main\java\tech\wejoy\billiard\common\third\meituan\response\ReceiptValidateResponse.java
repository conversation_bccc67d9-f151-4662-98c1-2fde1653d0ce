package tech.wejoy.billiard.common.third.meituan.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class ReceiptValidateResponse {
    private String orderId;
    private int count;
    private String receiptCode;
    private Long dealId;
    private Long dealgroupId;
    private Long productItemId;
    private int productType;
    private String dealTitle;
    private Double dealPrice;
    private Double dealMarketprice;
    private int bizType;
    @JsonAlias("receiptEndDate")
    private Long receiptEndDate;
    private List<PaymentDetail> paymentDetail;
    private String mobile;
    private Map<Long, List<ReceiptValidateResponse>> receiptInfoMap;
    private Map<String, String> extraMap;
    private boolean tgTimesCardFlag;
    private String purchaseToConsumeRatio;
    private BigDecimal timesCardUnitPrice;
    private BigDecimal platformAmount;
    private BigDecimal merchantAmount;

    @Data
    public static class PaymentDetail {
        private String paymentDetailId;
        private BigDecimal amount;
        private Long amountType;
    }
}

