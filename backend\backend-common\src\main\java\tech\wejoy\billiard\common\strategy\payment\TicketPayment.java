package tech.wejoy.billiard.common.strategy.payment;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.inject.spi.CDI;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.StartFromEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;
import tech.wejoy.billiard.common.service.TicketService;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.strategy.payment.model.MeituanPayParams;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class TicketPayment extends Payment {

    TicketService ticketService;

    public TicketPayment() {
        this.ticketService = CDI.current().select(TicketService.class).get();
    }

    @Override
    public PrepayBo prepay(StartTableDto dto, ClubTable table) {
        MeituanPayParams meituanPayParams = JsonUtils.toObject(JsonUtils.toJson(dto.getExtra()), MeituanPayParams.class);
        ClientUserTicket ticket = ticketService.getTicketById(meituanPayParams.getTicketId());
        if (ticket == null) {
            PrepayBo ret = new PrepayBo();
            ret.setSuccess(false);
            ret.setMessage("券不存在");
            return ret;
        }
        if (ticket.getChannel() == TicketChannelEnum.MEITUAN) {
            dto.setPayType(OrderPayTypeEnum.MEITUAN);
        } else if (ticket.getChannel() == TicketChannelEnum.DOUYIN) {
            dto.setPayType(OrderPayTypeEnum.DOUYIN);
        }
        ChannelDeal deal = ticketService.getDealByTicket(ticket);
        if (deal == null) {
            PrepayBo ret = new PrepayBo();
            ret.setSuccess(false);
            ret.setMessage("未找到券对应套餐");
            return ret;
        }
        if (dto.getFrom() == StartFromEnum.QRCODE) {
            dto.setStartTime(LocalDateTime.now());
        } else if (dto.getStartTime() == null) {
            dto.setStartTime(LocalDateTime.now().plusMinutes(5));
        }
        TimeBo period = null;
        if (StringUtils.isNotBlank(deal.getPeriod())) {
            period = JsonUtils.toObject(deal.getPeriod(), TimeBo.class);
        }
        if (period != null) {
            int value = period.getDate(dto.getStartTime()).getDayOfWeek().getValue();
            if (CollectionUtils.isNotEmpty(period.getDays()) && !period.getDays().contains(value)) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券不在使用时间内");
                return ret;
            }
            if (dto.getStartTime() == null || dto.getStartTime().isBefore(period.getStart(dto.getStartTime()))) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券不在使用时间内");
                return ret;
            }
            if (dto.getStartTime().isAfter(period.getEnd(dto.getStartTime()))) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券不在使用时间内");
                return ret;
            }
        }
        if (deal.getType() == CouponTypeEnum.HOURS) {
            dto.setEndTime(dto.getStartTime().plusMinutes(deal.getMinutes()));
        } else if (deal.getType() == CouponTypeEnum.PERIOD && period != null) {
            LocalDateTime endTime = dto.getStartTime().plusMinutes(deal.getMinutes());
            LocalDateTime end = period.getEnd(dto.getStartTime());
            if (endTime.isAfter(end)) {
                endTime = end;
            }
            dto.setEndTime(endTime);
        }
        return super.prepay(dto, table);
    }

    @Override
    public PayResultBo pay(OrderInfo order, Object params) {
        MeituanPayParams meituanPayParams = JsonUtils.toObject(JsonUtils.toJson(params), MeituanPayParams.class);
        Long ticketId = meituanPayParams.getTicketId();
        ClientUserTicket ticket = ticketService.getTicketById(ticketId);
        PayResultBo ret = new PayResultBo();
        if (ticket == null) {
            ret.setNeedPay(false);
            ret.setSuccess(false);
            ret.setMessage("券不存在");
            return ret;
        }
        try {
            ticketService.consumeTicket(order, ticket);
        } catch (Exception e) {
            ret.setSuccess(false);
            ret.setMessage(e.getMessage());
            return ret;
        }
        ret.setSuccess(true);
        return ret;
    }

    @Override
    public RefundResultBo refund(OrderInfo order, BigDecimal refundAmount) {
        // 不做退款
        RefundResultBo ret = new RefundResultBo();
        ret.setSuccess(true);
        ret.setAmount(BigDecimal.ZERO);
        return ret;
    }

    @Override
    public RefundResultBo cancel(OrderInfo order, BigDecimal refundAmount) {
        ticketService.reverseTicket(order);
        RefundResultBo ret = new RefundResultBo();
        ret.setSuccess(true);
        ret.setAmount(order.getRealAmount());
        return ret;
    }

    public OrderProfitBo profit(OrderInfo order, ClubInfo club) {
        OrderProfitBo profit = super.profit(order, club);
        profit.setOrderAmount(BigDecimal.ZERO);
        profit.setProfitAmount(BigDecimal.ZERO.subtract(profit.getServiceFee()));
        return profit;
    }

}
