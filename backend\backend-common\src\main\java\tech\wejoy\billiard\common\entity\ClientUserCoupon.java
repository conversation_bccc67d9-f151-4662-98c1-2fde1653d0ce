package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClientUserCoupon extends BaseEntity {

    private Long userId;

    private Long couponId;

    private String billNo;

    private IsEnum used;

    private IsEnum refund;

    private IsEnum gift;

    private IsEnum expired;

    private LocalDateTime expireTime;

}
