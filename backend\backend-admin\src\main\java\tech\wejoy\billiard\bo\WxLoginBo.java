package tech.wejoy.billiard.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@Data
@Schema(description = "微信登录结果")
public class WxLoginBo {

    @Schema(description = "ID")
    private Long id;

    private String username;

    private Long tenantId;

    private String tenantName;

    @Schema(description = "token")
    private String token;

    @Schema(description = "是否绑定手机号")
    private boolean hasPhone;

    private List<String> roles;

    private List<String> permissions;

}
