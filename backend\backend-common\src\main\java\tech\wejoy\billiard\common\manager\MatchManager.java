package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.web.bean.request.PageRequest;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.dto.MatchAdminQueryDto;
import tech.wejoy.billiard.common.dto.MatchQueryDto;
import tech.wejoy.billiard.common.entity.MatchInfo;
import tech.wejoy.billiard.common.entity.MatchRequest;
import tech.wejoy.billiard.common.enums.MatchStatusEnum;
import tech.wejoy.billiard.common.repo.MatchInfoRepo;
import tech.wejoy.billiard.common.repo.MatchRequestRepo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class MatchManager {

    private final MatchInfoRepo matchInfoRepo;

    private final MatchRequestRepo matchRequestRepo;

    public void save(MatchInfo info) {
        matchInfoRepo.save(info);
    }

    public Page<MatchInfo> pageByUser(PageRequest dto, Long userId) {
        String query = "userId = :userId and deleted = 0";
        HashMap<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        return matchInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public List<MatchRequest> fetchRequestByUser(Long userId) {
        return matchRequestRepo.list("userId", userId);
    }

    public List<MatchRequest> fetchRequestByUserAndIds(Long userId, List<Long> matchIds) {
        if (CollectionUtils.isEmpty(matchIds)) {
            return List.of();
        }
        return matchRequestRepo.list("userId = ?1 and matchId in ?2", userId, matchIds);
    }

    public Page<MatchInfo> pageByIds(PageRequest dto, List<Long> matchIds) {
        String query = "id in :ids and deleted = 0";
        HashMap<String, Object> params = new HashMap<>();
        params.put("ids", matchIds);
        return matchInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public MatchInfo fetchById(Long id) {
        return matchInfoRepo.findById(id);
    }

    public List<MatchRequest> fetchRequestByMatchId(Long id) {
        return matchRequestRepo.list("matchId", id);
    }

    public void saveRequest(MatchRequest request) {
        matchRequestRepo.save(request);
    }

    public MatchRequest fetchRequestById(Long id) {
        return matchRequestRepo.findById(id);
    }

    public Page<MatchInfo> pageByDistance(MatchQueryDto dto) {
        String select = "SELECT m.* FROM match_info m left join club_info c on m.club_id = c.id";
        String where = "m.deleted = 0";
        Map<String, Object> map = new HashMap<>();
        if (dto.getName() != null) {
            where += " AND c.name LIKE concat('%', :name, '%')";
            map.put("name", dto.getName());
        }
        String orderBy = "";

        String countSql = "FROM MatchInfo m left join ClubInfo c on m.clubId = c.id";
        long count = matchInfoRepo.count(countSql + " WHERE " + where, map);
        if (count == 0) {
            return dto.empty();
        }
        if (dto.getLng() != null && dto.getLat() != null) {
            orderBy = " ORDER BY earth_distance(ll_to_earth(c.longitude, c.latitude), ll_to_earth(:lng,:lat)), m.create_at DESC";
            map.put("lng", dto.getLng());
            map.put("lat", dto.getLat());
        }
        String sql = select + " WHERE " + where + orderBy;
        List<MatchInfo> list = matchInfoRepo.findBySql(sql, map, MatchInfo.class);
        return dto.of(count, list);
    }

    public List<MatchRequest> fetchRequestByMatchIds(List<Long> matchIds) {
        if (CollectionUtils.isEmpty(matchIds)) {
            return List.of();
        }
        return matchRequestRepo.list("matchId in ?1", matchIds);
    }

    public void deleteRequest(MatchRequest request) {
        matchRequestRepo.delete(request);
    }

    public Page<MatchInfo> pageByAdmin(MatchAdminQueryDto dto) {
        String query = "deleted = 0";
        HashMap<String, Object> params = new HashMap<>();
        if (dto.getClubId() != null) {
            query += " and clubId = :clubId";
            params.put("clubId", dto.getClubId());
        }
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            query += " and status in :status";
            params.put("status", dto.getStatus());
        }
        return matchInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public List<MatchInfo> fetchMatchByStatus(MatchStatusEnum matchStatusEnum) {
        return matchInfoRepo.list("status", matchStatusEnum);
    }

    public List<MatchInfo> fetchMatchByClub(Long clubId) {
        return matchInfoRepo.list("clubId = ?1 and status = 2", clubId);
    }

    public List<MatchInfo> fetchMatchByClubId(Long id) {
        return matchInfoRepo.list("clubId = ?1 and deleted = 0", Sort.descending("createAt"), id);
    }

}
