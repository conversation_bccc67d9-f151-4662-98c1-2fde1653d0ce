package tech.wejoy.billiard.common.third.aqara.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @date 2021/5/24
 */
@Slf4j
public class SignUtil {

    public static String createSign(LinkedHashMap<String, String> headers, String appKey) {
        StringBuilder sb = new StringBuilder();
        headers.forEach((k, v) -> sb.append(k).append("=").append(v).append("&"));
        String substring = sb.substring(0, sb.length() - 1);
        String signStr = (substring + appKey).toLowerCase();
        try {
            return MD5_32(signStr);
        } catch (Exception e) {
            log.error("create sign error, {}", e.getMessage(), e);
        }
        return null;
    }

    private static String MD5_32(String sourceStr) throws Exception {
        String result = "";

        try {
            byte[] b = md5(sourceStr.getBytes(StandardCharsets.UTF_8));
            StringBuilder buf = new StringBuilder();

            for (int j : b) {
                int i = j;
                if (i < 0) {
                    i += 256;
                }

                if (i < 16) {
                    buf.append("0");
                }

                buf.append(Integer.toHexString(i));
            }

            result = buf.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5_32 error, {}", e.getMessage(), e);
        }

        return result;
    }

    private static byte[] md5(byte[] bytes) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        md.update(bytes);
        return md.digest();
    }

}