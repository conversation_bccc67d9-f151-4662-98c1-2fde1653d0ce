{"version": 3, "file": "taro.js", "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AAuBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxCA;AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AC3BA;AAKA;AAAA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;;;;;;;;;;;;;;;;;;;;;;ACrCA;AAIA;AAAA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AAEA;AACA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;ACjCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;;AAGA;AAEA;AACA;;AAGA;AACA;AACA;;AAEA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;AC/CA;AACA;AACA;AAEA;AACA;AACA;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChEA;AACA;AACA;AACA;ACPA;AAEA;;AACA;;AAGA;AAEA;AAGA;AAEA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AACA;AAAA;AAGA;ACrCA;AACA;AACA;AAAA;AACA;AACA;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;AAMA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/EA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;;;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AAyCA;AAEA;AAIA;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AAEA;AACA;;AAGA;AAAA;AAAA;AAAA;;AACA;AACA;AACA;AAAA;;AAsCA;AAAA;AAAA;AAAA;;;AA7BA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;AAGA;AAEA;AAAA;AAKA;AAOA;AAEA;AAAA;;;AAIA;AAAA;AAAA;;AAlCA;AACA;AAAA;AAAA;;AACA;AAAA;AAkCA;AACA;AAEA;;;;;;;AAOA;AACA;AAMA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAEA;AACA;;AAGA;AACA;;;AAGA;AACA;AAAA;AAAA;;;AAIA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;;AAEA;AAOA;AAAA;AAAA;AACA;;AAJA;AACA;AAIA;AACA;AAAA;;AACA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AAAA;AAEA;AACA;AACA;;AAGA;AAEA;AACA;AAAA;AAAA;;AAGA;;AAKA;AAAA;AAGA;AACA;;AAGA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AACA;AAEA;AAKA;;;AAEA;AACA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;;AAEA;AACA;AAEA;;AAEA;AAGA;AACA;AAEA;;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AAEA;;AAEA;AAGA;AACA;;AACA;;AAEA;AACA;;AAEA;;AAEA;AACA;AAEA;;AAEA;AAGA;AACA;;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;;AAEA;AAGA;AACA;;AACA;AACA;AACA;AACA;AAEA;;AAEA;AAGA;AACA;;AACA;AACA;AACA;AACA;AAEA;;AAEA;AAGA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;AAKA;AACA;AACA;AC/ZA;AACA;AACA;AACA;AASA;;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;AAAA;AAAA;;AACA;AACA;AAAA;;AAqBA;AAAA;AAAA;AAAA;AAlBA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AAIA;AACA;AACA;;AAIA;AAAA;AAtBA;AAyBA;AAAA;AAAA;;AACA;AACA;AACA;AAAA;;AAmEA;AAAA;AAAA;AAAA;AAhEA;AACA;AACA;AACA;;AAEA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAMA;AAAA;;AAKA;AACA;AACA;AACA;AACA;AAAA;AAAA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;AAEA;AACA;AAAA;AAAA;;AAEA;AAAA;AAGA;AAEA;AACA;;AAEA;;AAEA;AACA;;;AAGA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;;AAEA;AAAA;AAAA;AACA;;AAGA;AACA;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;AAEA;AACA;AACA;AAAA;AAAA;;AAEA;AAEA;AACA;AACA;AACA;;AAEA;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AAAA;AAAA;AACA;AACA;;AAGA;;AAEA;AACA;AACA;;;AAGA;;AAEA;AACA;AAGA;;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;;AAGA;;AACA;AAIA;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AAEA;AAEA;AACA;;AAGA;AAEA;AACA;AAEA;AAKA;AACA;AACA;AACA;AAEA;AACA;AAEA;;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AAOA;;AAEA;AACA;AACA;;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AACA;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;;AAEA;AACA;;;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AAKA;AACA;AACA;;AAGA;AAeA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AC7gBA;AACA;AACA;;AAEA;AAEA;;;;;;;;;;;;;;;;;;;;;;;ACOA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;AAIA;AAAA;AACA;;;AAGA;AAAA;AACA;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;;AAEA;AACA;AChHA;AAEA;AACA;AACA;AACA;;AAGA;AACA;AACA;AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;;AAEA;AAAA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAEA;AAEA;AAAA;AACA;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAEA;AACA;AAAA;AAAA;AAEA;AAEA;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;ACxGA;;AAIA;;;;AAMA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AACA;AAAA;;AAGA;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAAA;AACA;AACA;;AAGA;AACA;AAEA;AACA;AACA;AAEA;;;AAGA;AACA;AACA;AAGA;AACA;AAAA;;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AAAA;;AAGA;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AACA;;;AAEA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AAAA;;;AAGA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;;AACA;AAGA;AADA;AAAA;AAEA;AACA;AACA;;;;;AAGA;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;AC7LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;ACvCA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;ACrCA;ACMA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AACA;AACA;AAAA;AAAA;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AACA;AACA;;AAEA;AACA;;AACA;AACA;AACA;ACj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fA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAAA;;;AAMA;AN7BA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AFLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AAIA;AACA;AACA;AACA;AACA;AACA;;ASpDA;AAEA;AACA;AAOA;;ACzCA;;;AAGA;AAQA;AACA;AACA;AAEA;AACA;AACA;AAUA;;;AAGA;AACA;AACA;AAEA;AACA;AAMA;AACA;AACA;;;AAGA;AACA;AAEA;;;AAGA;AACA;AACA;;;AAGA;;AAEA;AAEA;AACA;AACA;AAEA;AAIA;AAEA;AAIA;;ACrEA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;;AAEA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;AAIA;AAEA;;AAEA;AACA;;AAEA;;;AAGA;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAEA;;AAGA;AACA;;AAGA;AACA;AAEA;AACA;ACvFA;AACA;AACA;AAEA;AAKA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAEA;AAMA;;AAGA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;AACA;;AAGA;AACA;AAEA;AACA;AACA;;AAGA;AAEA;AACA;;AAGA;AAEA;AACA;AACA;;AAEA;AACA;ACzFA;AAEA;AACA;AACA;AAEA;;AAEA;AACA;;;AAIA;AACA;;;AAIA;AACA;;AAGA;AACA;;;AAIA;AACA;AACA;AACA;;;;AAKA;AACA;AAEA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AAAA;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AAAA;AAAA;;;AAIA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;;AAEA;AACA;;AAEA;AApBA;AAAA;AASA;AAYA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AAEA;AACA;AACA;;;AAGA;AACA;AACA;;AAEA;AAEA;;;AAIA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAGA;AAEA;AACA;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;AAEA;AACA;;AAEA;AAEA;AACA;;AAEA;AACA;;AAGA;AAMA;AAOA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AAEA;AACA;;;;;AAKA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA;AACA;;;;;AAMA;AACA;AACA;;AACA;AAEA;AAOA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;;AAEA;;AAEA;;AAEA;;;;AAKA;AACA;AACA;;AAEA;AACA;;AAEA;;;AAGA;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;AAEA;;AC1SA;AAaA;;;;;AAsBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAIA;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;AAMA;AACA;AAEA;AAIA;AACA;AACA;AACA;AAAA;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAOA;ACzLA;AAEA;AACA;;AACA;AACA;AAEA;AAEA;AACA;AAEA;;AAEA;AAEA;AACA;AAEA;AAEA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;;AAGA;AACA;AAEA;AACA;;AAEA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAEA;AAEA;AAAA;AAEA;AACA;AAEA;;AAEA;AChGA;AAAA;AAkBA;AAAA;AACA;AACA;;AACA;AAAA;AAAA;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAGA;AAGA;AACA;AAMA;AACA;AACA;AAGA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;AAEA;AAAA;AAAA;AAGA;AAAA;AACA;AACA;;AACA;AAAA;AAAA;AAGA;;AACA;AAAA;AAGA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AAEA;AAAA;;AACA;AACA;AACA;;;AAGA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;AACA;;AAGA;;AACA;AAEA;;;;AAKA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;A3B1IA;AAWA;AAEA;AACA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AAEA;AAIA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;A4B7FA;AAiBA;AAAA;;AAPA;AACA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;AAIA;AACA;;AAGA;AACA;;;;AAGA;AACA;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;AAIA;AACA;;AAGA;AACA;;;;AAGA;AACA;;AAGA;AACA;;;;AAGA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;;AAKA;AACA;AACA;;AAGA;AACA;AACA;AACA;;;;;AAIA;AACA;;AAGA;AACA;AACA;AACA;;;;;AAKA;AACA;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAIA;AACA;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;AAIA;AACA;;;;;AAIA;AACA;;;;AAGA;AACA;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AArKA;AACA;;;;AAGA;AACA;;;;;AAsKA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;ACjPA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAGA;AAAA;AAFA;AAGA;AAEA;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;AAMA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;;;;AAGA;AACA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;;AAEA;;;;;;;;;;;;;;;;;;;;;AC/HA;AACA;;;;;;;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;ACvCA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACaA;AAAA;AAUA;AAAA;AAAA;AACA;;;AAPA;AACA;AACA;AAEA;AAKA;AACA;AAEA;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AAEA;AAAA;;;;;;AAeA;AACA;;;;AAGA;AACA;;;;;AAIA;AACA;AAEA;AACA;AAEA;AAEA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;AAIA;AACA;;;AA7HA;AA0DA;AAAA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AA+DA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzIA;AACA;AAAA;AAQA;AAAA;AAAA;AACA;;;AALA;AACA;AACA;AAKA;AAEA;AAEA;AAGA;AAEA;AACA;AAEA;AACA;;AAGA;AACA;;AAKA;AAGA;AACA;AAIA;AAGA;AACA;AACA;AACA;AAIA;;AAIA;AACA;AACA;AACA;AACA;;AAEA;AAIA;AAGA;AACA;AAEA;;;;;;AAsEA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AACA;AAEA;;;;AAGA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AAEA;;;;AAGA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AAEA;;;;AAGA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AAEA;;;;AAGA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AAEA;;;;AAGA;AACA;;AAGA;AACA;AACA;AACA;AAEA;AACA;AAEA;;;;AAGA;AACA;;;;AAIA;AACA;AACA;AACA;AAEA;AACA;AAEA;;;;AAGA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AAEA;;;;AAGA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AAEA;;;;;AAIA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;;AAIA;AACA;;;AAhSA;;AA0EA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;;AAEA;AAGA;AACA;AAGA;AACA;AAGA;AACA;AAMA;AACA;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAGA;AACA;AACA;;;AAIA;AACA;;;AAIA;AACA;;;AAIA;AACA;AACA;;AAGA;AACA;AACA;AA6JA;AAEA;AAAA;AACA;AACA;AACA;;AAEA;AACA;;;;;;;;;;;;;;;;AChUA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;ACrBA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;;AAIA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjBA;AAWA;AAAA;AAAA;AACA;AAXA;AACA;AACA;AACA;AAUA;AAKA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;AAIA;AAEA;;AAGA;AAAA;AAAA;;AAEA;AAAA;AAAA;AAEA;AAAA;;;;;AAGA;AACA;AACA;AAEA;;AAEA;AACA;AAEA;;AAEA;AACA;AACA;AAEA;;AAEA;AACA;AACA;AAEA;;AAEA;AACA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;;;;AAGA;AACA;AACA;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AA1FA;AAiGA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;ACzCA;AACA;AACA;AACA;;AAGA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;ACLA;AAEA;;;;;;;AAOA;AAPA;AAcA;AAAA;AAFA;AAGA;;AAGA;;;;;;;AAOA;AAPA;AAAA;AAAA;AASA;AACA;AACA;AAEA;;AAGA;;;;AAIA;AAJA;AAAA;AAAA;AAMA;AAEA;AACA;AACA;;;AAIA;;;;AAIA;AAJA;AAAA;AAAA;AAMA;;;;AAIA;AACA;AAIA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;ACjIA;AAGA;AAAA;AACA;AAGA;AACA;;AAEA;AACA;AACA;AACA;AACA;;;;;AAIA;AAAA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEA;;AAGA;AACA;;;;AAGA;;AACA;;;;AAGA;;AACA;;;;AAGA;;AACA;;;;AAGA;;AACA;;;;AAGA;;AACA;;;;AAGA;;AACA;;;;AAGA;;AACA;;;;AAGA;AACA;AACA;AACA;AACA;;AAEA;AACA;;;;AA5CA;;;;;;;;;;;;;;;;;;;ACZA;AAKA;AAAA;AAAA;AAFA;AAGA;AACA;AAAA;AAAA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AACA;AAEA;;AAEA;AAEA;AACA;;;;;AAIA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AAEA;AACA;AAEA;;AAEA;AAEA;AACA;;;;;AAIA;AACA;AAEA;;;;AAGA;AACA;AACA;AAEA;;AAEA;;AAGA;AACA;AACA;AACA;;;;;AAIA;AACA;AAEA;AAEA;AACA;AACA;;;;;AAIA;AACA;AAAA;AAAA;;;;AAGA;AACA;AAEA;;;;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/FA;AAOA;AAAA;AAAA;AACA;AAJA;AAKA;AACA;AAAA;;;;;AAGA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AAEA;;;;;;AAKA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;;;;AAGA;;AAEA;AACA;;AAEA;;;;AAGA;;AAEA;;;;;AAIA;AACA;AACA;AACA;;;;AAGA;AACA;;;AA9EA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSA;AAQA;AAAA;AAAA;AACA;AANA;AAEA;AAKA;AACA;AACA;AAAA;;;;;AAGA;;AAEA;;AAEA;AACA;AAEA;AACA;;AAGA;AACA;AACA;;;;;;AAKA;AACA;;AAGA;AACA;;;;AAGA;AACA;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;;;;AAGA;AACA;AACA;AAEA;AACA;;AAGA;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;AACA;;;;AAIA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;AAKA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;AAEA;;AAEA;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAEA;AACA;AACA;;AAGA;AAEA;AACA;;;AAGA;AACA;AACA;AAGA;AACA;;AAEA;AACA;AACA;AACA;;;;;;AAKA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;;AAEA;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;;AAGA;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;;;;;AAKA;AACA;AACA;;;;AAGA;AAAA;AACA;AACA;AACA;;;;AAGA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AACA;;;;AAGA;AACA;AAEA;AAEA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAGA;AACA;AACA;AAAA;;;AAGA;AACA;;;AAIA;AACA;;AAGA;;;;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA;;;;AAGA;AAAA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAIA;AACA;;;AAlXA;;;;;;;;;;;;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AAAA;AACA;AACA;;;;AAGA;AAAA;AACA;AACA;AACA;AAAA;AAAA;;;AAVA;AAcA;;;;;;;;;;;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AACA;;;;AAEA;AACA;AAEA;AAEA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;;AAGA;;;;;AAMA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;AAIA;AACA;AAEA;;AAEA;AACA;AACA;;AAGA;AACA;;AAGA;AACA;AACA;;AAGA;AACA;AACA;AAEA;AAEA;;;;AAGA;AACA;AACA;AAAA;AAAA;AACA;;;;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5EA;AAAA;AA0BA;AAAA;AAfA;AAEA;AAEA;;AAGA;;;AAIA;AAKA;AACA;AACA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;;AACA;AACA;AACA;AACA;;AAEA;AAEA;AAKA;AACA;;AAGA;AAEA;AACA;AACA;;;;;AAIA;;AACA;AACA;AACA;AAEA;AAEA;AACA;AAEA;AACA;AACA;;AAGA;AAEA;AACA;;AAGA;AAEA;AACA;AACA;;;;;AAKA;AACA;;AAEA;AAAA;AAAA;AAAA;;AAGA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;;;AAIA;;AAEA;;AAGA;AACA;AAEA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;AAMA;AACA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjMA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;AAEA;;AAGA;AACA;;;;AAGA;;AAEA;AACA;;AAGA;AACA;;;;AAGA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;;;AAIA;;;AAhCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWA;AACA;AAAA;AAUA;AAAA;AAAA;AACA;AAJA;AACA;AASA;AAAA;AAAA;AAAA;AAAA;AALA;AACA;AACA;AAAA;;;;;AAKA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;AAGA;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;;;;AAGA;;AACA;;;;AAGA;AACA;AAEA;AAEA;;;;AAGA;AACA;AAEA;;AAEA;AAAA;AAAA;AACA;AACA;AAEA;;AAGA;;;;AAGA;AACA;AACA;;;;AAGA;AACA;AACA;;;;AAGA;AACA;AACA;AACA;;AAEA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;;AAGA;;;AAGA;;;;AAEA;AACA;AACA;;AAGA;AACA;AAAA;AAAA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAGA;;;;;;;AAOA;AAPA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;AAMA;AAAA;AAAA;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;;AAGA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAYA;AACA;;AAEA;AACA;;AAEA;;;;AAKA;AACA;AACA;AACA;AACA;AAGA;AAEA;AACA;AACA;AAEA;;AAGA;;;;;;;AAOA;AAPA;AAAA;AAAA;AASA;;AAGA;;;;;;;AAOA;AAPA;AAAA;AAAA;AASA;;AAGA;;;;AAKA;AAAA;AAAA;AAEA;;AAGA;;;;;;AAMA;AANA;AAAA;AAAA;AAOA;AACA;AAAA;AAEA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAIA;AACA;AACA;;AAGA;AACA;;;AAIA;AACA;;AAGA;;;;AAGA;;AACA;;;;AAGA;AACA;;;;AAGA;;AACA;;;;AAGA;AACA;;;;AAGA;AACA;;;AAzTA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACTA;;AAEA;AACA;AACA;AACA;AAEA;AACA;;AAEA;;AAEA;AAEA;AAEA;AACA;AAAA;AAAA;;AAGA;AAEA;AACA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AAWA;AAAA;AAAA;AACA;AATA;AAEA;AAEA;AAEA;AAIA;AACA;AAAA;;;;;AAGA;AACA;;;;AAGA;AACA;;;;AAGA;;AAEA;;;;AAGA;AACA;AAEA;AACA;;;;;AAIA;AAAA;AAAA;AAAA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AAMA;AACA;AAAA;AAAA;AACA;AACA;;AAEA;;AAGA;AACA;;AAEA;AACA;;AAEA;AAEA;AACA;AACA;;;AAVA;AAAA;AAAA;;AAeA;;AAGA;AACA;AACA;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AAAA;;AAEA;AAIA;;AAEA;;;;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAGA;AACA;AACA;;AAEA;;AAEA;AACA;;;AAIA;AACA;;AAEA;;AAEA;;AAEA;;;;AAGA;AACA;AACA;AACA;;;;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;AAxJA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAKA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA;AAEA;AACA;AAAA;AAGA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAbA;AAAA;AAAA;AAAA;AAgBA;AACA;AAEA;AACA;AACA;AAAA;AAWA;AAAA;AACA;AACA;AACA;;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;;AAGA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAGA;AAAA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;;;AAIA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;AAEA;;AAGA;AACA;;;;AAGA;AACA;;AAEA;AACA;AACA;;AAGA;AACA;AACA;AACA;;;;;AAIA;AACA;AACA;AACA;;AAGA;AACA;AACA;;;;AAGA;AACA;AACA;AACA;AACA;;AAGA;;;;AAIA;AAEA;AACA;AACA;AACA;AACA;AAEA;;AAEA;;;;;;;;;;;;;;;AC1MA;;;AAGA;AAEA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;AC5LA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACGA;AAKA;AAAA;AAAA;AACA;AAJA;AACA;AAIA;AAAA;;;;;AAgBA;AACA;;AAdA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAWA;AACA;;AALA;AACA;;;;AAWA;AACA;;AALA;AACA;;;AApCA;;;;;;;;;;;;;;;;;;;;;;;;;ACJA;AAGA;AAAA;AAAA;AACA;AADA;AAFA;AAAA;;;;;AAMA;AACA;;;AARA;;;;;;;;;;;;;;;ACIA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;;AAGA;;AAGA;AACA;AAEA;AACA;AACA;;AAGA;AACA;;AAGA;AAEA;AACA;AACA;;AAGA;AAEA;AACA;;AAEA;AACA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpDA;AAsBA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;;AAGA;AAEA;AACA;AAAA;AAAA;AACA;;AAGA;AACA;;AAGA;AACA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAGA;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAEA;AAEA;AACA;;AAGA;AAAA;AAAA;AACA;AACA;;AAIA;AACA;;AAGA;;AAGA;AACA;;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;;AAEA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAAA;AAAA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;;AAEA;AACA;;AAEA;AACA;;;AAGA;;AAEA;AAAA;AAAA;AACA;AACA;;AAGA;AACA;;;AAGA;AACA;AACA;;;AAGA;;AAEA;AAEA;AAEA;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAGA;;AACA;AAKA;;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;;AAEA;AAEA;AAEA;AACA;;AAGA;AAEA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AACA;;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AAGA;AACA;;AAGA;;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAAA;AAAA;AAEA;;AAGA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAKA;;AAGA;AACA;AAIA;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAEA;AACA;AACA;AAAA;AAEA;AAAA;AACA;;;;;;;;;;;;;;;;;;;ACxYA;;;;;;;;;;;;;;;;;A3DOA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;A4DQA;AACA;AAEA;;;;;AAKA;AACA;;;AAEA;;AAGA;AAEA;AACA;AAEA;AACA;AACA;AAAA;;AAMA;AAKA;AACA;;AAGA;AACA;AACA;AACA;AACA;;;AAIA;AACA;;;AAIA;AACA;AACA;AACA;AACA;AACA;AAMA;;AAEA;AAMA;;AAEA;AACA;;;;AAKA;AAAA;AAAA;AAEA;AACA;;AAGA;AACA;AACA;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAKA;AACA;;AAGA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzHA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;;;;;AAKA;AACA;;AACA;AACA;AACA;AAMA;;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;AAIA;AACA;;;;;;;;;;;;;;;AC7CA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCA;AAAA;AAAA;;AACA;;;;AAEA;AACA;AACA;;AAEA;;;;AAGA;AAAA;AACA;AACA;;AAEA;AACA;AAEA;AACA;;AAEA;;;;AAGA;AAAA;AAAA;AACA;AACA;;AAGA;AAAA;AAAA;AACA;AACA;AACA;;;;AAGA;AACA;AACA;AACA;AAGA;;;;;;;;;;;;;;;;;;;ACvCA;AACA;AAGA;AAGA;AAGA;AAGA;;AAIA;AAKA;AAEA;;;;;;;;;;;;;;;;;;;AC9BA;;AAEA;AAFA;AAQA;AAAA;AAFA;AAGA;;;;AAGA;AACA;;;;AAGA;AACA;AACA;;;;;AAIA;AACA;;;;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLA;AACA;;AAEA;AACA;;;AAGA;AACA;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAGA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AACA;;AACA;AACA;AACA;AACA;AACA;;;AAIA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AAMA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAAA;AAGA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;ACtIA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AC3BA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;Aj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eA;;;;;;;;;;;;;;;;;AkD5gBA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;ACxBA;AAIA;AAAA;;AACA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AACA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA;AAEA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;;AACA;AAAA;AAAA;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AACA;AATA;AAAA;AAUA;AACA;AACA;AACA;AACA;;;;AAIA;;AACA;AAAA;AA9FA;;;;;;;;;;;;;;;;;;;;;;;;;;ACLA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;ACAA;AA0HA;AACA;;AACA;AAEA;AACA;AACA;;AAEA;;AAGA;AAEA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAGA;AAAA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AAaA;AAEA;AAAA;AAIA;AACA;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;AAGA;AACA;AACA;;AAEA;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;;;AAIA;AACA;;AAGA;;AAGA;AACA;;AACA;AACA;AACA;AACA;AACA;AAAA;AAEA;AACA;;AAEA;AACA;;AACA;AACA;AACA;AACA;;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAGA;AACA;;;AAIA;AACA;AACA;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;;AAGA;AAEA;AACA;AAEA;;;;AAIA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAIA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzZA;AAAA;AACA;AACA;AACA;AACA;AAiEA;AACA;AAKA;AAWA;AACA;AAWA;AAIA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAKA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA;;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;;AACA;AAAA;AAAA;AAGA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA;AAAA;AAAA;;AAGA;AACA;AAEA;AAEA;AACA;AAEA;AAEA;AACA;AACA;AAAA;AAbA;AAAA;AAcA;AACA;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;;AAEA;AAAA;AAAA;;AAGA;;AACA;AAAA;AA6EA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAEA;AAAA;AAAA;AAEA;AACA;;;AAGA;AACA;AAeA;AACA;AAEA;AAAA;AAAA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AAEA;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AAEA;AAEA;AAEA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAEA;AAEA;AAEA;AAEA;AAEA;AAEA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;A5DlWA;AAEA;AAEA;AAWA;;;;;;AAMA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;;;;;;AAMA;AACA;AAAA;AAAA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AAEA;AACA;AAEA;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;;AAIA;AAEA;AACA;AACA;AACA;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAA;AACA;;AAGA;AACA;AACA;AACA;;;;AAIA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AACA;AACA;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAOA;AACA;AACA;AACA;AACA;;AAEA;AAEA;AACA;AAEA;AACA;AACA;AAEA;;;;;;;;;;;A6D1PA;AAAA;AACA;AAEA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;ACRA;AACA;AACA;AACA;;;;;;;;;;;;ACHA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/.._src_env.ts", "webpack://frontend-client/.._src_index.ts", "webpack://frontend-client/.._.._src_interceptor_chain.ts", "webpack://frontend-client/.._.._src_interceptor_index.ts", "webpack://frontend-client/.._.._src_interceptor_interceptors.ts", "webpack://frontend-client/.._src_tools.ts", "webpack://frontend-client/.._src_runtime_react-meta.ts", "webpack://frontend-client/.._src_runtime_utils.ts", "webpack://frontend-client/.._src_runtime_hooks.ts", "webpack://frontend-client/.._src_runtime_connect.ts", "webpack://frontend-client/.._src_runtime_connect-native.ts", "webpack://frontend-client/.._src_runtime_index.ts", "webpack://frontend-client/.._src_constant.ts", "webpack://frontend-client/.._src_utils.ts", "webpack://frontend-client/.._src_runtime.ts", "webpack://frontend-client/.._.._taro-components_mini_index.js", "webpack://frontend-client/.._src_components-react.ts", "webpack://frontend-client/.._src_apis-list.ts", "webpack://frontend-client/.._src_apis.ts", "webpack://frontend-client/.._src_components.ts", "webpack://frontend-client/.._src_runtime-utils.ts", "webpack://frontend-client/.._src_workTags.ts", "webpack://frontend-client/.._src_componentTree.ts", "webpack://frontend-client/.._src_domInput.ts", "webpack://frontend-client/.._src_inputValueTracking.ts", "webpack://frontend-client/.._src_props.ts", "webpack://frontend-client/.._src_reconciler.ts", "webpack://frontend-client/.._src_event.ts", "webpack://frontend-client/.._src_render.ts", "webpack://frontend-client/.._.._src_bom_URL.ts", "webpack://frontend-client/.._.._src_bom_URLSearchParams.ts", "webpack://frontend-client/.._.._src_bom_document.ts", "webpack://frontend-client/.._.._src_bom_getComputedStyle.ts", "webpack://frontend-client/.._.._src_bom_history.ts", "webpack://frontend-client/.._.._src_bom_location.ts", "webpack://frontend-client/.._.._src_bom_navigator.ts", "webpack://frontend-client/.._.._src_bom_raf.ts", "webpack://frontend-client/.._.._src_bom_window.ts", "webpack://frontend-client/.._.._src_constants_index.ts", "webpack://frontend-client/.._src_current.ts", "webpack://frontend-client/.._.._.._src_dom-external_mutation-observer_implements.ts", "webpack://frontend-client/.._.._.._src_dom-external_mutation-observer_index.ts", "webpack://frontend-client/.._.._src_dom_anchor-element.ts", "webpack://frontend-client/.._.._src_dom_class-list.ts", "webpack://frontend-client/.._.._src_dom_document.ts", "webpack://frontend-client/.._.._src_dom_element.ts", "webpack://frontend-client/.._.._src_dom_event-source.ts", "webpack://frontend-client/.._.._src_dom_event-target.ts", "webpack://frontend-client/.._.._src_dom_event.ts", "webpack://frontend-client/.._.._src_dom_form.ts", "webpack://frontend-client/.._.._src_dom_node.ts", "webpack://frontend-client/.._.._src_dom_root.ts", "webpack://frontend-client/.._.._src_dom_style.ts", "webpack://frontend-client/.._.._src_dom_style_properties.ts", "webpack://frontend-client/.._.._src_dom_svg.ts", "webpack://frontend-client/.._.._src_dom_text.ts", "webpack://frontend-client/.._.._src_dom_transfer.ts", "webpack://frontend-client/.._.._src_dom_tree.ts", "webpack://frontend-client/.._.._src_dsl_common.ts", "webpack://frontend-client/.._.._src_emitter_emitter.ts", "webpack://frontend-client/.._src_hydrate.ts", "webpack://frontend-client/.._src_next-tick.ts", "webpack://frontend-client/.._src_options.ts", "webpack://frontend-client/.._src_perf.ts", "webpack://frontend-client/.._.._src_polyfill_index.ts", "webpack://frontend-client/.._.._src_utils_cache.ts", "webpack://frontend-client/.._.._src_utils_index.ts", "webpack://frontend-client/.._.._src_utils_lodash.ts", "webpack://frontend-client/.._.._src_utils_router.ts", "webpack://frontend-client/.._src_constants.ts", "webpack://frontend-client/.._src_event-emitter.ts", "webpack://frontend-client/.._src_is.ts", "webpack://frontend-client/.._src_native-apis.ts", "webpack://frontend-client/.._src_runtime-hooks.ts", "webpack://frontend-client/._node_modules_@tarojs_taro_index.js", "webpack://frontend-client/._node_modules_@tarojs_webpack5-runner_dist_template_comp.js", "webpack://frontend-client/._node_modules_@tarojs_webpack5-runner_dist_template_custom-wrapper.js"], "sourcesContent": ["export const ENV_TYPE = {\n  WEAPP: 'WEAPP',\n  SWAN: 'SWAN',\n  ALIPAY: 'ALIPAY',\n  TT: 'TT',\n  QQ: 'QQ',\n  JD: 'JD',\n  WEB: 'WEB',\n  RN: 'RN',\n  HARMONY: 'HARMONY',\n  QUICKAPP: 'QUICKAPP',\n  HARMONYHYBRID: 'HARMONYHYBRID',\n}\n\nexport function getEnv () {\n  if (process.env.TARO_ENV === 'weapp') {\n    return ENV_TYPE.WEAPP\n  } else if (process.env.TARO_ENV === 'alipay') {\n    return ENV_TYPE.ALIPAY\n  } else if (process.env.TARO_ENV === 'swan') {\n    return ENV_TYPE.SWAN\n  } else if (process.env.TARO_ENV === 'tt') {\n    return ENV_TYPE.TT\n  } else if (process.env.TARO_ENV === 'jd') {\n    return ENV_TYPE.JD\n  } else if (process.env.TARO_ENV === 'qq') {\n    return ENV_TYPE.QQ\n  } else if (process.env.TARO_ENV === 'harmony-hybrid') {\n    return ENV_TYPE.HARMONYHYBRID\n  } else if (process.env.TARO_ENV === 'h5' || process.env.TARO_PLATFORM === 'web') {\n    return ENV_TYPE.WEB\n  } else if (process.env.TARO_ENV === 'rn') {\n    return ENV_TYPE.RN\n  } else if (process.env.TARO_ENV === 'harmony' || process.env.TARO_PLATFORM === 'harmony') {\n    return ENV_TYPE.HARMONY\n  } else if (process.env.TARO_ENV === 'quickapp') {\n    return ENV_TYPE.QUICKAPP\n  } else {\n    return process.env.TARO_ENV || 'Unknown'\n  }\n}\n", "/* eslint-disable camelcase */\nimport {\n  Current,\n  eventCenter,\n  Events,\n  getCurrentInstance,\n  nextTick,\n  options\n} from '@tarojs/runtime'\n\nimport { ENV_TYPE, getEnv } from './env'\nimport Link, { interceptorify } from './interceptor'\nimport * as interceptors from './interceptor/interceptors'\nimport {\n  Behavior,\n  getInitPxTransform,\n  getPreload,\n  getPxTransform,\n} from './tools'\n\nconst Taro: Record<string, unknown> = {\n  Behavior,\n  getEnv,\n  ENV_TYPE,\n  Link,\n  interceptors,\n  Current,\n  getCurrentInstance,\n  options,\n  nextTick,\n  eventCenter,\n  Events,\n  getInitPxTransform,\n  interceptorify\n}\n\nTaro.initPxTransform = getInitPxTransform(Taro)\nTaro.preload = getPreload(Current)\nTaro.pxTransform = getPxTransform(Taro)\n\nexport default Taro\n", "import { isFunction } from '@tarojs/shared'\n\nexport type TInterceptor = (c: Chain) => Promise<void>\n\nexport interface IRequestParams {\n  timeout?: number\n  method?: string\n  url?: string\n  data?: unknown\n}\n\nexport default class Chain {\n  index: number\n  requestParams: IRequestParams\n  interceptors: TInterceptor[]\n\n  constructor (requestParams?: IRequestParams, interceptors?: TInterceptor[], index?: number) {\n    this.index = index || 0\n    this.requestParams = requestParams || {}\n    this.interceptors = interceptors || []\n  }\n\n  proceed (requestParams: IRequestParams = {}) {\n    this.requestParams = requestParams\n    if (this.index >= this.interceptors.length) {\n      throw new Error('chain 参数错误, 请勿直接修改 request.chain')\n    }\n    const nextInterceptor = this._getNextInterceptor()\n    const nextChain = this._getNextChain()\n    const p = nextInterceptor(nextChain)\n    const res = p.catch(err => Promise.reject(err))\n    Object.keys(p).forEach(k => isFunction(p[k]) && (res[k] = p[k]))\n    return res\n  }\n\n  _getNextInterceptor () {\n    return this.interceptors[this.index]\n  }\n\n  _getNextChain () {\n    return new Chain(this.requestParams, this.interceptors, this.index + 1)\n  }\n}\n", "import Chain from './chain'\n\nimport type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TInterceptor } from './chain'\n\nexport default class Link {\n  taroInterceptor: TInterceptor\n  chain: Chain\n\n  constructor (interceptor: TInterceptor) {\n    this.taroInterceptor = interceptor\n    this.chain = new Chain()\n  }\n\n  request (requestParams: IRequestParams) {\n    const chain = this.chain\n    const taroInterceptor = this.taroInterceptor\n\n    chain.interceptors = chain.interceptors\n      .filter(interceptor => interceptor !== taroInterceptor)\n      .concat(taroInterceptor)\n\n    return chain.proceed({ ...requestParams })\n  }\n\n  addInterceptor (interceptor: TInterceptor) {\n    this.chain.interceptors.push(interceptor)\n  }\n\n  cleanInterceptors () {\n    this.chain = new Chain()\n  }\n}\n\nexport function interceptorify (promiseifyApi) {\n  return new Link(function (chain) {\n    return promiseifyApi(chain.requestParams)\n  })\n}\n", "import { isFunction, isUndefined } from '@tarojs/shared'\n\nimport type Chain from './chain'\n\nexport function timeoutInterceptor (chain: Chain) {\n  const requestParams = chain.requestParams\n  let p: Promise<void>\n  const res = new Promise<void>((resolve, reject) => {\n    const timeout: ReturnType<typeof setTimeout> = setTimeout(() => {\n      clearTimeout(timeout)\n      reject(new Error('网络链接超时,请稍后再试！'))\n    }, (requestParams && requestParams.timeout) || 60000)\n\n    p = chain.proceed(requestParams)\n    p\n      .then(res => {\n        if (!timeout) return\n        clearTimeout(timeout)\n        resolve(res)\n      })\n      .catch(err => {\n        timeout && clearTimeout(timeout)\n        reject(err)\n      })\n  })\n  // @ts-ignore\n  if (!isUndefined(p) && isFunction(p.abort)) res.abort = p.abort\n\n  return res\n}\n\nexport function logInterceptor (chain: Chain) {\n  const requestParams = chain.requestParams\n  const { method, data, url } = requestParams\n\n  // eslint-disable-next-line no-console\n  console.log(`http ${method || 'GET'} --> ${url} data: `, data)\n\n  const p = chain.proceed(requestParams)\n  const res = p\n    .then(res => {\n      // eslint-disable-next-line no-console\n      console.log(`http <-- ${url} result:`, res)\n      return res\n    })\n  // @ts-ignore\n  if (isFunction(p.abort)) res.abort = p.abort\n\n  return res\n}\n", "import { isFunction, isObject } from '@tarojs/shared'\n\nexport function Behavior (options) {\n  return options\n}\n\nexport function getPreload (current) {\n  return function (key: any, val: unknown) {\n    current.preloadData = isObject(key)\n      ? key\n      : {\n        [key]: val\n      }\n  }\n}\n\nconst defaultDesignWidth = 750\nconst defaultDesignRatio = {\n  640: 2.34 / 2,\n  750: 1,\n  828: 1.81 / 2\n}\nconst defaultBaseFontSize = 20\nconst defaultUnitPrecision = 5\nconst defaultTargetUnit = 'rpx'\n\nexport function getInitPxTransform (taro) {\n  return function (config) {\n    const {\n      designWidth = defaultDesignWidth,\n      deviceRatio = defaultDesignRatio,\n      baseFontSize = defaultBaseFontSize,\n      targetUnit = defaultTargetUnit,\n      unitPrecision = defaultUnitPrecision,\n    } = config\n    taro.config = taro.config || {}\n    taro.config.designWidth = designWidth\n    taro.config.deviceRatio = deviceRatio\n    taro.config.baseFontSize = baseFontSize\n    taro.config.targetUnit = targetUnit\n    taro.config.unitPrecision = unitPrecision\n  }\n}\n\nexport function getPxTransform (taro) {\n  return function (size) {\n    const config = taro.config || {}\n    const baseFontSize = config.baseFontSize\n    const deviceRatio = config.deviceRatio || defaultDesignRatio\n    const designWidth = ((input = 0) => isFunction(config.designWidth)\n      ? config.designWidth(input)\n      : config.designWidth || defaultDesignWidth)(size)\n    if (!(designWidth in deviceRatio)) {\n      throw new Error(`deviceRatio 配置中不存在 ${designWidth} 的设置！`)\n    }\n    const targetUnit = config.targetUnit || defaultTargetUnit\n    const unitPrecision = config.unitPrecision || defaultUnitPrecision\n    const formatSize = ~~size\n    let rootValue = 1 / deviceRatio[designWidth]\n    switch (targetUnit) {\n      case 'rem':\n        rootValue *= baseFontSize * 2\n        break\n      case 'px':\n        rootValue *= 2\n        break\n    }\n    let val = formatSize / rootValue\n    if (unitPrecision >= 0 && unitPrecision <= 100) {\n      val = Number(val.toFixed(unitPrecision))\n    }\n    return val + targetUnit\n  }\n}\n", "import { EMPTY_OBJ } from '@tarojs/shared'\n\nimport type React from 'react'\n\ninterface ReactMeta {\n  PageContext: React.Context<string>\n  R: typeof React\n}\n\nexport const reactMeta: ReactMeta = {\n  PageContext: EMPTY_OBJ,\n  R: EMPTY_OBJ,\n}\n", "import { Current } from '@tarojs/runtime'\nimport { isArray, isFunction } from '@tarojs/shared'\n\nimport type * as React from 'react'\n\nexport const HOOKS_APP_ID = 'taro-app'\n\nexport function isClassComponent (R: typeof React, component): boolean {\n  const prototype = component.prototype\n\n  // For React Redux\n  if (component.displayName?.includes('Connect')) return false\n\n  return (\n    isFunction(component.render) ||\n    !!prototype?.isReactComponent ||\n    prototype instanceof R.Component // compat for some others react-like library\n  )\n}\n\nexport function ensureIsArray<T> (item: T | T[]): T[] {\n  if (isArray(item)) {\n    return item\n  } else {\n    return item ? [item] : []\n  }\n}\n\n/**\n * set writable, enumerable to true\n */\nexport function setDefaultDescriptor (obj: Record<string, any>) {\n  obj.writable = true\n  obj.enumerable = true\n  return obj\n}\n\n/**\n * 设置入口的路由参数\n * @param options 小程序传入的参数\n */\nexport function setRouterParams (options) {\n  Current.router = {\n    params: options?.query,\n    ...options\n  }\n}\n", "import { Current, getPageInstance, injectPageInstance } from '@tarojs/runtime'\nimport { isArray, isFunction } from '@tarojs/shared'\n\nimport { reactMeta } from './react-meta'\nimport { HOOKS_APP_ID } from './utils'\n\nimport type { AppInstance, Instance, PageLifeCycle, PageProps } from '@tarojs/runtime'\nimport type { Func } from '@tarojs/taro/types/compile'\n\nconst createTaroHook = (lifecycle: keyof PageLifeCycle | keyof AppInstance) => {\n  return (fn: Func) => {\n    const { R: React, PageContext } = reactMeta\n    const id = React.useContext(PageContext) || HOOKS_APP_ID\n    const instRef = React.useRef<Instance<PageProps>>()\n\n    // hold fn ref and keep up to date\n    const fnRef = React.useRef(fn)\n    if (fnRef.current !== fn) fnRef.current = fn\n\n    React.useLayoutEffect(() => {\n      let inst = instRef.current = getPageInstance(id)\n      let first = false\n      if (!inst) {\n        first = true\n        instRef.current = Object.create(null)\n        inst = instRef.current!\n      }\n\n      // callback is immutable but inner function is up to date\n      const callback = (...args: any) => fnRef.current(...args)\n\n      if (isFunction(inst[lifecycle])) {\n        (inst[lifecycle]) = [inst[lifecycle], callback]\n      } else {\n        (inst[lifecycle]) = [\n          ...((inst[lifecycle]) || []),\n          callback\n        ]\n      }\n\n      if (first) {\n        injectPageInstance(inst!, id)\n      }\n      return () => {\n        const inst = instRef.current\n        if (!inst) return\n        const list = inst![lifecycle]\n        if (list === callback) {\n          (inst[lifecycle]) = undefined\n        } else if (isArray(list)) {\n          (inst[lifecycle]) = list.filter(item => item !== callback)\n        }\n        instRef.current = undefined\n      }\n    }, [])\n  }\n}\n\n/** LifeCycle */\nexport const useDidHide = createTaroHook('componentDidHide')\nexport const useDidShow = createTaroHook('componentDidShow')\n\n/** App */\nexport const useError = createTaroHook('onError')\nexport const useUnhandledRejection = createTaroHook('onUnhandledRejection')\nexport const useLaunch = createTaroHook('onLaunch')\nexport const usePageNotFound = createTaroHook('onPageNotFound')\n\n/** Page */\nexport const useLoad = createTaroHook('onLoad')\nexport const usePageScroll = createTaroHook('onPageScroll')\nexport const usePullDownRefresh = createTaroHook('onPullDownRefresh')\nexport const usePullIntercept = createTaroHook('onPullIntercept')\nexport const useReachBottom = createTaroHook('onReachBottom')\nexport const useResize = createTaroHook('onResize')\nexport const useUnload = createTaroHook('onUnload')\n\n/** Mini-Program */\nexport const useAddToFavorites = createTaroHook('onAddToFavorites')\nexport const useOptionMenuClick = createTaroHook('onOptionMenuClick')\nexport const useSaveExitState = createTaroHook('onSaveExitState')\nexport const useShareAppMessage = createTaroHook('onShareAppMessage')\nexport const useShareTimeline = createTaroHook('onShareTimeline')\nexport const useTitleClick = createTaroHook('onTitleClick')\n\n/** Router */\nexport const useReady = createTaroHook('onReady')\nexport const useRouter = (dynamic = false) => {\n  const React = reactMeta.R\n  return dynamic ? Current.router : React.useMemo(() => Current.router, [])\n}\nexport const useTabItemTap = createTaroHook('onTabItemTap')\n\nexport const useScope = () => undefined\n", "import { CONTAINER, Current, document, getPageInstance, incrementId, injectPageInstance, PAGE_INIT, perf } from '@tarojs/runtime'\nimport { EMPTY_OBJ, ensure, hooks } from '@tarojs/shared'\n\nimport { reactMeta } from './react-meta'\nimport { ensureIsArray, HOOKS_APP_ID, isClassComponent, setDefaultDescriptor, setRouterParams } from './utils'\n\nimport type { AppInstance, Instance, PageLifeCycle, PageProps, ReactAppInstance, ReactPageComponent } from '@tarojs/runtime'\nimport type { AppConfig } from '@tarojs/taro'\nimport type React from 'react'\nimport type TReactDOM from 'react-dom'\nimport type TReactDOMClient from 'react-dom/client'\n\ntype PageComponent = React.CElement<PageProps, React.Component<PageProps, any, any>>\n\nlet h: typeof React.createElement\nlet ReactDOM: typeof TReactDOM & typeof TReactDOMClient\nlet Fragment: typeof React.Fragment\n\nconst pageKeyId = incrementId()\n\nexport function setReconciler (ReactDOM?) {\n  hooks.tap('getLifecycle', function (instance, lifecycle: string) {\n    lifecycle = lifecycle.replace(/^on(Show|Hide)$/, 'componentDid$1')\n    return instance[lifecycle]\n  })\n\n  hooks.tap('modifyMpEvent', function (event) {\n    // Note: ohos 上事件没有设置 type 类型 setter 方法导致报错\n    Object.defineProperty(event, 'type', {\n      value: event.type.replace(/-/g, '')\n    })\n  })\n\n  hooks.tap('batchedEventUpdates', function (cb) {\n    ReactDOM?.unstable_batchedUpdates(cb)\n  })\n\n  hooks.tap('mergePageInstance', function (prev, next) {\n    if (!prev || !next) return\n\n    // 子组件使用 lifecycle hooks 注册了生命周期后，会存在 prev，里面是注册的生命周期回调。\n\n    // prev 使用 Object.create(null) 创建，H5 的 fast-refresh 可能也会导致存在 prev，要排除这些意外产生的 prev\n    if ('constructor' in prev) return\n\n    Object.keys(prev).forEach(item => {\n      const prevList = prev[item]\n      const nextList = ensureIsArray<() => any>(next[item])\n      next[item] = nextList.concat(prevList)\n    })\n  })\n\n  if (process.env.TARO_PLATFORM === 'web') {\n    hooks.tap('createPullDownComponent', (\n      el: React.FunctionComponent<PageProps> | React.ComponentClass<PageProps>,\n      _,\n      R: typeof React,\n      customWrapper\n    ) => {\n      const isReactComponent = isClassComponent(R, el)\n\n      return R.forwardRef((props, ref) => {\n        const newProps: React.ComponentProps<any> = { ...props }\n        const refs = isReactComponent ? { ref: ref } : {\n          forwardedRef: ref,\n          // 兼容 react-redux 7.20.1+\n          reactReduxForwardedRef: ref\n        }\n\n        return h(\n          customWrapper || 'taro-pull-to-refresh-core',\n          null,\n          h(el, {\n            ...newProps,\n            ...refs\n          })\n        )\n      })\n    })\n\n    hooks.tap('getDOMNode', (inst) => {\n      // 由于react 18移除了ReactDOM.findDOMNode方法，修复H5端 Taro.createSelectorQuery设置in(scope)时，报错问题\n      // https://zh-hans.react.dev/reference/react-dom/findDOMNode\n      if (!inst) {\n        return document\n      } else if (inst instanceof HTMLElement) {\n        return inst\n      } else if (inst.$taroPath) {\n        const el = document.getElementById(inst.$taroPath)\n        return el ?? document\n      }\n    })\n  }\n}\n\nexport function connectReactPage (\n  R: typeof React,\n  id: string\n) {\n  return (Page: ReactPageComponent): React.ComponentClass<PageProps> => {\n    // eslint-disable-next-line dot-notation\n    const isReactComponent = isClassComponent(R, Page)\n    const inject = (node?: Instance) => node && injectPageInstance(node, id)\n    const refs = isReactComponent ? { ref: inject } : {\n      forwardedRef: inject,\n      // 兼容 react-redux 7.20.1+\n      reactReduxForwardedRef: inject\n    }\n\n    if (reactMeta.PageContext === EMPTY_OBJ) {\n      reactMeta.PageContext = R.createContext('')\n    }\n\n    return class PageWrapper extends R.Component<PageProps, { hasError: boolean }> {\n      state = {\n        hasError: false\n      }\n\n      static getDerivedStateFromError (error: Error) {\n        Current.app?.onError?.(error.message + error.stack)\n        return { hasError: true }\n      }\n\n      // React 16 uncaught error 会导致整个应用 crash，\n      // 目前把错误缩小到页面\n      componentDidCatch (error: Error, info: React.ErrorInfo) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.warn(error)\n          console.error(info.componentStack)\n        }\n      }\n\n      render () {\n        const children = this.state.hasError\n          ? []\n          : h(reactMeta.PageContext.Provider, { value: id }, h(Page, {\n            ...this.props,\n            ...refs\n          }))\n\n        if (process.env.TARO_PLATFORM === 'web') {\n          return h(\n            'div',\n            { id, className: 'taro_page' },\n            children\n          )\n        } else {\n          return h(\n            'root',\n            { id },\n            children\n          )\n        }\n      }\n    }\n  }\n}\n\n/**\n * 桥接小程序 App 构造器和 React 渲染流程\n * @param App 用户编写的入口组件\n * @param react 框架\n * @param dom 框架渲染器\n * @param config 入口组件配置 app.config.js 的内容\n * @returns 传递给 App 构造器的对象 obj ：App(obj)\n */\nexport function createReactApp (\n  App: React.ComponentClass,\n  react: typeof React,\n  dom,\n  config: AppConfig\n) {\n  if (process.env.NODE_ENV !== 'production') {\n    ensure(!!dom, '构建 React/Preact 项目请把 process.env.FRAMEWORK 设置为 \\'react\\'/\\'preact\\' ')\n  }\n\n  reactMeta.R = react\n  h = react.createElement\n  ReactDOM = dom\n  Fragment = react.Fragment\n  const appInstanceRef = react.createRef<ReactAppInstance>()\n  const isReactComponent = isClassComponent(react, App)\n  let appWrapper: AppWrapper\n  let appWrapperResolver: (value: AppWrapper) => void\n  const appWrapperPromise = new Promise<AppWrapper>(resolve => (appWrapperResolver = resolve))\n\n  setReconciler(ReactDOM)\n\n  function getAppInstance (): ReactAppInstance | null {\n    return appInstanceRef.current\n  }\n\n  function waitAppWrapper (cb: () => void) {\n    /**\n     * 当同个事件触发多次时，waitAppWrapper 会出现同步和异步任务的执行顺序问题，\n     * 导致某些场景下 onShow 会优于 onLaunch 执行\n     */\n    appWrapperPromise.then(() => cb())\n    // appWrapper ? cb() : appWrapperPromise.then(() => cb())\n  }\n\n  function renderReactRoot () {\n    const appId = config?.appId || 'app'\n    let container = document.getElementById(appId)\n    if (container == null) {\n      const appContainer = document.getElementById(CONTAINER)\n      container = document.createElement(appId)\n      container.id = appId\n      appContainer?.appendChild(container)\n    }\n    if ((react.version || '').startsWith('18')) {\n      const root = ReactDOM.createRoot((container as unknown as Element))\n      root.render?.(h(AppWrapper))\n    } else {\n      // eslint-disable-next-line react/no-deprecated\n      ReactDOM.render?.(h(AppWrapper), container)\n    }\n  }\n\n  class AppWrapper extends react.Component {\n    // run createElement() inside the render function to make sure that owner is right\n    private pages: Array<() => PageComponent> = []\n    private elements: Array<PageComponent> = []\n\n    constructor (props) {\n      super(props)\n      appWrapper = this\n      appWrapperResolver(this)\n    }\n\n    public mount (pageComponent: ReactPageComponent, id: string, cb: () => void) {\n      const pageWrapper = connectReactPage(react, id)(pageComponent)\n      const key = id + pageKeyId()\n      const page = () => h(pageWrapper, { key, tid: id })\n      this.pages.push(page)\n      this.forceUpdate((...args) => {\n        perf.stop(PAGE_INIT)\n        return cb(...args)\n      })\n    }\n\n    public unmount (id: string, cb: () => void) {\n      const elements = this.elements\n      const idx = elements.findIndex(item => item.props.tid === id)\n      elements.splice(idx, 1)\n      this.forceUpdate(cb)\n    }\n\n    public render () {\n      const { pages, elements } = this\n\n      while (pages.length > 0) {\n        const page = pages.pop()!\n        elements.push(page())\n      }\n\n      let props: React.ComponentProps<any> | null = null\n\n      if (isReactComponent) {\n        props = { ref: appInstanceRef }\n      }\n\n      return h(\n        App,\n        props,\n        process.env.TARO_PLATFORM === 'web' ? h(Fragment ?? 'div', null, elements.slice()) : elements.slice()\n      )\n    }\n  }\n\n  if (process.env.TARO_PLATFORM !== 'web') {\n    renderReactRoot()\n  }\n\n  const [ONLAUNCH, ONSHOW, ONHIDE] = hooks.call('getMiniLifecycleImpl')!.app\n\n  const appObj: AppInstance = Object.create({\n    render (cb: () => void) {\n      appWrapper.forceUpdate(cb)\n    },\n\n    mount (component: ReactPageComponent, id: string, cb: () => void) {\n      if (appWrapper) {\n        appWrapper.mount(component, id, cb)\n      } else {\n        appWrapperPromise.then(appWrapper => appWrapper.mount(component, id, cb))\n      }\n    },\n\n    unmount (id: string, cb: () => void) {\n      if (appWrapper) {\n        appWrapper.unmount(id, cb)\n      } else {\n        appWrapperPromise.then(appWrapper => appWrapper.unmount(id, cb))\n      }\n    }\n  }, {\n    config: setDefaultDescriptor({\n      configurable: true,\n      value: config\n    }),\n\n    [ONLAUNCH]: setDefaultDescriptor({\n      value (options) {\n        setRouterParams(options)\n\n        if (process.env.TARO_PLATFORM === 'web') {\n          // 由于 H5 路由初始化的时候会清除 app 下的 dom 元素，所以需要在路由初始化后执行 render\n          renderReactRoot()\n        }\n\n        const onLaunch = () => {\n          // 用户编写的入口组件实例\n          const app = getAppInstance()\n          this.$app = app\n\n          if (app) {\n            // 把 App Class 上挂载的额外属性同步到全局 app 对象中\n            if (app.taroGlobalData) {\n              const globalData = app.taroGlobalData\n              const keys = Object.keys(globalData)\n              const descriptors = Object.getOwnPropertyDescriptors(globalData)\n              keys.forEach(key => {\n                Object.defineProperty(this, key, {\n                  configurable: true,\n                  enumerable: true,\n                  get () {\n                    return globalData[key]\n                  },\n                  set (value) {\n                    globalData[key] = value\n                  }\n                })\n              })\n              Object.defineProperties(this, descriptors)\n            }\n\n            app.onLaunch?.(options)\n          }\n          triggerAppHook('onLaunch', options)\n        }\n\n        waitAppWrapper(onLaunch)\n      }\n    }),\n\n    [ONSHOW]: setDefaultDescriptor({\n      value (options) {\n        setRouterParams(options)\n\n        const onShow = () => {\n          /**\n          * trigger lifecycle\n          */\n          const app = getAppInstance()\n          // class component, componentDidShow\n          app?.componentDidShow?.(options)\n          // functional component, useDidShow\n          triggerAppHook('onShow', options)\n        }\n\n        waitAppWrapper(onShow)\n      }\n    }),\n\n    [ONHIDE]: setDefaultDescriptor({\n      value () {\n        const onHide = () => {\n          /**\n           * trigger lifecycle\n           */\n          const app = getAppInstance()\n          // class component, componentDidHide\n          app?.componentDidHide?.()\n          // functional component, useDidHide\n          triggerAppHook('onHide')\n        }\n\n        waitAppWrapper(onHide)\n      }\n    }),\n\n    onError: setDefaultDescriptor({\n      value (error: string) {\n        const onError = () => {\n          const app = getAppInstance()\n          app?.onError?.(error)\n          triggerAppHook('onError', error)\n          if (process.env.NODE_ENV !== 'production' && error?.includes('Minified React error')) {\n            console.warn('React 出现报错，请打开编译配置 mini.debugReact 查看报错详情：https://docs.taro.zone/docs/config-detail#minidebugreact')\n          }\n        }\n\n        waitAppWrapper(onError)\n      }\n    }),\n\n    onUnhandledRejection: setDefaultDescriptor({\n      value (res: unknown) {\n        const onUnhandledRejection = () => {\n          const app = getAppInstance()\n          app?.onUnhandledRejection?.(res)\n          triggerAppHook('onUnhandledRejection', res)\n        }\n\n        waitAppWrapper(onUnhandledRejection)\n      }\n    }),\n\n    onPageNotFound: setDefaultDescriptor({\n      value (res: unknown) {\n        const onPageNotFound = () => {\n          const app = getAppInstance()\n          app?.onPageNotFound?.(res)\n          triggerAppHook('onPageNotFound', res)\n        }\n\n        waitAppWrapper(onPageNotFound)\n      }\n    })\n  })\n\n  function triggerAppHook (lifecycle: keyof PageLifeCycle | keyof AppInstance, ...option) {\n    const instance = getPageInstance(HOOKS_APP_ID)\n    if (instance) {\n      const app = getAppInstance()\n      const func = hooks.call('getLifecycle', instance, lifecycle)\n      if (Array.isArray(func)) {\n        func.forEach(cb => cb.apply(app, option))\n      }\n    }\n  }\n\n  Current.app = appObj\n  return appObj\n}\n", "import {\n  addLeadingSlash, CONTEXT_ACTIONS, Current, document, eventCenter,\n  eventHandler, getOnHideEventKey, getOnReadyEventKey, getOnShowEventKey, getPageInstance, getPath,\n  incrementId, injectPageInstance, ON_HIDE, ON_READY, ON_SHOW,\n  removePageInstance, requestAnimationFrame, safeExecute, window\n} from '@tarojs/runtime'\nimport { EMPTY_OBJ, ensure, hooks, isUndefined } from '@tarojs/shared'\n\nimport { setReconciler } from './connect'\nimport { reactMeta } from './react-meta'\nimport { isClassComponent } from './utils'\n\nimport type { Instance, TaroRootElement } from '@tarojs/runtime' // eslint-disable-line import/no-duplicates\nimport type { MpInstance } from '@tarojs/runtime/dist/runtime.esm' // eslint-disable-line import/no-duplicates\nimport type { AppInstance, PageInstance } from '@tarojs/taro'\nimport type { Func } from '@tarojs/taro/types/compile'\nimport type React from 'react'\n\ndeclare const getCurrentPages: () => PageInstance[]\n\nconst getNativeCompId = incrementId()\nlet h: typeof React.createElement\nlet ReactDOM\nlet nativeComponentApp: AppInstance\ninterface InitNativeComponentEntryParams {\n  R: typeof React\n  ReactDOM: typeof ReactDOM\n  cb?: Func\n  // 是否使用默认的 DOM 入口 - app；默认为true，false的时候，会创建一个新的dom并且把它挂载在 app 下面\n  isDefaultEntryDom?: boolean\n}\n\nfunction initNativeComponentEntry (params: InitNativeComponentEntryParams) {\n  const { R, ReactDOM, cb, isDefaultEntryDom = true } = params\n  interface IEntryState {\n    components: {\n      compId: string\n      element: React.ReactElement\n    }[]\n  }\n\n  interface IWrapperProps {\n    compId: string\n    getCtx: () => any\n    renderComponent: (ctx: any) => React.ReactElement\n  }\n\n  class NativeComponentWrapper extends R.Component<IWrapperProps, Record<any, any>> {\n    root = R.createRef<TaroRootElement>()\n    ctx = this.props.getCtx()\n\n    componentDidMount () {\n      this.ctx.component = this\n      const rootElement = this.root.current!\n      rootElement.ctx = this.ctx\n      rootElement.performUpdate(true)\n    }\n\n    render () {\n      return (\n        h(\n          'root',\n          {\n            ref: this.root,\n            id: this.props.compId\n          },\n          this.props.renderComponent(this.ctx)\n        )\n      )\n    }\n  }\n\n  class Entry extends R.Component<Record<any, any>, IEntryState> {\n    state: IEntryState = {\n      components: []\n    }\n\n    componentDidMount () {\n      if (isDefaultEntryDom) {\n        Current.app = this\n      } else {\n        nativeComponentApp = this\n      }\n      cb && cb()\n    }\n\n    mount (Component, compId, getCtx, cb?) {\n      const isReactComponent = isClassComponent(R, Component)\n      const inject = (node?: Instance) => node && injectPageInstance(node, compId)\n      const refs = isReactComponent ? { ref: inject } : {\n        forwardedRef: inject,\n        reactReduxForwardedRef: inject\n      }\n      if (reactMeta.PageContext === EMPTY_OBJ) {\n        reactMeta.PageContext = R.createContext('')\n      }\n      const item = {\n        compId,\n        element: h(NativeComponentWrapper, {\n          key: compId,\n          compId,\n          getCtx,\n          renderComponent (ctx) {\n            return h(\n              reactMeta.PageContext.Provider,\n              { value: compId },\n              h(\n                Component,\n                {\n                  ...(ctx.data ||= {}).props,\n                  ...refs,\n                  $scope: ctx\n                }\n              )\n            )\n          }\n        })\n      }\n      this.setState({\n        components: [...this.state.components, item]\n      }, () => cb && cb())\n    }\n\n    unmount (compId, cb?) {\n      const components = this.state.components\n      const index = components.findIndex(item => item.compId === compId)\n      const next = [...components.slice(0, index), ...components.slice(index + 1)]\n      this.setState({\n        components: next\n      }, () => {\n        removePageInstance(compId)\n        cb && cb()\n      })\n    }\n\n    render () {\n      const components = this.state.components\n\n      return (\n        components.map(({ element }) => element)\n      )\n    }\n  }\n\n  setReconciler(ReactDOM)\n\n  let app = document.getElementById('app')\n  if (!isDefaultEntryDom && !nativeComponentApp) {\n    // create\n    const nativeApp = document.createElement('nativeComponent')\n    // insert\n    app?.parentNode?.appendChild(nativeApp)\n    app = nativeApp\n  }\n  // eslint-disable-next-line react/no-deprecated\n  ReactDOM.render(\n    h(Entry, {}),\n    app\n  )\n}\n\nexport function createNativePageConfig (Component, pageName: string, data: Record<string, unknown>, react: typeof React, reactDOM: typeof ReactDOM, pageConfig) {\n  reactMeta.R = react\n  h = react.createElement\n  ReactDOM = reactDOM\n  setReconciler(ReactDOM)\n  const [\n    ONLOAD,\n    ONUNLOAD,\n    ONREADY,\n    ONSHOW,\n    ONHIDE,\n    LIFECYCLES,\n    SIDE_EFFECT_LIFECYCLES\n  ] = hooks.call('getMiniLifecycleImpl')!.page\n  let unmounting = false\n  let prepareMountList: (() => void)[] = []\n  let pageElement: TaroRootElement | null = null\n  let loadResolver: (...args: unknown[]) => void\n  let hasLoaded: Promise<void>\n  const id = pageName ?? `taro_page_${getNativeCompId()}`\n  function setCurrentRouter (page: MpInstance) {\n    const router = page.route || page.__route__ || page.$taroPath\n    Current.router = {\n      params: page.$taroParams!,\n      path: addLeadingSlash(router),\n      $taroPath: page.$taroPath,\n      onReady: getOnReadyEventKey(id),\n      onShow: getOnShowEventKey(id),\n      onHide: getOnHideEventKey(id)\n    }\n    if (!isUndefined(page.exitState)) {\n      Current.router.exitState = page.exitState\n    }\n  }\n\n  const pageObj: Record<string, any> = {\n    options: pageConfig,\n    [ONLOAD] (this: MpInstance, options: Readonly<Record<string, unknown>> = {}, cb?: TaroGeneral.TFunc) {\n      hasLoaded = new Promise(resolve => { loadResolver = resolve })\n      Current.page = this as any\n      this.config = pageConfig || {}\n      // this.$taroPath 是页面唯一标识\n      const uniqueOptions = Object.assign({}, options, { $taroTimestamp: Date.now() })\n      const $taroPath = this.$taroPath = getPath(id, uniqueOptions)\n\n      // this.$taroParams 作为暴露给开发者的页面参数对象，可以被随意修改\n      if (this.$taroParams == null) {\n        this.$taroParams = uniqueOptions\n      }\n\n      setCurrentRouter(this)\n      window.trigger(CONTEXT_ACTIONS.INIT, $taroPath)\n\n      const mountCallback = () => {\n        pageElement = document.getElementById($taroPath)\n        ensure(pageElement !== null, '没有找到页面实例。')\n        safeExecute($taroPath, ONLOAD, this.$taroParams)\n        loadResolver()\n        pageElement.ctx = this\n        pageElement.performUpdate(true, cb)\n      }\n\n      const mount = () => {\n        if (!Current.app) {\n          initNativeComponentEntry({\n            R: react,\n            ReactDOM,\n            cb: () => {\n              Current.app!.mount!(Component, $taroPath, () => this, mountCallback)\n            }\n          })\n        } else {\n          Current.app!.mount!(Component, $taroPath, () => this, mountCallback)\n        }\n      }\n\n      if (unmounting) {\n        prepareMountList.push(mount)\n      } else {\n        mount()\n      }\n    },\n    [ONUNLOAD] () {\n      const $taroPath = this.$taroPath\n      // 销毁当前页面的上下文信息\n      window.trigger(CONTEXT_ACTIONS.DESTORY, $taroPath)\n      // 触发onUnload生命周期\n      safeExecute($taroPath, ONUNLOAD)\n      resetCurrent()\n      unmounting = true\n      Current.app!.unmount!($taroPath, () => {\n        unmounting = false\n        removePageInstance($taroPath)\n        if (pageElement) {\n          pageElement.ctx = null\n          pageElement = null\n        }\n        if (prepareMountList.length) {\n          prepareMountList.forEach(fn => fn())\n          prepareMountList = []\n        }\n      })\n    },\n    [ONREADY] () {\n      hasLoaded.then(() => {\n        // 触发生命周期\n        safeExecute(this.$taroPath, ON_READY)\n        // 通过事件触发子组件的生命周期\n        requestAnimationFrame(() => eventCenter.trigger(getOnReadyEventKey(id)))\n        this.onReady.called = true\n      })\n    },\n    [ONSHOW] (options = {}) {\n      hasLoaded.then(() => {\n        // 设置 Current 的 page 和 router\n        Current.page = this as any\n        setCurrentRouter(this)\n        // 恢复上下文信息\n        window.trigger(CONTEXT_ACTIONS.RECOVER, this.$taroPath)\n        // 触发生命周期\n        safeExecute(this.$taroPath, ON_SHOW, options)\n        // 通过事件触发子组件的生命周期\n        requestAnimationFrame(() => eventCenter.trigger(getOnShowEventKey(id)))\n      })\n    },\n    [ONHIDE] () {\n      // 缓存当前页面上下文信息\n      window.trigger(CONTEXT_ACTIONS.RESTORE, this.$taroPath)\n      // 设置 Current 的 page 和 router\n      if (Current.page === this) {\n        Current.page = null\n        Current.router = null\n      }\n      // 触发生命周期\n      safeExecute(this.$taroPath, ON_HIDE)\n      // 通过事件触发子组件的生命周期\n      eventCenter.trigger(getOnHideEventKey(id))\n    },\n  }\n\n  function resetCurrent () {\n    // 小程序插件页面卸载之后返回到宿主页面时，需重置Current页面和路由。否则引发插件组件二次加载异常 fix:#11991\n    Current.page = null\n    Current.router = null\n  }\n\n  LIFECYCLES.forEach((lifecycle) => {\n    pageObj[lifecycle] = function () {\n      return safeExecute(this.$taroPath, lifecycle, ...arguments)\n    }\n  })\n\n  // onShareAppMessage 和 onShareTimeline 一样，会影响小程序右上方按钮的选项，因此不能默认注册。\n  SIDE_EFFECT_LIFECYCLES.forEach(lifecycle => {\n    if (Component[lifecycle] ||\n      Component.prototype?.[lifecycle] ||\n      Component[lifecycle.replace(/^on/, 'enable')]\n    ) {\n      pageObj[lifecycle] = function (...args) {\n        const target = args[0]?.target\n        if (target?.id) {\n          const id = target.id\n          const element = document.getElementById(id)\n          if (element) {\n            target.dataset = element.dataset\n          }\n        }\n        return safeExecute(this.$taroPath, lifecycle, ...args)\n      }\n    }\n  })\n\n  pageObj.eh = eventHandler\n\n  if (!isUndefined(data)) {\n    pageObj.data = data\n  }\n\n  hooks.call('modifyPageObject', pageObj)\n\n  return pageObj\n}\n\nexport function createH5NativeComponentConfig (\n  Component,\n  react: typeof React,\n  reactdom: typeof ReactDOM,\n) {\n  reactMeta.R = react\n  h = react.createElement\n  ReactDOM = reactdom\n  setReconciler(ReactDOM)\n\n  return Component\n}\n\nexport function createNativeComponentConfig (Component, react: typeof React, reactdom, componentConfig) {\n  reactMeta.R = react\n  h = react.createElement\n  ReactDOM = reactdom\n  setReconciler(ReactDOM)\n  const { isNewBlended } = componentConfig\n\n  const componentObj: Record<string, any> = {\n    options: componentConfig,\n    properties: {\n      props: {\n        type: null,\n        value: null,\n        observer (_newVal, oldVal) {\n          if (process.env.TARO_ENV === 'swan') {\n            // 百度模版传递 props 时 函数参数会被忽略，这里需要根据 id 获取 TaroElement 中的 props 赋值到 ctx.data 中\n            const inst: any = document.getElementById(this.id)\n            if (this.component?.ctx?.data && inst) {\n              this.component.ctx.data.props = inst?.props?.props\n            }\n          }\n          oldVal && this.component?.forceUpdate()\n        }\n      }\n    },\n    created () {\n      if (process.env.TARO_ENV === 'swan') {\n        const inst: any = document.getElementById(this.id)\n        // 百度小程序 真机上 props中的函数会被转为Object 调用报错 导致后续组件无法渲染 这里先取TaroElement上的props，在properties中会重新赋值\n        if (this.data?.props && inst) {\n          this.data.props = inst.props?.props || {}\n        }\n      }\n      const app = (isNewBlended ? nativeComponentApp : Current.app)\n      if (!app) {\n        initNativeComponentEntry({\n          R: react,\n          ReactDOM,\n          isDefaultEntryDom: !isNewBlended\n        })\n      }\n    },\n    attached () {\n      const compId = this.compId = getNativeCompId()\n      setCurrent(compId)\n      this.config = componentConfig\n      const app = (isNewBlended ? nativeComponentApp : Current.app)\n      app!.mount!(Component, compId, () => this, () => {\n        const instance = getPageInstance(compId)\n\n        if (instance && instance.node) {\n          const el = document.getElementById(instance.node.uid)\n\n          if (el) {\n            el.ctx = this\n          }\n        }\n      })\n    },\n    ready () {\n      safeExecute(this.compId, 'onReady')\n    },\n    detached () {\n      resetCurrent()\n      const app = (isNewBlended ? nativeComponentApp : Current.app)\n      app!.unmount!(this.compId)\n    },\n    pageLifetimes: {\n      show (options) {\n        safeExecute(this.compId, 'onShow', options)\n      },\n      hide () {\n        safeExecute(this.compId, 'onHide')\n      }\n    },\n    methods: {\n      eh: eventHandler,\n      onLoad (options) {\n        safeExecute(this.compId, 'onLoad', options)\n      },\n      onUnload () {\n        safeExecute(this.compId, 'onUnload')\n      }\n    }\n  }\n\n  function resetCurrent () {\n    // 小程序插件页面卸载之后返回到宿主页面时，需重置Current页面和路由。否则引发插件组件二次加载异常 fix:#11991\n    Current.page = null\n    Current.router = null\n  }\n\n  // onShareAppMessage 和 onShareTimeline 一样，会影响小程序右上方按钮的选项，因此不能默认注册。\n  if (\n    Component.onShareAppMessage ||\n    Component.prototype?.onShareAppMessage ||\n    Component.enableShareAppMessage\n  ) {\n    componentObj.methods.onShareAppMessage = function (options) {\n      const target = options?.target\n      if (target) {\n        const id = target.id\n        const element = document.getElementById(id)\n        if (element) {\n          target!.dataset = element.dataset\n        }\n      }\n      return safeExecute(this.compId, 'onShareAppMessage', options)\n    }\n  }\n  if (\n    Component.onShareTimeline ||\n    Component.prototype?.onShareTimeline ||\n    Component.enableShareTimeline\n  ) {\n    componentObj.methods.onShareTimeline = function () {\n      return safeExecute(this.compId, 'onShareTimeline')\n    }\n  }\n\n  if (process.env.TARO_ENV === 'alipay') {\n    /**\n     * 支付宝需要修改生命周期 同时宿主需要开启component2\n     * 如果不开启 props对象中的函数参数会被忽略 导致无法调用\n     * @see https://opendocs.alipay.com/mini/03dbc3#compileOptions\n     * @returns\n     */\n    componentObj.onInit = componentObj.created\n    componentObj.didMount = componentObj.attached\n    componentObj.didUpdate = function () {\n      this.data.props = this.props.props\n      this?.component?.forceUpdate?.()\n    }\n    componentObj.didUnmount = componentObj.detached\n  }\n  return componentObj\n}\n\nfunction setCurrent (compId: string) {\n  if (!getCurrentPages || typeof getCurrentPages !== 'function') return\n\n  const pages = getCurrentPages()\n  const currentPage = pages[pages.length - 1]\n  if (Current.page === currentPage) return\n\n  Current.page = currentPage\n\n  const route = (currentPage as any).route || (currentPage as any).__route__\n  const router = {\n    params: currentPage.options || {},\n    path: addLeadingSlash(route),\n    $taroPath: compId,\n    onReady: '',\n    onHide: '',\n    onShow: ''\n  }\n  Current.router = router\n\n  if (!currentPage.options) {\n    // 例如在微信小程序中，页面 options 的设置时机比组件 attached 慢\n    Object.defineProperty(currentPage, 'options', {\n      enumerable: true,\n      configurable: true,\n      get () {\n        return this._optionsValue\n      },\n      set (value) {\n        router.params = value\n        this._optionsValue = value\n      }\n    })\n  }\n}\n", "import { hooks } from '@tarojs/shared'\n\nimport * as taroHooks from './hooks'\n\nhooks.tap('initNativeApi', function (taro) {\n  for (const hook in taroHooks) {\n    taro[hook] = taroHooks[hook]\n  }\n})\n\nif (process.env.FRAMEWORK === 'preact' && process.env.TARO_PLATFORM === 'mini') {\n  const options = require('preact').options\n  const oldVNodeHook = options.vnode\n  const oldDiffedHook = options.diffed\n  options.vnode = vnode => {\n    const { type, props } = vnode\n    let normalizedProps = props\n\n    // only normalize props on Element nodes\n    if (typeof type === 'string') {\n      normalizedProps = {}\n\n      for (let i in props) {\n        const value = props[i]\n\n        if (/^on/.test(i)) {\n          i = i.toLowerCase()\n        }\n\n        if (type === 'map' && i === 'onregionchange') {\n          // map 组件的 regionchange 事件非常特殊，详情：https://github.com/NervJS/taro/issues/5766\n          normalizedProps.onbegin = value\n          normalizedProps.onend = value\n          continue\n        }\n\n        normalizedProps[i] = value\n      }\n\n      vnode.props = normalizedProps\n    }\n\n    if (oldVNodeHook) oldVNodeHook(vnode)\n  }\n  options.diffed = function (newVNode) {\n    const domProp = Object.keys(newVNode).find(k => (newVNode[k]?.setAttribute))\n    const dom = domProp ? newVNode[domProp] : null\n    const newVNodeProps = newVNode.props\n    if (dom) { /** ElementNode */\n      for (const propName in newVNodeProps) {\n        const propValue = newVNodeProps[propName]\n        if (propValue === false && dom.props?.[propName] === undefined) {\n          // 值为 false 的属性在 Preact 的 diff 中被 removeAttribute 了，这里手动 setAttribute\n          // fix https://github.com/NervJS/taro/issues/11197\n          dom.setAttribute(propName, propValue)\n        }\n      }\n    }\n    if (oldDiffedHook) oldDiffedHook(newVNode)\n  }\n\n  hooks.tap('modifyMpEvent', e => {\n    const type = e.type\n    if (type === 'tap') {\n      e.type = 'click'\n    } else if (type === 'focus') {\n      // 兼容 preact/compat/src/render.js options.vnode 的处理逻辑\n      e.type = 'focusin'\n    } else if (type === 'blur') {\n      e.type = 'focusout'\n    }\n  })\n  // hooks.modifyDispatchEventImpls?.push(e => {\n  // })\n}\n\nexport * from './connect'\nexport * from './connect-native'\nexport * from './hooks'\n", null, null, null, "export const View = 'view'\nexport const Icon = 'icon'\nexport const Progress = 'progress'\nexport const RichText = 'rich-text'\nexport const Text = 'text'\nexport const Button = 'button'\nexport const Checkbox = 'checkbox'\nexport const CheckboxGroup = 'checkbox-group'\nexport const Form = 'form'\nexport const Input = 'input'\nexport const Label = 'label'\nexport const Picker = 'picker'\nexport const PickerView = 'picker-view'\nexport const PickerViewColumn = 'picker-view-column'\nexport const Radio = 'radio'\nexport const RadioGroup = 'radio-group'\nexport const Slider = 'slider'\nexport const Switch = 'switch'\nexport const CoverImage = 'cover-image'\nexport const Textarea = 'textarea'\nexport const CoverView = 'cover-view'\nexport const MovableArea = 'movable-area'\nexport const MovableView = 'movable-view'\nexport const ScrollView = 'scroll-view'\nexport const Swiper = 'swiper'\nexport const SwiperItem = 'swiper-item'\nexport const Navigator = 'navigator'\nexport const Audio = 'audio'\nexport const Camera = 'camera'\nexport const Image = 'image'\nexport const LivePlayer = 'live-player'\nexport const Video = 'video'\nexport const Canvas = 'canvas'\nexport const Ad = 'ad'\nexport const WebView = 'web-view'\nexport const Block = 'block'\nexport const Map = 'map'\nexport const Slot = 'slot'\nexport const NativeSlot = 'native-slot'\nexport const CustomWrapper = 'custom-wrapper'\n", null, null, null, null, null, "export type WorkTag =\n  | 0\n  | 1\n  | 2\n  | 3\n  | 4\n  | 5\n  | 6\n  | 7\n  | 8\n  | 9\n  | 10\n  | 11\n  | 12\n  | 13\n  | 14\n  | 15\n  | 16\n  | 17\n  | 18\n  | 19\n  | 20\n  | 21\n  | 22\n  | 23\n  | 24\n  | 25\n\nexport const FunctionComponent = 0\nexport const ClassComponent = 1\nexport const IndeterminateComponent = 2 // Before we know whether it is function or class\nexport const HostRoot = 3 // Root of a host tree. Could be nested inside another node.\nexport const HostPortal = 4 // A subtree. Could be an entry point to a different renderer.\nexport const HostComponent = 5\nexport const HostText = 6\nexport const Fragment = 7\nexport const Mode = 8\nexport const ContextConsumer = 9\nexport const ContextProvider = 10\nexport const ForwardRef = 11\nexport const Profiler = 12\nexport const SuspenseComponent = 13\nexport const MemoComponent = 14\nexport const SimpleMemoComponent = 15\nexport const LazyComponent = 16\nexport const IncompleteClassComponent = 17\nexport const DehydratedFragment = 18\nexport const SuspenseListComponent = 19\nexport const ScopeComponent = 21\nexport const OffscreenComponent = 22\nexport const LegacyHiddenComponent = 23\nexport const CacheComponent = 24\nexport const TracingMarkerComponent = 25\n", "/**\n * 给 TaroElement 绑定 react fiber、react props 等属性\n * 提供 fiber -> element、element -> fiber、element -> props 的方法\n*/\nimport { internalContainerInstanceKey, internalInstanceKey, internalPropsKey } from './constant'\nimport { HostComponent, HostRoot, HostText, SuspenseComponent } from './workTags'\n\nimport type { TaroElement, TaroText } from '@tarojs/runtime'\nimport type { Fiber } from 'react-reconciler'\nimport type { Props } from './props'\n\nexport function precacheFiberNode (hostInst: Fiber, node: TaroElement | TaroText): void {\n  node[internalInstanceKey] = hostInst\n}\n\nexport function markContainerAsRoot (hostRoot: Fiber, node: TaroElement | TaroText): void {\n  node[internalContainerInstanceKey] = hostRoot\n}\n\nexport function unmarkContainerAsRoot (node: TaroElement | TaroText): void {\n  node[internalContainerInstanceKey] = null\n}\n\nexport function isContainerMarkedAsRoot (node: TaroElement | TaroText): boolean {\n  return !!node[internalContainerInstanceKey]\n}\n\n/**\n * Given a DOM node, return the ReactDOMComponent or ReactDOMTextComponent\n * instance, or null if the node was not rendered by this React.\n */\nexport function getInstanceFromNode (node: TaroElement | TaroText): Fiber | null {\n  const inst = node[internalInstanceKey] || node[internalContainerInstanceKey]\n\n  if (inst) {\n    if (\n      inst.tag === HostComponent ||\n      inst.tag === HostText ||\n      inst.tag === SuspenseComponent ||\n      inst.tag === HostRoot\n    ) {\n      return inst\n    } else {\n      return null\n    }\n  }\n  return null\n}\n\n/**\n * Given a ReactDOMComponent or ReactDOMTextComponent, return the corresponding\n * DOM node.\n */\nexport function getNodeFromInstance (inst: Fiber) {\n  if (inst.tag === HostComponent || inst.tag === HostText) {\n    // In Fiber this, is just the state node right now. We assume it will be\n    // a host component or host text.\n    return inst.stateNode\n  }\n}\n\nexport function getFiberCurrentPropsFromNode (node: TaroElement | TaroText): Props {\n  return node[internalPropsKey] || null\n}\n\nexport function updateFiberProps (\n  node: TaroElement | TaroText,\n  props: Props,\n): void {\n  node[internalPropsKey] = props\n\n  if (process.env.TARO_PLATFORM === 'harmony') {\n    // @ts-ignore\n    node.updateTextNode()\n  }\n}\n", "import { supportedInputTypes } from './constant'\nimport { Props } from './props'\n\nimport type { FormElement, TaroElement } from '@tarojs/runtime'\nimport type { RestoreType } from './event'\n\n// 从 props 中，更新 input 组件的 value 值\nfunction updateInputWrapper (element: TaroElement, oldValue: RestoreType, props: Props) {\n  const node = element\n  const checked = props.checked\n\n  if (checked != null) {\n    console.warn('updateCheck 未实现', node)\n    return\n  }\n\n  updateWrapper(element, oldValue, props)\n  updateNamedCousins(element, props)\n}\n\n// react 中原本处理 type=radio 的逻辑，这里留个空，暂时不处理\nfunction updateNamedCousins (rootNode, props) {\n  const name = props.name\n\n  if (props.type === 'radio' && name != null) {\n    console.warn('radio updateNamedCousins 未实现', rootNode, props)\n  }\n}\n\nexport function getToStringValue (value: any) {\n  const isEmptyType = typeof value === 'function' || typeof value === 'symbol'\n\n  return isEmptyType ? '' : value\n}\n\nexport function toString (value): string {\n  return '' + value\n}\n\nexport function updateWrapper (element: TaroElement, oldValue: RestoreType, props: Props) {\n  const node = element as FormElement\n  const value = getToStringValue(props.value)\n  const type = props.type as string\n\n  setNodeValue(node, oldValue, value, type)\n}\n\n// oldValue 为 event.detail.value，value 为 fiber.props.value\n// 如果 oldValue 和 value 不相等，代表受控组件需要更新\n// 更新的原则为，fiber.props.value 永远为用户所需要的值，因此 node.value = toString(value)\nexport function setNodeValue (node: FormElement, oldValue: RestoreType, value, type = 'string') {\n  if (value != null) {\n    if (type === 'number') {\n      if (\n        (value === 0 && node.value === '')\n        // We explicitly want to coerce to number here if possible.\n        // eslint-disable-next-line\n        || oldValue != value\n      ) {\n        node.value = toString(value)\n      }\n    } else if (oldValue !== toString(value)) {\n      node.value = toString(value)\n    }\n  } else if (type === 'submit' || type === 'reset') {\n    // Submit/reset inputs need the attribute removed completely to avoid\n    // blank-text buttons.\n    node.removeAttribute('value')\n  }\n}\n\n// 判断当前 TaroElement 是否为 supportedInputTypes input 或 textarea\nexport function isTextInputElement (elem: TaroElement): boolean {\n  const nodeName = elem && elem.nodeName && elem.nodeName.toLowerCase()\n\n  if (nodeName === 'input') {\n    const type = (elem as FormElement).type\n\n    return !type || !!supportedInputTypes[type]\n  }\n\n  if (nodeName === 'textarea') {\n    return true\n  }\n\n  return false\n}\n\nexport const ReactDOMTextareaRestoreControlledState = updateWrapper\nexport const ReactDOMInputRestoreControlledState = updateInputWrapper\n", "import type { FormElement } from '@tarojs/runtime'\n\nfunction isCheckable (elem: FormElement) {\n  const type = elem.type\n  const nodeName = elem.nodeName\n\n  return (\n    nodeName &&\n    nodeName.toLowerCase() === 'input' &&\n    (type === 'checkbox' || type === 'radio')\n  )\n}\n\nfunction getTracker (node) {\n  return node._valueTracker\n}\n\nfunction detachTracker (node) {\n  node._valueTracker = null\n}\n\n// 之所以单独创建一个 tacker，是为了统一监听不同 type 的 input 值\n// 比如 type=checkbox 或者 type=radio，就需要监听 checked，而不是 value\n// 虽然目前还未实现 checkbox 和 radio 的 finishEventHandle，但后续不好说，所以先统一和 react 一样的写法\n// 需要特别注意的是，tracker 初始化时的值为 node 的初始值，但后续会变更为事件的 detail.value 值\nfunction trackValueOnNode (node: any) {\n  const valueField = isCheckable(node) ? 'checked' : 'value'\n  const descriptor = Object.getOwnPropertyDescriptor(\n    node.constructor.prototype,\n    valueField,\n  )\n\n  let currentValue = '' + node[valueField]\n\n  if (\n    node.hasOwnProperty(valueField) ||\n    typeof descriptor === 'undefined' ||\n    typeof descriptor.get !== 'function' ||\n    typeof descriptor.set !== 'function'\n  ) {\n    return\n  }\n\n  const { get, set } = descriptor\n\n  Object.defineProperty(node, valueField, {\n    configurable: true,\n    enumerable: descriptor.enumerable,\n    get: function () {\n      return get.call(this)\n    },\n    set: function (value) {\n      currentValue = '' + value\n      set.call(this, value)\n    },\n  })\n\n  const tracker = {\n    getValue () {\n      return currentValue\n    },\n    setValue (value) {\n      currentValue = '' + value\n    },\n    stopTracking () {\n      detachTracker(node)\n      delete node[valueField]\n    },\n  }\n  return tracker\n}\n\nexport function track (node) {\n  if (getTracker(node)) {\n    return\n  }\n\n  node._valueTracker = trackValueOnNode(node)\n}\n\nexport function updateValueIfChanged (node, nextValue: string) {\n  if (!node) {\n    return false\n  }\n\n  const tracker = getTracker(node)\n\n  if (!tracker) {\n    return true\n  }\n\n  const lastValue = tracker.getValue()\n\n  if (nextValue !== lastValue) {\n    tracker.setValue(nextValue)\n    return true\n  }\n  return false\n}\n\nexport function stopTracking (node) {\n  const tracker = getTracker(node)\n  if (tracker) {\n    tracker.stopTracking()\n  }\n}\n", "import { convertNumber2PX, FormElement } from '@tarojs/runtime'\nimport { capitalize, internalComponents, isFunction, isNumber, isObject, isString, PLATFORM_TYPE, toCamelCase } from '@tarojs/shared'\n\nimport type { Style, TaroElement } from '@tarojs/runtime'\n\n// 拓展TaroElement的属性\n\nexport type Props = Record<string, unknown>\n\nconst IS_NON_DIMENSIONAL = /aspect|acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i\n\nfunction isEventName (s: string) {\n  return s[0] === 'o' && s[1] === 'n'\n}\n\nfunction isEqual (obj1, obj2) {\n  // 首先检查引用是否相同\n  if (obj1 === obj2) {\n    return true\n  }\n\n  // 如果两者中有一个不是对象，或者为 null，直接返回 false\n  if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {\n    return false\n  }\n\n  // 获取两个对象键的数组\n  const keys1 = Object.keys(obj1)\n  const keys2 = Object.keys(obj2)\n\n  // 如果键的数量不相同，对象显然不相等\n  if (keys1.length !== keys2.length) {\n    return false\n  }\n\n  // 遍历对象的每个键，比较两个对象同一键的值\n  for (let i = 0; i < keys1.length; i++) {\n    const key = keys1[i]\n    if (obj1[key] !== obj2[key]) {\n      return false\n    }\n  }\n\n  // 如果所有键的值都相等，返回 true\n  return true\n}\n\nexport function updateProps (dom: TaroElement, oldProps: Props, newProps: Props) {\n  const updatePayload = getUpdatePayload(dom, oldProps, newProps)\n  if (updatePayload) {\n    updatePropsByPayload(dom, oldProps, updatePayload)\n  }\n}\n\nexport function updatePropsByPayload (dom: TaroElement, oldProps: Props, updatePayload: any[]) {\n  const handlers: (() => void)[] = []\n  let fixedHandler: (() => void) | null = null\n  for (let i = 0; i < updatePayload.length; i += 2) {\n    // key, value 成对出现\n    const key = updatePayload[i]\n    const newProp = updatePayload[i + 1]\n    const oldProp = oldProps[key]\n    if (process.env.TARO_PLATFORM === PLATFORM_TYPE.HARMONY) {\n      if (key === '__fixed') {\n        // hack: __fixed最先识别\n        fixedHandler = () => setProperty(dom, key, newProp, oldProp)\n        continue\n      }\n      // 鸿蒙样式前置插入，防止覆盖style\n      if (key === '__hmStyle') {\n        handlers.splice(0, 0, () => setHarmonyStyle(dom, newProp, oldProp))\n      } else {\n        handlers.push(() => setProperty(dom, key, newProp, oldProp))\n      }\n    } else {\n      setProperty(dom, key, newProp, oldProp)\n    }\n  }\n  if (process.env.TARO_PLATFORM === PLATFORM_TYPE.HARMONY) {\n    fixedHandler && fixedHandler()\n    for (let i = 0; i < handlers.length; i++) {\n      handlers[i]()\n    }\n  }\n}\n\nexport function getUpdatePayload (dom: TaroElement, oldProps: Props, newProps: Props) {\n  let i: string\n  let updatePayload: any[] | null = null\n\n  for (i in oldProps) {\n    if (!(i in newProps)) {\n      (updatePayload = updatePayload || []).push(i, null)\n    }\n  }\n  const isFormElement = dom instanceof FormElement\n  for (i in newProps) {\n    if (oldProps[i] !== newProps[i] || (isFormElement && i === 'value')) {\n      // 如果都是 style，且 style 里面的值相等，则无需记录到 payload 中\n      if (i === 'style' && isObject(oldProps[i]) && isObject(newProps[i]) && isEqual(oldProps[i], newProps[i])) continue\n\n      (updatePayload = updatePayload || []).push(i, newProps[i])\n    }\n  }\n\n  return updatePayload\n}\n\n// function eventProxy (e: CommonEvent) {\n//   const el = document.getElementById(e.currentTarget.id)\n//   const handlers = el!.__handlers[e.type]\n//   handlers[0](e)\n// }\n\nfunction setEvent (dom: TaroElement, name: string, value: unknown, oldValue?: unknown) {\n  const isCapture = name.endsWith('Capture')\n  let eventName = name.toLowerCase().slice(2)\n  if (isCapture) {\n    eventName = eventName.slice(0, -7)\n  }\n\n  const compName = capitalize(toCamelCase(dom.tagName.toLowerCase()))\n\n  if (eventName === 'click' && process.env.TARO_PLATFORM !== PLATFORM_TYPE.HARMONY && compName in internalComponents) {\n    eventName = 'tap'\n  }\n\n  if (isFunction(value)) {\n    if (oldValue) {\n      dom.removeEventListener(eventName, oldValue as any, process.env.TARO_PLATFORM !== PLATFORM_TYPE.HARMONY ? false : undefined)\n      dom.addEventListener(eventName, value, process.env.TARO_PLATFORM !== PLATFORM_TYPE.HARMONY ? { isCapture, sideEffect: false } : undefined)\n    } else {\n      dom.addEventListener(eventName, value, process.env.TARO_PLATFORM !== PLATFORM_TYPE.HARMONY ? isCapture : undefined)\n    }\n  } else {\n    dom.removeEventListener(eventName, oldValue as any)\n  }\n}\n\nfunction setStyle (style: Style, key: string, value: unknown) {\n  if (key[0] === '-' && process.env.TARO_PLATFORM !== PLATFORM_TYPE.HARMONY) {\n    // css variables need not further judgment\n    style.setProperty(key, (value as string).toString())\n    return\n  }\n\n  style[key] =\n    isNumber(value) && IS_NON_DIMENSIONAL.test(key) === false\n      ? (process.env.TARO_PLATFORM === PLATFORM_TYPE.HARMONY ? value + 'px' : convertNumber2PX(value))\n      : value === null\n        ? ''\n        : value\n}\n\ntype StyleValue = Record<string, string | number>\ninterface DangerouslySetInnerHTML {\n  __html?: string\n}\n\n// 鸿蒙样式特殊处理，需要在插入顺序中前置插入，防止覆盖了style\nfunction setHarmonyStyle(dom: TaroElement, value: unknown, oldValue?: unknown) {\n  // @ts-ignore\n  const style = dom._st.hmStyle // __hmStyle是已经被处理过的鸿蒙样式，可以直接塞进hmStyle对象内\n  if (isObject<StyleValue>(oldValue)) {\n    for (const i in oldValue) {\n      if (!(value && i in (value as StyleValue))) {\n        // 鸿蒙伪类特殊处理\n        if (process.env.TARO_PLATFORM === PLATFORM_TYPE.HARMONY) {\n          if (i === '::after' || i === '::before') {\n            setPseudo(dom, i, null)\n          } else if (['::first-child', '::last-child', '::empty'].includes(i) || `${i}`.indexOf('::nth-child') === 0) {\n            // @ts-ignore\n            dom.set_pseudo_class(i, null)\n          } else {\n            if (i === 'position' && oldValue[i] === 'fixed') {\n              // @ts-ignore\n              dom.setLayer(0)\n            } else if (i === 'animationName') {\n              // @ts-ignore\n              dom.setAnimation(false)\n            }\n            style[i] = ''\n          }\n        } else {\n          style[i] = ''\n        }\n      }\n    }\n  }\n  if (isObject<StyleValue>(value)) {\n    for (const i in value) {\n      if (!oldValue || !isEqual(value[i], (oldValue as StyleValue)[i])) {\n        // 鸿蒙伪类特殊处理\n        if (process.env.TARO_PLATFORM === PLATFORM_TYPE.HARMONY) {\n          if (i === '::after' || i === '::before') {\n            setPseudo(dom, i, value[i] as unknown as StyleValue)\n          } else if (['::first-child', '::last-child', '::empty'].includes(i) || i.startsWith('::nth-child')) {\n            // @ts-ignore\n            dom.set_pseudo_class(i, value[i])\n          } else {\n            if (i === 'position') {\n              if (value[i] === 'fixed' || (value[i] !== 'fixed' && oldValue?.[i])) {\n                // @ts-ignore\n                dom.setLayer(value[i] === 'fixed' ? 1 : 0)\n              }\n            } else if (i === 'animationName') {\n              // @ts-ignore\n              dom.setAnimation(true)\n            }\n            style[i] = value[i]\n          }\n        } else {\n          style[i] = value[i]\n        }\n      }\n    }\n  }\n\n  dom.setAttribute('__hmStyle', value)\n}\nfunction setProperty (dom: TaroElement, name: string, value: unknown, oldValue?: unknown) {\n  name = name === 'className' ? 'class' : name\n\n  if (\n    name === 'key' ||\n    name === 'children' ||\n    name === 'ref'\n  ) {\n    // skip\n  } else if (name === 'style') {\n    if (/harmony.*cpp/.test(process.env.TARO_ENV || '')) {\n      return dom.setAttribute('_style4cpp', value)\n    }\n    const style = dom.style\n    if (isString(value)) {\n      style.cssText = value\n    } else {\n      if (isString(oldValue)) {\n        style.cssText = ''\n        oldValue = null\n      }\n\n      if (isObject<StyleValue>(oldValue)) {\n        for (const i in oldValue) {\n          if (!(value && i in (value as StyleValue))) {\n            // Harmony特殊处理\n            if (process.env.TARO_PLATFORM === PLATFORM_TYPE.HARMONY && i === 'position' && oldValue[i] === 'fixed') {\n              // @ts-ignore\n              dom.setLayer(0)\n            }\n            setStyle(style, i, '')\n          }\n        }\n      }\n\n      if (isObject<StyleValue>(value)) {\n        for (const i in value) {\n          if (!oldValue || !isEqual(value[i], (oldValue as StyleValue)[i])) {\n            // Harmony特殊处理\n            if (process.env.TARO_PLATFORM === PLATFORM_TYPE.HARMONY && i === 'position') {\n              if (value[i] === 'fixed' || (value[i] !== 'fixed' && oldValue?.[i])) {\n                // @ts-ignore\n                dom.setLayer(value[i] === 'fixed' ? 1 : 0)\n              }\n            }\n            setStyle(style, i, value[i])\n          }\n        }\n      }\n    }\n  } else if (isEventName(name)) {\n    setEvent(dom, name, value, oldValue)\n  } else if (name === 'dangerouslySetInnerHTML') {\n    const newHtml = (value as DangerouslySetInnerHTML)?.__html ?? ''\n    const oldHtml = (oldValue as DangerouslySetInnerHTML)?.__html ?? ''\n    if (newHtml || oldHtml) {\n      if (oldHtml !== newHtml) {\n        dom.innerHTML = newHtml\n      }\n    }\n  } else if (!isFunction(value)) {\n    if (value == null) {\n      dom.removeAttribute(name)\n    } else {\n      dom.setAttribute(name, value as string)\n    }\n  }\n}\n\n// 设置鸿蒙伪类属性(特殊设置)\nfunction setPseudo(dom: TaroElement, name: '::after' | '::before', value: StyleValue | null) {\n  if (name === '::after') {\n    // @ts-ignore\n    dom.set_pseudo_after(value)\n  } else if (name === '::before') {\n    // @ts-ignore\n    dom.set_pseudo_before(value)\n  }\n}\n", "/* eslint-disable @typescript-eslint/indent */\nimport { document, FormElement } from '@tarojs/runtime'\nimport { isBoolean, isUndefined, noop } from '@tarojs/shared'\nimport Reconciler from 'react-reconciler'\nimport { DefaultEventPriority } from 'react-reconciler/constants'\n\nimport { precacheFiberNode, updateFiberProps } from './componentTree'\nimport { track } from './inputValueTracking'\nimport { getUpdatePayload, Props, updateProps, updatePropsByPayload } from './props'\n\nimport type { TaroElement, TaroText } from '@tarojs/runtime'\nimport type { Fiber, HostConfig } from 'react-reconciler'\n\nconst hostConfig: HostConfig<\n  string, // Type\n  Props, // Props\n  TaroElement, // Container\n  TaroElement, // Instance\n  TaroText, // TextInstance\n  TaroElement, // SuspenseInstance\n  TaroElement, // HydratableInstance\n  TaroElement, // PublicInstance\n  Record<string, any>, // HostContext\n  string[], // UpdatePayload\n  unknown, // ChildSet\n  unknown, // TimeoutHandle\n  unknown // NoTimeout\n> & {\n  supportsMicrotasks: boolean // 待官方类型文件修复后删除\n} = {\n  // below keys order by {React ReactFiberHostConfig.custom.js}, convenient for comparing each other.\n\n  // -------------------\n  // required by @types/react-reconciler\n  // -------------------\n  getPublicInstance (inst: TaroElement) {\n    return inst\n  },\n  getRootHostContext () {\n    return {}\n  },\n  getChildHostContext (parentHostContext) {\n    return parentHostContext\n  },\n  prepareForCommit (..._: any[]) {\n    return null\n  },\n  resetAfterCommit: noop,\n  createInstance (type, props: Props, _rootContainerInstance: any, _hostContext: any, internalInstanceHandle: Fiber) {\n    const element = document.createElement(type)\n\n    precacheFiberNode(internalInstanceHandle, element)\n    updateFiberProps(element, props)\n\n    return element\n  },\n  appendInitialChild (parent, child) {\n    parent.appendChild(child)\n  },\n  finalizeInitialChildren (dom, type: string, props: any) {\n    let newProps = props\n    if (dom instanceof FormElement) {\n      const [defaultName, defaultKey] = ['switch', 'checkbox', 'radio'].includes(type) ? ['checked', 'defaultChecked'] : ['value', 'defaultValue']\n      if (props.hasOwnProperty(defaultKey)) {\n        newProps = { ...newProps, [defaultName]: props[defaultKey] }\n        delete newProps[defaultKey]\n      }\n    }\n\n    updateProps(dom, {}, newProps) // 提前执行更新属性操作，Taro 在 Page 初始化后会立即从 dom 读取必要信息\n\n    if (type === 'input' || type === 'textarea') {\n      track(dom)\n    }\n\n    return false\n  },\n  prepareUpdate (instance, _, oldProps, newProps) {\n    return getUpdatePayload(instance, oldProps, newProps)\n  },\n  shouldSetTextContent () {\n    return false\n  },\n  createTextInstance (text: string, _rootContainerInstance: any, _hostContext: any, internalInstanceHandle: Fiber) {\n    const textNode = document.createTextNode(text)\n\n    precacheFiberNode(internalInstanceHandle, textNode)\n\n    return textNode\n  },\n  scheduleTimeout: setTimeout,\n  cancelTimeout: clearTimeout,\n  noTimeout: -1,\n  isPrimaryRenderer: true,\n  warnsIfNotActing: true,\n  supportsMutation: true,\n  supportsPersistence: false,\n  supportsHydration: false,\n  getInstanceFromNode: () => null,\n  beforeActiveInstanceBlur: noop,\n  afterActiveInstanceBlur: noop,\n  preparePortalMount: noop,\n  prepareScopeUpdate: noop,\n  getInstanceFromScope: () => null,\n  getCurrentEventPriority () {\n    return DefaultEventPriority\n  },\n  detachDeletedInstance: noop,\n\n  // -------------------\n  //      Microtasks\n  //     (optional)\n  // -------------------\n  supportsMicrotasks: true,\n  scheduleMicrotask: isUndefined(Promise)\n    ? setTimeout\n    : (callback) =>\n        Promise.resolve(null)\n          .then(callback)\n          .catch(function (error) {\n            setTimeout(() => {\n              throw error\n            })\n          }),\n\n  // -------------------\n  //      Mutation\n  //     (required if supportsMutation is true)\n  // -------------------\n  appendChild (parent, child) {\n    parent.appendChild(child)\n  },\n  appendChildToContainer (parent, child) {\n    parent.appendChild(child)\n  },\n  commitTextUpdate (textInst, _, newText) {\n    textInst.nodeValue = newText\n  },\n  commitMount: noop,\n  commitUpdate (dom, updatePayload, _, oldProps, newProps) {\n    if (!updatePayload) return\n    // payload 只包含 children 的时候，不应该再继续触发后续的属性比较和更新的逻辑了\n    if (updatePayload.length === 2 && updatePayload.includes('children')) return\n\n    updatePropsByPayload(dom, oldProps, updatePayload)\n    updateFiberProps(dom, newProps)\n  },\n  insertBefore (parent, child, refChild) {\n    parent.insertBefore(child, refChild)\n  },\n  insertInContainerBefore (parent, child, refChild) {\n    parent.insertBefore(child, refChild)\n  },\n  removeChild (parent, child) {\n    parent.removeChild(child)\n  },\n  removeChildFromContainer (parent, child) {\n    parent.removeChild(child)\n  },\n  resetTextContent: noop,\n  hideInstance (instance) {\n    const style = instance.style\n    style.setProperty('display', 'none')\n  },\n  hideTextInstance (textInstance) {\n    textInstance.nodeValue = ''\n  },\n  unhideInstance (instance, props) {\n    const styleProp = props.style as { display?: any }\n    let display = styleProp?.hasOwnProperty('display') ? styleProp.display : null\n    display = display == null || isBoolean(display) || display === '' ? '' : ('' + display).trim()\n    // eslint-disable-next-line dot-notation\n    instance.style['display'] = display\n  },\n  unhideTextInstance (textInstance, text) {\n    textInstance.nodeValue = text\n  },\n  clearContainer (element) {\n    if (element.childNodes.length > 0) {\n      element.textContent = ''\n    }\n  }\n}\n\nconst TaroReconciler = Reconciler(hostConfig)\n\nif (process.env.NODE_ENV !== 'production') {\n  const foundDevTools = TaroReconciler.injectIntoDevTools({\n    bundleType: 1,\n    version: '18.0.0',\n    rendererPackageName: 'taro-react'\n  })\n  if (!foundDevTools) {\n    // eslint-disable-next-line no-console\n    console.info(\n      '%cDownload the React DevTools ' +\n        'for a better development experience: ' +\n        'https://reactjs.org/link/react-devtools',\n      'font-weight:bold'\n    )\n  }\n}\n\nexport { TaroReconciler }\n", "import { getFiberCurrentPropsFromNode, getInstanceFromNode, getNodeFromInstance } from './componentTree'\nimport { isTextInputElement, ReactDOMInputRestoreControlledState, ReactDOMTextareaRestoreControlledState, toString } from './domInput'\nimport { updateValueIfChanged } from './inputValueTracking'\nimport { TaroReconciler } from './reconciler'\n\nimport type { TaroElement, TaroEvent } from '@tarojs/runtime'\nimport type { Fiber } from 'react-reconciler'\nimport type { Props } from './props'\n\nexport type RestoreType = string | number | boolean | any[]\n\ninterface RestoreItem {\n  target: TaroElement\n  value: RestoreType\n}\n\nlet restoreQueue: RestoreItem[] | null = null\n\n// 对比 TaroElement tracker 下的 value 和事件下的 value，判断 element 的值是否存在更改\nexport function getTargetInstForInputOrChangeEvent (e: TaroEvent, node: TaroElement) {\n  const targetInst = getInstanceFromNode(node)\n  const domEventName = e.type\n\n  if (!targetInst || !isTextInputElement(node)) return\n\n  if (domEventName === 'input' || domEventName === 'change') {\n    const nextValue = toString(e.mpEvent?.detail?.value)\n\n    return getInstIfValueChanged(targetInst, nextValue)\n  }\n}\n\nfunction getInstIfValueChanged (targetInst: Fiber, nextValue: string) {\n  const targetNode = getNodeFromInstance(targetInst)\n\n  if (!targetNode) return false\n\n  if (updateValueIfChanged(targetNode, nextValue)) {\n    return targetInst\n  }\n}\n\n// 把 target 塞入更新队列中\nexport function enqueueStateRestore (target: RestoreItem): void {\n  if (restoreQueue) {\n    restoreQueue.push(target)\n  } else {\n    restoreQueue = [target]\n  }\n}\n\n// 判断是否需要恢复 target（input、textarea） 的状态\nexport function needsStateRestore (): boolean {\n  return restoreQueue !== null\n}\n\nexport function finishEventHandler () {\n  const controlledComponentsHavePendingUpdates = needsStateRestore()\n\n  if (controlledComponentsHavePendingUpdates) {\n    TaroReconciler.flushSync()\n    restoreStateIfNeeded()\n  }\n}\n\n// 遍历 restoreQueue、restoreTarget，恢复其状态\nexport function restoreStateIfNeeded () {\n  if (!restoreQueue) {\n    return\n  }\n\n  const queuedTargets = restoreQueue\n  restoreQueue = null\n\n  for (let i = 0; i < queuedTargets.length; i++) {\n    restoreStateOfTarget(queuedTargets[i])\n  }\n}\n\nfunction restoreImpl (\n  domElement: TaroElement,\n  tag: string,\n  oldValue: string | number | boolean | any[],\n  props: Props,\n): void {\n  switch (tag) {\n    case 'input':\n      ReactDOMInputRestoreControlledState(domElement, oldValue, props)\n      break\n    case 'textarea':\n      ReactDOMTextareaRestoreControlledState(domElement, oldValue, props)\n      break\n  }\n}\n\nfunction restoreStateOfTarget (item: RestoreItem) {\n  const internalInstance = getInstanceFromNode(item.target)\n\n  if (!internalInstance) return\n\n  const { stateNode, type } = internalInstance\n\n  if (stateNode) {\n    const props = getFiberCurrentPropsFromNode(stateNode)\n\n    restoreImpl(stateNode, type, item.value, props)\n  }\n}\n", "import { hooks } from '@tarojs/shared'\n\nimport { markContainerAsRoot } from './componentTree'\nimport { getEventPriority } from './constant'\nimport { enqueueStateRestore, getTargetInstForInputOrChangeEvent, RestoreType } from './event'\nimport { <PERSON>roReconci<PERSON> } from './reconciler'\n\nimport type { TaroElement, TaroEvent } from '@tarojs/runtime'\nimport type { ReactNode } from 'react'\nimport type { OpaqueRoot } from 'react-reconciler'\n\nexport const ContainerMap: WeakMap<TaroElement, Root> = new WeakMap()\n\ntype Renderer = typeof TaroReconciler\n\ntype CreateRootOptions = {\n  unstable_strictMode?: boolean\n  unstable_concurrentUpdatesByDefault?: boolean\n  unstable_transitionCallbacks?: any\n  identifierPrefix?: string\n  onRecoverableError?: (error: any) => void\n}\n\nexport type Callback = () => void | null | undefined\n\nclass Root {\n  private renderer: Renderer\n  public internalRoot: OpaqueRoot\n\n  public constructor (renderer: Renderer, domContainer: TaroElement, options?: CreateRootOptions) {\n    this.renderer = renderer\n    this.initInternalRoot(renderer, domContainer, options)\n  }\n\n  private initInternalRoot (renderer: Renderer, domContainer: TaroElement, options?: CreateRootOptions) {\n    // Since react-reconciler v0.27, createContainer need more parameters\n    // @see:https://github.com/facebook/react/blob/0b974418c9a56f6c560298560265dcf4b65784bc/packages/react-reconciler/src/ReactFiberReconciler.js#L248\n    const containerInfo = domContainer\n    if (options) {\n      const tag = 1 // ConcurrentRoot\n      const concurrentUpdatesByDefaultOverride = false\n      let isStrictMode = false\n      let identifierPrefix = ''\n      let onRecoverableError = (error: any) => console.error(error)\n      let transitionCallbacks = null\n      if (options.unstable_strictMode === true) {\n        isStrictMode = true\n      }\n      if (options.identifierPrefix !== undefined) {\n        identifierPrefix = options.identifierPrefix\n      }\n      if (options.onRecoverableError !== undefined) {\n        onRecoverableError = options.onRecoverableError\n      }\n      if (options.unstable_transitionCallbacks !== undefined) {\n        transitionCallbacks = options.unstable_transitionCallbacks\n      }\n\n      this.internalRoot = renderer.createContainer(\n        containerInfo,\n        tag,\n        null, // hydrationCallbacks\n        isStrictMode,\n        concurrentUpdatesByDefaultOverride,\n        identifierPrefix,\n        onRecoverableError,\n        transitionCallbacks\n      )\n    } else {\n      const tag = 0 // LegacyRoot\n      this.internalRoot = renderer.createContainer(\n        containerInfo,\n        tag,\n        null, // hydrationCallbacks\n        false, // isStrictMode\n        false, // concurrentUpdatesByDefaultOverride,\n        '', // identifierPrefix\n        () => {}, // onRecoverableError, this isn't reachable because onRecoverableError isn't called in the legacy API.\n        null // transitionCallbacks\n      )\n    }\n  }\n\n  public render (children: ReactNode, cb: Callback) {\n    const { renderer, internalRoot } = this\n    renderer.updateContainer(children, internalRoot, null, cb)\n    return renderer.getPublicRootInstance(internalRoot)\n  }\n\n  public unmount (cb: Callback) {\n    this.renderer.updateContainer(null, this.internalRoot, null, cb)\n  }\n}\n\nexport function render (element: ReactNode, domContainer: TaroElement, cb: Callback) {\n  const oldRoot = ContainerMap.get(domContainer)\n  if (oldRoot != null) {\n    return oldRoot.render(element, cb)\n  }\n\n  const root = new Root(TaroReconciler, domContainer)\n  ContainerMap.set(domContainer, root)\n  return root.render(element, cb)\n}\n\nexport function createRoot (domContainer: TaroElement, options: CreateRootOptions = {}) {\n  const oldRoot = ContainerMap.get(domContainer)\n  if (oldRoot != null) {\n    return oldRoot\n  }\n  // options should be an object\n  const root = new Root(TaroReconciler, domContainer, options)\n  ContainerMap.set(domContainer, root)\n\n  markContainerAsRoot(root?.internalRoot?.current, domContainer)\n\n  hooks.tap('dispatchTaroEvent', (e: TaroEvent, node: TaroElement) => {\n    const eventPriority = getEventPriority(e.type)\n\n    TaroReconciler.runWithPriority(eventPriority, () => {\n      node.dispatchEvent(e)\n    })\n  })\n\n  // 对比 event.detail.value 和 node.tracker.value，判断 value 值是否有变动，存在变动则塞入队列中\n  hooks.tap('modifyTaroEvent', (e: TaroEvent, node: TaroElement) => {\n    const inst = getTargetInstForInputOrChangeEvent(e, node)\n\n    if (!inst) return\n\n    // 这里塞入的是 event.detail.value，也就是事件的值，在受控组件中，你可以理解为需要被变更的值\n    // 后续会在 finishEventHandler 中，使用最新的 fiber.props.value 来与其比较\n    // 如果不一致，则表示需要更新，会执行 node.value = fiber.props.value 的更新操作\n    const nextValue = e.mpEvent?.detail?.value as unknown as RestoreType\n    enqueueStateRestore({ target: node, value: nextValue })\n  })\n\n  return root\n}\n", "import { isString, isUndefined } from '@tarojs/shared'\n\nimport env from '../env'\nimport { URLSearchParams } from './URLSearchParams'\n\nclass TaroURL {\n  static createObjectURL () {\n    throw new Error('Oops, not support URL.createObjectURL() in miniprogram.')\n  }\n\n  static revokeObjectURL () {\n    throw new Error('Oops, not support URL.revokeObjectURL() in miniprogram.')\n  }\n\n  /* private property */\n  #hash = ''\n  #hostname = ''\n  #pathname = ''\n  #port = ''\n  #protocol = ''\n  #search: URLSearchParams\n\n  constructor (url: string, base?: string) {\n    if (!isString(url)) url = String(url)\n\n    const parseResult = parseUrlBase(url, base)\n    const { hash, hostname, pathname, port, protocol, search } = parseResult\n\n    this.#hash = hash\n    this.#hostname = hostname\n    this.#pathname = pathname || '/'\n    this.#port = port\n    this.#protocol = protocol\n    this.#search = new URLSearchParams(search)\n  }\n\n  /* public property */\n  get protocol () {\n    return this.#protocol\n  }\n\n  set protocol (val: string) {\n    isString(val) && (this.#protocol = val.trim())\n  }\n\n  get host () {\n    return this.hostname + (this.port ? ':' + this.port : '')\n  }\n\n  set host (val: string) {\n    if (val && isString(val)) {\n      val = val.trim()\n      const { hostname, port } = parseUrl(`//${val}`)\n      this.hostname = hostname\n      this.port = port\n    }\n  }\n\n  get hostname () {\n    return this.#hostname\n  }\n\n  set hostname (val: string) {\n    val && isString(val) && (this.#hostname = val.trim())\n  }\n\n  get port () {\n    return this.#port\n  }\n\n  set port (val: string) {\n    isString(val) && (this.#port = val.trim())\n  }\n\n  get pathname () {\n    return this.#pathname\n  }\n\n  set pathname (val: string) {\n    if (isString(val)) {\n      val = val.trim()\n      const HEAD_REG = /^(\\/|\\.\\/|\\.\\.\\/)/\n      let temp = val\n      while (HEAD_REG.test(temp)) {\n        temp = temp.replace(HEAD_REG, '')\n      }\n      if (temp) this.#pathname = '/' + temp\n      else this.#pathname = '/'\n    }\n  }\n\n  get search () {\n    const val = this.#search.toString()\n    return (val.length === 0 || val.startsWith('?')) ? val : `?${val}`\n  }\n\n  set search (val: string) {\n    if (isString(val)) {\n      val = val.trim()\n      this.#search = new URLSearchParams(val)\n    }\n  }\n\n  get hash () {\n    return this.#hash\n  }\n\n  set hash (val: string) {\n    if (isString(val)) {\n      val = val.trim()\n      if (val) this.#hash = val.startsWith('#') ? val : `#${val}`\n      else this.#hash = ''\n    }\n  }\n\n  get href () {\n    return `${this.protocol}//${this.host}${this.pathname}${this.search}${this.hash}`\n  }\n\n  set href (val: string) {\n    if (val && isString(val)) {\n      val = val.trim()\n      const { protocol, hostname, port, hash, search, pathname } = parseUrl(val)\n      this.protocol = protocol\n      this.hostname = hostname\n      this.pathname = pathname\n      this.port = port\n      this.hash = hash\n      this.search = search\n    }\n  }\n\n  get origin () {\n    return `${this.protocol}//${this.host}`\n  }\n\n  set origin (val: string) {\n    if (val && isString(val)) {\n      val = val.trim()\n      const { protocol, hostname, port } = parseUrl(val)\n      this.protocol = protocol\n      this.hostname = hostname\n      this.port = port\n    }\n  }\n\n  get searchParams () {\n    return this.#search\n  }\n\n  // public method\n  toString () {\n    return this.href\n  }\n\n  toJSON () {\n    return this.toString()\n  }\n\n  // convenient for deconstructor\n  _toRaw () {\n    return {\n      protocol: this.protocol,\n      port: this.port,\n      host: this.host,\n      hostname: this.hostname,\n      pathname: this.pathname,\n      hash: this.hash,\n      search: this.search,\n      origin: this.origin,\n      href: this.href,\n    }\n  }\n}\n\nexport type { TaroURL }\n\n// Note: 小程序端 vite 打包成 commonjs，const URL = xxx 会报错，所以把 URL 改为 TaroURLProvider\nexport const TaroURLProvider: typeof TaroURL = process.env.TARO_PLATFORM === 'web' ? env.window.URL : TaroURL\n\nexport function parseUrl (url = '') {\n  const result = {\n    href: '',\n    origin: '',\n    protocol: '',\n    hostname: '',\n    host: '',\n    port: '',\n    pathname: '',\n    search: '',\n    hash: ''\n  }\n  if (!url || !isString(url)) return result\n\n  url = url.trim()\n  const PATTERN = /^(([^:/?#]+):)?\\/\\/(([^/?#]+):(.+)@)?([^/?#:]*)(:(\\d+))?([^?#]*)(\\?([^#]*))?(#(.*))?/\n  const matches = url.match(PATTERN)\n\n  if (!matches) return result\n\n  // TODO: username & password ?\n  result.protocol = matches[1] || 'https:'\n  result.hostname = matches[6] || 'taro.com'\n  result.port = matches[8] || ''\n  result.pathname = matches[9] || '/'\n  result.search = matches[10] || ''\n  result.hash = matches[12] || ''\n  result.href = url\n  result.origin = result.protocol + '//' + result.hostname\n  result.host = result.hostname + (result.port ? `:${result.port}` : '')\n\n  return result\n}\n\nfunction parseUrlBase (url: string, base?: string) {\n  const VALID_URL = /^(https?:)\\/\\//i\n\n  let fullUrl = ''\n  let parsedBase: ReturnType<typeof parseUrl> | null = null\n\n  if (!isUndefined(base)) {\n    base = String(base).trim()\n    if (!VALID_URL.test(base)) throw new TypeError(`Failed to construct 'URL': Invalid base URL`)\n    parsedBase = parseUrl(base)\n  }\n\n  url = String(url).trim()\n\n  if (VALID_URL.test(url)) {\n    fullUrl = url\n  } else if (parsedBase) {\n    if (url) {\n      if (url.startsWith('//')) {\n        fullUrl = parsedBase.protocol + url\n      } else {\n        fullUrl = parsedBase.origin + (url.startsWith('/') ? url : `/${url}`)\n      }\n    } else {\n      fullUrl = parsedBase.href\n    }\n  } else {\n    throw new TypeError(`Failed to construct 'URL': Invalid URL`)\n  }\n\n  return parseUrl(fullUrl)\n}\n", "import { isArray } from '@tarojs/shared'\n\nimport env from '../env'\n\nconst findReg = /[!'()~]|%20|%00/g\nconst plusReg = /\\+/g\nconst replaceCharMap = {\n  '!': '%21',\n  \"'\": '%27',\n  '(': '%28',\n  ')': '%29',\n  '~': '%7E',\n  '%20': '+',\n  '%00': '\\x00',\n}\n\nfunction replacer (match: string) {\n  return replaceCharMap[match]\n}\n\nfunction appendTo (dict: Record<string, string[]>, name: string, value: string) {\n  const res = isArray(value) ? value.join(',') : value\n  if (name in dict) dict[name].push(res)\n  else dict[name] = [res]\n}\n\nfunction addEach (value: string, key: string) {\n  appendTo(this, key, value)\n}\n\nfunction decode (str: string) {\n  return decodeURIComponent(str.replace(plusReg, ' '))\n}\n\nfunction encode (str: string) {\n  return encodeURIComponent(str).replace(findReg, replacer)\n}\n\nexport const URLSearchParams = process.env.TARO_PLATFORM === 'web' ? env.window.URLSearchParams : class {\n  #dict = Object.create(null)\n\n  constructor (query) {\n    query ??= ''\n\n    const dict = this.#dict\n\n    if (typeof query === 'string') {\n      if (query.charAt(0) === '?') {\n        query = query.slice(1)\n      }\n      for (let pairs = query.split('&'), i = 0, length = pairs.length; i < length; i++) {\n        const value = pairs[i]\n        const index = value.indexOf('=')\n\n        // 针对不规范的 url 参数做容错处理，如：word=你%好\n        try {\n          if (index > -1) {\n            appendTo(dict, decode(value.slice(0, index)), decode(value.slice(index + 1)))\n          } else if (value.length) {\n            appendTo(dict, decode(value), '')\n          }\n        } catch (err) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(`[Taro warn] URL 参数 ${value} decode 异常`)\n          }\n        }\n      }\n    } else {\n      if (isArray(query)) {\n        for (let i = 0, length = query.length; i < length; i++) {\n          const value = query[i]\n          appendTo(dict, value[0], value[1])\n        }\n      } else if (query.forEach) {\n        query.forEach(addEach, dict)\n      } else {\n        for (const key in query) {\n          appendTo(dict, key, query[key])\n        }\n      }\n    }\n  }\n\n  append (name: string, value: string) {\n    appendTo(this.#dict, name, value)\n  }\n\n  delete (name: string) {\n    delete this.#dict[name]\n  }\n\n  get (name: string) {\n    const dict = this.#dict\n    return name in dict ? dict[name][0] : null\n  }\n\n  getAll (name: string) {\n    const dict = this.#dict\n    return name in dict ? dict[name].slice(0) : []\n  }\n\n  has (name: string) {\n    return name in this.#dict\n  }\n\n  keys () {\n    return Object.keys(this.#dict)\n  }\n\n  set (name: string, value: string) {\n    this.#dict[name] = ['' + value]\n  }\n\n  forEach (callback, thisArg) {\n    const dict = this.#dict\n    Object.getOwnPropertyNames(dict).forEach(function (name) {\n      dict[name].forEach(function (value: string) {\n        callback.call(thisArg, value, name, this)\n      }, this)\n    }, this)\n  }\n\n  toJSON () {\n    return {}\n  }\n\n  toString () {\n    const dict = this.#dict\n    const query: any[] = []\n    for (const key in dict) {\n      const name = encode(key)\n      for (let i = 0, value = dict[key]; i < value.length; i++) {\n        query.push(name + '=' + encode(value[i]))\n      }\n    }\n    return query.join('&')\n  }\n}\n", "import {\n  APP,\n  BODY,\n  CONTAINER,\n  HEAD,\n  HTML\n} from '../constants'\nimport { TaroDocument } from '../dom/document'\nimport env from '../env'\n\nfunction createDocument (): TaroDocument {\n  /**\n     * <document>\n     *   <html>\n     *     <head></head>\n     *     <body>\n     *       <container>\n     *         <app id=\"app\" />\n     *       </container>\n     *     </body>\n     *   </html>\n     * </document>\n     */\n  const doc = new TaroDocument()\n  const documentCreateElement = doc.createElement.bind(doc)\n  const html = documentCreateElement(HTML)\n  const head = documentCreateElement(HEAD)\n  const body = documentCreateElement(BODY)\n  const app = documentCreateElement(APP)\n  app.id = APP\n  const container = documentCreateElement(CONTAINER) // 多包一层主要为了兼容 vue\n\n  doc.appendChild(html)\n  html.appendChild(head)\n  html.appendChild(body)\n  body.appendChild(container)\n  container.appendChild(app)\n\n  doc.documentElement = html\n  doc.head = head\n  doc.body = body\n\n  return doc\n}\n\n// Note: 小程序端 vite 打包成 commonjs，const document = xxx 会报错，所以把 document 改为 taroDocumentProvider\nexport const taroDocumentProvider: TaroDocument = process.env.TARO_PLATFORM === 'web' ? env.document : (env.document = createDocument())\n", "import env from '../env'\n\nimport type { TaroElement } from '../dom/element'\nimport type { Style } from '../dom/style'\n\nexport type TGetComputedStyle = typeof window.getComputedStyle | ((el: TaroElement) => Style)\n\n// Note: 小程序端 vite 打包成 commonjs，const getComputedStyle = xxx 会报错，所以把 GetComputedStyle 改为 taroGetComputedStyleProvider\nexport const taroGetComputedStyleProvider: TGetComputedStyle = process.env.TARO_PLATFORM === 'web' ? env.window.getComputedStyle : function (element: TaroElement): Style {\n  return element.style\n}\n", "import { isNumber, isString } from '@tarojs/shared'\n\nimport { CONTEXT_ACTIONS } from '../constants'\nimport { Events } from '../emitter/emitter'\nimport env from '../env'\nimport { RuntimeCache } from '../utils/cache'\n\nimport type { <PERSON>roLocation } from './location'\n\nexport interface HistoryState {\n  state: Record<string, any> | null\n  title: string\n  url: string\n}\n\ntype Options = {\n  window: any\n}\ntype HistoryContext = {\n  location: TaroLocation\n  stack: HistoryState[]\n  cur: number\n}\nconst cache = new RuntimeCache<HistoryContext>('history')\n\nclass TaroHistory extends Events {\n  /* private property */\n  #location: TaroLocation\n  #stack: HistoryState[] = []\n  #cur = 0\n\n  #window: any\n\n  constructor (location: TaroLocation, options: Options) {\n    super()\n\n    this.#window = options.window\n    this.#location = location\n\n    this.#location.on('__record_history__', (href: string) => {\n      this.#cur++\n      this.#stack = this.#stack.slice(0, this.#cur)\n      this.#stack.push({\n        state: null,\n        title: '',\n        url: href\n      })\n    }, null)\n\n    this.#location.on('__reset_history__', (href: string) => {\n      this.#reset(href)\n    }, null)\n\n    // 切换上下文行为\n\n    this.on(CONTEXT_ACTIONS.INIT, () => {\n      this.#reset()\n    }, null)\n\n    this.on(CONTEXT_ACTIONS.RESTORE, (pageId: string) => {\n      cache.set(pageId, {\n        location: this.#location,\n        stack: this.#stack.slice(),\n        cur: this.#cur\n      })\n    }, null)\n\n    this.on(CONTEXT_ACTIONS.RECOVER, (pageId: string) => {\n      if (cache.has(pageId)) {\n        const ctx = cache.get(pageId)!\n        this.#location = ctx.location\n        this.#stack = ctx.stack\n        this.#cur = ctx.cur\n      }\n    }, null)\n\n    this.on(CONTEXT_ACTIONS.DESTORY, (pageId: string) => {\n      cache.delete(pageId)\n    }, null)\n\n    this.#reset()\n  }\n\n  #reset (href = '') {\n    this.#stack = [\n      {\n        state: null,\n        title: '',\n        url: href || this.#location.href\n      }\n    ]\n    this.#cur = 0\n  }\n\n  /* public property */\n  get length () {\n    return this.#stack.length\n  }\n\n  get state () {\n    return this.#stack[this.#cur].state\n  }\n\n  /* public method */\n  go (delta: number) {\n    if (!isNumber(delta) || isNaN(delta)) return\n\n    let targetIdx = this.#cur + delta\n    targetIdx = Math.min(Math.max(targetIdx, 0), this.length - 1)\n\n    this.#cur = targetIdx\n\n    this.#location.trigger('__set_href_without_history__', this.#stack[this.#cur].url)\n    this.#window.trigger('popstate', this.#stack[this.#cur])\n  }\n\n  back () {\n    this.go(-1)\n  }\n\n  forward () {\n    this.go(1)\n  }\n\n  pushState (state: any, title: string, url: string) {\n    if (!url || !isString(url)) return\n    this.#stack = this.#stack.slice(0, this.#cur + 1)\n    this.#stack.push({\n      state,\n      title,\n      url\n    })\n    this.#cur = this.length - 1\n\n    this.#location.trigger('__set_href_without_history__', url)\n  }\n\n  replaceState (state: any, title: string, url: string) {\n    if (!url || !isString(url)) return\n    this.#stack[this.#cur] = {\n      state,\n      title,\n      url\n    }\n\n    this.#location.trigger('__set_href_without_history__', url)\n  }\n\n  // For debug\n  get cache () {\n    return cache\n  }\n}\n\nexport type { TaroHistory }\nexport const History: typeof TaroHistory = process.env.TARO_PLATFORM === 'web' ? env.window.History : TaroHistory\n", "import { isNumber, isString, warn } from '@tarojs/shared'\n\nimport { CONTEXT_ACTIONS } from '../constants'\nimport { getCurrentInstance } from '../current'\nimport { Events } from '../emitter/emitter'\nimport env from '../env'\nimport { RuntimeCache } from '../utils/cache'\nimport { TaroURLProvider } from './URL'\n\ntype PreValue = ReturnType<typeof TaroURLProvider.prototype._toRaw>\n\ntype Options = {\n  window: any\n}\ntype LocationContext = {\n  lastHref: string\n}\n\nconst INIT_URL = 'https://taro.com'\nconst cache = new RuntimeCache<LocationContext>('location')\n\nclass TaroLocation extends Events {\n  /* private property */\n  #url = new TaroURLProvider(INIT_URL)\n  #noCheckUrl = false\n  #window: any\n\n  constructor (options: Options) {\n    super()\n\n    this.#window = options.window\n\n    this.#reset()\n\n    this.on(\n      '__set_href_without_history__',\n      (href: string) => {\n        this.#noCheckUrl = true\n\n        const lastHash = this.#url.hash\n        this.#url.href = generateFullUrl(href)\n\n        if (lastHash !== this.#url.hash) {\n          this.#window.trigger('hashchange')\n        }\n\n        this.#noCheckUrl = false\n      },\n      null\n    )\n\n    // 切换上下文行为\n    this.on(\n      CONTEXT_ACTIONS.INIT,\n      () => {\n        this.#reset()\n      },\n      null\n    )\n\n    this.on(\n      CONTEXT_ACTIONS.RESTORE,\n      (pageId: string) => {\n        cache.set(pageId, {\n          lastHref: this.href,\n        })\n      },\n      null\n    )\n\n    this.on(\n      CONTEXT_ACTIONS.RECOVER,\n      (pageId: string) => {\n        // 数据恢复时，不需要执行跳转\n        if (cache.has(pageId)) {\n          const ctx = cache.get(pageId)!\n          this.#noCheckUrl = true\n          this.#url.href = ctx.lastHref\n          this.#noCheckUrl = false\n        }\n      },\n      null\n    )\n\n    this.on(\n      CONTEXT_ACTIONS.DESTORY,\n      (pageId: string) => {\n        cache.delete(pageId)\n      },\n      null\n    )\n  }\n\n  /* private method */\n  #reset () {\n    const Current = getCurrentInstance()\n    const router = Current.router\n    if (router) {\n      const { path, params } = router\n      const searchArr = Object.keys(params).map((key) => {\n        return `${key}=${params[key]}`\n      })\n      const searchStr = searchArr.length > 0 ? '?' + searchArr.join('&') : ''\n      const url = `${INIT_URL}${path.startsWith('/') ? path : '/' + path}${searchStr}`\n\n      this.#url = new TaroURLProvider(url)\n\n      this.trigger('__reset_history__', this.href)\n    }\n  }\n\n  #getPreValue (): PreValue {\n    return this.#url._toRaw()\n  }\n\n  #rollBack (href: string) {\n    this.#url.href = href\n  }\n\n  #recordHistory () {\n    this.trigger('__record_history__', this.href)\n  }\n\n  /**\n   * 校验url的变化，是否需要更新history\n   */\n  #checkUrlChange (preValue: PreValue): boolean {\n    if (this.#noCheckUrl) {\n      return false\n    }\n\n    const { protocol, hostname, port, pathname, search, hash } = this.#url._toRaw()\n\n    // 跨域三要素不允许修改\n    if (protocol !== preValue.protocol || hostname !== preValue.hostname || port !== preValue.port) {\n      this.#rollBack(preValue.href)\n      return false\n    }\n\n    // pathname\n    if (pathname !== preValue.pathname) {\n      return true\n    }\n\n    // search\n    if (search !== preValue.search) {\n      return true\n    }\n\n    // hashchange\n    if (hash !== preValue.hash) {\n      this.#window.trigger('hashchange')\n      return true\n    }\n\n    this.#rollBack(preValue.href)\n    return false\n  }\n\n  /* public property */\n  get protocol () {\n    return this.#url.protocol\n  }\n\n  set protocol (val: string) {\n    const REG = /^(http|https):$/i\n    if (!val || !isString(val) || !REG.test(val.trim())) return\n\n    val = val.trim()\n    const preValue = this.#getPreValue()\n    this.#url.protocol = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get host () {\n    return this.#url.host\n  }\n\n  set host (val: string) {\n    if (!val || !isString(val)) return\n    val = val.trim()\n\n    const preValue = this.#getPreValue()\n    this.#url.host = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get hostname () {\n    return this.#url.hostname\n  }\n\n  set hostname (val: string) {\n    if (!val || !isString(val)) return\n    val = val.trim()\n\n    const preValue = this.#getPreValue()\n    this.#url.hostname = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get port () {\n    return this.#url.port\n  }\n\n  set port (val: string) {\n    const xVal = Number((val = val.trim()))\n    if (!isNumber(xVal) || xVal <= 0) return\n\n    const preValue = this.#getPreValue()\n    this.#url.port = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get pathname () {\n    return this.#url.pathname\n  }\n\n  set pathname (val: string) {\n    if (!val || !isString(val)) return\n    val = val.trim()\n\n    const preValue = this.#getPreValue()\n    this.#url.pathname = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get search () {\n    return this.#url.search\n  }\n\n  set search (val: string) {\n    if (!val || !isString(val)) return\n    val = val.trim()\n    val = val.startsWith('?') ? val : `?${val}`\n\n    const preValue = this.#getPreValue()\n    this.#url.search = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get hash () {\n    return this.#url.hash\n  }\n\n  // 小程序的navigateTo存在截断hash字符串的问题\n  set hash (val: string) {\n    if (!val || !isString(val)) return\n    val = val.trim()\n    val = val.startsWith('#') ? val : `#${val}`\n\n    const preValue = this.#getPreValue()\n    this.#url.hash = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get href () {\n    return this.#url.href\n  }\n\n  set href (val: string) {\n    const REG = /^(http:|https:)?\\/\\/.+/\n    if (!val || !isString(val) || !REG.test((val = val.trim()))) return\n\n    const preValue = this.#getPreValue()\n    this.#url.href = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  get origin () {\n    return this.#url.origin\n  }\n\n  set origin (val: string) {\n    const REG = /^(http:|https:)?\\/\\/.+/\n    if (!val || !isString(val) || !REG.test((val = val.trim()))) return\n\n    const preValue = this.#getPreValue()\n    this.#url.origin = val\n\n    if (this.#checkUrlChange(preValue)) this.#recordHistory()\n  }\n\n  /* public method */\n  assign () {\n    warn(true, '小程序环境中调用location.assign()无效.')\n  }\n\n  reload () {\n    warn(true, '小程序环境中调用location.reload()无效.')\n  }\n\n  replace (url: string) {\n    this.trigger('__set_href_without_history__', url)\n  }\n\n  toString () {\n    return this.href\n  }\n\n  // For debug\n  get cache () {\n    return cache\n  }\n}\n\nexport type { TaroLocation }\nexport const Location: typeof TaroLocation = process.env.TARO_PLATFORM === 'web' ? env.window.Location : TaroLocation\n\nfunction generateFullUrl (val = '') {\n  const origin = INIT_URL\n  if (/^[/?#]/.test(val)) {\n    return origin + val\n  }\n  return val\n}\n", "import env from '../env'\n\nconst machine = 'Macintosh'\nconst arch = 'Intel Mac OS X 10_14_5'\nconst engine = 'AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36'\n\nconst msg = '(' + machine + '; ' + arch + ') ' + engine\n\nexport const nav: typeof window.navigator = process.env.TARO_PLATFORM === 'web' ? env.window.navigator : {\n  appCodeName: 'Mozilla',\n  appName: 'Netscape',\n  appVersion: '5.0 ' + msg,\n  cookieEnabled: true,\n  mimeTypes: [],\n  onLine: true,\n  platform: 'MacIntel',\n  plugins: [],\n  product: 'Taro',\n  productSub: '20030107',\n  userAgent: 'Mozilla/5.0 ' + msg,\n  vendor: 'Joyent',\n  vendorSub: ''\n}\n", "// https://github.com/myrne/performance-now\nexport let now: () => number\n\n(function () {\n  let loadTime\n  if ((typeof performance !== 'undefined' && performance !== null) && performance.now) {\n    now = () => performance.now()\n  } else if (Date.now) {\n    loadTime = Date.now()\n    now = () => Date.now() - loadTime\n  } else {\n    loadTime = new Date().getTime()\n    now = () => new Date().getTime() - loadTime\n  }\n})()\n\nlet lastTime = 0\n\n// https://gist.github.com/paulirish/1579671\n// https://gist.github.com/jalbam/5fe05443270fa6d8136238ec72accbc0\nconst _raf = process.env.TARO_PLATFORM === 'web' ? requestAnimationFrame : function (callback) {\n  const _now = now()\n  const nextTime = Math.max(lastTime + 16, _now) // First time will execute it immediately but barely noticeable and performance is gained.\n  return setTimeout(function () { callback(lastTime = nextTime) }, nextTime - _now)\n}\n\nconst _caf = process.env.TARO_PLATFORM === 'web'\n  ? cancelAnimationFrame\n  : function (seed) {\n    // fix https://github.com/NervJS/taro/issues/7749\n    clearTimeout(seed)\n  }\n\nexport {\n  _caf as caf,\n  _raf as raf\n}\n", "import { isString } from '@tarojs/shared'\n\nimport { CONTEXT_ACTIONS } from '../constants'\nimport { Events } from '../emitter/emitter'\nimport env from '../env'\nimport { taroGetComputedStyleProvider } from './getComputedStyle'\nimport { History } from './history'\nimport { Location } from './location'\nimport { nav as navigator } from './navigator'\nimport { caf, raf } from './raf'\n\nimport type { TaroHistory } from './history'\nimport type { TaroLocation } from './location'\n\nclass TaroWindow extends Events {\n  navigator = navigator\n  requestAnimationFrame = raf\n  cancelAnimationFrame = caf\n  getComputedStyle = taroGetComputedStyleProvider\n  Date: DateConstructor\n\n  location: TaroLocation\n  history: TaroHistory\n  XMLHttpRequest?: Partial<XMLHttpRequest>\n\n  constructor () {\n    super()\n\n    const globalProperties = [\n      ...Object.getOwnPropertyNames(global || {}),\n      ...Object.getOwnPropertySymbols(global || {})\n    ]\n\n    globalProperties.forEach(property => {\n      if (property === 'atob' || property === 'document') return\n      if (!Object.prototype.hasOwnProperty.call(this, property)) {\n        // 防止小程序环境下，window 上的某些 get 属性在赋值时报错\n        try {\n          this[property] = global[property]\n        } catch (e) {\n          if (process.env.NODE_ENV !== 'production') {\n            console.warn(`[Taro warn] window.${String(property)} 在赋值到 window 时报错`)\n          }\n        }\n      }\n    })\n\n    this.Date ||= Date\n\n    // 应用启动时，提供给需要读取历史信息的库使用\n    this.location = new Location({ window: this }) as any\n    // @ts-ignore\n    this.history = new History(this.location, { window: this })\n\n    this.initEvent()\n  }\n\n  initEvent () {\n    const _location = this.location\n    const _history = this.history\n\n    this.on(CONTEXT_ACTIONS.INIT, (pageId: string) => {\n      // 页面onload，为该页面建立新的上下文信息\n      _location.trigger(CONTEXT_ACTIONS.INIT, pageId)\n    }, null)\n\n    this.on(CONTEXT_ACTIONS.RECOVER, (pageId: string) => {\n      // 页面onshow，恢复当前页面的上下文信息\n      _location.trigger(CONTEXT_ACTIONS.RECOVER, pageId)\n      _history.trigger(CONTEXT_ACTIONS.RECOVER, pageId)\n    }, null)\n\n    this.on(CONTEXT_ACTIONS.RESTORE, (pageId: string) => {\n      // 页面onhide，缓存当前页面的上下文信息\n      _location.trigger(CONTEXT_ACTIONS.RESTORE, pageId)\n      _history.trigger(CONTEXT_ACTIONS.RESTORE, pageId)\n    }, null)\n\n    this.on(CONTEXT_ACTIONS.DESTORY, (pageId: string) => {\n      // 页面onunload，清除当前页面的上下文信息\n      _location.trigger(CONTEXT_ACTIONS.DESTORY, pageId)\n      _history.trigger(CONTEXT_ACTIONS.DESTORY, pageId)\n    }, null)\n  }\n\n  get document () {\n    return env.document\n  }\n\n  addEventListener (event: string, callback: (arg: any) => void) {\n    if (!isString(event)) return\n    this.on(event, callback, null)\n  }\n\n  removeEventListener (event: string, callback: (arg: any) => void) {\n    if (!isString(event)) return\n    this.off(event, callback, null)\n  }\n\n  setTimeout (...args: Parameters<typeof setTimeout>) {\n    return setTimeout(...args)\n  }\n\n  clearTimeout (...args: Parameters<typeof clearTimeout>) {\n    return clearTimeout(...args)\n  }\n}\n\nexport type { TaroWindow }\n\n// Note: 小程序端 vite 打包成 commonjs，const window = xxx 会报错，所以把 window 改为 taroWindowProvider，location 和 history 同理\nexport const taroWindowProvider: TaroWindow = process.env.TARO_PLATFORM === 'web' ? env.window : (env.window = new TaroWindow())\nexport const taroLocationProvider = taroWindowProvider.location\nexport const taroHistoryProvider = taroWindowProvider.history\n", "export const PROPERTY_THRESHOLD = 2046\nexport const TARO_RUNTIME = 'Taro runtime'\nexport const HOOKS_APP_ID = 'taro-app'\nexport const SET_DATA = '小程序 setData'\nexport const PAGE_INIT = '页面初始化'\nexport const ROOT_STR = 'root'\nexport const HTML = 'html'\nexport const HEAD = 'head'\nexport const BODY = 'body'\nexport const APP = 'app'\nexport const CONTAINER = 'container'\nexport const DOCUMENT_ELEMENT_NAME = '#document'\nexport const DOCUMENT_FRAGMENT = 'document-fragment'\nexport const ID = 'id'\nexport const UID = 'uid'\nexport const CLASS = 'class'\nexport const STYLE = 'style'\nexport const FOCUS = 'focus'\nexport const VIEW = 'view'\nexport const STATIC_VIEW = 'static-view'\nexport const PURE_VIEW = 'pure-view'\nexport const CLICK_VIEW = 'click-view'\nexport const PROPS = 'props'\nexport const DATASET = 'dataset'\nexport const OBJECT = 'object'\nexport const VALUE = 'value'\nexport const INPUT = 'input'\nexport const CHANGE = 'change'\nexport const CUSTOM_WRAPPER = 'custom-wrapper'\nexport const TARGET = 'target'\nexport const CURRENT_TARGET = 'currentTarget'\nexport const TYPE = 'type'\nexport const CONFIRM = 'confirm'\nexport const TIME_STAMP = 'timeStamp'\nexport const KEY_CODE = 'keyCode'\nexport const TOUCHMOVE = 'touchmove'\nexport const DATE = 'Date'\nexport const SET_TIMEOUT = 'setTimeout'\nexport const COMPILE_MODE = 'compileMode'\nexport const CATCHMOVE = 'catchMove'\nexport const CATCH_VIEW = 'catch-view'\nexport const COMMENT = 'comment'\nexport const ON_LOAD = 'onLoad'\nexport const ON_READY = 'onReady'\nexport const ON_SHOW = 'onShow'\nexport const ON_HIDE = 'onHide'\nexport const OPTIONS = 'options'\nexport const EXTERNAL_CLASSES = 'externalClasses'\nexport const EVENT_CALLBACK_RESULT = 'e_result'\nexport const BEHAVIORS = 'behaviors'\nexport const A = 'a'\n\n/**\n * 页面上下文切换时的行为\n */\nexport enum CONTEXT_ACTIONS {\n  INIT = '0',\n  RESTORE = '1',\n  RECOVER = '2',\n  DESTORY = '3'\n}\n", "import { AppInstance, PageInstance } from './dsl/instance'\n\nexport interface Router {\n  params: Record<string, unknown>\n  path: string\n  $taroPath: string\n  onReady: string\n  onHide: string\n  onShow: string\n  exitState?: any\n}\n\ninterface Current {\n  app: AppInstance | null\n  router: Router | null\n  page: PageInstance | null\n  preloadData?: any\n}\n\nexport const Current: Current = {\n  app: null,\n  router: null,\n  page: null\n}\n\nexport const getCurrentInstance = () => Current\n", "import { MutationRecordType } from './record'\n\nimport type { TaroNode } from '../../dom/node'\nimport type { MutationRecord } from './record'\n\nexport type MutationCallback = (mutations: MutationRecord[]) => any\n\n/**\n * @see https://dom.spec.whatwg.org/#dictdef-mutationobserverinit\n */\nexport interface MutationObserverInit {\n  attributeFilter?: string[]\n  attributeOldValue?: boolean\n  attributes?: boolean\n  characterData?: boolean\n  characterDataOldValue?: boolean\n  childList?: boolean\n  subtree?: boolean\n}\n\nconst observers: MutationObserverImpl[] = []\n\n/**\n * The MutationObserver provides the ability\n * to watch for changes being made to the DOM tree.\n * It will invoke a specified callback function\n * when DOM changes occur.\n * @see https://dom.spec.whatwg.org/#mutationobserver\n * @see https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver\n */\nexport class MutationObserverImpl {\n  public callback: MutationCallback\n  public target: TaroNode | null\n  public options: MutationObserverInit\n  public records: MutationRecord[] = []\n\n  constructor (callback: MutationCallback) {\n    this.callback = callback\n  }\n\n  /**\n   * Configures the MutationObserver\n   * to begin receiving notifications\n   * through its callback function\n   * when DOM changes matching the given options occur.\n   *\n   * Options matching is to be implemented.\n   */\n  observe (target: TaroNode, options?: MutationObserverInit): void {\n    this.disconnect()\n    this.target = target\n    this.options = options || {}\n\n    observers.push(this)\n  }\n\n  /**\n   * Stop the MutationObserver instance\n   * from receiving further notifications\n   * until and unless observe() is called again.\n   */\n  disconnect (): void {\n    this.target = null\n\n    const index = observers.indexOf(this)\n    if (index >= 0) {\n      observers.splice(index, 1)\n    }\n  }\n\n  /**\n   * Removes all pending notifications\n   * from the MutationObserver's notification queue\n   * and returns them in a new Array of MutationRecord objects.\n   */\n  takeRecords (): MutationRecord[] {\n    return this.records.splice(0, this.records.length)\n  }\n}\n\n/** Match two TaroNodes by sid. */\nconst sidMatches = (\n  observerTarget: TaroNode | null,\n  target: TaroNode | null\n): boolean => {\n  return !!observerTarget && observerTarget.sid === target?.sid\n}\n\nconst isConcerned = (record: MutationRecord, options: MutationObserverInit) => {\n  const { characterData, characterDataOldValue, attributes, attributeOldValue, childList } = options\n  switch (record.type) {\n    case MutationRecordType.CHARACTER_DATA:\n      if (characterData) {\n        if (!characterDataOldValue) record.oldValue = null\n        return true\n      }\n      return false\n    case MutationRecordType.ATTRIBUTES:\n      if (attributes) {\n        if (!attributeOldValue) record.oldValue = null\n        return true\n      }\n      return false\n    case MutationRecordType.CHILD_LIST:\n      if (childList) {\n        return true\n      }\n      return false\n  }\n}\n\nlet pendingMuatations = false\n\nfunction logMutation (observer: MutationObserverImpl, record: MutationRecord) {\n  observer.records.push(record)\n  if (!pendingMuatations) {\n    pendingMuatations = true\n    Promise\n      .resolve()\n      .then(() => {\n        pendingMuatations = false\n        observers.forEach(observer => {\n          return observer.callback(observer.takeRecords())\n        })\n      })\n  }\n}\n\nexport function recordMutation (record: MutationRecord) {\n  observers.forEach(observer => {\n    const { options } = observer\n    for (let t: TaroNode | null = record.target; t; t = t.parentNode) {\n      if (sidMatches(observer.target, t) && isConcerned(record, options)) {\n        logMutation(observer, record)\n        break\n      }\n      if (!options.subtree) break\n    }\n  })\n}\n", "import { noop } from '@tarojs/shared'\n\nimport { MutationObserverImpl, recordMutation } from './implements'\nimport { MutationRecord, MutationRecordType } from './record'\n\nimport type { TaroNode } from '../../dom/node'\nimport type { MutationCallback, MutationObserverInit } from './implements'\n\ndeclare const ENABLE_MUTATION_OBSERVER: boolean\n\nexport class MutationObserver {\n  core: Pick<MutationObserverImpl, 'observe' | 'disconnect' | 'takeRecords'>\n\n  constructor (callback: MutationCallback) {\n    if (ENABLE_MUTATION_OBSERVER) {\n      this.core = new MutationObserverImpl(callback)\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('[Taro Warning] 若要使用 MutationObserver，请在 Taro 编译配置中设置 \\'mini.runtime.enableMutationObserver: true\\'')\n      }\n      this.core = {\n        observe: noop,\n        disconnect: noop,\n        takeRecords: (noop as () => any)\n      }\n    }\n  }\n\n  public observe (...args: [TaroNode, MutationObserverInit?]) {\n    this.core.observe(...args)\n  }\n\n  public disconnect () {\n    this.core.disconnect()\n  }\n\n  public takeRecords () {\n    return this.core.takeRecords()\n  }\n\n  static record (record: MutationRecord) {\n    recordMutation(record)\n  }\n}\n\nexport {\n  MutationRecordType\n}\n", "import { parseUrl } from '../bom/URL'\nimport { TaroElement } from './element'\n\nconst enum AnchorElementAttrs {\n  HREF = 'href',\n  PROTOCOL = 'protocol',\n  HOST = 'host',\n  SEARCH = 'search',\n  HASH = 'hash',\n  HOSTNAME = 'hostname',\n  PORT = 'port',\n  PATHNAME = 'pathname'\n}\n\nexport class AnchorElement extends TaroElement {\n  public get href () {\n    return this.props[AnchorElementAttrs.HREF] ?? ''\n  }\n\n  public set href (val: string) {\n    this.setAttribute(AnchorElementAttrs.HREF, val)\n  }\n\n  get protocol () {\n    return this.props[AnchorElementAttrs.PROTOCOL] ?? ''\n  }\n\n  get host () {\n    return this.props[AnchorElementAttrs.HOST] ?? ''\n  }\n\n  get search () {\n    return this.props[AnchorElementAttrs.SEARCH] ?? ''\n  }\n\n  get hash () {\n    return this.props[AnchorElementAttrs.HASH] ?? ''\n  }\n\n  get hostname () {\n    return this.props[AnchorElementAttrs.HOSTNAME] ?? ''\n  }\n\n  get port () {\n    return this.props[AnchorElementAttrs.PORT] ?? ''\n  }\n\n  get pathname () {\n    return this.props[AnchorElementAttrs.PATHNAME] ?? ''\n  }\n\n  public setAttribute (qualifiedName: string, value: any): void {\n    if (qualifiedName === AnchorElementAttrs.HREF) {\n      const willSetAttr = parseUrl(value)\n      for (const k in willSetAttr) {\n        super.setAttribute(k, willSetAttr[k])\n      }\n    } else {\n      super.setAttribute(qualifiedName, value)\n    }\n  }\n}\n", "import type { TaroElement } from './element'\n\nexport class ClassList {\n  private el: TaroElement\n\n  private tokenList: string[] = []\n\n  constructor (className: string, el: TaroElement) {\n    this.el = el\n    className.trim().split(/\\s+/).forEach(token => this.tokenList.push(token))\n  }\n\n  public get value () {\n    return this.toString()\n  }\n\n  public get length () {\n    return this.tokenList.length\n  }\n\n  add () {\n    let index = 0\n    let updated = false\n\n    const tokens = arguments\n    const length = tokens.length\n    const tokenList = this.tokenList\n\n    do {\n      const token: string = tokens[index]\n\n      if (this.checkTokenIsValid(token) && !~tokenList.indexOf(token)) {\n        tokenList.push(token)\n\n        updated = true\n      }\n    } while (++index < length)\n\n    if (updated) {\n      this._update()\n    }\n  }\n\n  remove () {\n    let i = 0\n    let updated = false\n\n    const tokens = arguments\n    const length = tokens.length\n    const tokenList = this.tokenList\n\n    do {\n      const token = tokens[i] + ''\n\n      if (!this.checkTokenIsValid(token)) continue\n\n      const index = tokenList.indexOf(token)\n\n      if (~tokenList.indexOf(token)) {\n        tokenList.splice(index, 1)\n\n        updated = true\n      }\n    } while (++i < length)\n\n    if (updated) {\n      this._update()\n    }\n  }\n\n  contains (token: string) {\n    if (!this.checkTokenIsValid(token)) return false\n\n    return !!~this.tokenList.indexOf(token)\n  }\n\n  toggle (token: string, force: boolean) {\n    const result = this.contains(token)\n    const method = result ? force !== true && 'remove' : force !== false && 'add'\n\n    if (method) {\n      // @ts-ignore\n      this[method](token)\n    }\n\n    if (force === true || force === false) {\n      return force\n    } else {\n      return !result\n    }\n  }\n\n  replace (token: string, replacement_token: string) {\n    if (!this.checkTokenIsValid(token) || !this.checkTokenIsValid(replacement_token)) return\n\n    const index = this.tokenList.indexOf(token)\n\n    if (~index) {\n      this.tokenList.splice(index, 1, replacement_token)\n      this._update()\n    }\n  }\n\n  toString () {\n    return this.tokenList.filter(v => v !== '').join(' ')\n  }\n\n  private checkTokenIsValid (token: string) {\n    if (token === '' || /\\s/.test(token)) return false\n\n    return true\n  }\n\n  private _update () {\n    this.el.className = this.value\n  }\n}\n", "import { controlledComponent, isUndefined, toCamelCase } from '@tarojs/shared'\n\nimport {\n  A,\n  COMMENT,\n  DOCUMENT_ELEMENT_NAME,\n  ROOT_STR\n} from '../constants'\nimport { TaroElement } from '../dom/element'\nimport { createEvent } from '../dom/event'\nimport { eventSource } from '../dom/event-source'\nimport { FormElement } from '../dom/form'\nimport { NodeType } from '../dom/node_types'\nimport { TaroRootElement } from '../dom/root'\nimport { TaroText } from '../dom/text'\nimport env from '../env'\nimport { AnchorElement } from './anchor-element'\nimport { TransferElement } from './transfer'\n\nexport class TaroDocument extends TaroElement {\n  public documentElement: TaroElement\n  public head: TaroElement\n  public body: TaroElement\n  public createEvent = createEvent\n  cookie?: string\n\n  public constructor () {\n    super()\n    this.nodeType = NodeType.DOCUMENT_NODE\n    this.nodeName = DOCUMENT_ELEMENT_NAME\n  }\n\n  public createElement (type: string): TaroElement | TaroRootElement | FormElement {\n    const nodeName = type.toLowerCase()\n\n    let element: TaroElement\n    switch (true) {\n      case nodeName === ROOT_STR:\n        element = new TaroRootElement()\n        return element\n      case controlledComponent.has(nodeName):\n        element = new FormElement()\n        break\n      case nodeName === A:\n        element = new AnchorElement()\n        break\n      case nodeName === 'page-meta':\n      case nodeName === 'navigation-bar':\n        element = new TransferElement(toCamelCase(nodeName))\n        break\n      default:\n        element = new TaroElement()\n        break\n    }\n\n    element.nodeName = nodeName\n    element.tagName = type.toUpperCase()\n\n    return element\n  }\n\n  // an ugly fake createElementNS to deal with @vue/runtime-dom's\n  // support mounting app to svg container since vue@3.0.8\n  public createElementNS (_svgNS: string, type: string): TaroElement | TaroRootElement | FormElement {\n    return this.createElement(type)\n  }\n\n  public createTextNode (text: string): TaroText {\n    return new TaroText(text)\n  }\n\n  public getElementById<T extends TaroElement> (id: string | undefined | null): T | null {\n    const el = eventSource.get(id)\n    return isUndefined(el) ? null : el as T\n  }\n\n  public querySelector<T extends TaroElement> (query: string): T | null {\n    // 为了 Vue3 的乞丐版实现\n    if (/^#/.test(query)) {\n      return this.getElementById<T>(query.slice(1))\n    }\n    return null\n  }\n\n  public querySelectorAll () {\n    // fake hack\n    return []\n  }\n\n  // @TODO: @PERF: 在 hydrate 移除掉空的 node\n  public createComment (): TaroText {\n    const textnode = new TaroText('')\n    textnode.nodeName = COMMENT\n    return textnode\n  }\n\n  get defaultView () {\n    return env.window\n  }\n}\n", "import { EMPTY_OBJ, hooks, isArray, isFunction, isObject, isString, isUndefined, Shortcuts, toCamelCase, warn } from '@tarojs/shared'\n\nimport {\n  CATCH_VIEW,\n  CATCHMOVE,\n  CLASS,\n  CLICK_VIEW,\n  EVENT_CALLBACK_RESULT,\n  FOCUS,\n  ID,\n  PROPERTY_THRESHOLD,\n  PURE_VIEW,\n  STATIC_VIEW,\n  STYLE,\n  VIEW\n} from '../constants'\nimport { MutationObserver, MutationRecordType } from '../dom-external/mutation-observer'\nimport { extend, getComponentsAlias, isElement, isHasExtractProp, shortcutAttr } from '../utils'\nimport { ClassList } from './class-list'\nimport { eventSource } from './event-source'\nimport { TaroNode } from './node'\nimport { NodeType } from './node_types'\nimport { Style } from './style'\nimport { treeToArray } from './tree'\n\nimport type { Attributes, TFunc } from '../interface'\nimport type { TaroEvent } from './event'\n\nexport class TaroElement extends TaroNode {\n  public ctx?\n  public tagName: string\n  public props: Record<string, any> = {}\n  public style: Style\n  public dataset: Record<string, unknown> = EMPTY_OBJ\n  public innerHTML: string\n\n  public constructor () {\n    super()\n    this.nodeType = NodeType.ELEMENT_NODE\n    this.style = new Style(this)\n    hooks.call('patchElement', this)\n  }\n\n  private _stopPropagation (event: TaroEvent) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    let target = this\n    // eslint-disable-next-line no-cond-assign\n    while ((target = target.parentNode as this)) {\n      const listeners = target.__handlers[event.type]\n\n      if (!isArray(listeners)) {\n        continue\n      }\n\n      for (let i = listeners.length; i--;) {\n        const l = listeners[i]\n        l._stop = true\n      }\n    }\n  }\n\n  public get id (): string {\n    return this.getAttribute(ID)!\n  }\n\n  public set id (val: string) {\n    this.setAttribute(ID, val)\n  }\n\n  public get className (): string {\n    return this.getAttribute(CLASS) || ''\n  }\n\n  public set className (val: string) {\n    this.setAttribute(CLASS, val)\n  }\n\n  public get cssText (): string {\n    return this.getAttribute(STYLE) || ''\n  }\n\n  public get classList (): ClassList {\n    return new ClassList(this.className, this)\n  }\n\n  public get children (): TaroElement[] {\n    return this.childNodes.filter(isElement)\n  }\n\n  public get attributes (): Attributes[] {\n    const props = this.props\n    const propKeys = Object.keys(props)\n    const style = this.style.cssText\n    const attrs = propKeys.map(key => ({ name: key, value: props[key] }))\n    return attrs.concat(style ? { name: STYLE, value: style } : [])\n  }\n\n  public get textContent (): string {\n    let text = ''\n    const childNodes = this.childNodes\n\n    for (let i = 0; i < childNodes.length; i++) {\n      text += childNodes[i].textContent\n    }\n\n    return text\n  }\n\n  public set textContent (text: string) {\n    super.textContent = text\n  }\n\n  public hasAttribute (qualifiedName: string): boolean {\n    return !isUndefined(this.props[qualifiedName])\n  }\n\n  public hasAttributes (): boolean {\n    return this.attributes.length > 0\n  }\n\n  public get focus () {\n    return function () {\n      this.setAttribute(FOCUS, true)\n    }\n  }\n\n  // 兼容 Vue3，详情请见：https://github.com/NervJS/taro/issues/10579\n  public set focus (value) {\n    this.setAttribute(FOCUS, value)\n  }\n\n  public blur () {\n    this.setAttribute(FOCUS, false)\n  }\n\n  public setAttribute (qualifiedName: string, value: any): void {\n    process.env.NODE_ENV !== 'production' && warn(\n      isString(value) && value.length > PROPERTY_THRESHOLD,\n      `元素 ${this.nodeName} 的 ${qualifiedName} 属性值数据量过大，可能会影响渲染性能。考虑降低图片转为 base64 的阈值或在 CSS 中使用 base64。`\n    )\n\n    const isPureView = this.nodeName === VIEW && !isHasExtractProp(this) && !this.isAnyEventBinded()\n\n    if (qualifiedName !== STYLE) {\n      MutationObserver.record({\n        target: this,\n        type: MutationRecordType.ATTRIBUTES,\n        attributeName: qualifiedName,\n        oldValue: this.getAttribute(qualifiedName)\n      })\n    }\n\n    switch (qualifiedName) {\n      case STYLE:\n        this.style.cssText = value as string\n        break\n      case ID:\n        if (this.uid !== this.sid) {\n          // eventSource[sid] 永远保留，直到组件卸载\n          // eventSource[uid] 可变\n          eventSource.delete(this.uid)\n        }\n        value = String(value)\n        this.props[qualifiedName] = this.uid = value\n        eventSource.set(value, this)\n        break\n      default:\n        this.props[qualifiedName] = value as string\n\n        if (qualifiedName.startsWith('data-')) {\n          if (this.dataset === EMPTY_OBJ) {\n            this.dataset = Object.create(null)\n          }\n          this.dataset[toCamelCase(qualifiedName.replace(/^data-/, ''))] = value\n        }\n        break\n    }\n\n    // Serialization\n    if (!this._root) return\n\n    const componentsAlias = getComponentsAlias()\n    const _alias = componentsAlias[this.nodeName]\n    const viewAlias = componentsAlias[VIEW]._num\n    const clickViewAlias = componentsAlias[CLICK_VIEW]._num\n    const staticViewAlias = componentsAlias[STATIC_VIEW]._num\n    const catchViewAlias = componentsAlias[CATCH_VIEW]._num\n    const _path = this._path\n\n    qualifiedName = shortcutAttr(qualifiedName)\n\n    const qualifiedNameInCamelCase = toCamelCase(qualifiedName)\n    const payload = {\n      path: `${_path}.${qualifiedNameInCamelCase}`,\n      value: isFunction(value) ? () => value : value\n    }\n\n    hooks.call('modifySetAttrPayload', this, qualifiedName, payload, componentsAlias)\n\n    if (_alias) {\n      const qualifiedNameAlias = _alias[qualifiedNameInCamelCase] || qualifiedName\n      payload.path = `${_path}.${toCamelCase(qualifiedNameAlias)}`\n    }\n\n    this.enqueueUpdate(payload)\n\n    if (this.nodeName === VIEW) {\n      if (qualifiedNameInCamelCase === CATCHMOVE) {\n        // catchMove = true: catch-view\n        // catchMove = false: view or click-view or static-view\n        this.enqueueUpdate({\n          path: `${_path}.${Shortcuts.NodeName}`,\n          value: value ? catchViewAlias : (\n            this.isOnlyClickBinded() && !isHasExtractProp(this) ? clickViewAlias : (this.isAnyEventBinded() ? viewAlias : staticViewAlias)\n          )\n        })\n      } else if (isPureView && isHasExtractProp(this)) {\n        // pure-view => static-view\n        this.enqueueUpdate({\n          path: `${_path}.${Shortcuts.NodeName}`,\n          value: staticViewAlias\n        })\n      }\n    }\n  }\n\n  public removeAttribute (qualifiedName: string) {\n    const isStaticView = this.nodeName === VIEW && isHasExtractProp(this) && !this.isAnyEventBinded()\n\n    MutationObserver.record({\n      target: this,\n      type: MutationRecordType.ATTRIBUTES,\n      attributeName: qualifiedName,\n      oldValue: this.getAttribute(qualifiedName)\n    })\n\n    if (qualifiedName === STYLE) {\n      this.style.cssText = ''\n    } else {\n      const isInterrupt = hooks.call('onRemoveAttribute', this, qualifiedName)\n      if (isInterrupt) {\n        return\n      }\n\n      if (!this.props.hasOwnProperty(qualifiedName)) {\n        return\n      }\n      delete this.props[qualifiedName]\n    }\n\n    // Serialization\n    if (!this._root) return\n\n    const componentsAlias = getComponentsAlias()\n    const _alias = componentsAlias[this.nodeName]\n    const viewAlias = componentsAlias[VIEW]._num\n    const staticViewAlias = componentsAlias[STATIC_VIEW]._num\n    const pureViewAlias = componentsAlias[PURE_VIEW]._num\n    const clickViewAlias = componentsAlias[CLICK_VIEW]._num\n    const _path = this._path\n\n    qualifiedName = shortcutAttr(qualifiedName)\n\n    const qualifiedNameInCamelCase = toCamelCase(qualifiedName)\n    const payload = {\n      path: `${_path}.${qualifiedNameInCamelCase}`,\n      value: ''\n    }\n\n    hooks.call('modifyRmAttrPayload', this, qualifiedName, payload, componentsAlias)\n\n    if (_alias) {\n      const qualifiedNameAlias = _alias[qualifiedNameInCamelCase] || qualifiedName\n      payload.path = `${_path}.${toCamelCase(qualifiedNameAlias)}`\n    }\n\n    this.enqueueUpdate(payload)\n\n    if (this.nodeName === VIEW) {\n      if (qualifiedNameInCamelCase === CATCHMOVE) {\n        // catch-view => view or click-view or static-view or pure-view\n        this.enqueueUpdate({\n          path: `${_path}.${Shortcuts.NodeName}`,\n          value: this.isOnlyClickBinded() && !isHasExtractProp(this) ? clickViewAlias : (this.isAnyEventBinded() ? viewAlias : (isHasExtractProp(this) ? staticViewAlias : pureViewAlias))\n        })\n      } else if (isStaticView && !isHasExtractProp(this)) {\n        // static-view => pure-view\n        this.enqueueUpdate({\n          path: `${_path}.${Shortcuts.NodeName}`,\n          value: pureViewAlias\n        })\n      }\n    }\n  }\n\n  public getAttribute (qualifiedName: string): string {\n    const attr = qualifiedName === STYLE ? this.style.cssText : this.props[qualifiedName]\n    return attr ?? ''\n  }\n\n  public getElementsByTagName (tagName: string): TaroElement[] {\n    return treeToArray(this, (el) => {\n      return el.nodeName === tagName || (tagName === '*' && this !== el)\n    })\n  }\n\n  public getElementsByClassName (className: string): TaroElement[] {\n    const classNames = className.trim().split(/\\s+/)\n\n    return treeToArray(this, (el) => {\n      const classList = el.classList\n      return classNames.every(c => classList.contains(c))\n    })\n  }\n\n  public dispatchEvent (event: TaroEvent): boolean {\n    const cancelable = event.cancelable\n\n    const listeners = this.__handlers[event.type]\n\n    if (!isArray(listeners)) {\n      return false\n    }\n\n    for (let i = listeners.length; i--;) {\n      const listener = listeners[i]\n      let result: unknown\n      if (listener._stop) {\n        listener._stop = false\n      } else {\n        hooks.call('modifyDispatchEvent', event, this)\n        result = listener.call(this, event)\n      }\n      if ((result === false || event._end) && cancelable) {\n        event.defaultPrevented = true\n      }\n\n      if (!isUndefined(result) && event.mpEvent) {\n        const res = hooks.call('modifyTaroEventReturn', this, event, result)\n        if (res) { event.mpEvent[EVENT_CALLBACK_RESULT] = result }\n      }\n\n      if (event._end && event._stop) {\n        break\n      }\n    }\n\n    if (event._stop) {\n      this._stopPropagation(event)\n    }\n\n    return listeners != null\n  }\n\n  public addEventListener (type, handler, options) {\n    const name = this.nodeName\n    const SPECIAL_NODES = hooks.call('getSpecialNodes')!\n\n    let sideEffect = true\n    if (isObject<Record<string, any>>(options) && options.sideEffect === false) {\n      sideEffect = false\n      delete options.sideEffect\n    }\n\n    hooks.call('modifyAddEventListener', this, sideEffect, getComponentsAlias)\n\n    if (sideEffect !== false && !this.isAnyEventBinded() && SPECIAL_NODES.indexOf(name) > -1) {\n      const componentsAlias = getComponentsAlias()\n      const alias = componentsAlias[name]._num\n      this.enqueueUpdate({\n        path: `${this._path}.${Shortcuts.NodeName}`,\n        value: alias\n      })\n    }\n\n    super.addEventListener(type, handler, options)\n  }\n\n  public removeEventListener (type, handler, sideEffect = true) {\n    super.removeEventListener(type, handler)\n\n    const name = this.nodeName\n    const SPECIAL_NODES = hooks.call('getSpecialNodes')!\n\n    hooks.call('modifyRemoveEventListener', this, sideEffect, getComponentsAlias)\n\n    if (sideEffect !== false && !this.isAnyEventBinded() && SPECIAL_NODES.indexOf(name) > -1) {\n      const componentsAlias = getComponentsAlias()\n      const value = isHasExtractProp(this) ? `static-${name}` : `pure-${name}`\n      const valueAlias = componentsAlias[value]._num\n      this.enqueueUpdate({\n        path: `${this._path}.${Shortcuts.NodeName}`,\n        value: valueAlias\n      })\n    }\n  }\n\n  static extend (methodName: string, options: TFunc | Record<string, any>) {\n    extend(TaroElement, methodName, options)\n  }\n}\n", "import type { TaroNode } from './node'\n\ninterface IEventSource extends Map<string | undefined | null, TaroNode> {\n  removeNode (child: TaroNode): void\n  removeNodeTree (child: TaroNode): void\n}\n\nclass EventSource extends Map {\n  removeNode (child: TaroNode) {\n    const { sid, uid } = child\n    this.delete(sid)\n    if (uid !== sid && uid) this.delete(uid)\n  }\n\n  removeNodeTree (child: TaroNode) {\n    this.removeNode(child)\n    const { childNodes } = child\n    childNodes.forEach(node => this.removeNodeTree(node))\n  }\n}\n\nexport const eventSource: IEventSource = new EventSource()\n", "import { hooks, isArray, isObject, warn } from '@tarojs/shared'\n\nimport type { AddEventListenerOptions, EventHandler } from '../interface'\n\nexport class TaroEventTarget {\n  public __handlers: Record<string, EventHandler[]> = {}\n\n  public addEventListener (type: string, handler: EventHandler, options?: boolean | AddEventListenerOptions) {\n    type = type.toLowerCase()\n\n    hooks.call('onAddEvent', type, handler, options, this)\n\n    if (type === 'regionchange') {\n      // map 组件的 regionchange 事件非常特殊，详情：https://github.com/NervJS/taro/issues/5766\n      this.addEventListener('begin', handler, options)\n      this.addEventListener('end', handler, options)\n      return\n    }\n\n    let isCapture = Boolean(options)\n    let isOnce = false\n    if (isObject<AddEventListenerOptions>(options)) {\n      isCapture = Boolean(options.capture)\n      isOnce = Boolean(options.once)\n    }\n\n    if (isOnce) {\n      const wrapper = function () {\n        handler.apply(this, arguments) // this 指向 Element\n        this.removeEventListener(type, wrapper)\n      }\n      this.addEventListener(type, wrapper, {\n        ...(options as AddEventListenerOptions),\n        once: false\n      })\n      return\n    }\n\n    process.env.NODE_ENV !== 'production' && warn(isCapture, 'Taro 暂未实现 event 的 capture 特性。')\n\n    // 某些框架，如 PReact 有委托的机制，handler 始终是同一个函数\n    // 这会导致多层停止冒泡失败：view -> view(handler.stop = false) -> view(handler.stop = true)\n    // 这样解决：view -> view(handlerA.stop = false) -> view(handlerB.stop = false)\n    // 因此每次绑定事件都新建一个函数，如果带来了性能问题，可以把这段逻辑抽取到 PReact 插件中。\n    const oldHandler = handler\n    handler = function () {\n      return oldHandler.apply(this, arguments) // this 指向 Element\n    }\n    ;(handler as any).oldHandler = oldHandler\n\n    const handlers = this.__handlers[type]\n    if (isArray(handlers)) {\n      handlers.push(handler)\n    } else {\n      this.__handlers[type] = [handler]\n    }\n  }\n\n  public removeEventListener (type: string, handler: EventHandler) {\n    type = type.toLowerCase()\n\n    if (type === 'regionchange') {\n      // map 组件的 regionchange 事件非常特殊，详情：https://github.com/NervJS/taro/issues/5766\n      this.removeEventListener('begin', handler)\n      this.removeEventListener('end', handler)\n      return\n    }\n\n    if (!handler) {\n      return\n    }\n\n    const handlers = this.__handlers[type]\n    if (!isArray(handlers)) {\n      return\n    }\n\n    const index = handlers.findIndex(item => {\n      if (item === handler || (item as any).oldHandler === handler) return true\n    })\n\n    process.env.NODE_ENV !== 'production' && warn(index === -1, `事件: '${type}' 没有注册在 DOM 中，因此不会被移除。`)\n\n    handlers.splice(index, 1)\n  }\n\n  public isAnyEventBinded (): boolean {\n    const handlers = this.__handlers\n    const isAnyEventBinded = Object.keys(handlers).find(key => handlers[key].length)\n    return Boolean(isAnyEventBinded)\n  }\n\n  public isOnlyClickBinded (): boolean {\n    const handlers = this.__handlers\n    const isOnlyClickBinded = handlers.tap && Object.keys(handlers).length === 1\n    return Boolean(isOnlyClickBinded)\n  }\n}\n", "import { EMPTY_OBJ, hooks, isUndefined } from '@tarojs/shared'\n\nimport {\n  CONFIRM,\n  CURRENT_TARGET,\n  EVENT_CALLBACK_RESULT,\n  INPUT,\n  KEY_CODE,\n  TARGET,\n  TIME_STAMP,\n  TOUCHMOVE,\n  TYPE\n} from '../constants'\nimport env from '../env'\nimport { isParentBinded } from '../utils'\n\nimport type { EventOptions, MpEvent } from '../interface'\nimport type { TaroElement } from './element'\n\n// Taro 事件对象。以 Web 标准的事件对象为基础，加入小程序事件对象中携带的部分信息，并模拟实现事件冒泡。\nexport class TaroEvent {\n  private cacheTarget\n  private cacheCurrentTarget\n\n  public type: string\n\n  public bubbles: boolean\n\n  public cancelable: boolean\n\n  public _stop = false\n\n  public _end = false\n\n  public defaultPrevented = false\n\n  // Mouse Event botton property, it's used in 3rd lib, like react-router. default 0 in general\n  public button = 0\n\n  // timestamp can either be hi-res ( relative to page load) or low-res (relative to UNIX epoch)\n  // here use hi-res timestamp\n  public timeStamp = Date.now()\n\n  public mpEvent: MpEvent | undefined\n\n  public constructor (type: string, opts: EventOptions, event?: MpEvent) {\n    this.type = type.toLowerCase()\n    this.mpEvent = event\n    this.bubbles = Boolean(opts && opts.bubbles)\n    this.cancelable = Boolean(opts && opts.cancelable)\n  }\n\n  public stopPropagation () {\n    this._stop = true\n  }\n\n  public stopImmediatePropagation () {\n    this._end = this._stop = true\n  }\n\n  public preventDefault () {\n    this.defaultPrevented = true\n  }\n\n  get target () {\n    const cacheTarget = this.cacheTarget\n    if (!cacheTarget) {\n      const target = Object.create(this.mpEvent?.target || null)\n      const currentEle = env.document.getElementById(target.dataset?.sid || target.id || null)\n      // Note：优先判断冒泡场景alipay的targetDataset的sid, 不然冒泡场景target属性吐出不对，其余拿取当前绑定id\n      const element = env.document.getElementById(target.targetDataset?.sid || target.dataset?.sid || target.id || null)\n\n      target.dataset = {\n        ...(currentEle !== null ? currentEle.dataset : EMPTY_OBJ),\n        ...(element !== null ? element.dataset : EMPTY_OBJ)\n      }\n\n      for (const key in this.mpEvent?.detail) {\n        target[key] = this.mpEvent!.detail[key]\n      }\n\n      this.cacheTarget = target\n\n      return target\n    } else {\n      return cacheTarget\n    }\n  }\n\n  get currentTarget () {\n    const cacheCurrentTarget = this.cacheCurrentTarget\n    if (!cacheCurrentTarget) {\n      const doc = env.document\n\n      const currentTarget = Object.create(this.mpEvent?.currentTarget || null)\n\n      const element = doc.getElementById(currentTarget.dataset?.sid || currentTarget.id || null)\n      const targetElement = doc.getElementById(this.mpEvent?.target?.dataset?.sid as string || this.mpEvent?.target?.id as string || null)\n\n      if (element === null || (element && element === targetElement)) {\n        this.cacheCurrentTarget = this.target\n        return this.target\n      }\n\n      currentTarget.dataset = element.dataset\n\n      for (const key in this.mpEvent?.detail) {\n        currentTarget[key] = this.mpEvent!.detail[key]\n      }\n\n      this.cacheCurrentTarget = currentTarget\n\n      return currentTarget\n    } else {\n      return cacheCurrentTarget\n    }\n  }\n}\n\nexport function createEvent (event: MpEvent | string, node?: TaroElement) {\n  if (typeof event === 'string') {\n    // For Vue3 using document.createEvent\n    return new TaroEvent(event, { bubbles: true, cancelable: true })\n  }\n\n  const domEv = new TaroEvent(event.type, { bubbles: true, cancelable: true }, event)\n\n  for (const key in event) {\n    if (key === CURRENT_TARGET || key === TARGET || key === TYPE || key === TIME_STAMP) {\n      continue\n    } else {\n      domEv[key] = event[key]\n    }\n  }\n\n  if (domEv.type === CONFIRM && node?.nodeName === INPUT) {\n    // eslint-disable-next-line dot-notation\n    domEv[KEY_CODE] = 13\n  }\n\n  return domEv\n}\n\nconst eventsBatch = {}\n\nfunction getEventCBResult (event: MpEvent) {\n  const result = event[EVENT_CALLBACK_RESULT]\n  if (!isUndefined(result)) {\n    delete event[EVENT_CALLBACK_RESULT]\n  }\n  return result\n}\n\n// 小程序的事件代理回调函数\nexport function eventHandler (event: MpEvent) {\n  // Note: ohos 上事件没有设置 type、detail 类型 setter 方法，且部分事件（例如 load 等）缺失 target 导致事件错误\n  event.type === undefined && Object.defineProperty(event, 'type', {\n    value: (event as any)._type // ohos only\n  })\n  event.detail === undefined && Object.defineProperty(event, 'detail', {\n    value: (event as any)._detail || { ...event } // ohos only\n  })\n  event.currentTarget = event.currentTarget || event.target || { ...event }\n  hooks.call('modifyMpEventImpl', event)\n\n  const currentTarget = event.currentTarget\n  const id = currentTarget.dataset?.sid as string /** sid */ || currentTarget.id /** uid */ || event.detail?.id as string || ''\n\n  const node = env.document.getElementById(id)\n  if (node) {\n    const dispatch = () => {\n      const e = createEvent(event, node)\n\n      hooks.call('modifyTaroEvent', e, node)\n      hooks.call('dispatchTaroEvent', e, node)\n      hooks.call('dispatchTaroEventFinish', e, node)\n    }\n    if (hooks.isExist('batchedEventUpdates')) {\n      const type = event.type\n\n      if (\n        !hooks.call('isBubbleEvents', type) ||\n        !isParentBinded(node, type) ||\n        (type === TOUCHMOVE && !!node.props.catchMove)\n      ) {\n        // 最上层组件统一 batchUpdate\n        hooks.call('batchedEventUpdates', () => {\n          if (eventsBatch[type]) {\n            eventsBatch[type].forEach(fn => fn())\n            delete eventsBatch[type]\n          }\n          dispatch()\n        })\n        return getEventCBResult(event)\n      } else {\n        // 如果上层组件也有绑定同类型的组件，委托给上层组件调用事件回调\n        (eventsBatch[type] ||= []).push(dispatch)\n      }\n    } else {\n      dispatch()\n      return getEventCBResult(event)\n    }\n  }\n}\n", "import {\n  CHANGE,\n  INPUT,\n  TYPE,\n  VALUE\n} from '../constants'\nimport { TaroElement } from './element'\n\nimport type { TaroEvent } from './event'\n\nexport class FormElement extends TaroElement {\n  public get type () {\n    return this.props[TYPE] ?? ''\n  }\n\n  public set type (val: string) {\n    this.setAttribute(TYPE, val)\n  }\n\n  public get value () {\n    // eslint-disable-next-line dot-notation\n    const val = this.props[VALUE]\n    return val == null ? '' : val\n  }\n\n  public set value (val: string | boolean | number | any[]) {\n    this.setAttribute(VALUE, val)\n  }\n\n  public dispatchEvent (event: TaroEvent) {\n    if (event.mpEvent) {\n      const val = event.mpEvent.detail.value\n      if (event.type === CHANGE) {\n        this.props.value = val as string\n      } else if (event.type === INPUT) {\n        // Web 规范中表单组件的 value 应该跟着输入改变\n        // 只是改 this.props.value 的话不会进行 setData，因此这里修改 this.value。\n        // 只测试了 React、Vue3 input 组件的 onInput 事件，onChange 事件不确定有没有副作用，所以暂不修改。\n        this.value = val as string\n      }\n    }\n\n    return super.dispatchEvent(event)\n  }\n}\n", "import { ensure, hooks, Shortcuts } from '@tarojs/shared'\n\nimport { DOCUMENT_FRAGMENT } from '../constants'\nimport { MutationObserver, MutationRecordType } from '../dom-external/mutation-observer'\nimport env from '../env'\nimport { hydrate } from '../hydrate'\nimport { extend, incrementId, isComment } from '../utils'\nimport { eventSource } from './event-source'\nimport { TaroEventTarget } from './event-target'\nimport { NodeType } from './node_types'\n\nimport type { TFunc, UpdatePayload } from '../interface'\nimport type { TaroDocument } from './document'\nimport type { TaroElement } from './element'\nimport type { TaroRootElement } from './root'\n\ninterface RemoveChildOptions {\n  cleanRef?: boolean\n  doUpdate?: boolean\n}\n\nconst CHILDNODES = Shortcuts.Childnodes\nconst nodeId = incrementId()\n\nexport class TaroNode extends TaroEventTarget {\n  public uid: string\n  public sid: string\n  public nodeType: NodeType\n  public nodeName: string\n  public parentNode: TaroNode | null = null\n  public childNodes: TaroNode[] = []\n\n  public constructor () {\n    super()\n    this.uid = '_' + nodeId() // dom 节点 id，开发者可修改\n    this.sid = this.uid // dom 节点全局唯一 id，不可被修改\n    eventSource.set(this.sid, this)\n  }\n\n  private hydrate = (node: TaroNode) => () => hydrate(node as TaroElement)\n\n  private updateChildNodes (isClean?: boolean) {\n    const cleanChildNodes = () => []\n    const rerenderChildNodes = () => {\n      const childNodes = this.childNodes.filter(node => !isComment(node))\n      return childNodes.map(hydrate)\n    }\n\n    this.enqueueUpdate({\n      path: `${this._path}.${CHILDNODES}`,\n      value: isClean ? cleanChildNodes : rerenderChildNodes\n    })\n  }\n\n  private updateSingleChild (index: number) {\n    this.childNodes.forEach((child, childIndex) => {\n      if (isComment(child)) return\n\n      if (index && childIndex < index) return\n\n      this.enqueueUpdate({\n        path: child._path,\n        value: this.hydrate(child)\n      })\n    })\n  }\n\n  public get _root (): TaroRootElement | null {\n    return this.parentNode?._root || null\n  }\n\n  protected findIndex (refChild: TaroNode): number {\n    const index = this.childNodes.indexOf(refChild)\n\n    ensure(index !== -1, 'The node to be replaced is not a child of this node.')\n\n    return index\n  }\n\n  public get _path (): string {\n    const parentNode = this.parentNode\n\n    if (parentNode) {\n      // 计算路径时，先过滤掉 comment 节点\n      const list = parentNode.childNodes.filter(node => !isComment(node))\n      const indexOfNode = list.indexOf(this)\n      const index = hooks.call('getPathIndex', indexOfNode)\n\n      return `${parentNode._path}.${CHILDNODES}.${index}`\n    }\n\n    return ''\n  }\n\n  public get nextSibling (): TaroNode | null {\n    const parentNode = this.parentNode\n    return parentNode?.childNodes[parentNode.findIndex(this) + 1] || null\n  }\n\n  public get previousSibling (): TaroNode | null {\n    const parentNode = this.parentNode\n    return parentNode?.childNodes[parentNode.findIndex(this) - 1] || null\n  }\n\n  public get parentElement (): TaroElement | null {\n    const parentNode = this.parentNode\n    if (parentNode?.nodeType === NodeType.ELEMENT_NODE) {\n      return parentNode as TaroElement\n    }\n    return null\n  }\n\n  public get firstChild (): TaroNode | null {\n    return this.childNodes[0] || null\n  }\n\n  public get lastChild (): TaroNode | null {\n    const childNodes = this.childNodes\n    return childNodes[childNodes.length - 1] || null\n  }\n\n  /**\n   * @textContent 目前只能置空子元素\n   * @TODO 等待完整 innerHTML 实现\n   */\n  // eslint-disable-next-line accessor-pairs\n  public set textContent (text: string) {\n    const removedNodes = this.childNodes.slice()\n    const addedNodes: TaroNode[] = []\n\n    // Handle old children' data structure & ref\n    while (this.firstChild) {\n      this.removeChild(this.firstChild, { doUpdate: false })\n    }\n\n    if (text === '') {\n      this.updateChildNodes(true)\n    } else {\n      const newText = env.document.createTextNode(text)\n      addedNodes.push(newText)\n      this.appendChild(newText)\n      this.updateChildNodes()\n    }\n\n    // @Todo: appendChild 会多触发一次\n    MutationObserver.record({\n      type: MutationRecordType.CHILD_LIST,\n      target: this,\n      removedNodes,\n      addedNodes\n    })\n  }\n\n  /**\n   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/insertBefore\n   * @scenario\n   * [A,B,C]\n   *   1. insert D before C, D has no parent\n   *   2. insert D before C, D has the same parent of C\n   *   3. insert D before C, D has the different parent of C\n   */\n  public insertBefore<T extends TaroNode> (newChild: T, refChild?: TaroNode | null, isReplace?: boolean): T {\n    if (newChild.nodeName === DOCUMENT_FRAGMENT) {\n      newChild.childNodes.reduceRight((previousValue, currentValue) => {\n        this.insertBefore(currentValue, previousValue)\n        return currentValue\n      }, refChild)\n      return newChild\n    }\n\n    // Parent release newChild\n    //   - cleanRef: false (No need to clean eventSource, because newChild is about to be inserted)\n    //   - update: true (Need to update parent.childNodes, because parent.childNodes is reordered)\n    newChild.remove({ cleanRef: false })\n\n    let index = 0\n    // Data structure\n    newChild.parentNode = this\n    if (refChild) {\n      // insertBefore & replaceChild\n      index = this.findIndex(refChild)\n      this.childNodes.splice(index, 0, newChild)\n    } else {\n      // appendChild\n      this.childNodes.push(newChild)\n    }\n\n    const childNodesLength = this.childNodes.length\n    // Serialization\n    if (this._root) {\n      if (!refChild) {\n        // appendChild\n        const isOnlyChild = childNodesLength === 1\n        if (isOnlyChild) {\n          this.updateChildNodes()\n        } else {\n          this.enqueueUpdate({\n            path: newChild._path,\n            value: this.hydrate(newChild)\n          })\n        }\n      } else if (isReplace) {\n        // replaceChild\n        this.enqueueUpdate({\n          path: newChild._path,\n          value: this.hydrate(newChild)\n        })\n      } else {\n        // insertBefore 有两种更新模式\n        // 比方说有 A B C 三个节点，现在要在 C 前插入 D\n        // 1. 插入 D，然后更新整个父节点的 childNodes 数组\n        // setData({ cn: [A, B, D, C] })\n        // 2. 插入 D，然后更新 D 以及 D 之后每个节点的数据\n        // setData ({\n        //   cn.[2]: D,\n        //   cn.[3]: C,\n        // })\n        // 由于微信解析 ’cn.[2]‘ 这些路径的时候也需要消耗时间，\n        // 所以根据 insertBefore 插入的位置来做不同的处理\n        const mark = childNodesLength * 2 / 3\n        if (mark > index) {\n          // 如果 insertBefore 的位置在 childNodes 的 2/3 前，则为了避免解析路径消耗过多的时间，采用第一种方式\n          this.updateChildNodes()\n        } else {\n          // 如果 insertBefore 的位置在 childNodes 的 2/3 之后，则采用第二种方式，避免 childNodes 的全量更新\n          this.updateSingleChild(index)\n        }\n      }\n    }\n\n    MutationObserver.record({\n      type: MutationRecordType.CHILD_LIST,\n      target: this,\n      addedNodes: [newChild],\n      removedNodes: isReplace\n        ? [refChild as TaroNode] /** replaceChild */\n        : [],\n      nextSibling: isReplace\n        ? (refChild as TaroNode).nextSibling /** replaceChild */\n        : (refChild || null), /** insertBefore & appendChild */\n      previousSibling: newChild.previousSibling\n    })\n\n    return newChild\n  }\n\n  /**\n   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/appendChild\n   * @scenario\n   * [A,B,C]\n   *   1. append C, C has no parent\n   *   2. append C, C has the same parent of B\n   *   3. append C, C has the different parent of B\n   */\n  public appendChild (newChild: TaroNode) {\n    return this.insertBefore(newChild)\n  }\n\n  /**\n   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/replaceChild\n   * @scenario\n   * [A,B,C]\n   *   1. replace B with C, C has no parent\n   *   2. replace B with C, C has no parent, C has the same parent of B\n   *   3. replace B with C, C has no parent, C has the different parent of B\n   */\n  public replaceChild (newChild: TaroNode, oldChild: TaroNode) {\n    if (oldChild.parentNode !== this) return\n\n    // Insert the newChild\n    this.insertBefore(newChild, oldChild, true)\n\n    // Destroy the oldChild\n    //   - cleanRef: true (Need to clean eventSource, because the oldChild was detached from the DOM tree)\n    //   - update: false (No need to update parent.childNodes, because replace will not cause the parent.childNodes being reordered)\n    oldChild.remove({ doUpdate: false })\n\n    return oldChild\n  }\n\n  /**\n   * @doc https://developer.mozilla.org/zh-CN/docs/Web/API/Node/removeChild\n   * @scenario\n   * [A,B,C]\n   *   1. remove A or B\n   *   2. remove C\n   */\n  public removeChild<T extends TaroNode> (child: T, options: RemoveChildOptions = {}): T {\n    const { cleanRef, doUpdate } = options\n\n    if (cleanRef !== false && doUpdate !== false) {\n      // appendChild/replaceChild/insertBefore 不应该触发\n      // @Todo: 但其实如果 newChild 的父节点是另一颗子树的节点，应该是要触发的\n      MutationObserver.record({\n        type: MutationRecordType.CHILD_LIST,\n        target: this,\n        removedNodes: [child],\n        nextSibling: child.nextSibling,\n        previousSibling: child.previousSibling\n      })\n    }\n\n    // Data Structure\n    const index = this.findIndex(child)\n    this.childNodes.splice(index, 1)\n    child.parentNode = null\n\n    // Set eventSource\n    if (cleanRef !== false) {\n      eventSource.removeNodeTree(child)\n    }\n\n    // Serialization\n    if (this._root && doUpdate !== false) {\n      this.updateChildNodes()\n    }\n\n    return child\n  }\n\n  public remove (options?: RemoveChildOptions) {\n    this.parentNode?.removeChild(this, options)\n  }\n\n  public hasChildNodes () {\n    return this.childNodes.length > 0\n  }\n\n  public enqueueUpdate (payload: UpdatePayload) {\n    this._root?.enqueueUpdate(payload)\n  }\n\n  public get ownerDocument (): TaroDocument {\n    return env.document\n  }\n\n  static extend (methodName: string, options: TFunc | Record<string, any>) {\n    extend(TaroNode, methodName, options)\n  }\n}\n", "import { hooks, isArray, isFunction, isUndefined, Shortcuts } from '@tarojs/shared'\n\nimport {\n  CUSTOM_WRAPPER,\n  PAGE_INIT,\n  ROOT_STR,\n  SET_DATA\n} from '../constants'\nimport { options } from '../options'\nimport { perf } from '../perf'\nimport { customWrapperCache, isComment } from '../utils'\nimport { TaroElement } from './element'\n\nimport type { HydratedData, MpInstance, TFunc, UpdatePayload, UpdatePayloadValue } from '../interface'\n\nfunction findCustomWrapper (root: TaroRootElement, dataPathArr: string[]) {\n  // ['root', 'cn', '[0]'] remove 'root' => ['cn', '[0]']\n  const list = dataPathArr.slice(1)\n  let currentData: any = root\n  let customWrapper: Record<string, any> | undefined\n  let splitedPath = ''\n\n  list.some((item, i) => {\n    const key = item\n      // '[0]' => '0'\n      .replace(/^\\[(.+)\\]$/, '$1')\n      // 'cn' => 'childNodes'\n      .replace(/\\bcn\\b/g, 'childNodes')\n\n    currentData = currentData[key]\n\n    if (isArray(currentData)) {\n      currentData = currentData.filter(el => !isComment(el))\n    }\n\n    if (isUndefined(currentData)) return true\n\n    if (currentData.nodeName === CUSTOM_WRAPPER) {\n      const res = customWrapperCache.get(currentData.sid)\n      if (res) {\n        customWrapper = res\n        splitedPath = dataPathArr.slice(i + 2).join('.')\n      }\n    }\n  })\n\n  if (customWrapper) {\n    return {\n      customWrapper,\n      splitedPath\n    }\n  }\n}\n\nexport class TaroRootElement extends TaroElement {\n  private updatePayloads: UpdatePayload[] = []\n\n  private updateCallbacks: TFunc[] = []\n\n  public pendingUpdate = false\n\n  public ctx: null | MpInstance = null\n\n  public constructor () {\n    super()\n    this.nodeName = ROOT_STR\n    this.tagName = ROOT_STR.toUpperCase()\n  }\n\n  public get _path (): string {\n    return ROOT_STR\n  }\n\n  public get _root (): TaroRootElement {\n    return this\n  }\n\n  public scheduleTask(fn: TFunc) {\n    // 这里若使用微任务可略微提前setData的执行时机，但在部分场景下可能会出现连续setData两次，造成更大的性能问题\n    setTimeout(fn)\n  }\n\n  public enqueueUpdate (payload: UpdatePayload): void {\n    this.updatePayloads.push(payload)\n\n    if (!this.pendingUpdate && this.ctx) {\n      this.performUpdate()\n    }\n  }\n\n  public performUpdate (initRender = false, prerender?: TFunc) {\n    this.pendingUpdate = true\n\n    const ctx = hooks.call('proxyToRaw', this.ctx)!\n\n    this.scheduleTask(() => {\n      const setDataMark = `${SET_DATA} 开始时间戳 ${Date.now()}`\n      perf.start(setDataMark)\n      const data: Record<string, UpdatePayloadValue | ReturnType<HydratedData>> = Object.create(null)\n      const resetPaths = new Set<string>(\n        initRender\n          ? ['root.cn.[0]', 'root.cn[0]']\n          : []\n      )\n\n      while (this.updatePayloads.length > 0) {\n        const { path, value } = this.updatePayloads.shift()!\n        if (path.endsWith(Shortcuts.Childnodes)) {\n          resetPaths.add(path)\n        }\n        data[path] = value\n      }\n\n      for (const path in data) {\n        resetPaths.forEach(p => {\n          // 已经重置了数组，就不需要分别再设置了\n          if (path.includes(p) && path !== p) {\n            delete data[path]\n          }\n        })\n\n        const value = data[path]\n        if (isFunction(value)) {\n          data[path] = value()\n        }\n      }\n\n      // 预渲染\n      if (isFunction(prerender)) return prerender(data)\n\n      // 正常渲染\n      this.pendingUpdate = false\n      let normalUpdate = {}\n      const customWrapperMap: Map<Record<any, any>, Record<string, any>> = new Map()\n\n      if (initRender) {\n        // 初次渲染，使用页面级别的 setData\n        normalUpdate = data\n      } else {\n        // 更新渲染，区分 CustomWrapper 与页面级别的 setData\n        for (const p in data) {\n          const dataPathArr = p.split('.')\n          const found = findCustomWrapper(this, dataPathArr)\n          if (found) {\n            // 此项数据使用 CustomWrapper 去更新\n            const { customWrapper, splitedPath } = found\n            // 合并同一个 customWrapper 的相关更新到一次 setData 中\n            customWrapperMap.set(customWrapper, {\n              ...(customWrapperMap.get(customWrapper) || {}),\n              [`i.${splitedPath}`]: data[p]\n            })\n          } else {\n            // 此项数据使用页面去更新\n            normalUpdate[p] = data[p]\n          }\n        }\n      }\n\n      const customWrapperCount = customWrapperMap.size\n      const isNeedNormalUpdate = Object.keys(normalUpdate).length > 0\n      const updateArrLen = customWrapperCount + (isNeedNormalUpdate ? 1 : 0)\n      let executeTime = 0\n\n      const cb = () => {\n        if (++executeTime === updateArrLen) {\n          perf.stop(setDataMark)\n          this.flushUpdateCallback()\n          initRender && perf.stop(PAGE_INIT)\n        }\n      }\n\n      // custom-wrapper setData\n      if (customWrapperCount) {\n        customWrapperMap.forEach((data, ctx) => {\n          if (process.env.NODE_ENV !== 'production' && options.debug) {\n            // eslint-disable-next-line no-console\n            console.log('custom wrapper setData: ', data)\n          }\n          ctx.setData(data, cb)\n        })\n      }\n\n      // page setData\n      if (isNeedNormalUpdate) {\n        if (process.env.NODE_ENV !== 'production' && options.debug) {\n          // eslint-disable-next-line no-console\n          console.log('page setData:', normalUpdate)\n        }\n        ctx.setData(normalUpdate, cb)\n      }\n    })\n  }\n\n  public enqueueUpdateCallback (cb: TFunc, ctx?: Record<string, any>) {\n    this.updateCallbacks.push(() => {\n      ctx ? cb.call(ctx) : cb()\n    })\n  }\n\n  public flushUpdateCallback () {\n    const updateCallbacks = this.updateCallbacks\n    if (!updateCallbacks.length) return\n\n    const copies = updateCallbacks.slice(0)\n    this.updateCallbacks.length = 0\n    for (let i = 0; i < copies.length; i++) {\n      copies[i]()\n    }\n  }\n}\n", "import { hooks, isArray, isNull, isString, isUndefined, Shortcuts, toCamelCase, toDashed, warn } from '@tarojs/shared'\n\nimport { PROPERTY_THRESHOLD } from '../constants'\nimport { MutationObserver, MutationRecordType } from '../dom-external/mutation-observer'\nimport { TaroElement } from './element'\nimport { styleProperties } from './style_properties'\n\nfunction recordCss (obj: Style) {\n  MutationObserver.record({\n    type: MutationRecordType.ATTRIBUTES,\n    target: obj._element,\n    attributeName: 'style',\n    oldValue: obj.cssText\n  })\n}\n\nfunction enqueueUpdate (obj: Style) {\n  const element = obj._element\n  if (element._root) {\n    element.enqueueUpdate({\n      path: `${element._path}.${Shortcuts.Style}`,\n      value: obj.cssText\n    })\n  }\n}\n\nfunction setStyle (this: Style, newVal: string, styleKey: string) {\n  process.env.NODE_ENV !== 'production' && warn(\n    isString(newVal) && newVal.length > PROPERTY_THRESHOLD,\n    `Style 属性 ${styleKey} 的值数据量过大，可能会影响渲染性能，考虑使用 CSS 类或其它方案替代。`\n  )\n\n  const old = this[styleKey]\n\n  if (old === newVal) return\n\n  !this._pending && recordCss(this)\n\n  if (isNull(newVal) || isUndefined(newVal) || newVal === '') {\n    this._usedStyleProp.delete(styleKey)\n    delete this._value[styleKey]\n  } else {\n    this._usedStyleProp.add(styleKey)\n    this._value[styleKey] = newVal\n  }\n\n  !this._pending && enqueueUpdate(this)\n}\n\nfunction initStyle (ctor: typeof Style, styleProperties: string[]) {\n  const properties = {}\n\n  for (let i = 0; i < styleProperties.length; i++) {\n    const styleKey = styleProperties[i]\n\n    if (ctor[styleKey]) return\n\n    properties[styleKey] = {\n      get (this: Style) {\n        const val = this._value[styleKey]\n        return isNull(val) || isUndefined(val) ? '' : val\n      },\n      set (this: Style, newVal: string) {\n        setStyle.call(this, newVal, styleKey)\n      }\n    }\n  }\n\n  Object.defineProperties(ctor.prototype, properties)\n}\n\nfunction isCssVariable (propertyName) {\n  return /^--/.test(propertyName)\n}\n\nexport class Style {\n  public _pending: boolean\n\n  public _usedStyleProp: Set<string>\n\n  public _value: Partial<CSSStyleDeclaration>\n\n  public _element: TaroElement\n\n  public constructor (element: TaroElement) {\n    this._element = element\n    this._usedStyleProp = new Set()\n    this._value = {}\n  }\n\n  private setCssVariables (styleKey: string) {\n    this.hasOwnProperty(styleKey) || Object.defineProperty(this, styleKey, {\n      enumerable: true,\n      configurable: true,\n      get: () => {\n        return this._value[styleKey] || ''\n      },\n      set: (newVal: string) => {\n        setStyle.call(this, newVal, styleKey)\n      }\n    })\n  }\n\n  public get cssText () {\n    if (!this._usedStyleProp.size) return ''\n\n    const texts: string[] = []\n    this._usedStyleProp.forEach(key => {\n      const val = this[key]\n      if (isNull(val) || isUndefined(val)) return\n      let styleName = isCssVariable(key) ? key : toDashed(key)\n      if (styleName.indexOf('webkit') === 0 || styleName.indexOf('Webkit') === 0) {\n        styleName = `-${styleName}`\n      }\n      texts.push(`${styleName}: ${val};`)\n    })\n    return texts.join(' ')\n  }\n\n  public set cssText (str: string) {\n    this._pending = true\n    recordCss(this)\n\n    this._usedStyleProp.forEach(prop => {\n      this.removeProperty(prop)\n    })\n\n    if (str === '' || isUndefined(str) || isNull(str)) {\n      this._pending = false\n      enqueueUpdate(this)\n      return\n    }\n\n    const rules = str.split(';')\n\n    for (let i = 0; i < rules.length; i++) {\n      const rule = rules[i].trim()\n      if (rule === '') {\n        continue\n      }\n\n      // 可能存在 'background: url(http:x/y/z)' 的情况\n      const [propName, ...valList] = rule.split(':')\n      const val = valList.join(':')\n\n      if (isUndefined(val)) {\n        continue\n      }\n      this.setProperty(propName.trim(), val.trim())\n    }\n\n    this._pending = false\n    enqueueUpdate(this)\n  }\n\n  public setProperty (propertyName: string, value?: string | null) {\n    if (propertyName[0] === '-') {\n      // 支持 webkit 属性或 css 变量\n      this.setCssVariables(propertyName)\n    } else {\n      propertyName = toCamelCase(propertyName)\n    }\n\n    if (isNull(value) || isUndefined(value)) {\n      this.removeProperty(propertyName)\n    } else {\n      this[propertyName] = value\n    }\n  }\n\n  public removeProperty (propertyName: string): string {\n    propertyName = toCamelCase(propertyName)\n    if (!this._usedStyleProp.has(propertyName)) {\n      return ''\n    }\n\n    const value = this[propertyName]\n    this[propertyName] = undefined\n    return value\n  }\n\n  public getPropertyValue (propertyName: string) {\n    propertyName = toCamelCase(propertyName)\n    const value = this[propertyName]\n    if (!value) {\n      return ''\n    }\n\n    return value\n  }\n}\n\ninitStyle(Style, styleProperties)\n\nhooks.tap('injectNewStyleProperties', (newStyleProperties: string[]) => {\n  if (isArray(newStyleProperties)) {\n    initStyle(Style, newStyleProperties)\n  } else {\n    if (typeof newStyleProperties !== 'string') return\n\n    initStyle(Style, [newStyleProperties])\n  }\n})\n", "/*\n *\n * https://www.w3.org/Style/CSS/all-properties.en.html\n */\n\nconst WEBKIT = 'webkit'\n\nconst styleProperties = [\n  'all',\n  'appearance',\n  'blockOverflow',\n  'blockSize',\n  'bottom',\n  'clear',\n  'contain',\n  'content',\n  'continue',\n  'cursor',\n  'direction',\n  'display',\n  'filter',\n  'float',\n  'gap',\n  'height',\n  'inset',\n  'isolation',\n  'left',\n  'letterSpacing',\n  'lightingColor',\n  'markerSide',\n  'mixBlendMode',\n  'opacity',\n  'order',\n  'position',\n  'quotes',\n  'resize',\n  'right',\n  'rowGap',\n  'tabSize',\n  'tableLayout',\n  'top',\n  'userSelect',\n  'verticalAlign',\n  'visibility',\n  'voiceFamily',\n  'volume',\n  'whiteSpace',\n  'widows',\n  'width',\n  'zIndex',\n  'pointerEvents',\n  'aspectRatio'\n\n  /** 非常用 style */\n  // 'azimuth',\n  // 'backfaceVisibility',\n  // 'baselineShift',\n  // 'captionSide',\n  // 'chains',\n  // 'dominantBaseline',\n  // 'elevation',\n  // 'emptyCells',\n  // 'forcedColorAdjust',\n  // 'glyphOrientationVertical',\n  // 'hangingPunctuation',\n  // 'hyphenateCharacter',\n  // 'hyphens',\n  // 'imageOrientation',\n  // 'imageResolution',\n  // 'orphans',\n  // 'playDuring',\n  // 'pointerEvents',\n  // 'regionFragment',\n  // 'richness',\n  // 'running',\n  // 'scrollBehavior',\n  // 'speechRate',\n  // 'stress',\n  // 'stringSet',\n  // 'unicodeBidi',\n  // 'willChange',\n  // 'writingMode',\n]\n\n// 减少文件体积\nfunction combine (prefix: string, list: string[], excludeSelf?: boolean) {\n  !excludeSelf && styleProperties.push(prefix)\n  list.forEach(item => {\n    styleProperties.push(prefix + item)\n    if (prefix === WEBKIT) {\n      styleProperties.push('Webkit' + item)\n    }\n  })\n}\n\nconst color = 'Color'\nconst style = 'Style'\nconst width = 'Width'\nconst image = 'Image'\nconst size = 'Size'\nconst color_style_width = [color, style, width]\nconst fitlength_fitwidth_image = ['FitLength', 'FitWidth', image]\nconst fitlength_fitwidth_image_radius = [...fitlength_fitwidth_image, 'Radius']\nconst color_style_width_fitlength_fitwidth_image = [...color_style_width, ...fitlength_fitwidth_image]\nconst endRadius_startRadius = ['EndRadius', 'StartRadius']\nconst bottom_left_right_top = ['Bottom', 'Left', 'Right', 'Top']\nconst end_start = ['End', 'Start']\nconst content_items_self = ['Content', 'Items', 'Self']\nconst blockSize_height_inlineSize_width = ['BlockSize', 'Height', 'InlineSize', width]\nconst after_before = ['After', 'Before']\n\ncombine('borderBlock', color_style_width)\ncombine('borderBlockEnd', color_style_width)\ncombine('borderBlockStart', color_style_width)\ncombine('outline', [...color_style_width, 'Offset'])\ncombine('border', [...color_style_width, 'Boundary', 'Break', 'Collapse', 'Radius', 'Spacing'])\ncombine('borderFit', ['Length', width])\ncombine('borderInline', color_style_width)\ncombine('borderInlineEnd', color_style_width)\ncombine('borderInlineStart', color_style_width)\ncombine('borderLeft', color_style_width_fitlength_fitwidth_image)\ncombine('borderRight', color_style_width_fitlength_fitwidth_image)\ncombine('borderTop', color_style_width_fitlength_fitwidth_image)\ncombine('borderBottom', color_style_width_fitlength_fitwidth_image)\ncombine('textDecoration', [color, style, 'Line'])\ncombine('textEmphasis', [color, style, 'Position'])\ncombine('scrollMargin', bottom_left_right_top)\ncombine('scrollPadding', bottom_left_right_top)\ncombine('padding', bottom_left_right_top)\ncombine('margin', [...bottom_left_right_top, 'Trim'])\ncombine('scrollMarginBlock', end_start)\ncombine('scrollMarginInline', end_start)\ncombine('scrollPaddingBlock', end_start)\ncombine('scrollPaddingInline', end_start)\ncombine('gridColumn', end_start)\ncombine('gridRow', end_start)\ncombine('insetBlock', end_start)\ncombine('insetInline', end_start)\ncombine('marginBlock', end_start)\ncombine('marginInline', end_start)\ncombine('paddingBlock', end_start)\ncombine('paddingInline', end_start)\ncombine('pause', after_before)\ncombine('cue', after_before)\ncombine('mask', ['Clip', 'Composite', image, 'Mode', 'Origin', 'Position', 'Repeat', size, 'Type'])\ncombine('borderImage', ['Outset', 'Repeat', 'Slice', 'Source', 'Transform', width])\ncombine('maskBorder', ['Mode', 'Outset', 'Repeat', 'Slice', 'Source', width])\ncombine('font', ['Family', 'FeatureSettings', 'Kerning', 'LanguageOverride', 'MaxSize', 'MinSize', 'OpticalSizing', 'Palette', size, 'SizeAdjust', 'Stretch', style, 'Weight', 'VariationSettings'])\ncombine('transform', ['Box', 'Origin', style])\ncombine('background', [color, image, 'Attachment', 'BlendMode', 'Clip', 'Origin', 'Position', 'Repeat', size])\ncombine('listStyle', [image, 'Position', 'Type'])\ncombine('scrollSnap', ['Align', 'Stop', 'Type'])\ncombine('grid', ['Area', 'AutoColumns', 'AutoFlow', 'AutoRows'])\ncombine('gridTemplate', ['Areas', 'Columns', 'Rows'])\ncombine('overflow', ['Block', 'Inline', 'Wrap', 'X', 'Y'])\ncombine('transition', ['Delay', 'Duration', 'Property', 'TimingFunction'])\ncombine('color', ['Adjust', 'InterpolationFilters', 'Scheme'])\ncombine('textAlign', ['All', 'Last'])\ncombine('page', ['BreakAfter', 'BreakBefore', 'BreakInside'])\ncombine('animation', ['Delay', 'Direction', 'Duration', 'FillMode', 'IterationCount', 'Name', 'PlayState', 'TimingFunction'])\ncombine('flex', ['Basis', 'Direction', 'Flow', 'Grow', 'Shrink', 'Wrap'])\ncombine('offset', [...after_before, ...end_start, 'Anchor', 'Distance', 'Path', 'Position', 'Rotate'])\ncombine('perspective', ['Origin'])\ncombine('clip', ['Path', 'Rule'])\ncombine('flow', ['From', 'Into'])\n\ncombine('align', ['Content', 'Items', 'Self'], true)\ncombine('alignment', ['Adjust', 'Baseline'], true)\ncombine('borderStart', endRadius_startRadius, true)\ncombine('borderEnd', endRadius_startRadius, true)\ncombine('borderCorner', ['Fit', image, 'ImageTransform'], true)\ncombine('borderTopLeft', fitlength_fitwidth_image_radius, true)\ncombine('borderTopRight', fitlength_fitwidth_image_radius, true)\ncombine('borderBottomLeft', fitlength_fitwidth_image_radius, true)\ncombine('borderBottomRight', fitlength_fitwidth_image_radius, true)\ncombine('column', ['s', 'Count', 'Fill', 'Gap', 'Rule', 'RuleColor', 'RuleStyle', 'RuleWidth', 'Span', width], true)\ncombine('break', [...after_before, 'Inside'], true)\ncombine('wrap', [...after_before, 'Flow', 'Inside', 'Through'], true)\ncombine('justify', content_items_self, true)\ncombine('place', content_items_self, true)\ncombine('max', [...blockSize_height_inlineSize_width, 'Lines'], true)\ncombine('min', blockSize_height_inlineSize_width, true)\ncombine('line', ['Break', 'Clamp', 'Grid', 'Height', 'Padding', 'Snap'], true)\ncombine('inline', ['BoxAlign', size, 'Sizing'], true)\ncombine('text', ['CombineUpright', 'GroupAlign', 'Height', 'Indent', 'Justify', 'Orientation', 'Overflow', 'Shadow', 'SpaceCollapse', 'SpaceTrim', 'Spacing', 'Transform', 'UnderlinePosition', 'Wrap'], true)\ncombine('shape', ['ImageThreshold', 'Inside', 'Margin', 'Outside'], true)\ncombine('word', ['Break', 'Spacing', 'Wrap'], true)\ncombine('object', ['Fit', 'Position'], true)\ncombine('box', ['DecorationBreak', 'Shadow', 'Sizing', 'Snap'], true)\n\ncombine(WEBKIT, ['LineClamp', 'BoxOrient', 'TextFillColor', 'TextStroke', 'TextStrokeColor', 'TextStrokeWidth'], true)\n\n/** 非常用 style */\n// combine('caret', [color, 'Shape'])\n// combine('counter', ['Increment', 'Reset', 'Set'], true)\n// combine('dropInitial', ['AfterAdjust', 'AfterAlign', 'BeforeAdjust', 'BeforeAlign', size, 'Value'], true)\n// combine('flood', [color, 'Opacity'], true)\n// combine('footnote', ['Display', 'Policy'], true)\n// combine('hyphenateLimit', ['Chars', 'Last', 'Lines', 'Zone'], true)\n// combine('initialLetters', ['Align', 'Wrap'])\n// combine('ruby', ['Align', 'Merge', 'Position'], true)\n// combine('lineStacking', ['Ruby', 'Shift', 'Strategy'])\n// combine('bookmark', ['Label', 'Level', 'State'], true)\n// combine('speak', ['Header', 'Numeral', 'Punctuation'])\n// combine('pitch', ['Range'])\n// combine('nav', ['Down', 'Left', 'Right', 'Up'], true)\n// combine('fontSynthesis', ['SmallCaps', style, 'Weight'])\n// combine('fontVariant', ['Alternates', 'Caps', 'EastAsian', 'Emoji', 'Ligatures', 'Numeric', 'Position'])\n\nexport { styleProperties }\n", "import { TaroElement } from './element'\n\n// for Vue3\nexport class SVGElement extends TaroElement {}\n", "import { Shortcuts } from '@tarojs/shared'\n\nimport { MutationObserver, MutationRecordType } from '../dom-external/mutation-observer'\nimport { TaroNode } from './node'\nimport { NodeType } from './node_types'\n\nexport class TaroText extends TaroNode {\n  public _value: string\n  public nodeType = NodeType.TEXT_NODE\n  public nodeName = '#text'\n\n  constructor (value) {\n    super()\n    this._value = value\n  }\n\n  public set textContent (text: string) {\n    MutationObserver.record({\n      target: this,\n      type: MutationRecordType.CHARACTER_DATA,\n      oldValue: this._value\n    })\n    this._value = text\n    this.enqueueUpdate({\n      path: `${this._path}.${Shortcuts.Text}`,\n      value: text\n    })\n  }\n\n  public get textContent (): string {\n    return this._value\n  }\n\n  public set nodeValue (text: string) {\n    this.textContent = text\n  }\n\n  public get nodeValue (): string {\n    return this._value\n  }\n\n  public set data (text: string) {\n    this.textContent = text\n  }\n\n  public get data (): string {\n    return this._value\n  }\n}\n", "import { TaroElement } from './element'\n\nexport class TransferElement extends TaroElement {\n  public isTransferElement = true\n\n  constructor (public dataName: string) {\n    super()\n  }\n\n  public get _path () {\n    return this.dataName\n  }\n}\n", "import { NodeType } from './node_types'\n\nimport type { TaroElement } from './element'\n\ntype Filter = (element: TaroElement) => boolean\n\nfunction returnTrue () {\n  return true\n}\n\nexport function treeToArray (root: TaroElement, predict?: Filter): TaroElement[] {\n  const array: TaroElement[] = []\n  const filter = predict ?? returnTrue\n\n  let object: TaroElement | null = root\n\n  while (object) {\n    if (object.nodeType === NodeType.ELEMENT_NODE && filter(object)) {\n      array.push(object)\n    }\n\n    object = following(object, root)\n  }\n\n  return array\n}\n\nfunction following (el: TaroElement, root: TaroElement): TaroElement | null {\n  const firstChild = el.firstChild\n  const isElmentTypeValid = el.nodeType === NodeType.ELEMENT_NODE || el.nodeType === NodeType.DOCUMENT_NODE\n\n  // 如果当前 el 不是 element 或 document 元素，则可以直接不递归他的子元素了\n  if (firstChild && isElmentTypeValid) {\n    return firstChild as TaroElement\n  }\n\n  let current: TaroElement | null = el\n\n  do {\n    if (current === root) {\n      return null\n    }\n\n    const nextSibling = current.nextSibling\n\n    if (nextSibling) {\n      return nextSibling as TaroElement\n    }\n    current = current.parentElement\n  } while (current)\n\n  return null\n}\n", "/* eslint-disable dot-notation */\nimport {\n  EMPTY_OBJ, ensure, EventChannel,\n  getComponents<PERSON>lias, hooks, internalComponents,\n  isArray, isFunction, isString, isUndefined, Shortcuts\n} from '@tarojs/shared'\n\nimport { raf } from '../bom/raf'\nimport { taroWindowProvider } from '../bom/window'\nimport { BEHAVIORS, CONTEXT_ACTIONS, CUSTOM_WRAPPER, EXTERNAL_CLASSES, ON_HIDE, ON_LOAD, ON_READY, ON_SHOW, OPTIONS, PAGE_INIT, VIEW } from '../constants'\nimport { Current } from '../current'\nimport { eventHandler } from '../dom/event'\nimport { eventCenter } from '../emitter/emitter'\nimport env from '../env'\nimport { perf } from '../perf'\nimport { customWrapperCache, incrementId } from '../utils'\nimport { addLeadingSlash } from '../utils/router'\n\nimport type { TaroRootElement } from '../dom/root'\nimport type { MpInstance, PageConfig, TFunc } from '../interface'\nimport type { Instance, PageInstance, PageProps } from './instance'\n\nconst instances = new Map<string, Instance>()\nconst pageId = incrementId()\n\nexport function injectPageInstance (inst: Instance<PageProps>, id: string) {\n  hooks.call('mergePageInstance', instances.get(id), inst)\n  instances.set(id, inst)\n}\n\nexport function getPageInstance (id: string): Instance | undefined {\n  return instances.get(id)\n}\n\nexport function removePageInstance (id: string) {\n  instances.delete(id)\n}\n\nexport function safeExecute (path: string, lifecycle: string, ...args: unknown[]) {\n  const instance = instances.get(path)\n\n  if (instance == null) {\n    return\n  }\n\n  const func = hooks.call('getLifecycle', instance, lifecycle as keyof PageInstance)\n\n  if (isArray(func)) {\n    const res = func.map(fn => fn.apply(instance, args))\n    return res[0]\n  }\n\n  if (!isFunction(func)) {\n    return\n  }\n\n  return func.apply(instance, args)\n}\n\nexport function stringify (obj?: Record<string, unknown>) {\n  if (obj == null) {\n    return ''\n  }\n  const path = Object.keys(obj).map((key) => {\n    return key + '=' + obj[key]\n  }).join('&')\n  return path === '' ? path : '?' + path\n}\n\nexport function getPath (id: string, options?: Record<string, unknown>): string {\n  const idx = id.indexOf('?')\n  if (process.env.TARO_PLATFORM === 'web') {\n    return `${idx > -1 ? id.substring(0, idx) : id}${stringify(options?.stamp ? { stamp: options.stamp } : {})}`\n  } else {\n    return `${idx > -1 ? id.substring(0, idx) : id}${stringify(options)}`\n  }\n}\n\nexport function getOnReadyEventKey (path: string) {\n  return path + '.' + ON_READY\n}\n\nexport function getOnShowEventKey (path: string) {\n  return path + '.' + ON_SHOW\n}\n\nexport function getOnHideEventKey (path: string) {\n  return path + '.' + ON_HIDE\n}\n\nexport function createPageConfig (component: any, pageName?: string, data?: Record<string, unknown>, pageConfig?: PageConfig) {\n  // 小程序 Page 构造器是一个傲娇小公主，不能把复杂的对象挂载到参数上\n  const id = pageName ?? `taro_page_${pageId()}`\n  const [\n    ONLOAD,\n    ONUNLOAD,\n    ONREADY,\n    ONSHOW,\n    ONHIDE,\n    LIFECYCLES,\n    SIDE_EFFECT_LIFECYCLES,\n  ] = hooks.call('getMiniLifecycleImpl')!.page\n  let pageElement: TaroRootElement | null = null\n  let unmounting = false\n  let prepareMountList: (() => void)[] = []\n\n  function setCurrentRouter (page: MpInstance) {\n    const router = process.env.TARO_PLATFORM === 'web' ? page.$taroPath : page.route || page.__route__ || page.$taroPath\n    Current.router = {\n      params: page.$taroParams!,\n      path: addLeadingSlash(router),\n      $taroPath: page.$taroPath,\n      onReady: getOnReadyEventKey(id),\n      onShow: getOnShowEventKey(id),\n      onHide: getOnHideEventKey(id)\n    }\n    if (!isUndefined(page.exitState)) {\n      Current.router.exitState = page.exitState\n    }\n  }\n  let loadResolver: (...args: unknown[]) => void\n  let hasLoaded: Promise<void>\n  const config: PageInstance = {\n    [ONLOAD] (this: MpInstance, options: Readonly<Record<string, unknown>> = {}, cb?: TFunc) {\n      hasLoaded = new Promise(resolve => { loadResolver = resolve })\n\n      perf.start(PAGE_INIT)\n\n      Current.page = this as any\n      this.config = pageConfig || {}\n\n      // this.$taroPath 是页面唯一标识\n      const uniqueOptions = Object.assign({}, options, { $taroTimestamp: Date.now() })\n      const $taroPath = this.$taroPath = getPath(id, uniqueOptions)\n      if (process.env.TARO_PLATFORM === 'web') {\n        config.path = $taroPath\n      }\n      // this.$taroParams 作为暴露给开发者的页面参数对象，可以被随意修改\n      if (this.$taroParams == null) {\n        this.$taroParams = uniqueOptions\n      }\n\n      setCurrentRouter(this)\n\n      // 初始化当前页面的上下文信息\n      if (process.env.TARO_PLATFORM !== 'web') {\n        taroWindowProvider.trigger(CONTEXT_ACTIONS.INIT, $taroPath)\n      }\n\n      const mount = () => {\n        Current.app!.mount!(component, $taroPath, () => {\n          pageElement = env.document.getElementById<TaroRootElement>($taroPath)\n\n          ensure(pageElement !== null, '没有找到页面实例。')\n          safeExecute($taroPath, ON_LOAD, this.$taroParams)\n          loadResolver()\n          if (process.env.TARO_PLATFORM !== 'web') {\n            pageElement.ctx = this\n            pageElement.performUpdate(true, cb)\n          } else {\n            isFunction(cb) && cb()\n          }\n        })\n      }\n      if (unmounting) {\n        prepareMountList.push(mount)\n      } else {\n        mount()\n      }\n    },\n    [ONUNLOAD] () {\n      const $taroPath = this.$taroPath\n      // 销毁当前页面的上下文信息\n      if (process.env.TARO_PLATFORM !== 'web') {\n        taroWindowProvider.trigger(CONTEXT_ACTIONS.DESTORY, $taroPath)\n      }\n      // 触发onUnload生命周期\n      safeExecute($taroPath, ONUNLOAD)\n      unmounting = true\n      Current.app!.unmount!($taroPath, () => {\n        unmounting = false\n        instances.delete($taroPath)\n        if (pageElement) {\n          pageElement.ctx = null\n          pageElement = null\n        }\n        if (prepareMountList.length) {\n          prepareMountList.forEach(fn => fn())\n          prepareMountList = []\n        }\n      })\n    },\n    [ONREADY] () {\n      hasLoaded.then(() => {\n        // 触发生命周期\n        safeExecute(this.$taroPath, ON_READY)\n        // 通过事件触发子组件的生命周期\n        raf(() => eventCenter.trigger(getOnReadyEventKey(id)))\n        this.onReady.called = true\n      })\n    },\n    [ONSHOW] (options = {}) {\n      hasLoaded.then(() => {\n        // 设置 Current 的 page 和 router\n        Current.page = this as any\n        setCurrentRouter(this)\n        // 恢复上下文信息\n        if (process.env.TARO_PLATFORM !== 'web') {\n          taroWindowProvider.trigger(CONTEXT_ACTIONS.RECOVER, this.$taroPath)\n        }\n        // 触发生命周期\n        safeExecute(this.$taroPath, ON_SHOW, options)\n        // 通过事件触发子组件的生命周期\n        raf(() => eventCenter.trigger(getOnShowEventKey(id)))\n      })\n    },\n    [ONHIDE] () {\n      // 缓存当前页面上下文信息\n      if (process.env.TARO_PLATFORM !== 'web') {\n        taroWindowProvider.trigger(CONTEXT_ACTIONS.RESTORE, this.$taroPath)\n      }\n      // 设置 Current 的 page 和 router\n      if (Current.page === this) {\n        Current.page = null\n        Current.router = null\n      }\n      // 触发生命周期\n      safeExecute(this.$taroPath, ON_HIDE)\n      // 通过事件触发子组件的生命周期\n      eventCenter.trigger(getOnHideEventKey(id))\n    }\n  }\n\n  if (process.env.TARO_PLATFORM === 'web') {\n    config.getOpenerEventChannel = () => {\n      return EventChannel.pageChannel\n    }\n  }\n\n  LIFECYCLES.forEach((lifecycle) => {\n    let isDefer = false\n    lifecycle = lifecycle.replace(/^defer:/, () => {\n      isDefer = true\n      return ''\n    })\n    config[lifecycle] = function () {\n      const exec = () => safeExecute(this.$taroPath, lifecycle, ...arguments)\n      if (isDefer) {\n        hasLoaded.then(exec)\n      } else {\n        return exec()\n      }\n    }\n  })\n\n  // onShareAppMessage 和 onShareTimeline 一样，会影响小程序右上方按钮的选项，因此不能默认注册。\n  SIDE_EFFECT_LIFECYCLES.forEach(lifecycle => {\n    if (component[lifecycle] ||\n      component.prototype?.[lifecycle] ||\n      component[lifecycle.replace(/^on/, 'enable')] ||\n      pageConfig?.[lifecycle.replace(/^on/, 'enable')]\n    ) {\n      config[lifecycle] = function (...args) {\n        const target = args[0]?.target\n        if (target?.id) {\n          const id = target.id\n          const element = env.document.getElementById(id)\n          if (element) {\n            target.dataset = element.dataset\n          }\n        }\n        return safeExecute(this.$taroPath, lifecycle, ...args)\n      }\n    }\n  })\n\n  config.eh = eventHandler\n\n  if (!isUndefined(data)) {\n    config.data = data\n  }\n\n  hooks.call('modifyPageObject', config)\n\n  return config\n}\n\nexport function createComponentConfig (component: React.ComponentClass, componentName?: string, data?: Record<string, unknown>) {\n  const id = componentName ?? `taro_component_${pageId()}`\n  let componentElement: TaroRootElement | null = null\n  const [ATTACHED, DETACHED] = hooks.call('getMiniLifecycleImpl')!.component\n\n  const config: any = {\n    [ATTACHED] () {\n      perf.start(PAGE_INIT)\n      this.pageIdCache = this.getPageId?.() || pageId()\n\n      const path = getPath(id, { id: this.pageIdCache })\n\n      Current.app!.mount!(component, path, () => {\n        componentElement = env.document.getElementById<TaroRootElement>(path)\n        ensure(componentElement !== null, '没有找到组件实例。')\n        this.$taroInstances = instances.get(path)\n        safeExecute(path, ON_LOAD)\n        if (process.env.TARO_PLATFORM !== 'web') {\n          componentElement.ctx = this\n          componentElement.performUpdate(true)\n        }\n      })\n    },\n    [DETACHED] () {\n      const path = getPath(id, { id: this.pageIdCache })\n\n      Current.app!.unmount!(path, () => {\n        instances.delete(path)\n        if (componentElement) {\n          componentElement.ctx = null\n        }\n      })\n    },\n    methods: {\n      eh: eventHandler\n    }\n  }\n\n  if (!isUndefined(data)) {\n    config.data = data\n  }\n\n  [OPTIONS, EXTERNAL_CLASSES, BEHAVIORS].forEach(key => {\n    config[key] = component[key] ?? EMPTY_OBJ\n  })\n\n  return config\n}\n\nexport function createRecursiveComponentConfig (componentName?: string) {\n  const isCustomWrapper = componentName === CUSTOM_WRAPPER\n  const [ATTACHED, DETACHED] = hooks.call('getMiniLifecycleImpl')!.component\n\n  const lifeCycles = isCustomWrapper\n    ? {\n      [ATTACHED] () {\n        const componentId = this.data.i?.sid || this.props.i?.sid\n        if (isString(componentId)) {\n          customWrapperCache.set(componentId, this)\n          const el = env.document.getElementById(componentId)\n          if (el) {\n            el.ctx = this\n          }\n        }\n      },\n      [DETACHED] () {\n        const componentId = this.data.i?.sid || this.props.i?.sid\n        if (isString(componentId)) {\n          customWrapperCache.delete(componentId)\n          const el = env.document.getElementById(componentId)\n          if (el) {\n            el.ctx = null\n          }\n        }\n      }\n    }\n    : EMPTY_OBJ\n\n  // 不同平台的个性化配置\n  const extraOptions: { [key: string]: any } = {}\n  if (process.env.TARO_ENV === 'jd') {\n    extraOptions.addGlobalClass = true\n  }\n\n  return hooks.call('modifyRecursiveComponentConfig',\n    {\n      properties: {\n        i: {\n          type: Object,\n          value: {\n            [Shortcuts.NodeName]: getComponentsAlias(internalComponents)[VIEW]._num\n          }\n        },\n        l: {\n          type: String,\n          value: ''\n        }\n      },\n      options: {\n        ...extraOptions,\n        virtualHost: !isCustomWrapper\n      },\n      methods: {\n        eh: eventHandler\n      },\n      ...lifeCycles\n    }, { isCustomWrapper })\n}\n", "import { Events, hooks } from '@tarojs/shared'\n\nconst eventCenter = hooks.call('getEventCenter', Events)!\n\nexport type EventsType = typeof Events\nexport { eventCenter, Events }\n", "import { hooks, Shortcuts, toCamelCase } from '@tarojs/shared'\n\nimport {\n  CATCH_VIEW,\n  CATCHMOVE,\n  CLASS,\n  CLICK_VIEW,\n  COMPILE_MODE,\n  ID,\n  PURE_VIEW,\n  STYLE,\n  VIEW\n} from './constants'\nimport { getComponents<PERSON>lias, isComment, isHasExtractProp, isText } from './utils'\n\nimport type { TaroElement } from './dom/element'\nimport type { TaroText } from './dom/text'\nimport type { MiniData, MiniElementData } from './interface'\n\nlet SPECIAL_NODES\nlet componentsAlias\n\n/**\n * React also has a fancy function's name for this: `hydrate()`.\n * You may have been heard `hydrate` as a SSR-related function,\n * actually, `hydrate` basicly do the `render()` thing, but ignore some properties,\n * it's a vnode traverser and modifier: that's exactly what <PERSON><PERSON>'s doing in here.\n */\nexport function hydrate (node: TaroElement | TaroText): MiniData {\n  // 初始化 componentsAlias\n  componentsAlias ||= getComponentsAlias()\n\n  // 初始化 SPECIAL_NODES\n  SPECIAL_NODES ||= hooks.call('getSpecialNodes')!\n\n  const nodeName = node.nodeName\n  let compileModeName = null\n\n  if (isText(node)) {\n    return {\n      sid: node.sid,\n      [Shortcuts.Text]: node.nodeValue,\n      [Shortcuts.NodeName]: componentsAlias[nodeName]?._num || '8'\n    }\n  }\n\n  const data: MiniElementData = {\n    [Shortcuts.NodeName]: nodeName,\n    sid: node.sid\n  }\n\n  if (node.uid !== node.sid) {\n    data.uid = node.uid\n  }\n\n  if (SPECIAL_NODES.indexOf(nodeName) > -1) {\n    if (!node.isAnyEventBinded()) {\n      data[Shortcuts.NodeName] = `static-${nodeName}`\n      if (nodeName === VIEW && !isHasExtractProp(node)) {\n        data[Shortcuts.NodeName] = PURE_VIEW\n      }\n    }\n\n    if (nodeName === VIEW && node.isOnlyClickBinded() && !isHasExtractProp(node)) {\n      data[Shortcuts.NodeName] = CLICK_VIEW\n    }\n  }\n\n  const { props } = node\n  for (const prop in props) {\n    const propInCamelCase = toCamelCase(prop)\n    if (\n      !prop.startsWith('data-') && // 在 node.dataset 的数据\n      prop !== CLASS &&\n      prop !== STYLE &&\n      prop !== ID &&\n      propInCamelCase !== CATCHMOVE &&\n      propInCamelCase !== COMPILE_MODE\n    ) {\n      data[propInCamelCase] = props[prop]\n    }\n    if (\n      process.env.TARO_ENV !== 'swan' &&\n      nodeName === VIEW &&\n      propInCamelCase === CATCHMOVE &&\n      props[prop] !== false\n    ) {\n      data[Shortcuts.NodeName] = CATCH_VIEW\n    }\n    if (propInCamelCase === COMPILE_MODE) {\n      compileModeName = props[prop]\n    }\n  }\n\n  // Children\n  data[Shortcuts.Childnodes] = node.childNodes.filter(node => !isComment(node)).map(hydrate)\n\n  if (node.className !== '') {\n    data[Shortcuts.Class] = node.className\n  }\n\n  const cssText = node.cssText\n  if (cssText !== '' && nodeName !== 'swiper-item') {\n    data[Shortcuts.Style] = cssText\n  }\n\n  hooks.call('modifyHydrateData', data, node)\n\n  const nn = data[Shortcuts.NodeName]\n  const componentAlias = componentsAlias[nn]\n  if (componentAlias) {\n    data[Shortcuts.NodeName] = componentAlias._num\n    for (const prop in data) {\n      if (prop in componentAlias) {\n        data[componentAlias[prop]] = data[prop]\n        delete data[prop]\n      }\n    }\n  }\n\n  if (compileModeName !== null) {\n    data[Shortcuts.NodeName] = compileModeName\n  }\n\n  const resData = hooks.call('transferHydrateData', data, node, componentAlias)\n\n  return resData || data\n}\n", "import { Current } from './current'\nimport { TaroRootElement } from './dom/root'\nimport env from './env'\n\nimport type { TFunc } from './interface'\n\nconst TIMEOUT = 100\n\nexport const nextTick = (cb: TFunc, ctx?: Record<string, any>) => {\n  const beginTime = Date.now()\n  const router = Current.router\n\n  const timerFunc = () => {\n    setTimeout(function () {\n      ctx ? cb.call(ctx) : cb()\n    }, 1)\n  }\n\n  if (router === null) return timerFunc()\n\n  const path = router.$taroPath\n\n  /**\n   * 三种情况\n   *   1. 调用 nextTick 时，pendingUpdate 已经从 true 变为 false（即已更新完成），那么需要光等 100ms\n   *   2. 调用 nextTick 时，pendingUpdate 为 true，那么刚好可以搭上便车\n   *   3. 调用 nextTick 时，pendingUpdate 还是 false，框架仍未启动更新逻辑，这时最多轮询 100ms，等待 pendingUpdate 变为 true。\n   */\n  function next () {\n    const pageElement: TaroRootElement | null = env.document.getElementById<TaroRootElement>(path)\n    if (pageElement?.pendingUpdate) {\n      if (process.env.TARO_PLATFORM === 'web') {\n        // eslint-disable-next-line dot-notation\n        pageElement.firstChild?.['componentOnReady']?.().then(() => {\n          timerFunc()\n        }) ?? timerFunc()\n      } else {\n        pageElement.enqueueUpdateCallback(cb, ctx)\n      }\n    } else if (Date.now() - beginTime > TIMEOUT) {\n      timerFunc()\n    } else {\n      setTimeout(() => next(), 20)\n    }\n  }\n\n  next()\n}\n", "import type { Options } from './interface'\n\nexport const options: Options = {\n  prerender: true,\n  debug: false\n}\n", "import { options } from './options'\nimport { debounce } from './utils'\n\nimport type { TFunc } from './interface'\n\nclass Performance {\n  private recorder = new Map<string, number>()\n\n  public start (id: string) {\n    if (!options.debug) {\n      return\n    }\n    this.recorder.set(id, Date.now())\n  }\n\n  public stop (id: string, now = Date.now()) {\n    if (!options.debug) {\n      return\n    }\n    const prev = this.recorder.get(id)!\n    if (!(prev >= 0)) return\n\n    this.recorder.delete(id)\n    const time = now - prev\n    // eslint-disable-next-line no-console\n    console.log(`${id} 时长： ${time}ms 开始时间：${this.#parseTime(prev)} 结束时间：${this.#parseTime(now)}`)\n  }\n\n  public delayStop (id: string, delay = 500) {\n    if (!options.debug) {\n      return\n    }\n\n    return debounce((now = Date.now(), cb?: TFunc) => {\n      this.stop(id, now)\n      cb?.()\n    }, delay)\n  }\n\n  #parseTime (time: number) {\n    const d = new Date(time)\n    return `${d.getHours()}:${d.getMinutes()}:${d.getSeconds()}.${`${d.getMilliseconds()}`.padStart(3, '0')}`\n  }\n}\n\nexport const perf = new Performance()\n", "import { isObject } from '@tarojs/shared'\n\nimport { handleArrayFindPolyfill, handleArrayIncludesPolyfill } from './array'\nimport { handleIntersectionObserverPolyfill } from './intersection-observer'\nimport { handleObjectAssignPolyfill, handleObjectDefinePropertyPolyfill, handleObjectEntriesPolyfill } from './object'\n\nfunction handlePolyfill () {\n  if (process.env.SUPPORT_TARO_POLYFILL === 'enabled' || process.env.SUPPORT_TARO_POLYFILL === 'Object' || process.env.SUPPORT_TARO_POLYFILL === 'Object.assign') {\n    handleObjectAssignPolyfill()\n  }\n  if (process.env.SUPPORT_TARO_POLYFILL === 'enabled' || process.env.SUPPORT_TARO_POLYFILL === 'Object' || process.env.SUPPORT_TARO_POLYFILL === 'Object.entries') {\n    handleObjectEntriesPolyfill()\n  }\n  if (process.env.SUPPORT_TARO_POLYFILL === 'enabled' || process.env.SUPPORT_TARO_POLYFILL === 'Object' || process.env.SUPPORT_TARO_POLYFILL === 'Object.defineProperty') {\n    handleObjectDefinePropertyPolyfill()\n  }\n  if (process.env.SUPPORT_TARO_POLYFILL === 'enabled' || process.env.SUPPORT_TARO_POLYFILL === 'Array' || process.env.SUPPORT_TARO_POLYFILL === 'Array.find') {\n    handleArrayFindPolyfill()\n  }\n  if (process.env.SUPPORT_TARO_POLYFILL === 'enabled' || process.env.SUPPORT_TARO_POLYFILL === 'Array' || process.env.SUPPORT_TARO_POLYFILL === 'Array.includes') {\n    handleArrayIncludesPolyfill()\n  }\n  // Exit early if we're not running in a browser.\n  if (process.env.TARO_PLATFORM === 'web' && isObject(window)) {\n    if (process.env.SUPPORT_TARO_POLYFILL === 'enabled' || process.env.SUPPORT_TARO_POLYFILL === 'IntersectionObserver') {\n      handleIntersectionObserverPolyfill()\n    }\n  }\n}\n\nif (process.env.SUPPORT_TARO_POLYFILL !== 'disabled' && process.env.TARO_PLATFORM !== 'web') {\n  handlePolyfill()\n}\n\nexport { handlePolyfill }\n\n", "/**\n * 一个小型缓存池，用于在切换页面时，存储一些上下文信息\n */\n\nexport class RuntimeCache<T> {\n  name: string\n  cache = new Map<string, T>()\n\n  constructor (name: string) {\n    this.name = name\n  }\n\n  has (identifier: string) {\n    return this.cache.has(identifier)\n  }\n\n  set (identifier: string, ctx: T) {\n    if (identifier && ctx) {\n      this.cache.set(identifier, ctx)\n    }\n  }\n\n  get (identifier: string): T | undefined {\n    if (this.has(identifier)) return this.cache.get(identifier)\n  }\n\n  delete (identifier: string) {\n    this.cache.delete(identifier)\n  }\n}\n", "import {\n  getComponents<PERSON>lias as _getCom<PERSON><PERSON><PERSON><PERSON>,\n  internalComponents,\n  isFunction,\n  Shortcuts\n} from '@tarojs/shared'\n\nimport {\n  CLASS,\n  COMMENT,\n  ID,\n  ROOT_STR,\n  STYLE,\n  UID\n} from '../constants'\nimport { NodeType } from '../dom/node_types'\n\nimport type { TaroElement } from '../dom/element'\nimport type { TaroNode } from '../dom/node'\nimport type { TaroText } from '../dom/text'\nimport type { TFunc } from '../interface'\n\nexport const incrementId = () => {\n  const chatCodes: number[] = []\n  // A-Z\n  for (let i = 65; i <= 90; i++) {\n    chatCodes.push(i)\n  }\n  // a-z\n  for (let i = 97; i <= 122; i++) {\n    chatCodes.push(i)\n  }\n  const chatCodesLen = chatCodes.length - 1\n  const list = [0, 0]\n  return () => {\n    const target = list.map(item => chatCodes[item])\n    const res = String.fromCharCode(...target)\n\n    let tailIdx = list.length - 1\n\n    list[tailIdx]++\n\n    while (list[tailIdx] > chatCodesLen) {\n      list[tailIdx] = 0\n      tailIdx = tailIdx - 1\n      if (tailIdx < 0) {\n        list.push(0)\n        break\n      }\n      list[tailIdx]++\n    }\n\n    return res\n  }\n}\n\nexport function isElement (node: TaroNode): node is TaroElement {\n  return node.nodeType === NodeType.ELEMENT_NODE\n}\n\nexport function isText (node: TaroNode): node is TaroText {\n  return node.nodeType === NodeType.TEXT_NODE\n}\n\nexport function isComment (node: TaroNode): boolean {\n  return node.nodeName === COMMENT\n}\n\nexport function isHasExtractProp (el: TaroElement): boolean {\n  const res = Object.keys(el.props).find(prop => {\n    return !(/^(class|style|id)$/.test(prop) || prop.startsWith('data-'))\n  })\n  return Boolean(res)\n}\n\n/**\n * 往上寻找组件树直到 root，寻找是否有祖先组件绑定了同类型的事件\n * @param node 当前组件\n * @param type 事件类型\n */\nexport function isParentBinded (node: TaroElement | null, type: string): boolean {\n  while ((node = node?.parentElement || null)) {\n    if (!node || node.nodeName === ROOT_STR || node.nodeName === 'root-portal') {\n      return false\n    } else if (node.__handlers[type]?.length) {\n      return true\n    }\n  }\n\n  return false\n}\n\nexport function shortcutAttr (key: string): string {\n  switch (key) {\n    case STYLE:\n      return Shortcuts.Style\n    case ID:\n      return UID\n    case CLASS:\n      return Shortcuts.Class\n    default:\n      return key\n  }\n}\n\nexport const customWrapperCache = new Map<string, Record<string, any>>()\n\ninterface Ctor {\n  new (...args: any[]): any\n}\n\nexport function extend (ctor: Ctor, methodName: string, options: TFunc | Record<string, any>) {\n  if (isFunction(options)) {\n    options = {\n      value: options\n    }\n  }\n  Object.defineProperty(ctor.prototype, methodName, {\n    configurable: true,\n    enumerable: true,\n    ...options\n  })\n}\n\nlet componentsAlias\nexport function getComponentsAlias () {\n  if (!componentsAlias) {\n    componentsAlias = _getComponentsAlias(internalComponents)\n  }\n  return componentsAlias\n}\n\nexport function convertNumber2PX (value: number) {\n  return value + 'px'\n}\n\nexport * from './lodash'\nexport * from './router'\n", "export function throttle (fn, threshold = 250, scope?) {\n  let lastTime = 0\n  let deferTimer: ReturnType<typeof setTimeout>\n  return function (...args) {\n    const context = scope || this\n    const now = Date.now()\n    if (now - lastTime > threshold) {\n      fn.apply(this, args)\n      lastTime = now\n    } else {\n      clearTimeout(deferTimer)\n      deferTimer = setTimeout(() => {\n        lastTime = now\n        fn.apply(context, args)\n      }, threshold)\n    }\n  }\n}\n\nexport function debounce (fn, ms = 250, scope?) {\n  let timer: ReturnType<typeof setTimeout>\n\n  return function (...args) {\n    const context = scope || this\n    clearTimeout(timer)\n    timer = setTimeout(function () {\n      fn.apply(context, args)\n    }, ms)\n  }\n}\n", "import { taroLocationProvider as location } from '../bom/window'\n\n// export const removeLeadingSlash = (str = '') => str.replace(/^\\.?\\//, '')\n// export const removeTrailingSearch = (str = '') => str.replace(/\\?[\\s\\S]*$/, '')\nexport const addLeadingSlash = (url = '') => (url.charAt(0) === '/' ? url : '/' + url)\n\nexport const hasBasename = (path = '', prefix = '') =>\n  new RegExp('^' + prefix + '(\\\\/|\\\\?|#|$)', 'i').test(path) || path === prefix\n\nexport const stripBasename = (path = '', prefix = '') =>\n  hasBasename(path, prefix) ? path.substring(prefix.length) : path\n\nexport const stripTrailing = (str = '') => str.replace(/[?#][\\s\\S]*$/, '')\n\nexport const stripSuffix = (path = '', suffix = '') =>\n  path.includes(suffix) ? path.substring(0, path.length - suffix.length) : path\n\nexport const getHomePage = (path = '', basename = '', customRoutes: Record<string, string | string[]> = {}, entryPagePath = '') => {\n  const routePath = addLeadingSlash(stripBasename(path, basename))\n  const alias = Object.entries(customRoutes).find(\n    ([key]) => key === routePath\n  )?.[1] || routePath\n  return entryPagePath || (typeof alias === 'string' ? alias : alias[0]) || basename\n}\n\nexport const getCurrentPage = (routerMode = 'hash', basename = '/') => {\n  const pagePath = routerMode === 'hash'\n    ? location.hash.slice(1).split('?')[0]\n    : location.pathname\n  return addLeadingSlash(stripBasename(pagePath, basename))\n}\n", "export enum PLATFORM_TYPE {\n  MINI = 'mini',\n  WEB = 'web',\n  RN = 'rn',\n  HARMONY = 'harmony',\n  QUICK = 'quickapp',\n}\n\nexport const COMPILE_MODE_IDENTIFIER_PREFIX = 'f'\n\nexport const COMPILE_MODE_SUB_RENDER_FN = 'subRenderFn'\n\nexport const PLATFORM_CONFIG_MAP = {\n  h5: {\n    type: PLATFORM_TYPE.WEB\n  },\n  harmony: {\n    type: PLATFORM_TYPE.HARMONY\n  },\n  mini: {\n    type: PLATFORM_TYPE.MINI\n  },\n  rn: {\n    type: PLATFORM_TYPE.RN\n  },\n  quickapp: {\n    type: PLATFORM_TYPE.QUICK\n  },\n}\n", "type EventName = string | symbol\ntype EventCallbacks = Record<EventName, Record<'next' | 'tail', unknown>>\n\nexport class Events {\n  protected callbacks?: EventCallbacks\n  static eventSplitter = ',' // Note: Harmony ACE API 8 开发板不支持使用正则 split 字符串 /\\s+/\n\n  constructor (opts?) {\n    this.callbacks = opts?.callbacks ?? {}\n  }\n\n  on (eventName: EventName, callback: (...args: any[]) => void, context?: any): this {\n    let event: EventName | undefined, tail, _eventName: EventName[]\n    if (!callback) {\n      return this\n    }\n    if (typeof eventName === 'symbol') {\n      _eventName = [eventName]\n    } else {\n      _eventName = eventName.split(Events.eventSplitter)\n    }\n    this.callbacks ||= {}\n    const calls = this.callbacks\n    while ((event = _eventName.shift())) {\n      const list = calls[event]\n      const node: any = list ? list.tail : {}\n      node.next = tail = {}\n      node.context = context\n      node.callback = callback\n      calls[event] = {\n        tail,\n        next: list ? list.next : node\n      }\n    }\n    return this\n  }\n\n  once (events: EventName, callback: (...r: any[]) => void, context?: any): this {\n    const wrapper = (...args: any[]) => {\n      callback.apply(this, args)\n      this.off(events, wrapper, context)\n    }\n\n    this.on(events, wrapper, context)\n\n    return this\n  }\n\n  off (events?: EventName, callback?: (...args: any[]) => void, context?: any) {\n    let event: EventName | undefined, calls: EventCallbacks | undefined, _events: EventName[]\n    if (!(calls = this.callbacks)) {\n      return this\n    }\n    if (!(events || callback || context)) {\n      delete this.callbacks\n      return this\n    }\n    if (typeof events === 'symbol') {\n      _events = [events]\n    } else {\n      _events = events ? events.split(Events.eventSplitter) : Object.keys(calls)\n    }\n    while ((event = _events.shift())) {\n      let node: any = calls[event]\n      delete calls[event]\n      if (!node || !(callback || context)) {\n        continue\n      }\n      const tail = node.tail\n      while ((node = node.next) !== tail) {\n        const cb = node.callback\n        const ctx = node.context\n        if ((callback && cb !== callback) || (context && ctx !== context)) {\n          this.on(event, cb, ctx)\n        }\n      }\n    }\n    return this\n  }\n\n  trigger (events: EventName, ...args: any[]) {\n    let event: EventName | undefined, node, calls: EventCallbacks | undefined, _events: EventName[]\n    if (!(calls = this.callbacks)) {\n      return this\n    }\n    if (typeof events === 'symbol') {\n      _events = [events]\n    } else {\n      _events = events.split(Events.eventSplitter)\n    }\n    while ((event = _events.shift())) {\n      if ((node = calls[event])) {\n        const tail = node.tail\n        while ((node = node.next) !== tail) {\n          node.callback.apply(node.context || this, args)\n        }\n      }\n    }\n    return this\n  }\n}\n", "export function isString (o: unknown): o is string {\n  return typeof o === 'string'\n}\n\nexport function isUndefined (o: unknown): o is undefined {\n  return typeof o === 'undefined'\n}\n\nexport function isNull (o: unknown): o is null {\n  return o === null\n}\n\nexport function isObject<T> (o: unknown): o is T {\n  return o !== null && typeof o === 'object'\n}\n\nexport function isBoolean (o: unknown): o is boolean {\n  return o === true || o === false\n}\n\nexport function isFunction (o: unknown): o is (...args: any[]) => any {\n  return typeof o === 'function'\n}\n\nexport function isNumber (o: unknown): o is number {\n  if (Number.isFinite) return Number.isFinite(o)\n  return typeof o === 'number'\n}\n\nexport function isBooleanStringLiteral (o: unknown): o is string {\n  return o === 'true' || o === 'false' || o === '!0' || o === '!1'\n}\n\nexport function isObjectStringLiteral (o: unknown): o is string {\n  return o === '{}'\n}\n\nexport const isArray = Array.isArray\n\nexport const isWebPlatform = () => process.env.TARO_ENV === 'h5' || process.env.TARO_PLATFORM === 'web'\n", "import { isFunction, isString } from './is'\nimport { nonsupport, setUniqueKeyToRoute } from './utils'\n\ndeclare const getCurrentPages: () => any\ndeclare const getApp: () => any\ndeclare const requirePlugin: () => void\n\ntype IObject = Record<string, any>\n\ninterface IProcessApisIOptions {\n  noPromiseApis?: Set<string>\n  needPromiseApis?: Set<string>\n  handleSyncApis?: (key: string, global: IObject, args: any[]) => any\n  transformMeta?: (key: string, options: IObject) => { key: string, options: IObject }\n  modifyApis?: (apis: Set<string>) => void\n  modifyAsyncResult?: (key: string, res) => void\n  isOnlyPromisify?: boolean\n  [propName: string]: any\n}\n\nexport interface IApiDiff {\n  [key: string]: {\n    /** API重命名 */\n    alias?: string\n    options?: {\n      /** API参数键名修改 */\n      change?: {\n        old: string\n        new: string\n      }[]\n      /** API参数值修改 */\n      set?: {\n        key: string\n        value: ((options: Record<string, any>) => unknown) | unknown\n      }[]\n    }\n  }\n}\n\nconst needPromiseApis = new Set<string>([\n  'addPhoneContact',\n  'authorize',\n  'canvasGetImageData',\n  'canvasPutImageData',\n  'canvasToTempFilePath',\n  'checkSession',\n  'chooseAddress',\n  'chooseImage',\n  'chooseInvoiceTitle',\n  'chooseLocation',\n  'chooseVideo',\n  'clearStorage',\n  'closeBLEConnection',\n  'closeBluetoothAdapter',\n  'closeSocket',\n  'compressImage',\n  'connectSocket',\n  'createBLEConnection',\n  'downloadFile',\n  'exitMiniProgram',\n  'getAvailableAudioSources',\n  'getBLEDeviceCharacteristics',\n  'getBLEDeviceServices',\n  'getBatteryInfo',\n  'getBeacons',\n  'getBluetoothAdapterState',\n  'getBluetoothDevices',\n  'getClipboardData',\n  'getConnectedBluetoothDevices',\n  'getConnectedWifi',\n  'getExtConfig',\n  'getFileInfo',\n  'getImageInfo',\n  'getLocation',\n  'getNetworkType',\n  'getSavedFileInfo',\n  'getSavedFileList',\n  'getScreenBrightness',\n  'getSetting',\n  'getStorage',\n  'getStorageInfo',\n  'getSystemInfo',\n  'getUserInfo',\n  'getWifiList',\n  'hideHomeButton',\n  'hideShareMenu',\n  'hideTabBar',\n  'hideTabBarRedDot',\n  'loadFontFace',\n  'login',\n  'makePhoneCall',\n  'navigateBack',\n  'navigateBackMiniProgram',\n  'navigateTo',\n  'navigateToBookshelf',\n  'navigateToMiniProgram',\n  'notifyBLECharacteristicValueChange',\n  'hideKeyboard',\n  'hideLoading',\n  'hideNavigationBarLoading',\n  'hideToast',\n  'openBluetoothAdapter',\n  'openDocument',\n  'openLocation',\n  'openSetting',\n  'pageScrollTo',\n  'previewImage',\n  'queryBookshelf',\n  'reLaunch',\n  'readBLECharacteristicValue',\n  'redirectTo',\n  'removeSavedFile',\n  'removeStorage',\n  'removeTabBarBadge',\n  'requestSubscribeMessage',\n  'saveFile',\n  'saveImageToPhotosAlbum',\n  'saveVideoToPhotosAlbum',\n  'scanCode',\n  'sendSocketMessage',\n  'setBackgroundColor',\n  'setBackgroundTextStyle',\n  'setClipboardData',\n  'setEnableDebug',\n  'setInnerAudioOption',\n  'setKeepScreenOn',\n  'setNavigationBarColor',\n  'setNavigationBarTitle',\n  'setScreenBrightness',\n  'setStorage',\n  'setTabBarBadge',\n  'setTabBarItem',\n  'setTabBarStyle',\n  'showActionSheet',\n  'showFavoriteGuide',\n  'showLoading',\n  'showModal',\n  'showShareMenu',\n  'showTabBar',\n  'showTabBarRedDot',\n  'showToast',\n  'startBeaconDiscovery',\n  'startBluetoothDevicesDiscovery',\n  'startDeviceMotionListening',\n  'startPullDownRefresh',\n  'stopBeaconDiscovery',\n  'stopBluetoothDevicesDiscovery',\n  'stopCompass',\n  'startCompass',\n  'startAccelerometer',\n  'stopAccelerometer',\n  'showNavigationBarLoading',\n  'stopDeviceMotionListening',\n  'stopPullDownRefresh',\n  'switchTab',\n  'uploadFile',\n  'vibrateLong',\n  'vibrateShort',\n  'writeBLECharacteristicValue'\n])\n\nfunction getCanIUseWebp (taro) {\n  return function () {\n    const res = taro.getSystemInfoSync?.()\n\n    if (!res) {\n      if (process.env.NODE_ENV !== 'production') {\n        console.error('不支持 API canIUseWebp')\n      }\n      return false\n    }\n\n    const { platform } = res\n\n    const platformLower = platform.toLowerCase()\n    if (platformLower === 'android' || platformLower === 'devtools') {\n      return true\n    }\n    return false\n  }\n}\n\nfunction getNormalRequest (global) {\n  return function request (options) {\n    options = options\n      ? (\n        isString(options)\n          ? { url: options }\n          : options\n      )\n      : {}\n\n    const originSuccess = options.success\n    const originFail = options.fail\n    const originComplete = options.complete\n    let requestTask\n    const p: any = new Promise((resolve, reject) => {\n      options.success = res => {\n        originSuccess && originSuccess(res)\n        resolve(res)\n      }\n      options.fail = res => {\n        originFail && originFail(res)\n        reject(res)\n      }\n\n      options.complete = res => {\n        originComplete && originComplete(res)\n      }\n\n      requestTask = global.request(options)\n    })\n\n    equipTaskMethodsIntoPromise(requestTask, p)\n\n    p.abort = (cb) => {\n      cb && cb()\n      if (requestTask) {\n        requestTask.abort()\n      }\n      return p\n    }\n    return p\n  }\n}\n\nfunction processApis (taro, global, config: IProcessApisIOptions = {}) {\n  const patchNeedPromiseApis = config.needPromiseApis || []\n  const _needPromiseApis = new Set<string>([...patchNeedPromiseApis, ...needPromiseApis])\n  const preserved = [\n    'getEnv',\n    'interceptors',\n    'Current',\n    'getCurrentInstance',\n    'options',\n    'nextTick',\n    'eventCenter',\n    'Events',\n    'preload',\n    'webpackJsonp'\n  ]\n\n  const apis = new Set(\n    !config.isOnlyPromisify\n      ? Object.keys(global).filter(api => preserved.indexOf(api) === -1)\n      : patchNeedPromiseApis\n  )\n\n  if (config.modifyApis) {\n    config.modifyApis(apis)\n  }\n\n  apis.forEach(key => {\n    if (_needPromiseApis.has(key)) {\n      const originKey = key\n      taro[originKey] = (options: Record<string, any> | string = {}, ...args) => {\n        let key = originKey\n\n        // 第一个参数 options 为字符串，单独处理\n        if (typeof options === 'string') {\n          if (args.length) {\n            return global[key](options, ...args)\n          }\n          return global[key](options)\n        }\n\n        // 改变 key 或 option 字段，如需要把支付宝标准的字段对齐微信标准的字段\n        if (config.transformMeta) {\n          const transformResult = config.transformMeta(key, options)\n          key = transformResult.key\n          ; (options as Record<string, any>) = transformResult.options\n          // 新 key 可能不存在\n          if (!global.hasOwnProperty(key)) {\n            return nonsupport(key)()\n          }\n        }\n\n        let task: any = null\n        const obj: Record<string, any> = Object.assign({}, options)\n\n        // 为页面跳转相关的 API 设置一个随机数作为路由参数。为了给 runtime 区分页面。\n        setUniqueKeyToRoute(key, options)\n\n        // Promise 化\n        const p: any = new Promise((resolve, reject) => {\n          obj.success = res => {\n            config.modifyAsyncResult?.(key, res)\n            options.success?.(res)\n            if (key === 'connectSocket') {\n              resolve(\n                Promise.resolve().then(() => task ? Object.assign(task, res) : res)\n              )\n            } else {\n              resolve(res)\n            }\n          }\n          obj.fail = res => {\n            options.fail?.(res)\n            reject(res)\n          }\n          obj.complete = res => {\n            options.complete?.(res)\n          }\n          if (args.length) {\n            task = global[key](obj, ...args)\n          } else {\n            task = global[key](obj)\n          }\n        })\n\n        // 给 promise 对象挂载属性\n        if (['uploadFile', 'downloadFile'].includes(key)) {\n          equipTaskMethodsIntoPromise(task, p)\n          p.progress = cb => {\n            task?.onProgressUpdate(cb)\n            return p\n          }\n          p.abort = cb => {\n            cb?.()\n            task?.abort()\n            return p\n          }\n        }\n        return p\n      }\n    } else {\n      let platformKey = key\n\n      // 改变 key 或 option 字段，如需要把支付宝标准的字段对齐微信标准的字段\n      if (config.transformMeta) {\n        platformKey = config.transformMeta(key, {}).key\n      }\n\n      // API 不存在\n      if (!global.hasOwnProperty(platformKey)) {\n        taro[key] = nonsupport(key)\n        return\n      }\n      if (isFunction(global[key])) {\n        taro[key] = (...args) => {\n          if (config.handleSyncApis) {\n            return config.handleSyncApis(key, global, args)\n          } else {\n            return global[platformKey].apply(global, args)\n          }\n        }\n      } else {\n        taro[key] = global[platformKey]\n      }\n    }\n  })\n\n  !config.isOnlyPromisify && equipCommonApis(taro, global, config)\n}\n\n/**\n * 挂载常用 API\n * @param taro Taro 对象\n * @param global 小程序全局对象，如微信的 wx，支付宝的 my\n */\nfunction equipCommonApis (taro, global, apis: Record<string, any> = {}) {\n  taro.canIUseWebp = getCanIUseWebp(taro)\n  taro.getCurrentPages = getCurrentPages || nonsupport('getCurrentPages')\n  taro.getApp = getApp || nonsupport('getApp')\n  taro.env = global.env || {}\n\n  try {\n    taro.requirePlugin = requirePlugin || nonsupport('requirePlugin')\n  } catch (error) {\n    taro.requirePlugin = nonsupport('requirePlugin')\n  }\n\n  // request & interceptors\n  const request = apis.request || getNormalRequest(global)\n  function taroInterceptor (chain) {\n    return request(chain.requestParams)\n  }\n  const link = new taro.Link(taroInterceptor)\n  taro.request = link.request.bind(link)\n  taro.addInterceptor = link.addInterceptor.bind(link)\n  taro.cleanInterceptors = link.cleanInterceptors.bind(link)\n  taro.miniGlobal = taro.options.miniGlobal = global\n  taro.getAppInfo = function () {\n    return {\n      platform: process.env.TARO_PLATFORM || 'MiniProgram',\n      taroVersion: process.env.TARO_VERSION || 'unknown',\n      designWidth: taro.config.designWidth\n    }\n  }\n  taro.createSelectorQuery = delayRef(taro, global, 'createSelectorQuery', 'exec')\n  taro.createIntersectionObserver = delayRef(taro, global, 'createIntersectionObserver', 'observe')\n}\n\n/**\n * 将Task对象中的方法挂载到promise对象中，适配小程序api原生返回结果\n * @param task Task对象 {RequestTask | DownloadTask | UploadTask}\n * @param promise Promise\n */\nfunction equipTaskMethodsIntoPromise (task, promise) {\n  if (!task || !promise) return\n  const taskMethods = ['abort', 'onHeadersReceived', 'offHeadersReceived', 'onProgressUpdate', 'offProgressUpdate', 'onChunkReceived', 'offChunkReceived']\n  task && taskMethods.forEach(method => {\n    if (method in task) {\n      promise[method] = task[method].bind(task)\n    }\n  })\n}\n\nfunction delayRef (taro, global, name: string, method: string) {\n  return function (...args) {\n    const res = global[name](...args)\n    const raw = res[method].bind(res)\n    res[method] = function (...methodArgs) {\n      taro.nextTick(() => raw(...methodArgs))\n    }\n    return res\n  }\n}\n\nexport {\n  processApis\n}\n", "import { Events } from './event-emitter'\nimport { isFunction } from './is'\n\nimport type { Shortcuts } from './template'\n\n// Note: @tarojs/runtime 不依赖 @tarojs/taro, 所以不能改为从 @tarojs/taro 引入 (可能导致循环依赖)\ntype TFunc = (...args: any[]) => any\n\nexport enum HOOK_TYPE {\n  SINGLE,\n  MULTI,\n  WATERFALL\n}\n\ninterface Hook {\n  type: HOOK_TYPE\n  initial?: TFunc | null\n}\n\ninterface Node {\n  next: Node\n  context?: any\n  callback?: TFunc\n}\n\ninterface MiniLifecycle {\n  app: [\n    string, /** onLaunch */\n    string, /** onShow */\n    string /** onHide */\n  ]\n  page: [\n    string, /** onLoad */\n    string, /** onUnload */\n    string, /** onReady */\n    string, /** onShow */\n    string, /** onHide */\n    string[], /** others */\n    string[] /** side-effects */\n  ]\n  component: [\n    string, /** attached */\n    string, /** detached */\n  ]\n}\n\ninterface MiniElementData {\n  [Shortcuts.Childnodes]?: MiniData[]\n  [Shortcuts.NodeName]: string\n  [Shortcuts.Class]?: string\n  [Shortcuts.Style]?: string\n  uid?: string\n  sid: string\n  [key: string]: unknown\n}\n\ninterface MiniTextData {\n  [Shortcuts.Text]: string\n  [Shortcuts.NodeName]: string\n}\n\ntype MiniData = MiniElementData | MiniTextData\n\ninterface UpdatePayload {\n  path: string\n  value: string | boolean | (() => MiniData | MiniData[])\n}\n\ntype Target = Record<string, unknown> & { dataset: Record<string, unknown>, id: string }\n\ninterface MpEvent {\n  type: string\n  detail: Record<string, unknown>\n  target: Target\n  currentTarget: Target\n}\n\nconst defaultMiniLifecycle: MiniLifecycle = {\n  app: [\n    'onLaunch',\n    'onShow',\n    'onHide'\n  ],\n  page: [\n    'onLoad',\n    'onUnload',\n    'onReady',\n    'onShow',\n    'onHide',\n    [\n      'onPullDownRefresh',\n      'onReachBottom',\n      'onPageScroll',\n      'onResize',\n      'defer:onTabItemTap', // defer: 需要等页面组件挂载后再调用\n      'onTitleClick',\n      'onOptionMenuClick',\n      'onPopMenuClick',\n      'onPullIntercept',\n      'onAddToFavorites'\n    ],\n    [\n      'onShareAppMessage',\n      'onShareTimeline'\n    ]\n  ],\n  component: [\n    'attached',\n    'detached'\n  ]\n}\n\nexport function TaroHook (type: HOOK_TYPE, initial?: TFunc): Hook {\n  return {\n    type,\n    initial: initial || null\n  }\n}\n\nexport class TaroHooks<T extends Record<string, TFunc> = any> extends Events {\n  hooks: Record<keyof T, Hook>\n\n  constructor (hooks: Record<keyof T, Hook>, opts?) {\n    super(opts)\n    this.hooks = hooks\n    for (const hookName in hooks) {\n      const { initial } = hooks[hookName]\n      if (isFunction(initial)) {\n        this.on(hookName, initial)\n      }\n    }\n  }\n\n  private tapOneOrMany<K extends Extract<keyof T, string>> (hookName: K, callback: T[K] | T[K][]) {\n    const list = isFunction(callback) ? [callback] : callback\n    list.forEach(cb => this.on(hookName, cb))\n  }\n\n  tap<K extends Extract<keyof T, string>> (hookName: K, callback: T[K] | T[K][]) {\n    const hooks = this.hooks\n    const { type, initial } = hooks[hookName]\n    if (type === HOOK_TYPE.SINGLE) {\n      this.off(hookName)\n      this.on(hookName, isFunction(callback) ? callback : callback[callback.length - 1])\n    } else {\n      initial && this.off(hookName, initial)\n      this.tapOneOrMany(hookName, callback)\n    }\n  }\n\n  call<K extends Extract<keyof T, string>> (hookName: K, ...rest: Parameters<T[K]>): ReturnType<T[K]> | undefined {\n    const hook = this.hooks[hookName]\n    if (!hook) return\n\n    const { type } = hook\n\n    const calls = this.callbacks\n    if (!calls) return\n\n    const list = calls[hookName] as { tail: Node, next: Node }\n\n    if (list) {\n      const tail = list.tail\n      let node: Node = list.next\n      let args = rest\n      let res\n\n      while (node !== tail) {\n        res = node.callback?.apply(node.context || this, args)\n        if (type === HOOK_TYPE.WATERFALL) {\n          const params: any = [res]\n          args = params\n        }\n        node = node.next\n      }\n      return res\n    }\n  }\n\n  isExist (hookName: string) {\n    return Boolean(this.callbacks?.[hookName])\n  }\n}\n\ntype ITaroHooks = {\n  /** 小程序端 App、Page 构造对象的生命周期方法名称 */\n  getMiniLifecycle: (defaultConfig: MiniLifecycle) => MiniLifecycle\n  getMiniLifecycleImpl: () => MiniLifecycle\n  /** 解决 React 生命周期名称的兼容问题 */\n  getLifecycle: (instance, lifecyle) => TFunc | Array<TFunc> | undefined\n  /** 提供Hook，为不同平台提供修改生命周期配置 */\n  modifyRecursiveComponentConfig: (defaultConfig:MiniLifecycle, options:any) => any\n  /** 解决百度小程序的模版语法问题 */\n  getPathIndex: (indexOfNode: number) => string\n  /** 解决支付宝小程序分包时全局作用域不一致的问题 */\n  getEventCenter: (EventsClass: typeof Events) => Events\n  isBubbleEvents: (eventName: string) => boolean\n  getSpecialNodes: () => string[]\n  onRemoveAttribute: (element, qualifiedName: string) => boolean\n  /** 用于把 React 同一事件回调中的所有 setState 合并到同一个更新处理中 */\n  batchedEventUpdates: (cb: TFunc) => void\n  /** 用于处理 React 中的小程序生命周期 hooks */\n  mergePageInstance: (prev, next) => void\n  /** 用于修改传递给小程序 Page 构造器的对象 */\n  modifyPageObject: (config: Record<any, any>) => void\n  /** H5 下拉刷新 wrapper */\n  createPullDownComponent: (el, path: string, framework, customWrapper?: any, stampId?: string) => void\n  /** H5 获取原生 DOM 对象 */\n  getDOMNode: (instance) => any\n  /**\n   * @todo: multi\n   * 修改 Taro DOM 序列化数据\n   **/\n  modifyHydrateData:(data: Record<string, any>, node) => void\n  /**\n   * 自定义处理 Taro DOM 序列化数据，如使其脱离 data 树\n   */\n  transferHydrateData: (data: Record<string, any>, element, componentsAlias: Record<string, any>) => void\n  /**\n    * @todo: multi\n    * 修改 Taro DOM 序列化数据\n    **/\n  modifySetAttrPayload: (element, key: string, payload: UpdatePayload, componentsAlias: Record<string, any>) => void\n  /**\n    * @todo: multi\n    * 修改 Taro DOM 序列化数据\n    **/\n  modifyRmAttrPayload: (element, key: string, payload: UpdatePayload, componentsAlias: Record<string, any>) => void\n  /**\n    * @todo: multi\n    * 调用 addEventListener 时触发\n    **/\n  onAddEvent: (type: string, handler, options: any, node) => void\n  /** 用于修改小程序原生事件对象 */\n  modifyMpEvent: (event: MpEvent) => void\n  modifyMpEventImpl: (event: MpEvent) => void\n  /** 用于修改 Taro DOM 事件对象 */\n  modifyTaroEvent: (event, element) => void\n\n  dispatchTaroEvent: (event, element) => void\n  dispatchTaroEventFinish: (event, element) => void\n  modifyTaroEventReturn: (node, event, returnVal) => any\n\n  modifyDispatchEvent: (event, element) => void\n  injectNewStyleProperties: (styleProperties: string[]) => void\n  initNativeApi: (taro: Record<string, any>) => void\n  patchElement: (node) => void\n\n  /** 解 Proxy */\n  proxyToRaw: (proxyObj) => Record<any, any>\n  /** 元素增加事件监听钩子 */\n  modifyAddEventListener: (element, sideEffect: boolean, getComponentsAlias: () => Record<string, any>) => void\n  /** 元素删除事件监听钩子 */\n  modifyRemoveEventListener: (element, sideEffect: boolean, getComponentsAlias: () => Record<string, any>) => void\n  /** 鸿蒙用于监听 memory 等级的钩子 */\n  getMemoryLevel: (level: { level: number }) => void\n}\n\nexport const hooks = new TaroHooks<ITaroHooks>({\n  getMiniLifecycle: TaroHook(HOOK_TYPE.SINGLE, defaultConfig => defaultConfig),\n\n  getMiniLifecycleImpl: TaroHook(HOOK_TYPE.SINGLE, function (this: TaroHooks<ITaroHooks>) {\n    return this.call('getMiniLifecycle', defaultMiniLifecycle)\n  }),\n\n  getLifecycle: TaroHook(HOOK_TYPE.SINGLE, (instance, lifecycle) => instance[lifecycle]),\n\n  modifyRecursiveComponentConfig: TaroHook(HOOK_TYPE.SINGLE, (defaultConfig) => defaultConfig),\n\n  getPathIndex: TaroHook(HOOK_TYPE.SINGLE, indexOfNode => `[${indexOfNode}]`),\n\n  getEventCenter: TaroHook(HOOK_TYPE.SINGLE, Events => new Events()),\n\n  isBubbleEvents: TaroHook(HOOK_TYPE.SINGLE, eventName => {\n    /**\n     * 支持冒泡的事件, 除 支付宝小程序外，其余的可冒泡事件都和微信保持一致\n     * 详见 见 https://developers.weixin.qq.com/miniprogram/dev/framework/view/wxml/event.html\n     */\n    const BUBBLE_EVENTS = new Set([\n      'touchstart',\n      'touchmove',\n      'touchcancel',\n      'touchend',\n      'touchforcechange',\n      'tap',\n      'longpress',\n      'longtap',\n      'transitionend',\n      'animationstart',\n      'animationiteration',\n      'animationend'\n    ])\n\n    return BUBBLE_EVENTS.has(eventName)\n  }),\n\n  getSpecialNodes: TaroHook(HOOK_TYPE.SINGLE, () => ['view', 'text', 'image']),\n\n  onRemoveAttribute: TaroHook(HOOK_TYPE.SINGLE),\n\n  batchedEventUpdates: TaroHook(HOOK_TYPE.SINGLE),\n\n  mergePageInstance: TaroHook(HOOK_TYPE.SINGLE),\n\n  modifyPageObject: TaroHook(HOOK_TYPE.SINGLE),\n\n  createPullDownComponent: TaroHook(HOOK_TYPE.SINGLE),\n\n  getDOMNode: TaroHook(HOOK_TYPE.SINGLE),\n\n  modifyHydrateData: TaroHook(HOOK_TYPE.SINGLE),\n\n  transferHydrateData: TaroHook(HOOK_TYPE.SINGLE),\n\n  modifySetAttrPayload: TaroHook(HOOK_TYPE.SINGLE),\n\n  modifyRmAttrPayload: TaroHook(HOOK_TYPE.SINGLE),\n\n  onAddEvent: TaroHook(HOOK_TYPE.SINGLE),\n\n  proxyToRaw: TaroHook(HOOK_TYPE.SINGLE, function (proxyObj) {\n    return proxyObj\n  }),\n\n  modifyMpEvent: TaroHook(HOOK_TYPE.MULTI),\n\n  modifyMpEventImpl: TaroHook(HOOK_TYPE.SINGLE, function (this: TaroHooks<ITaroHooks>, e: MpEvent) {\n    try {\n      // 有些小程序的事件对象的某些属性只读\n      this.call('modifyMpEvent', e)\n    } catch (error) {\n      console.warn('[Taro modifyMpEvent hook Error]: ' + error?.message)\n    }\n  }),\n\n  injectNewStyleProperties: TaroHook(HOOK_TYPE.SINGLE),\n\n  modifyTaroEvent: TaroHook(HOOK_TYPE.MULTI),\n\n  dispatchTaroEvent: TaroHook(HOOK_TYPE.SINGLE, (e, node) => {\n    node.dispatchEvent(e)\n  }),\n\n  dispatchTaroEventFinish: TaroHook(HOOK_TYPE.MULTI),\n\n  modifyTaroEventReturn: TaroHook(HOOK_TYPE.SINGLE, () => undefined),\n\n  modifyDispatchEvent: TaroHook(HOOK_TYPE.MULTI),\n\n  initNativeApi: TaroHook(HOOK_TYPE.MULTI),\n\n  patchElement: TaroHook(HOOK_TYPE.MULTI),\n\n  modifyAddEventListener: TaroHook(HOOK_TYPE.SINGLE),\n\n  modifyRemoveEventListener: TaroHook(HOOK_TYPE.SINGLE),\n\n  getMemoryLevel: TaroHook(HOOK_TYPE.SINGLE),\n})\n", "const { hooks } = require('@tarojs/runtime')\nconst taro = require('@tarojs/api').default\n\nif (hooks.isExist('initNativeApi')) {\n  hooks.call('initNativeApi', taro)\n}\n\nmodule.exports = taro\nmodule.exports.default = module.exports\n", "/* eslint-disable no-undef */\nimport { createRecursiveComponentConfig } from '@tarojs/runtime'\n// @ts-ignore\nComponent(createRecursiveComponentConfig())\n", "/* eslint-disable no-undef */\nimport { createRecursiveComponentConfig } from '@tarojs/runtime'\n// @ts-ignore\nComponent(createRecursiveComponentConfig('custom-wrapper'))\n"], "names": [], "sourceRoot": ""}