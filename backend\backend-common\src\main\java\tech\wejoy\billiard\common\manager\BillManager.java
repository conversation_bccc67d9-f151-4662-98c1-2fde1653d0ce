package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.web.bean.request.PageRequest;
import com.google.common.collect.Lists;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.bo.ClubUserOrderCountBo;
import tech.wejoy.billiard.common.bo.UserStatsBo;
import tech.wejoy.billiard.common.dto.BillQueryDto;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.enums.BillTypeEnum;
import tech.wejoy.billiard.common.repo.BillInfoRepo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class BillManager {

    private final BillInfoRepo billInfoRepo;

    public void save(BillInfo bill) {
        billInfoRepo.save(bill);
    }

    public void createBill(BillInfo bill) {
        bill.setStatus(BillStatusEnum.PENDING);
        billInfoRepo.persist(bill);
        bill.setBillNo(generateBillNo(bill));
        billInfoRepo.persist(bill);
    }

    public String generateBillNo(BillInfo bill) {
        String s = "B" + System.currentTimeMillis();
        return s + (bill.getId() + 10088006600L);
    }

    public BillInfo fetchByBillNo(String billNo) {
        return billInfoRepo.find("billNo", billNo).firstResult();
    }

    public BillInfo fetchByOrderNo(String orderNo) {
        return billInfoRepo.find("orderNo = ?1 and type = ?2", orderNo, BillTypeEnum.ORDER).firstResult();
    }

    public List<BillInfo> fetchTenantBillByDate(Long tenantId, Long clubId, LocalDate startDate, LocalDate endDate) {
        LocalDateTime start = startDate.atStartOfDay();
        LocalDateTime end = endDate.plusDays(1).atStartOfDay();
        Map<String, Object> map = new HashMap<>();
        map.put("start", start);
        map.put("end", end);
        map.put("type", BillTypeEnum.TENANT_PLAN);
        map.put("status", BillStatusEnum.PAID);
        String query = "updateAt >= :start and updateAt < :end and type = :type and status = :status";
        if (tenantId != null && tenantId != 0L) {
            query += " and tenantId = :tenantId";
            map.put("tenantId", tenantId);
        }
        if (clubId != null && clubId != 0L) {
            query += " and clubId = :clubId";
            map.put("clubId", clubId);
        }
        return billInfoRepo.find(query, map).list();
    }

    public BillInfo fetchById(Long id) {
        return billInfoRepo.findById(id);
    }

    public List<Long> fetchUserIdsByTenantIdAndClubId(Long tenantId, Long clubId) {
        String sql = "select distinct user_id from bill_info " +
                "where status = :status";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null) {
            sql += " and tenant_id = :tenantId";
            params.put("tenantId", tenantId);
        }
        if (clubId != null) {
            sql += " and club_id = :clubId";
            params.put("clubId", clubId);
        }
        params.put("status", BillStatusEnum.PAID);
        return billInfoRepo.findBySql(sql, params, ClubUserOrderCountBo.class).stream().map(ClubUserOrderCountBo::getUserId).toList();
    }

    public Page<BillInfo> fetchPageByByTenantIdAndClubIdAndType(PageRequest page, LocalDate startDate, LocalDate endDate, Long tenantId, Long clubId, BillTypeEnum type) {
        String query = "type = :type and status in :status";
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("status", Lists.newArrayList(BillStatusEnum.PAID, BillStatusEnum.REFUND));
        if (tenantId != null && tenantId != 0L) {
            query += " and tenantId = :tenantId";
            map.put("tenantId", tenantId);
        }
        if (clubId != null && clubId != 0L) {
            query += " and clubId = :clubId";
            map.put("clubId", clubId);
        }
        if (startDate != null) {
            query += " and createAt >= :startDate";
            map.put("startDate", startDate.atStartOfDay());
        }
        if (endDate != null) {
            query += " and createAt < :endDate";
            map.put("endDate", endDate.plusDays(1).atStartOfDay());
        }
        return billInfoRepo.page(page.toPage(), query, Sort.descending("createAt"), map);
    }


    public List<BillInfo> fetchPaidListByByTenantIdAndClubIdAndType(LocalDate startDate, LocalDate endDate, Long tenantId, Long clubId, BillTypeEnum type) {
        String query = "type = :type and status in :status";
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("status", Lists.newArrayList(BillStatusEnum.PAID));
        if (tenantId != null && tenantId != 0L) {
            query += " and tenantId = :tenantId";
            map.put("tenantId", tenantId);
        }
        if (clubId != null && clubId != 0L) {
            query += " and clubId = :clubId";
            map.put("clubId", clubId);
        }
        if (startDate != null) {
            query += " and createAt >= :startDate";
            map.put("startDate", startDate.atStartOfDay());
        }
        if (endDate != null) {
            query += " and createAt < :endDate";
            map.put("endDate", endDate.plusDays(1).atStartOfDay());
        }
        return billInfoRepo.find(query, Sort.descending("createAt"), map).list();
    }

    public Page<BillInfo> list(BillQueryDto dto) {
        String query = "1=1";
        Map<String, Object> map = new HashMap<>();
        if (dto.getBillNo() != null) {
            query += " and billNo like concat('%', :billNo, '%')";
            map.put("billNo", dto.getBillNo());
        }
        if (CollectionUtils.isNotEmpty(dto.getType())) {
            query += " and type in :type";
            map.put("type", dto.getType());
        }
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            query += " and status in :status";
            map.put("status", dto.getStatus());
        }
        if (dto.getStartDate() != null) {
            query += " and createAt >= :startDate";
            map.put("startDate", dto.getStartDate().atStartOfDay());
        }
        if (dto.getEndDate() != null) {
            query += " and createAt < :endDate";
            map.put("endDate", dto.getEndDate().plusDays(1).atStartOfDay());
        }
        if (dto.getUserIds() != null) {
            query += " and userId in :userIds";
            map.put("userIds", dto.getUserIds());
        }
        return billInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), map);
    }

    public UserStatsBo fetchClubBillStatsByTenantId(Long tenantId) {
        String sql = "select count(1) as order_count, sum(pay_amount-refund_amount) as total_amount from bill_info ";
        String where = "where status = 1 and type = 3";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null && tenantId != 0L) {
            where += " and tenant_id = :tenantId";
            params.put("tenantId", tenantId);
        }
        return billInfoRepo.findBySql(sql + where, params, UserStatsBo.class).stream().findFirst().orElse(new UserStatsBo());
    }
}
