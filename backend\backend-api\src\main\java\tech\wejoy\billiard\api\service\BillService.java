package tech.wejoy.billiard.api.service;

import com.congeer.core.exception.BaseException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.manager.BillManager;
import tech.wejoy.billiard.common.service.AssistantService;
import tech.wejoy.billiard.common.service.ClientUserService;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApplicationScoped
@RequiredArgsConstructor
public class BillService {

    private final BillManager billManager;

    @Inject
    CouponApiService couponService;

    @Inject
    ClientUserService clientUserService;

    @Inject
    OrderApiService orderService;

    @Inject
    AssistantService assistantService;

    @Transactional(rollbackOn = Exception.class)
    public void successBill(String outerNo, String billNo, BigDecimal total, BigDecimal payerTotal, LocalDateTime payTime, String resultInfo) {
        BillInfo bill = billManager.fetchByBillNo(billNo);
        if (bill == null) {
            throw new BaseException("账单不存在");
        }
        bill.setStatus(BillStatusEnum.PAID);
        bill.setPayTime(payTime);
        bill.setThirdPayNo(outerNo);
        bill.setTotalAmount(total);
        bill.setPushTime(LocalDateTime.now());
        bill.setPayAmount(payerTotal);
        bill.setResultInfo(resultInfo);
        billManager.save(bill);
        switch (bill.getType()) {
            case TENANT_PLAN -> clientUserService.rechargeClub(bill);
            case MEMBER_PLAN -> clientUserService.planMember(bill);
            case ORDER -> orderService.payOrder(bill);
            case MEMBER_RECHARGE -> clientUserService.rechargeMember(bill);
            case COUPON -> couponService.issueCoupon(bill);
            case ASSISTANT_ORDER -> assistantService.payOrder(bill);
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void refundBill(String billNo, BigDecimal total, BigDecimal payerTotal, String json) {
        BillInfo bill = billManager.fetchByBillNo(billNo);
        if (bill == null) {
            throw new BaseException("账单不存在");
        }
        bill.setStatus(BillStatusEnum.REFUND);
        bill.setRefundTime(LocalDateTime.now());
        bill.setRefundAmount(payerTotal);
        bill.setRefundInfo(json);
        billManager.save(bill);
    }

}
