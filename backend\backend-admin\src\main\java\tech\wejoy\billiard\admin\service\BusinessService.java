package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.DateUtils;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.admin.bo.*;
import tech.wejoy.billiard.admin.dto.WithdrawConfigQueryDto;
import tech.wejoy.billiard.admin.dto.WithdrawRecordQueryDto;
import tech.wejoy.billiard.admin.dto.WithdrawStatusDto;
import tech.wejoy.billiard.admin.manager.TenantManager;
import tech.wejoy.billiard.admin.utils.PermissionUtils;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.WithdrawDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.PermissionTypeEnum;
import tech.wejoy.billiard.common.enums.ProfitTypeEnum;
import tech.wejoy.billiard.common.enums.WithdrawStatusEnum;
import tech.wejoy.billiard.common.manager.*;
import tech.wejoy.billiard.common.strategy.Payment;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class BusinessService {

    private final OrderManager orderManager;

    private final TenantManager tenantManager;

    private final BillManager billManager;

    private final ClubManager clubManager;

    private final ClientUserManager clientUserManager;

    private final ConfigManager configManager;

    private final MemberManager memberManager;

    public RevenueBo todayRevenue(Long tenantId, Long clubId) {
        LocalDate date = LocalDate.now();
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getClubId() != null && user.getClubId() != 0) {
            clubId = user.getClubId();
        }
        if (user.getTenantId() != 0L) {
            tenantId = user.getTenantId();
        } else if (clubId != null) {
            ClubInfo clubInfo = clubManager.fetchClubById(clubId);
            tenantId = clubInfo.getTenantId();
        }
        List<OrderInfo> orderList = orderManager.fetchFinishedOrderByClubAndDate(null, tenantId, clubId, date, date);
        List<BillInfo> billList = billManager.fetchTenantBillByDate(tenantId, clubId, date, date);
        List<PlanGiftRecord> planGiftRecords = memberManager.fetchGiftRecordByTenantIdAndClubId(tenantId, clubId, date, date);
        RevenueBo one = new RevenueBo();
        one.setOrderCount(orderList.size());
        for (OrderInfo order : orderList) {
            BigDecimal amount = order.getRealAmount().subtract(order.getRefundAmount());
            if (order.getPayType() != OrderPayTypeEnum.CLUB) {
                one.setTotal(one.getTotal().add(amount));
            }
            switch (order.getPayType()) {
                case WECHAT:
                case DEPOSIT:
                case COUPON:
                case MEMBER:
                    one.setCash(one.getCash().add(amount));
                    break;
                case CLUB:
                    one.setClubUsed(one.getClubUsed().add(amount));
                    break;
                case MEITUAN:
                case DOUYIN:
                    one.setTicket(one.getTicket().add(amount));
                    break;
            }
        }
        for (BillInfo billInfo : billList) {
            BigDecimal amount = billInfo.getPayAmount();
            if (billInfo.getRefundAmount() != null) {
                amount = amount.add(billInfo.getRefundAmount());
            }
            one.setTotal(one.getTotal().add(amount));
            one.setClubRecharge(one.getClubRecharge().add(amount));
        }
        for (PlanGiftRecord record : planGiftRecords) {
            BigDecimal payAmount = record.getAmount();
            one.setTotal(one.getTotal().add(payAmount));
            one.setClubRecharge(one.getClubRecharge().add(payAmount));
        }
        one.setDate(DateUtils.getDateString(date, "MM-dd"));

        return one;
    }

    public List<RevenueBo> listRevenue(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
        List<TenantProfitRecord> records = tenantManager.fetchProfitRecord(tenantId, clubId, start, end);
        LinkedHashMap<String, RevenueBo> retMap = new LinkedHashMap<>();
        for (TenantProfitRecord v : records) {
            String key = DateUtils.getDateString(v.getDate(), "MM-dd");
            RevenueBo one = retMap.get(key);
            if (one == null) {
                one = new RevenueBo();
                retMap.put(key, one);
            }
            one.setDate(key);
            one.setOrderCount(one.getOrderCount() + v.getOrderCount());
            one.setTotal(one.getTotal().add(v.getTotal()));
            one.setCash(one.getCash().add(v.getOnline()));
            one.setClubRecharge(one.getClubRecharge().add(v.getClub()));
            one.setClubUsed(one.getClubUsed().add(v.getClubUsed()));
            one.setTicket(one.getTicket().add(v.getTicket()));
        }
        RevenueBo total = new RevenueBo();
        retMap.values().forEach(r -> {
            total.setTotal(total.getTotal().add(r.getTotal()));
            total.setCash(total.getCash().add(r.getCash()));
            total.setClubRecharge(total.getClubRecharge().add(r.getClubRecharge()));
            total.setClubUsed(total.getClubUsed().add(r.getClubUsed()));
            total.setTicket(total.getTicket().add(r.getTicket()));
            total.setOrderCount(total.getOrderCount() + r.getOrderCount());
        });
        List<RevenueBo> ret = new ArrayList<>(retMap.values());
        total.setDate("总计");
        ret.add(total);
        return ret;
    }

    // public List<RevenueBo> listRevenue(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
    //     AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
    //     if (user.getClubId() != null && user.getClubId() != 0) {
    //         clubId = user.getClubId();
    //     }
    //     if (user.getTenantId() != 0L) {
    //         tenantId = user.getTenantId();
    //     } else if (clubId != null) {
    //         ClubInfo clubInfo = clubManager.fetchClubInfo(clubId);
    //         tenantId = clubInfo.getTenantId();
    //     }
    //     if (start == null) {
    //         start = LocalDate.now();
    //     }
    //     if (end == null) {
    //         end = LocalDate.now();
    //     }
    //     List<OrderInfo> orderList = orderManager.fetchFinishedOrderByClubAndDate(null, tenantId, clubId, start, end);
    //     List<BillInfo> billList = billManager.fetchTenantBillByDate(tenantId, clubId, start, end);
    //
    //     Map<LocalDate, List<OrderInfo>> orderGroup = orderList.stream().collect(Collectors.groupingBy(o -> o.getRealEndTime().toLocalDate()));
    //     Map<LocalDate, List<BillInfo>> billGroup = billList.stream().collect(Collectors.groupingBy(b -> b.getUpdateAt().toLocalDate()));
    //     List<RevenueBo> ret = new ArrayList<>();
    //     while (!end.equals(start.plusDays(-1))) {
    //         if (end.equals(LocalDate.now())) {
    //             end = end.plusDays(-1);
    //             continue;
    //         }
    //         List<OrderInfo> orderInfos = orderGroup.get(end);
    //         List<BillInfo> billInfos = billGroup.get(end);
    //         if (orderInfos == null && billInfos == null) {
    //             end = end.plusDays(-1);
    //             continue;
    //         }
    //         if (orderInfos == null) {
    //             orderInfos = Lists.newArrayList();
    //         }
    //         if (billInfos == null) {
    //             billInfos = Lists.newArrayList();
    //         }
    //         RevenueBo one = new RevenueBo();
    //         one.setOrderCount(orderInfos.size());
    //         for (OrderInfo order : orderInfos) {
    //             BigDecimal amount = order.getRealAmount().subtract(order.getRefundAmount());
    //             if (order.getPayType() != OrderPayTypeEnum.CLUB) {
    //                 one.setTotal(one.getTotal().add(amount));
    //             }
    //             switch (order.getPayType()) {
    //                 case WECHAT:
    //                 case DEPOSIT:
    //                 case COUPON:
    //                 case MEMBER:
    //                     one.setCash(one.getCash().add(amount));
    //                     break;
    //                 case CLUB:
    //                     one.setClubUsed(one.getClubUsed().add(amount));
    //                     break;
    //                 case MEITUAN:
    //                 case DOUYIN:
    //                     one.setTicket(one.getTicket().add(amount));
    //                     break;
    //             }
    //         }
    //         for (BillInfo billInfo : billInfos) {
    //             BigDecimal amount = billInfo.getPayAmount();
    //             if (billInfo.getRefundAmount() != null) {
    //                 amount = amount.add(billInfo.getRefundAmount());
    //             }
    //             one.setTotal(one.getTotal().add(amount));
    //             one.setClubRecharge(one.getClubRecharge().add(amount));
    //         }
    //         one.setDate(DateUtils.getDateString(end, "MM-dd"));
    //         ret.add(one);
    //         end = end.plusDays(-1);
    //     }
    //     RevenueBo total = new RevenueBo();
    //     ret.forEach(r -> {
    //         total.setTotal(total.getTotal().add(r.getTotal()));
    //         total.setCash(total.getCash().add(r.getCash()));
    //         total.setClubRecharge(total.getClubRecharge().add(r.getClubRecharge()));
    //         total.setClubUsed(total.getClubUsed().add(r.getClubUsed()));
    //         total.setTicket(total.getTicket().add(r.getTicket()));
    //         total.setOrderCount(total.getOrderCount() + r.getOrderCount());
    //     });
    //     total.setDate("总计");
    //     ret.add(total);
    //     return ret;
    // }

    public ProfitListBo listProfit(String orderNo, ProfitTypeEnum type, Long tenantId, Long clubId, LocalDate start, LocalDate end) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getClubId() != null && user.getClubId() != 0) {
            clubId = user.getClubId();
        }
        if (user.getTenantId() != 0L) {
            tenantId = user.getTenantId();
        } else if (clubId != null) {
            ClubInfo clubInfo = clubManager.fetchClubById(clubId);
            tenantId = clubInfo.getTenantId();
        }
        if (start == null) {
            start = LocalDate.now();
        }
        if (end == null) {
            end = LocalDate.now();
        }
        List<OrderInfo> orderList = orderManager.fetchFinishedOrderByClubAndDate(orderNo, tenantId, clubId, start, end);
        List<BillInfo> billList = Lists.newArrayList();
        List<PlanGiftRecord> planGiftRecords = Lists.newArrayList();
        if (StringUtils.isBlank(orderNo)) {
            billList = billManager.fetchTenantBillByDate(tenantId, clubId, start, end);
            planGiftRecords = memberManager.fetchGiftRecordByTenantIdAndClubId(tenantId, clubId, start, end);
        }
        List<Long> clubIds = orderList.stream().map(OrderInfo::getClubId).collect(Collectors.toList());
        clubIds.addAll(billList.stream().map(BillInfo::getClubId).toList());
        clubIds.addAll(planGiftRecords.stream().map(PlanGiftRecord::getClubId).collect(Collectors.toSet()));

        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(ClubInfo::getId, c -> c));

        List<Long> tableIds = orderList.stream().map(OrderInfo::getTableId).toList();
        List<ClubTable> tables = clubManager.fetchTableByIds(tableIds);
        Map<Long, ClubTable> tableMap = tables.stream().collect(Collectors.toMap(ClubTable::getId, t -> t));

        List<Long> userIds = orderList.stream().map(OrderInfo::getUserId).collect(Collectors.toList());
        userIds.addAll(billList.stream().map(BillInfo::getUserId).toList());
        userIds.addAll(planGiftRecords.stream().map(PlanGiftRecord::getUserId).toList());

        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, u -> u));

        Map<OrderPayTypeEnum, Payment> cache = new HashMap<>();

        List<ProfitDetailBo> ret = new ArrayList<>();

        if (type == null || type == ProfitTypeEnum.NONE || type == ProfitTypeEnum.ORDER) {
            List<ProfitDetailBo> orders = orderList.stream().map(o -> {
                ProfitDetailBo bo = new ProfitDetailBo();
                bo.setType(ProfitTypeEnum.ORDER);
                bo.setEndTime(o.getRealEndTime());
                ClubInfo clubInfo = clubMap.get(o.getClubId());
                Payment payment = cache.get(o.getPayType());
                if (payment == null) {
                    payment = Payment.getPayment(o.getPayType());
                    cache.put(o.getPayType(), payment);
                }
                OrderProfitBo profit = payment.profit(o, clubInfo);
                ClubTable table = tableMap.get(o.getTableId());
                profit.setTableName(table.getName());
                ClientUser u = userMap.get(o.getUserId());
                if (u != null) {
                    profit.setUserPhone(u.getPhone());
                }
                bo.setOrder(profit);
                return bo;
            }).toList();
            ret.addAll(orders);
        }
        if (type == null || type == ProfitTypeEnum.NONE || type == ProfitTypeEnum.CLUB_PLAN) {
            List<ProfitDetailBo> bills = billList.stream().map(b -> {
                ProfitDetailBo bo = new ProfitDetailBo();
                bo.setType(ProfitTypeEnum.CLUB_PLAN);
                bo.setEndTime(b.getUpdateAt());
                ClubRechargeProfitBo profit = BeanUtils.copy(b, ClubRechargeProfitBo.class);
                ClubInfo clubInfo = clubMap.get(b.getClubId());
                Integer serviceFeeRatio = null;
                if (clubInfo != null) {
                    profit.setClubName(clubInfo.getName());
                    serviceFeeRatio = clubInfo.getServiceFeeRatio();
                }
                if (serviceFeeRatio == null) {
                    serviceFeeRatio = 5;
                }
                TenantPlan plan = JsonUtils.toObject(b.getPayInfo(), TenantPlan.class);
                profit.setPlanName(plan.getName());
                BigDecimal serviceFee = profit.getPayAmount().multiply(new BigDecimal(serviceFeeRatio)).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                profit.setServiceFee(serviceFee);
                profit.setProfitAmount(profit.getPayAmount().subtract(serviceFee));
                ClientUser u = userMap.get(b.getUserId());
                profit.setUserPhone(u.getPhone());
                bo.setClub(profit);
                bo.setClubRecharge(profit);
                return bo;
            }).toList();
            ret.addAll(bills);
        }
        if (type == null || type == ProfitTypeEnum.NONE || type == ProfitTypeEnum.CLUB_GIFT) {
            List<ProfitDetailBo> bills = planGiftRecords.stream().map(g -> {
                ProfitDetailBo bo = new ProfitDetailBo();
                bo.setType(ProfitTypeEnum.CLUB_GIFT);
                bo.setEndTime(g.getUpdateAt());
                ClubGiftProfitBo profit = BeanUtils.copy(g, ClubGiftProfitBo.class);
                profit.setPayAmount(g.getAmount());
                ClubInfo clubInfo = clubMap.get(g.getClubId());
                Integer serviceFeeRatio = null;
                if (clubInfo != null) {
                    profit.setClubName(clubInfo.getName());
                    serviceFeeRatio = clubInfo.getServiceFeeRatio();
                }
                if (serviceFeeRatio == null) {
                    serviceFeeRatio = 5;
                }
                BigDecimal serviceFee = profit.getPayAmount().multiply(new BigDecimal(serviceFeeRatio)).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                profit.setServiceFee(serviceFee);
                profit.setProfitAmount(profit.getPayAmount().subtract(serviceFee));
                ClientUser u = userMap.get(g.getUserId());
                profit.setUserPhone(u.getPhone());
                bo.setClubGift(profit);
                return bo;
            }).toList();
            ret.addAll(bills);
        }
        ret.sort((o1, o2) -> o2.getEndTime().compareTo(o1.getEndTime()));
        ProfitListBo profitListBo = new ProfitListBo();
        profitListBo.setRecords(ret);
        ret.stream().map(v -> {
            if (v.getType() == ProfitTypeEnum.ORDER) {
                return v.getOrder().getProfitAmount();
            } else if (v.getType() == ProfitTypeEnum.CLUB_PLAN) {
                return v.getClub().getProfitAmount();
            } else {
                return v.getClubGift().getPayAmount();
            }
        }).forEach(v -> profitListBo.setProfit(profitListBo.getProfit().add(v)));
        return profitListBo;
    }

    public List<ProfitSimpleBo> listProfitTask(LocalDate date) {
        Map<Long, ProfitSimpleBo> map = new HashMap<>();
        List<OrderInfo> orderList = orderManager.fetchFinishedOrderByClubAndDate(null, null, null, date, date);
        List<BillInfo> billList = billManager.fetchTenantBillByDate(null, null, date, date);
        List<PlanGiftRecord> planGiftRecords = memberManager.fetchGiftRecordByTenantIdAndClubId(null, null, date, date);
        List<Long> clubIds = orderList.stream().map(OrderInfo::getClubId).collect(Collectors.toList());
        clubIds.addAll(billList.stream().map(BillInfo::getClubId).toList());
        clubIds.addAll(planGiftRecords.stream().map(PlanGiftRecord::getClubId).collect(Collectors.toSet()));

        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(ClubInfo::getId, c -> c));

        List<Long> userIds = orderList.stream().map(OrderInfo::getUserId).collect(Collectors.toList());
        userIds.addAll(billList.stream().map(BillInfo::getUserId).toList());
        userIds.addAll(planGiftRecords.stream().map(PlanGiftRecord::getUserId).toList());

        Map<OrderPayTypeEnum, Payment> cache = new HashMap<>();

        for (OrderInfo order : orderList) {
            ProfitSimpleBo one = map.get(order.getClubId());
            if (one == null) {
                one = new ProfitSimpleBo();
                one.setTenantId(order.getTenantId());
                one.setClubId(order.getClubId());
                map.put(order.getClubId(), one);
            }
            ClubInfo clubInfo = clubMap.get(order.getClubId());
            Payment payment = cache.get(order.getPayType());
            if (payment == null) {
                payment = Payment.getPayment(order.getPayType());
                cache.put(order.getPayType(), payment);
            }
            OrderProfitBo profit = payment.profit(order, clubInfo);
            BigDecimal amount = profit.getRevenueAmount();

            one.setOrderCount(one.getOrderCount() + 1);
            one.setFee(one.getFee().add(profit.getServiceFee()));
            one.setProfit(one.getProfit().add(profit.getProfitAmount()));
            if (order.getPayType() != OrderPayTypeEnum.CLUB) {
                one.setTotal(one.getTotal().add(amount));
            }
            switch (order.getPayType()) {
                case MEITUAN:
                    Map<String, Object> params = JsonUtils.toObject(order.getDescription(), new TypeReference<>() {
                    });
                    ClientUserTicket ticket = JsonUtils.toObject(JsonUtils.toJson(params.get("ticket")), ClientUserTicket.class);
                    switch (ticket.getChannel()) {
                        case MEITUAN -> one.setMeituan(one.getMeituan().add(amount));
                        case DOUYIN -> one.setDouyin(one.getDouyin().add(amount));
                    }
                    one.setTicket(one.getTicket().add(amount));
                    break;
                case DOUYIN:
                    one.setDouyin(one.getDouyin().add(amount));
                    one.setTicket(one.getTicket().add(amount));
                    break;
                case COUPON:
                    one.setCoupon(one.getCoupon().add(amount));
                    one.setOnline(one.getOnline().add(amount));
                    break;
                case CLUB:
                    one.setClubUsed(one.getClubUsed().add(amount));
                    break;
                case WECHAT:
                case DEPOSIT:
                case MEMBER:
                    one.setCash(one.getCash().add(amount));
                default:
                    one.setOnline(one.getOnline().add(amount));
                    break;
            }
        }
        for (BillInfo bill : billList) {
            ProfitSimpleBo one = map.get(bill.getClubId());
            if (one == null) {
                one = new ProfitSimpleBo();
                one.setTenantId(bill.getTenantId());
                one.setClubId(bill.getClubId());
                map.put(bill.getClubId(), one);
            }
            ClubInfo clubInfo = clubMap.get(bill.getClubId());
            Integer serviceFeeRatio = null;
            if (clubInfo != null) {
                serviceFeeRatio = clubInfo.getServiceFeeRatio();
            }
            if (serviceFeeRatio == null) {
                serviceFeeRatio = 5;
            }
            BigDecimal payAmount = bill.getPayAmount();
            if (bill.getRefundAmount() != null) {
                payAmount = payAmount.subtract(bill.getRefundAmount());
            }
            BigDecimal serviceFee = payAmount.multiply(new BigDecimal(serviceFeeRatio)).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);

            one.setFee(one.getFee().add(serviceFee));
            one.setClub(one.getClub().add(payAmount));
            one.setClubRecharge(one.getClubRecharge().add(payAmount));
            one.setProfit(one.getProfit().add(payAmount.subtract(serviceFee)));
            one.setTotal(one.getTotal().add(payAmount));
        }
        for (PlanGiftRecord record : planGiftRecords) {
            ProfitSimpleBo one = map.get(record.getClubId());
            if (one == null) {
                one = new ProfitSimpleBo();
                one.setTenantId(record.getTenantId());
                one.setClubId(record.getClubId());
                map.put(record.getClubId(), one);
            }
            ClubInfo clubInfo = clubMap.get(record.getClubId());
            Integer serviceFeeRatio = null;
            if (clubInfo != null) {
                serviceFeeRatio = clubInfo.getServiceFeeRatio();
            }
            if (serviceFeeRatio == null) {
                serviceFeeRatio = 5;
            }
            BigDecimal payAmount = record.getAmount();
            BigDecimal serviceFee = payAmount.multiply(new BigDecimal(serviceFeeRatio)).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);

            one.setFee(one.getFee().add(serviceFee));
            one.setClub(one.getClub().add(payAmount));
            one.setClubGift(one.getClubGift().add(payAmount));
            one.setProfit(one.getProfit().add(payAmount.subtract(serviceFee)));
            one.setTotal(one.getTotal().add(payAmount));
        }
        return map.values().stream().toList();
    }

    public ProfitStatsBo stats(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
        List<TenantProfitRecord> records = tenantManager.fetchProfitRecord(tenantId, clubId, start, end);
        ProfitStatsBo stats = new ProfitStatsBo();
        for (TenantProfitRecord record : records) {
            stats.setTotal(stats.getTotal().add(record.getTotal()));
            stats.setOrder(stats.getOrder().add(record.getCash()));
            stats.setClub(stats.getClub().add(record.getClub()));
            stats.setMeituan(stats.getMeituan().add(record.getMeituan()));
            stats.setDouyin(stats.getDouyin().add(record.getDouyin()));
            stats.setCoupon(stats.getCoupon().add(record.getCoupon()));
            stats.setOnline(stats.getOnline().add(record.getProfit()));
        }
        return stats;
    }

    // public ProfitStatsBo stats(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
    //     AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
    //     if (user.getClubId() != null && user.getClubId() != 0) {
    //         clubId = user.getClubId();
    //     }
    //     if (user.getTenantId() != 0L) {
    //         tenantId = user.getTenantId();
    //     } else if (clubId != null) {
    //         ClubInfo clubInfo = clubManager.fetchClubInfo(clubId);
    //         tenantId = clubInfo.getTenantId();
    //     }
    //     if (start == null) {
    //         start = LocalDate.now().plusDays(-1);
    //     }
    //     if (end == null) {
    //         end = LocalDate.now().plusDays(-1);
    //     }
    //     if (!end.isBefore(LocalDate.now())) {
    //         end = LocalDate.now().plusDays(-1);
    //     }
    //     List<OrderInfo> orderList = orderManager.fetchFinishedOrderByClubAndDate(null, tenantId, clubId, start, end);
    //     List<BillInfo> billList = billManager.fetchTenantBillByDate(tenantId, clubId, start, end);
    //
    //     List<Long> clubIds = orderList.stream().map(OrderInfo::getClubId).collect(Collectors.toList());
    //     clubIds.addAll(billList.stream().map(BillInfo::getClubId).toList());
    //
    //     List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
    //     Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(ClubInfo::getId, c -> c));
    //
    //     ProfitStatsBo stats = new ProfitStatsBo();
    //
    //     Map<OrderPayTypeEnum, Payment> cache = new HashMap<>();
    //     for (OrderInfo order : orderList) {
    //         ClubInfo clubInfo = clubMap.get(order.getClubId());
    //         Payment payment = cache.get(order.getPayType());
    //         if (payment == null) {
    //             payment = Payment.getPayment(order.getPayType());
    //             cache.put(order.getPayType(), payment);
    //         }
    //         OrderProfitBo profit = payment.profit(order, clubInfo);
    //         stats.setOnline(stats.getOnline().add(profit.getProfitAmount()));
    //
    //         switch (order.getPayType()) {
    //             case MEITUAN:
    //                 Map<String, Object> params = JsonUtils.toObject(order.getDescription(), new TypeReference<>() {
    //                 });
    //                 stats.setTotal(stats.getTotal().add(order.getRealAmount().subtract(order.getRefundAmount())));
    //                 ClientUserTicket ticket = JsonUtils.toObject(JsonUtils.toJson(params.get("ticket")), ClientUserTicket.class);
    //                 switch (ticket.getChannel()) {
    //                     case MEITUAN -> stats.setMeituan(stats.getMeituan().add(order.getRealAmount()));
    //                     case DOUYIN -> stats.setDouyin(stats.getDouyin().add(order.getRealAmount()));
    //                 }
    //                 break;
    //             case DOUYIN:
    //                 stats.setDouyin(stats.getDouyin().add(order.getRealAmount()));
    //                 stats.setTotal(stats.getTotal().add(order.getRealAmount().subtract(order.getRefundAmount())));
    //                 break;
    //             case COUPON:
    //                 stats.setCoupon(stats.getCoupon().add(order.getRealAmount()));
    //                 stats.setTotal(stats.getTotal().add(order.getRealAmount().subtract(order.getRefundAmount())));
    //                 break;
    //             case CLUB:
    //                 break;
    //             default:
    //                 stats.setTotal(stats.getTotal().add(order.getRealAmount().subtract(order.getRefundAmount())));
    //                 stats.setOrder(stats.getOrder().add(profit.getOrderAmount().subtract(profit.getRefundAmount())));
    //                 break;
    //         }
    //     }
    //     for (BillInfo bill : billList) {
    //         ClubInfo clubInfo = clubMap.get(bill.getClubId());
    //         Integer serviceFeeRatio = null;
    //         if (clubInfo != null) {
    //             serviceFeeRatio = clubInfo.getServiceFeeRatio();
    //         }
    //         if (serviceFeeRatio == null) {
    //             serviceFeeRatio = 5;
    //         }
    //         BigDecimal serviceFee = bill.getPayAmount().multiply(new BigDecimal(serviceFeeRatio)).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
    //         stats.setTotal(stats.getTotal().add(bill.getPayAmount()));
    //         stats.setOnline(stats.getOnline().add(bill.getPayAmount()).subtract(serviceFee));
    //         stats.setClub(stats.getClub().add(bill.getPayAmount()));
    //     }
    //     return stats;
    // }


    public OperationsStatsBo statsOperations(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getClubId() != null && user.getClubId() != 0) {
            clubId = user.getClubId();
        }
        if (user.getTenantId() != 0L) {
            tenantId = user.getTenantId();
        } else if (clubId != null) {
            ClubInfo clubInfo = clubManager.fetchClubById(clubId);
            tenantId = clubInfo.getTenantId();
        }
        ClubInfo club = clubManager.fetchClubById(clubId);
        OperationsStatsBo ret = new OperationsStatsBo();
        if (!club.getTenantId().equals(tenantId)) {
            return ret;
        }
        List<OrderInfo> orders = orderManager.fetchFinishedOrderByClubAndDate(null, tenantId, clubId, start, end);
        if (orders.isEmpty()) {
            return ret;
        }
        List<Long> userIds = orders.stream().map(OrderInfo::getUserId).filter(v -> v != 0L).distinct().toList();
        ret.setUserCount(userIds.size());
        ret.setOrderCount(orders.size());
        long seconds = orders.stream().map(o -> {
            if (o.getRealEndTime() == null) {
                return 0L;
            }
            return o.getRealStartTime().until(o.getRealEndTime(), java.time.temporal.ChronoUnit.SECONDS);
        }).reduce(0L, Long::sum);
        ret.setTotalTableHours(new BigDecimal(seconds).divide(new BigDecimal(3600), 2, RoundingMode.HALF_EVEN));
        ret.setAvgTableHours(ret.getTotalTableHours().divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_EVEN));

        BigDecimal memberTotal = BigDecimal.ZERO;
        BigDecimal total = BigDecimal.ZERO;
        for (OrderInfo order : orders) {
            switch (order.getPayType()) {
                case MEMBER:
                case CLUB:
                    ret.setMemberOrderCount(ret.getMemberOrderCount() + 1);
                    memberTotal = memberTotal.add(order.getRealAmount().subtract(order.getRefundAmount()));
                    total = total.add(order.getRealAmount().subtract(order.getRefundAmount()));
                    break;
                case MEITUAN:
                case DOUYIN:
                    ret.setTicketOrderCount(ret.getTicketOrderCount() + 1);
                default:
                    ret.setNonMemberOrderCount(ret.getNonMemberOrderCount() + 1);
                    total = total.add(order.getRealAmount().subtract(order.getRefundAmount()));
                    break;
            }
        }

        ret.setMemberConversionRate(new BigDecimal(ret.getMemberOrderCount()).multiply(new BigDecimal(100)).divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_EVEN));
        if (total.compareTo(BigDecimal.ZERO) != 0) {
            ret.setMemberRechargeRate(memberTotal.multiply(new BigDecimal(100)).divide(total, 2, RoundingMode.HALF_EVEN));
        }

        ret.setPerHourPrice(total.divide(ret.getTotalTableHours(), 2, RoundingMode.HALF_EVEN));

        List<ClubUserOrderCountBo> clubUserOrderCountBos = orderManager.fetchClubOrderCountGroupByUserIds(tenantId, clubId, userIds);
        Map<Long, Long> userOrderCountMap = clubUserOrderCountBos.stream().collect(Collectors.toMap(ClubUserOrderCountBo::getUserId, ClubUserOrderCountBo::getCount));
        Map<Long, List<OrderInfo>> currentMap = orders.stream().collect(Collectors.groupingBy(OrderInfo::getUserId));
        int newUserCount = 0;
        for (Long userId : userIds) {
            Long count = userOrderCountMap.get(userId);
            if (count == currentMap.get(userId).size()) {
                newUserCount++;
            }
        }
        ret.setNewUserCount(newUserCount);

        return ret;
    }

    public List<TableHoursBo> statsTableHours(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getClubId() != null && user.getClubId() != 0) {
            clubId = user.getClubId();
        }
        if (user.getTenantId() != 0L) {
            tenantId = user.getTenantId();
        } else if (clubId != null) {
            ClubInfo clubInfo = clubManager.fetchClubById(clubId);
            tenantId = clubInfo.getTenantId();
        }
        ClubInfo club = clubManager.fetchClubById(clubId);
        if (!club.getTenantId().equals(tenantId)) {
            return List.of();
        }
        List<ClubTable> tables = clubManager.fetchTableByClub(clubId);
        List<OrderInfo> orders = orderManager.fetchFinishedOrderByClubAndDate(null, tenantId, clubId, start, end);
        Map<Long, List<OrderInfo>> orderGroup = orders.stream().collect(Collectors.groupingBy(OrderInfo::getTableId));
        List<TableHoursBo> ret = new ArrayList<>();
        for (ClubTable table : tables) {
            TableHoursBo bo = new TableHoursBo();
            bo.setId(table.getId());
            bo.setTableName(table.getName());
            List<OrderInfo> orderInfos = orderGroup.get(table.getId());
            if (orderInfos != null) {
                long seconds = orderInfos.stream().map(o -> {
                    if (o.getRealEndTime() == null) {
                        return 0L;
                    }
                    return o.getRealStartTime().until(o.getRealEndTime(), java.time.temporal.ChronoUnit.SECONDS);
                }).reduce(0L, Long::sum);
                bo.setHours(new BigDecimal(seconds).divide(new BigDecimal(3600), 2, RoundingMode.HALF_EVEN));
                bo.setCount(orderInfos.size());
            }
            ret.add(bo);
        }
        return ret;
    }

    public List<TableOperationsBo> statsTable(Long tenantId, Long clubId) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getClubId() != null && user.getClubId() != 0) {
            clubId = user.getClubId();
        }
        if (user.getTenantId() != 0L) {
            tenantId = user.getTenantId();
        } else if (clubId != null) {
            ClubInfo clubInfo = clubManager.fetchClubById(clubId);
            tenantId = clubInfo.getTenantId();
        }
        ClubInfo club = clubManager.fetchClubById(clubId);
        if (!club.getTenantId().equals(tenantId)) {
            return List.of();
        }
        List<ClubTable> tables = clubManager.fetchTableByClub(clubId);
        List<Long> tableIds = tables.stream().map(BaseEntity::getId).toList();
        List<Long> userIds = tables.stream().map(ClubTable::getUserId).filter(Objects::nonNull).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, u -> u));
        List<OrderInfo> orders = orderManager.fetchUsingOrderByTableIds(tableIds);
        Map<Long, OrderInfo> orderMap = orders.stream().collect(Collectors.toMap(OrderInfo::getTableId, o -> o, (o1, o2) -> o1));
        List<OrderInfo> userOrders = orderManager.fetchFinishOrderByClubAndUserIds(clubId, userIds);
        Map<Long, List<OrderInfo>> userOrderGroup = userOrders.stream().collect(Collectors.groupingBy(OrderInfo::getUserId));
        List<TableOperationsBo> ret = new ArrayList<>();
        for (ClubTable table : tables) {
            TableOperationsBo bo = new TableOperationsBo();
            bo.setId(table.getId());
            bo.setTableName(table.getName());
            bo.setStatus(table.getStatus());
            OrderInfo order = orderMap.get(table.getId());
            if (order != null) {
                bo.setUserId(order.getUserId());
                bo.setStartTime(order.getStartTime());
                bo.setEndTime(order.getEndTime());
                bo.setPayType(order.getPayType());
                bo.setOrderId(order.getId());
                bo.setOrderNo(order.getOrderNo());
                bo.setPayAmount(order.getPayAmount());
            }
            if (table.getUserId() != null && table.getUserId() != 0L) {
                ClientUser u = userMap.get(table.getUserId());
                bo.setUserPhone(u.getPhone());
            }
            List<OrderInfo> userOrder = userOrderGroup.get(table.getUserId());
            if (userOrder != null) {
                bo.setUserOrderCount(userOrder.size() + 1);
            }
            ret.add(bo);
        }
        return ret;
    }

    public UserStatsBo userStats() {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        UserStatsBo userStatsBo = orderManager.fetchOrderStatsByTenantId(user.getTenantId());
        UserStatsBo billStats = billManager.fetchClubBillStatsByTenantId(user.getTenantId());
        userStatsBo.setTotalAmount(userStatsBo.getTotalAmount().add(billStats.getTotalAmount()));
        userStatsBo.setOrderCount(userStatsBo.getOrderCount() + billStats.getOrderCount());
        return userStatsBo;
    }

    public WithdrawConfigBo withdrawConfig() {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() == 0) {
            return null;
        }
        TenantWithdrawConfig config = tenantManager.fetchWithdrawConfig(user.getTenantId());
        if (config == null) {
            return null;
        }
        return BeanUtils.copy(config, WithdrawConfigBo.class);
    }

    public void saveWithdrawConfig(WithdrawDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() == 0) {
            throw new BaseException("总部账户无法设置提现");
        }
        TenantWithdrawConfig config = tenantManager.fetchWithdrawConfig(user.getTenantId());
        if (config == null) {
            config = new TenantWithdrawConfig();
            config.setTenantId(user.getTenantId());
        } else {
            if (config.getStatus() == WithdrawStatusEnum.REVIEWING) {
                throw new BaseException("提现配置正在审核中");
            }
        }
        config.setAccount(dto.getAccount());
        config.setBank(dto.getBank());
        config.setBranch(dto.getBranch());
        config.setName(dto.getName());
        config.setStatus(WithdrawStatusEnum.REVIEWING);
        tenantManager.saveWithdrawConfig(config);
    }

    public Page<WithdrawRecordBo> withdrawList(WithdrawRecordQueryDto dto) {
        if (PermissionUtils.getPermissionType() == PermissionTypeEnum.BUSINESS) {
            AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
            if (user.getTenantId() == 0 || user.getClubId() == null || CollectionUtils.isNotEmpty(user.getClubIds())) {
                return Page.empty();
            }
            dto.setTenantId(Lists.newArrayList(user.getTenantId()));
        }
        Page<TenantWithdrawRecord> recordPage = tenantManager.pageWithdrawRecord(dto);
        return recordPage.convert(v -> BeanUtils.copy(v, WithdrawRecordBo.class));
    }

    @Transactional(rollbackOn = Exception.class)
    public void saveProfitTask(List<ProfitSimpleBo> list, LocalDate date) {
        Map<Long, TenantProfitAccount> accountMap = new HashMap<>();
        for (ProfitSimpleBo one : list) {
            TenantProfitAccount account = accountMap.get(one.getTenantId());
            if (account == null) {
                account = tenantManager.fetchProfitAccount(one.getTenantId());
                if (account == null) {
                    account = new TenantProfitAccount();
                    account.setTenantId(one.getTenantId());
                    account.setBalance(BigDecimal.ZERO);
                    account.setFee(BigDecimal.ZERO);
                    account.setTotal(BigDecimal.ZERO);
                }
                accountMap.put(one.getTenantId(), account);
            }
            TenantProfitRecord exist = tenantManager.fetchProfitRecord(one.getTenantId(), one.getClubId(), date);
            if (exist != null) {
                account.setTotal(account.getTotal().subtract(exist.getProfit()));
                account.setFee(account.getFee().subtract(exist.getFee()));
                account.setBalance(account.getBalance().subtract(exist.getProfit()));
                tenantManager.deleteProfitRecord(exist.getId());
            }
            TenantProfitRecord record = BeanUtils.copy(one, TenantProfitRecord.class);
            record.setDate(date);
            tenantManager.saveProfitRecord(record);
            account.setTotal(account.getTotal().add(one.getProfit()));
            account.setFee(account.getFee().add(one.getFee()));
            account.setBalance(account.getBalance().add(one.getProfit()));
        }
        for (TenantProfitAccount account : accountMap.values()) {
            tenantManager.saveProfitAccount(account);
        }
    }

    public void checkProfit(LocalDate date) {
        if (date == null) {
            date = LocalDate.now().plusDays(-1);
        }
        List<ProfitSimpleBo> list = listProfitTask(date);
        saveProfitTask(list, date);
    }

    @Transactional(rollbackOn = Exception.class)
    public void autoWithdraw() {
        List<TenantProfitAccount> accounts = tenantManager.fetchProfitAccounts();
        BaseConfig rate = configManager.fetchBaseConfig("tenant.withdraw.rate");
        BaseConfig min = configManager.fetchBaseConfig("tenant.withdraw.min");
        BigDecimal rateValue = new BigDecimal(rate == null ? "0.6" : rate.getValue());
        BigDecimal minValue = new BigDecimal(min == null ? "1000" : min.getValue());
        List<TenantWithdrawConfig> configs = tenantManager.fetchWithdrawConfigs();
        Map<Long, TenantWithdrawConfig> configMap = configs.stream().collect(Collectors.toMap(TenantWithdrawConfig::getTenantId, Function.identity()));
        for (TenantProfitAccount account : accounts) {
            TenantWithdrawConfig config = configMap.get(account.getTenantId());
            if (config == null) {
                continue;
            }
            if (config.getStatus() != WithdrawStatusEnum.SUCCESS) {
                continue;
            }
            if (account.getBalance().compareTo(minValue) < 0) {
                continue;
            }
            TenantWithdrawRecord record = new TenantWithdrawRecord();
            record.setTenantId(account.getTenantId());
            record.setFee(account.getBalance().multiply(rateValue).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN));
            record.setAmount(account.getBalance());
            record.setRealAmount(account.getBalance().subtract(record.getFee()));
            record.setBank(config.getBank());
            record.setBranch(config.getBranch());
            record.setAccount(config.getAccount());
            record.setName(config.getName());
            record.setWithdrawTime(LocalDateTime.now());
            record.setStatus(WithdrawStatusEnum.REVIEWING);
            tenantManager.saveWithdrawRecord(record);
            record.setOrderNo("W" + System.currentTimeMillis() + (record.getId() + 1050700L));
            tenantManager.saveWithdrawRecord(record);
            account.setBalance(BigDecimal.ZERO);
            tenantManager.saveProfitAccount(account);
        }
    }

    public WithdrawAccountBo withdrawAccount() {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() == 0) {
            return new WithdrawAccountBo();
        }
        TenantProfitAccount tenantProfitAccount = tenantManager.fetchProfitAccount(user.getTenantId());
        if (tenantProfitAccount == null) {
            return new WithdrawAccountBo();
        }
        return BeanUtils.copy(tenantProfitAccount, WithdrawAccountBo.class);
    }

    public Page<WithdrawConfigBo> withdrawConfigList(WithdrawConfigQueryDto dto) {
        Page<TenantWithdrawConfig> page = tenantManager.pageWithdrawConfig(dto);
        List<Long> tenantIds = page.getRecords().stream().map(TenantWithdrawConfig::getTenantId).toList();
        List<TenantProfitAccount> accounts = tenantManager.fetchProfitAccountByTenantIds(tenantIds);
        Map<Long, TenantProfitAccount> accountMap = accounts.stream().collect(Collectors.toMap(TenantProfitAccount::getTenantId, Function.identity()));
        return page.convert(v -> {
            WithdrawConfigBo copy = BeanUtils.copy(v, WithdrawConfigBo.class);
            TenantProfitAccount account = accountMap.get(v.getTenantId());
            if (account != null) {
                copy.setBalance(account.getBalance());
                copy.setTotal(account.getTotal());
                copy.setFee(account.getFee());
            }
            return copy;
        });
    }

    public void changeWithdrawConfigStatus(WithdrawStatusDto dto) {
        TenantWithdrawConfig config = tenantManager.fetchWithdrawConfigById(dto.getId());
        if (config == null) {
            throw new BaseException("提现配置不存在");
        }
        config.setStatus(dto.getStatus());
        tenantManager.saveWithdrawConfig(config);
    }

    public void changeWithdrawStatus(WithdrawStatusDto dto) {
        TenantWithdrawRecord record = tenantManager.fetchWithdrawRecord(dto.getId());
        if (record == null) {
            throw new BaseException("提现记录不存在");
        }
        record.setStatus(dto.getStatus());
        if (dto.getStatus() != WithdrawStatusEnum.SUCCESS) {
            TenantProfitAccount account = tenantManager.fetchProfitAccount(record.getTenantId());
            account.setBalance(account.getBalance().add(record.getAmount()));
            tenantManager.saveProfitAccount(account);
        }
        tenantManager.saveWithdrawRecord(record);
    }

}
