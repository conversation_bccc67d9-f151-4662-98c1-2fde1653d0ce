package tech.wejoy.billiard.common.bo;

import lombok.Data;

@Data
public class CompetitionMatchBo {

    private Long id;

    private Long competitionId;

    private Long scheduleId;

    private Long player1UserId;

    private Long tableId;

    private Integer player1Number;

    private String player1Name;

    private String player1Avatar;

    private Long player2UserId;

    private Integer player2Number;

    private String player2Name;

    private String player2Avatar;

    private Integer player1Score;

    private Integer player2Score;

    private Integer round;

    private Integer status;

    private Long winner;

    private Long loser;

    private Integer number;

}
