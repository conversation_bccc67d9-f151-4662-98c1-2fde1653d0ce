package tech.wejoy.billiard.common.strategy;

import com.congeer.core.exception.BaseException;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.entity.AssistantOrder;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.strategy.payment.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Comparator;
import java.util.List;

public abstract class Payment {

    public static Payment getPayment(OrderPayTypeEnum paymentType) {
        return switch (paymentType) {
            case WECHAT -> new WechatPayment();
            case CLUB -> new ClubPayment();
            case MEMBER -> new MemberPayment();
            case DEPOSIT -> new WechatDepositPayment();
            case MEITUAN, DOUYIN -> new TicketPayment();
            case COUPON -> new CouponPayment();
            case FREE -> new FreePayment();
            default -> throw new BaseException("不支持的支付方式");
        };
    }

    public PrepayBo prepay(StartTableDto dto, ClubTable table) {
        PrepayBo ret = new PrepayBo();
        ret.setSuccess(true);
        if (!dto.getEndTime().isAfter(dto.getStartTime())) {
            ret.setSuccess(false);
            ret.setMessage("余额不足");
            return ret;
        }
        return ret;
    }

    public abstract PayResultBo pay(OrderInfo order, Object params);

    public PayResultBo pay(AssistantOrder order, Object params) {
        throw new BaseException("不支持的支付方式");
    }

    public abstract RefundResultBo refund(OrderInfo order, BigDecimal refundAmount);

    public RefundResultBo refund(AssistantOrder order, BigDecimal refundAmount) {
        throw new BaseException("不支持的支付方式");
    }

    public RefundResultBo cancel(OrderInfo order, BigDecimal refundAmount) {
        return refund(order, refundAmount);
    }

    public RefundResultBo cancel(AssistantOrder order, BigDecimal refundAmount) {
        return refund(order, refundAmount);
    }

    public OrderProfitBo profit(OrderInfo order, ClubInfo club) {
        OrderProfitBo profit = new OrderProfitBo();
        profit.setId(order.getId());
        profit.setOrderNo(order.getOrderNo());
        profit.setTenantId(order.getTenantId());
        profit.setUserId(order.getUserId());
        profit.setClubId(order.getClubId());
        profit.setClubName(club.getName());
        profit.setTableId(order.getTableId());
        profit.setPayType(order.getPayType());
        profit.setStartTime(order.getStartTime());
        profit.setEndTime(order.getEndTime());
        long serviceSeconds = order.getRealStartTime().until(order.getRealEndTime(), ChronoUnit.SECONDS);
        BigDecimal serviceHours = BigDecimal.valueOf(serviceSeconds).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_EVEN);
        profit.setServiceHours(serviceHours);
        long appointmentSeconds = order.getStartTime().until(order.getEndTime(), ChronoUnit.SECONDS);
        BigDecimal appointmentHours = BigDecimal.valueOf(appointmentSeconds).divide(BigDecimal.valueOf(3600), 2, RoundingMode.HALF_EVEN);
        profit.setAppointmentHours(appointmentHours);
        profit.setOrderAmount(order.getRealAmount());
        profit.setRefundAmount(order.getRefundAmount());
        Integer serviceFeeRatio = club.getServiceFeeRatio();
        if (serviceFeeRatio == null) {
            serviceFeeRatio = 5;
        }
        BigDecimal profitAmount = profit.getOrderAmount().subtract(profit.getRefundAmount());
        BigDecimal serviceFee = profitAmount.multiply(BigDecimal.valueOf(serviceFeeRatio)).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN);
        profit.setServiceFee(serviceFee);
        profit.setProfitAmount(profitAmount.subtract(serviceFee));
        profit.setRevenueAmount(profitAmount);
        return profit;
    }

    protected LocalDateTime calEndTime(List<TimeSlotBo> timeSlots, LocalDateTime start, BigDecimal maxAmount, Integer memberMaxTime) {
        timeSlots.sort(Comparator.comparing(TimeSlotBo::getStartTime));
        LocalDateTime sourceStart = start.withSecond(0);
        BigDecimal maxPrice = maxAmount;
        for (int i = -1; ; i++) {
            for (TimeSlotBo timeSlot : timeSlots) {
                LocalDate localDate = sourceStart.toLocalDate().plusDays(i);
                LocalDateTime startTime = LocalDateTime.of(localDate, timeSlot.getStartTime());
                LocalDateTime endTime;
                if (timeSlot.isOvernight()) {
                    endTime = LocalDateTime.of(localDate.plusDays(1), timeSlot.getEndTime());
                } else {
                    endTime = LocalDateTime.of(localDate, timeSlot.getEndTime());
                }
                if (!start.isBefore(startTime) && start.isBefore(endTime)) {
                    BigDecimal perPrice = timeSlot.getPerPrice();
                    long seconds = start.until(endTime, ChronoUnit.SECONDS);
                    BigDecimal hours = BigDecimal.valueOf(seconds).divide(BigDecimal.valueOf(3600), 8, RoundingMode.HALF_EVEN);
                    BigDecimal value = perPrice.multiply(hours);
                    if (value.compareTo(maxPrice) > 0) {
                        int realSeconds = maxPrice.divide(perPrice, 20, RoundingMode.HALF_EVEN).multiply(BigDecimal.valueOf(3600)).intValue();
                        LocalDateTime maxEnd = start.plusSeconds(realSeconds);
                        if (maxEnd.isAfter(sourceStart.plusHours(memberMaxTime))) {
                            return sourceStart.plusHours(memberMaxTime);
                        }
                        return maxEnd;
                    } else {
                        maxPrice = maxPrice.subtract(value);
                        start = endTime.plusSeconds(1);
                        if (start.isAfter(sourceStart.plusHours(memberMaxTime))) {
                            return sourceStart.plusHours(memberMaxTime);
                        }
                    }
                }
            }
        }
    }

}
