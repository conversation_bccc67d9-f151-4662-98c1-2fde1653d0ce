package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.util.List;

@Data
public class AdminCouponBo {

    private Long id;

    @Schema(description = "优惠券名称")
    private String title;

    @Schema(description = "优惠券描述")
    private String description;

    @Schema(description = "优惠券价格")
    private BigDecimal price;

    @Schema(description = "优惠券类型")
    private CouponTypeEnum type;

    @Schema(description = "优惠券有效时间")
    private Integer minutes;

    @Schema(description = "优惠券有效时段")
    private String period;

    @Schema(description = "优惠券过期天数")
    private Integer expireDays;

    @Schema(description = "用户限制")
    private Integer userTotalLimit;

    @Schema(description = "每日限制")
    private Integer userDayLimit;

    @Schema(description = "每日限制")
    private Integer dayLimit;

    @Schema(description = "排序")
    private Integer seq;

    @Schema(description = "上架状态")
    private IsEnum status;

    @Schema(description = "是否展示")
    private IsEnum show;

    private List<Long> clubIds;

}
