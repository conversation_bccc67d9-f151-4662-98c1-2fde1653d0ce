package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.DeviceBrandEnum;
import tech.wejoy.billiard.common.enums.DeviceTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class TenantDevice extends BaseEntity {

    private Long tenantId;

    private Long accountId;

    private String name;

    @Column(columnDefinition = "text")
    private String description;

    @Column(columnDefinition = "text")
    private String sourceInfo;

    private DeviceBrandEnum brand;

    private DeviceTypeEnum type;

    private IsEnum online;

    private IsEnum open;

    @Column(columnDefinition = "text")
    private String deviceId;

    private String resourceId;

    private Long parentId;

    private IsEnum auto;

}
