package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class MemberPlan extends BaseEntity {

    private Integer level;

    private String name;

    private BigDecimal initialPrice;

    private BigDecimal renewalPrice;

    private BigDecimal discount;

    private Integer duration;

    @Column(columnDefinition = "text")
    private String remark;

    @Transient
    private boolean init;

}
