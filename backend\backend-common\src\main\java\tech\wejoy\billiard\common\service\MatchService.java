package tech.wejoy.billiard.common.service;

import com.congeer.core.bean.Page;
import com.congeer.database.bean.BaseEntity;
import com.congeer.utils.BeanUtils;
import com.congeer.web.bean.request.PageRequest;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.bo.MatchBo;
import tech.wejoy.billiard.common.bo.MatchRequestBo;
import tech.wejoy.billiard.common.dto.MatchAdminQueryDto;
import tech.wejoy.billiard.common.dto.MatchQueryDto;
import tech.wejoy.billiard.common.entity.ClientUser;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.entity.MatchInfo;
import tech.wejoy.billiard.common.entity.MatchRequest;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.MatchStatusEnum;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.MatchManager;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class MatchService {

    private final MatchManager matchManager;

    private final ClientUserManager clientUserManager;

    private final ClubManager clubManager;

    public Page<MatchBo> page(MatchQueryDto dto) {
        Page<MatchBo> page = matchManager.pageByDistance(dto).convert(v -> BeanUtils.copy(v, MatchBo.class));
        updateMatchInfo(page, false);
        if (dto.getUserId() != null) {
            List<Long> matchIds = page.getRecords().stream().map(MatchBo::getId).toList();
            List<MatchRequest> requests = matchManager.fetchRequestByUserAndIds(dto.getUserId(), matchIds);
            Map<Long, MatchRequest> requestMap = requests.stream().collect(Collectors.toMap(MatchRequest::getMatchId, v -> v));
            page.getRecords().forEach(v -> {
                MatchRequest request = requestMap.get(v.getId());
                if (request != null) {
                    v.setRequest(true);
                }
            });
        }
        return page;
    }

    private void updateMatchInfo(Page<MatchBo> page, boolean hasPhone) {
        List<Long> clubIds = page.getRecords().stream().map(MatchBo::getClubId).toList();
        List<Long> userIds = page.getRecords().stream().map(MatchBo::getUserId).collect(Collectors.toList());
        List<Long> acceptUserIds = page.getRecords().stream().map(MatchBo::getAcceptUserId).toList();
        userIds.addAll(acceptUserIds);
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        page.getRecords().forEach(v -> {
            ClientUser clientUser = userMap.get(v.getUserId());
            if (clientUser != null) {
                v.setNickname(clientUser.getNickname());
                v.setAvatar(clientUser.getAvatar());
                if (hasPhone) {
                    v.setUserPhone(clientUser.getPhone());
                }
            }
            ClientUser acceptUser = userMap.get(v.getAcceptUserId());
            if (acceptUser != null) {
                v.setAcceptNickname(acceptUser.getNickname());
            }
            ClubInfo clubInfo = clubMap.get(v.getClubId());
            if (clubInfo != null) {
                v.setClubName(clubInfo.getName());
                v.setClubHeadImg(clubInfo.getHeadImage());
            }
        });
    }

    @Transactional(rollbackOn = Exception.class)
    public MatchBo create(MatchBo matchBo) {
        MatchInfo info = BeanUtils.copy(matchBo, MatchInfo.class);
        info.setDeleted(IsEnum.FALSE);
        info.setStatus(MatchStatusEnum.WAITING);
        matchManager.save(info);
        return BeanUtils.copy(info, MatchBo.class);
    }

    public Page<MatchBo> myMatch(PageRequest dto, Long userId) {
        Page<MatchInfo> sourcePage = matchManager.pageByUser(dto, userId);
        Page<MatchBo> page = sourcePage.convert(v -> BeanUtils.copy(v, MatchBo.class));
        updateMatchInfo(page, false);
        List<Long> matchIds = page.getRecords().stream().map(MatchBo::getId).toList();
        List<MatchRequest> requests = matchManager.fetchRequestByMatchIds(matchIds);
        List<Long> userIds = requests.stream().map(MatchRequest::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        Map<Long, List<MatchRequest>> requestGroup = requests.stream().collect(Collectors.groupingBy(MatchRequest::getMatchId));
        for (MatchBo v : page.getRecords()) {
            List<MatchRequest> matchRequests = requestGroup.get(v.getId());
            if (matchRequests != null) {
                v.setRequests(matchRequests.stream().map(u -> {
                    MatchRequestBo copy = BeanUtils.copy(u, MatchRequestBo.class);
                    ClientUser clientUser = userMap.get(u.getUserId());
                    if (clientUser != null) {
                        copy.setNickname(clientUser.getNickname());
                        copy.setPhone(clientUser.getPhone());
                        copy.setAvatar(clientUser.getAvatar());
                    }
                    return copy;
                }).toList());
            }
        }

        return page;
    }

    public Page<MatchBo> myAccept(PageRequest dto, Long userId) {
        List<MatchRequest> requests = matchManager.fetchRequestByUser(userId);
        List<Long> matchIds = requests.stream().map(MatchRequest::getMatchId).toList();
        Page<MatchInfo> sourcePage = matchManager.pageByIds(dto, matchIds);
        Page<MatchBo> page = sourcePage.convert(v -> BeanUtils.copy(v, MatchBo.class));
        updateMatchInfo(page, true);
        for (MatchBo v : page.getRecords()) {
            v.setRequest(true);
        }
        return page;
    }

    @Transactional(rollbackOn = Exception.class)
    public void delete(Long id) {
        MatchInfo match = matchManager.fetchById(id);
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        match.setDeleted(IsEnum.TRUE);
        matchManager.save(match);
    }

    @Transactional(rollbackOn = Exception.class)
    public void accept(Long id) {
        MatchRequest request = matchManager.fetchRequestById(id);
        if (request == null) {
            throw new RuntimeException("请求不存在");
        }
        MatchInfo match = matchManager.fetchById(request.getMatchId());
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        request.setStatus(IsEnum.TRUE);
        matchManager.saveRequest(request);
        match.setStatus(MatchStatusEnum.ACCEPTED);
        match.setAcceptUserId(request.getUserId());
        matchManager.save(match);
    }

    public MatchBo update(MatchBo matchBo) {
        Long id = matchBo.getId();
        MatchInfo match = matchManager.fetchById(id);
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        if (match.getStatus() != MatchStatusEnum.WAITING) {
            throw new RuntimeException("已不能修改约球信息");
        }
        if (matchBo.getLevel() != null) {
            match.setLevel(matchBo.getLevel());
        }
        if (matchBo.getMinutes() != null) {
            match.setMinutes(matchBo.getMinutes());
        }
        if (matchBo.getStartTime() != null) {
            match.setStartTime(matchBo.getStartTime());
        }
        if (matchBo.getClubId() != null) {
            match.setClubId(matchBo.getClubId());
        }
        if (matchBo.getGameType() != null) {
            match.setGameType(matchBo.getGameType());
        }
        matchManager.save(match);
        return BeanUtils.copy(match, MatchBo.class);
    }

    public MatchBo get(Long id, Long userId) {
        MatchInfo match = matchManager.fetchById(id);
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        List<MatchRequest> requests = matchManager.fetchRequestByMatchId(id);
        List<Long> userIds = requests.stream().map(MatchRequest::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        MatchBo bo = BeanUtils.copy(match, MatchBo.class);
        if (match.getUserId().equals(userId)) {
            bo.setRequests(requests.stream().map(v -> {
                MatchRequestBo one = BeanUtils.copy(v, MatchRequestBo.class);
                ClientUser clientUser = userMap.get(one.getUserId());
                if (clientUser != null) {
                    one.setNickname(clientUser.getNickname());
                    one.setPhone(clientUser.getPhone());
                }
                return one;
            }).toList());
        } else if (requests.stream().anyMatch(v -> v.getUserId().equals(userId))) {
            bo.setRequest(true);
        }
        ClientUser clientUser = clientUserManager.fetchUserById(match.getUserId());
        if (clientUser != null) {
            bo.setNickname(clientUser.getNickname());
            bo.setAvatar(clientUser.getAvatar());
        }
        if (match.getAcceptUserId() != null) {
            ClientUser acceptUser = clientUserManager.fetchUserById(match.getAcceptUserId());
            if (acceptUser != null) {
                bo.setAcceptNickname(acceptUser.getNickname());
            }
        }
        ClubInfo clubInfo = clubManager.fetchClubById(match.getClubId());
        if (clubInfo != null) {
            bo.setClubName(clubInfo.getName());
            bo.setClubHeadImg(clubInfo.getHeadImage());
        }
        return bo;
    }

    @Transactional(rollbackOn = Exception.class)
    public void request(Long id, Long userId) {
        MatchInfo match = matchManager.fetchById(id);
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        List<MatchRequest> requests = matchManager.fetchRequestByMatchId(id);
        if (requests.stream().anyMatch(v -> v.getUserId().equals(userId))) {
            throw new RuntimeException("已经请求过了");
        }
        MatchRequest request = new MatchRequest();
        request.setMatchId(id);
        request.setUserId(userId);
        request.setStatus(IsEnum.FALSE);
        matchManager.saveRequest(request);
    }

    @Transactional(rollbackOn = Exception.class)
    public void finish(Long id, Long userId) {
        MatchInfo match = matchManager.fetchById(id);
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        if (!match.getUserId().equals(userId)) {
            throw new RuntimeException("不能结束别人的约球");
        }
        match.setStatus(MatchStatusEnum.FINISHED);
        matchManager.save(match);
    }

    public void cancel(Long id, Long userId) {
        MatchInfo match = matchManager.fetchById(id);
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        List<MatchRequest> requests = matchManager.fetchRequestByMatchId(id);
        List<MatchRequest> list = requests.stream().filter(v -> v.getUserId().equals(userId)).toList();
        if (list.isEmpty()) {
            throw new RuntimeException("约球不存在");
        }
        MatchRequest request = list.getFirst();
        matchManager.deleteRequest(request);
    }

    public Page<MatchBo> adminList(MatchAdminQueryDto dto) {
        Page<MatchInfo> page = matchManager.pageByAdmin(dto);
        Page<MatchBo> result = page.convert(v -> BeanUtils.copy(v, MatchBo.class));
        updateMatchInfo(result, false);
        return result;
    }

    public MatchBo adminDetail(Long id) {
        MatchInfo match = matchManager.fetchById(id);
        if (match == null || match.getDeleted() == IsEnum.TRUE) {
            throw new RuntimeException("约球不存在");
        }
        List<MatchRequest> requests = matchManager.fetchRequestByMatchId(id);
        List<Long> userIds = requests.stream().map(MatchRequest::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        MatchBo bo = BeanUtils.copy(match, MatchBo.class);
        bo.setRequests(requests.stream().map(v -> {
            MatchRequestBo one = BeanUtils.copy(v, MatchRequestBo.class);
            ClientUser clientUser = userMap.get(one.getUserId());
            if (clientUser != null) {
                one.setNickname(clientUser.getNickname());
                one.setPhone(clientUser.getPhone());
            }
            return one;
        }).toList());
        ClientUser clientUser = clientUserManager.fetchUserById(match.getUserId());
        if (clientUser != null) {
            bo.setNickname(clientUser.getNickname());
            bo.setAvatar(clientUser.getAvatar());
            bo.setUserPhone(clientUser.getPhone());
        }
        if (match.getAcceptUserId() != null) {
            ClientUser acceptUser = clientUserManager.fetchUserById(match.getAcceptUserId());
            if (acceptUser != null) {
                bo.setAcceptNickname(acceptUser.getNickname());
                bo.setAcceptUserPhone(acceptUser.getPhone());
            }
        }
        ClubInfo clubInfo = clubManager.fetchClubById(match.getClubId());
        if (clubInfo != null) {
            bo.setClubName(clubInfo.getName());
            bo.setClubHeadImg(clubInfo.getHeadImage());
        }
        return bo;
    }

    public void updateMatchStatus(LocalDateTime now) {
        List<MatchInfo> waitingList = matchManager.fetchMatchByStatus(MatchStatusEnum.WAITING);
        for (MatchInfo v : waitingList) {
            if (v.getStartTime().plusMinutes(v.getMinutes()).isBefore(now)) {
                v.setStatus(MatchStatusEnum.TIMEOUT);
                matchManager.save(v);
            }
        }
        List<MatchInfo> acceptList = matchManager.fetchMatchByStatus(MatchStatusEnum.ACCEPTED);
        for (MatchInfo v : acceptList) {
            if (v.getStartTime().plusMinutes(v.getMinutes()).isBefore(now)) {
                v.setStatus(MatchStatusEnum.FINISHED);
                matchManager.save(v);
            }
        }
        List<MatchInfo> matchingList = matchManager.fetchMatchByStatus(MatchStatusEnum.MATCHING);
        for (MatchInfo v : matchingList) {
            if (v.getStartTime().plusMinutes(v.getMinutes()).isBefore(now)) {
                v.setStatus(MatchStatusEnum.FINISHED);
                matchManager.save(v);
            }
        }
    }

    public List<MatchBo> listByClub(Long clubId) {
        List<MatchInfo> list = matchManager.fetchMatchByClub(clubId);
        return list.stream().map(v -> {
            MatchBo bo = BeanUtils.copy(v, MatchBo.class);
            ClientUser clientUser = clientUserManager.fetchUserById(v.getUserId());
            if (clientUser != null) {
                bo.setNickname(clientUser.getNickname());
                bo.setAvatar(clientUser.getAvatar());
            }
            return bo;
        }).toList();
    }

}
