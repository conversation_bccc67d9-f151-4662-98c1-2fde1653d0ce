import Taro from "@tarojs/taro";
import Button, { MODES } from "./Button";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import api from "@/api/bussiness";
import { ORDERTYPES } from "@/constants";
interface ICancelButtonProps {
  code: string;
  status: ORDERTYPES;
  isOwner: boolean;
  updateStatus?: () => Promise<void>;
}

interface ICancelButtonRef {
  cancel: () => Promise<void>;
}

const CancelButton = forwardRef<ICancelButtonRef, ICancelButtonProps>(
  (props, ref) => {
    const { code, status,isOwner } = props;
    const configuration = {
      buttonText: "取消订单",
      mode: MODES.DANGER,
    };

    const cancel = useCallback(async () => {
      const { confirm } = await Taro.showModal({
        title: "提示",
        content: "是否确认当前操作",
        confirmText: "确定",
      });
      if (confirm) {
        await api.order.cancelOrder(code);
        props.updateStatus && props.updateStatus();
      }
    }, [code, props.updateStatus]);

    useImperativeHandle(
      ref,
      () => {
        return { cancel };
      },
      [code]
    );

    if (status === ORDERTYPES.PAID && isOwner) {
      return (
        <Button
          mode={configuration.mode}
          buttonText={configuration.buttonText}
          onClick={cancel}
        />
      );
    }
    return <></>;
  }
);

export default CancelButton;
