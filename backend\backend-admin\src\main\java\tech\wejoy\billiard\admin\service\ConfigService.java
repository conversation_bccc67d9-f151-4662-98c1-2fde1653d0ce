package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.utils.BeanUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.admin.bo.BaseConfigBo;
import tech.wejoy.billiard.admin.dto.BannerAdminQueryDto;
import tech.wejoy.billiard.admin.dto.FeedbackQueryDto;
import tech.wejoy.billiard.common.bo.BannerBo;
import tech.wejoy.billiard.common.bo.FeedbackBo;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;
import tech.wejoy.billiard.common.enums.FileLinkEnum;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.manager.ConfigManager;
import tech.wejoy.billiard.common.manager.FileManager;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class ConfigService {

    private final ConfigManager configManager;

    private final ClientUserManager clientUserManager;

    private final FileManager fileManager;

    public Page<FeedbackBo> listFeedback(FeedbackQueryDto dto) {
        List<Long> userIds = null;
        if (StringUtils.isNotBlank(dto.getPhone())) {
            List<ClientUser> clientUsers = clientUserManager.fetchListByPhone(dto.getPhone());
            if (clientUsers.isEmpty()) {
                return Page.empty();
            }
            userIds = clientUsers.stream().map(ClientUser::getId).toList();
        }

        Page<FeedbackBo> page = configManager.listFeedback(dto.getTitle(), dto.getContent(), userIds, dto).convert(v -> BeanUtils.copy(v, FeedbackBo.class));
        List<Long> userId = page.getRecords().stream().map(FeedbackBo::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userId);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        page.getRecords().forEach(v -> {
            ClientUser clientUser = userMap.get(v.getUserId());
            if (clientUser != null) {
                v.setUserPhone(clientUser.getPhone());
                v.setNickname(clientUser.getNickname());
            }
        });
        return page;
    }

    public BannerBo createBanner(BannerBo bo) {
        BannerInfo bannerInfo = BeanUtils.copy(bo, BannerInfo.class);
        configManager.saveBanner(bannerInfo);
        return BeanUtils.copy(bannerInfo, BannerBo.class);
    }

    public Page<BannerBo> listBanner(BannerAdminQueryDto dto) {
        return configManager.fetchBannerByType(dto.getType(), dto);
    }

    public BannerBo updateBanner(BannerBo bo) {
        BannerInfo db = configManager.getBanner(bo.getId());
        if (db == null) {
            return null;
        }
        if (bo.getContent() != null) {
            db.setContent(bo.getContent());
        }
        if (bo.getLink() != null) {
            db.setLink(bo.getLink());
        }
        if (bo.getImage() != null) {
            db.setImage(bo.getImage());
        }
        if (bo.getTitle() != null) {
            db.setTitle(bo.getTitle());
        }
        if (bo.getType() != null) {
            db.setType(bo.getType());
        }
        if (bo.getParams() != null) {
            db.setParams(bo.getParams());
        }
        if (bo.getSeq() != null) {
            db.setSeq(bo.getSeq());
        }
        if (bo.getStatus() != null) {
            db.setStatus(bo.getStatus());
        }
        configManager.saveBanner(db);
        return BeanUtils.copy(db, BannerBo.class);
    }

    public void deleteBanner(Long id) {
        configManager.deleteBanner(id);
    }

    public List<BaseConfigBo> getAllConfig() {
        List<BaseConfig> allConfig = configManager.fetchBaseConfig();
        return BeanUtils.copyList(allConfig, BaseConfigBo.class);
    }

    @Transactional(rollbackOn = Exception.class)
    public void updateConfig(List<BaseConfigBo> list) {
        List<BaseConfig> allConfig = configManager.fetchBaseConfig();
        Map<String, BaseConfig> configMap = allConfig.stream().collect(Collectors.toMap(BaseConfig::getKey, v -> v));
        for (BaseConfigBo baseConfigBo : list) {
            BaseConfig baseConfig = configMap.get(baseConfigBo.getKey());
            if (baseConfig == null) {
                baseConfig = new BaseConfig();
                baseConfig.setKey(baseConfigBo.getKey());
            }
            baseConfig.setValue(baseConfigBo.getValue());
            configManager.saveBaseConfig(baseConfig);
        }
    }

    public List<BannerBo> getBannerList(BannerTypeEnum type) {
        return configManager.getBannerListByType(type).stream().map(BannerBo::from).toList();
    }

    public void feedback(FeedbackBo dto) {
        Feedback entity = BeanUtils.copy(dto, Feedback.class);
        configManager.saveFeedback(entity);
        List<String> images = dto.getImages();
        for (int i = 0; i < images.size(); i++) {
            String image = images.get(i);
            FileInfo file = fileManager.findByUrl(image);
            if (file != null) {
                file.setSeq(i + 1);
                file.setLink(FileLinkEnum.FEEDBACK);
                file.setOuterId(entity.getId());
                fileManager.save(file);
            }
        }
    }

    public FeedbackBo getFeedback(Long id) {
        Feedback feedback = configManager.getFeedback(id);
        if (feedback == null) {
            return null;
        }
        List<FileInfo> files = fileManager.list(FileLinkEnum.FEEDBACK, feedback.getId());
        List<String> images = files.stream().map(FileInfo::getUrl).toList();
        FeedbackBo bo = BeanUtils.copy(feedback, FeedbackBo.class);
        bo.setImages(images);
        return bo;
    }

}
