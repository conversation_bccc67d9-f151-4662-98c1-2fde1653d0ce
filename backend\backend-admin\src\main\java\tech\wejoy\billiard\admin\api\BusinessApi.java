package tech.wejoy.billiard.admin.api;

import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import tech.wejoy.billiard.admin.bo.AdminUserBo;
import tech.wejoy.billiard.admin.dto.BannerQueryDto;
import tech.wejoy.billiard.admin.service.ConfigService;
import tech.wejoy.billiard.common.bo.BannerBo;
import tech.wejoy.billiard.common.bo.FeedbackBo;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;

import java.util.List;

@Slf4j
@Path("/business")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"business"})
@RequiredArgsConstructor
public class BusinessApi {

    private final ConfigService configService;

    @POST
    @Path("/feedback")
    @Operation(summary = "意见反馈")
    public void feedback(FeedbackBo dto) {
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        dto.setUserId(userId);
        configService.feedback(dto);
    }

    @GET
    @Path("/banner")
    @Operation(summary = "获取banner列表")
    public List<BannerBo> bannerList(BannerQueryDto dto) {
        BannerTypeEnum type = dto.getType();
        if (type == null) {
            type = BannerTypeEnum.BUSINESS;
        }
        return configService.getBannerList(type);
    }

}
