package tech.wejoy.billiard.common.third.aqara.client;

import com.congeer.utils.HttpUtils;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.enterprise.inject.spi.CDI;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.entity.TenantAqaraAccount;
import tech.wejoy.billiard.common.manager.DeviceManager;
import tech.wejoy.billiard.common.third.aqara.request.*;
import tech.wejoy.billiard.common.third.aqara.response.*;
import tech.wejoy.billiard.common.third.aqara.utils.SignUtil;

import java.time.LocalDateTime;
import java.util.*;

@RequiredArgsConstructor
public class AqaraClient {

    private final String appId;

    private final String keyId;

    private final String appKey;

    private synchronized String getAccessToken(TenantAqaraAccount account) {
        if (account.getExpireAt() == null || account.getExpireAt().isBefore(LocalDateTime.now())) {
            TokenResponse token = refreshToken(account.getRefreshToken());
            account.setOpenId(token.getOpenId());
            account.setAccessToken(token.getAccessToken());
            account.setRefreshToken(token.getRefreshToken());
            account.setExpiresIn(token.getExpiresIn());
            account.setExpireAt(LocalDateTime.now().plusSeconds(Integer.parseInt(token.getExpiresIn()) - 60));
            DeviceManager deviceManager = CDI.current().select(DeviceManager.class).get();
            deviceManager.saveAqaraAccount(account);
        }
        return account.getAccessToken();
    }

    public <T> AqaraResponse request(AqaraRequest<T> request) {
        return request(request, null);
    }

    public <T> AqaraResponse request(AqaraRequest<T> request, TenantAqaraAccount account) {
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        if (account != null) {
            headers.put("Accesstoken", getAccessToken(account));
        }
        headers.put("Appid", appId);
        headers.put("Keyid", keyId);
        headers.put("Nonce", UUID.randomUUID().toString().replace("-", ""));
        headers.put("Time", String.valueOf(System.currentTimeMillis()));
        headers.put("Sign", SignUtil.createSign(headers, appKey));
        headers.put("Content-Type", "application/json");

        String json = JsonUtils.toJson(request);
        HttpUtils.HttpResult response = HttpUtils.post("https://open-cn.aqara.com/v3.0/open/api").header(headers).body(json).send();
        return JsonUtils.toObject(response.body(), AqaraResponse.class);
    }

    /**
     * 获取auth_code
     *
     * @param account
     * @param accountType
     */
    public void getAuthCode(String account, Integer accountType) {
        Map<String, Object> data = new HashMap<>();
        data.put("account", account);
        data.put("accountType", accountType);
        data.put("accessTokenValidity", "1y");
        AqaraRequest<Map<String, Object>> req = new AqaraRequest<>("config.auth.getAuthCode", data);
        AqaraResponse response = request(req);
        response.assertSuccess();
    }

    /**
     * 获取用户token
     *
     * @param authCode
     * @param account
     * @param accountType
     * @return
     */
    public TokenResponse getToken(String authCode, String account, Integer accountType) {
        Map<String, Object> data = new HashMap<>();
        data.put("authCode", authCode);
        data.put("account", account);
        data.put("accountType", accountType);
        AqaraRequest<Map<String, Object>> req = new AqaraRequest<>("config.auth.getToken", data);
        AqaraResponse response = request(req);
        return response.getResult(TokenResponse.class);
    }

    /**
     * 获取用户token
     *
     * @param refreshToken
     * @return
     */
    public TokenResponse refreshToken(String refreshToken) {
        Map<String, Object> data = new HashMap<>();
        data.put("refreshToken", refreshToken);
        AqaraRequest<Map<String, Object>> req = new AqaraRequest<>("config.auth.refreshToken", data);
        AqaraResponse response = request(req);
        return response.getResult(TokenResponse.class);
    }

    /**
     * 获取用户设备列表
     *
     * @return
     */
    public List<AqaraDevice> queryDeviceList(TenantAqaraAccount account) {
        AqaraRequest<PageRequest> req = new AqaraRequest<>("query.device.info", new PageRequest());
        AqaraResponse response = request(req, account);
        PageResult<AqaraDevice> page = response.getResult(new TypeReference<>() {
        });
        return page.getData();
    }

    public List<AqaraResource> queryResourceList(TenantAqaraAccount account, String model) {
        ResourceListRequest request = new ResourceListRequest();
        request.setModel(model);
        AqaraRequest<PageRequest> req = new AqaraRequest<>("query.resource.info", request);
        AqaraResponse response = request(req, account);
        return response.getResult(new TypeReference<>() {
        });
    }

    /**
     * 获取设备资源值
     *
     * @param query
     * @return
     */
    public List<ResourceValue> queryResourceValue(TenantAqaraAccount account, ResourceValueQuery... query) {
        ResourceRequest request = new ResourceRequest();
        request.setResources(Arrays.asList(query));
        AqaraRequest<ResourceRequest> req = new AqaraRequest<>("query.resource.value", request);
        AqaraResponse response = request(req, account);
        return response.getResultList(ResourceValue.class);
    }

    /**
     * 设置设备资源值（控制）
     *
     * @param write
     * @return
     */
    public List<ResourceWriteResp> writeResourceValue(TenantAqaraAccount account, ResourceWrite... write) {
        AqaraRequest<List<ResourceWrite>> req = new AqaraRequest<>("write.resource.device", Arrays.asList(write));
        AqaraResponse response = request(req, account);
        return response.getResultList(ResourceWriteResp.class);
    }

}
