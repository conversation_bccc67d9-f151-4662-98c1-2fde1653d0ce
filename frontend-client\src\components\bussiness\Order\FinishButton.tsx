import Taro from "@tarojs/taro";
import Button, { MODES } from "./Button";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import api from "@/api/bussiness";
import { ORDERTYPES } from "@/constants";
interface ICancelButtonProps {
  code: string;
  status: ORDERTYPES;
  isOwner: boolean;
  updateStatus?: () => Promise<void>;
}

interface ICancelButtonRef {
  finish: () => Promise<void>;
}

const FinishButton = forwardRef<ICancelButtonRef, ICancelButtonProps>(
  (props, ref) => {
    const { code, status,isOwner } = props;
    const configuration = {
      buttonText: "结束订单",
      mode: MODES.DANGER,
    };

    const finish = useCallback(async () => {
      const { confirm } = await Taro.showModal({
        title: "提示",
        content: "您当前的操作即将关闭球桌，是否确认",
        confirmText: "确定",
      });
      if (confirm) {
        await api.order.finishOrder(code);
        props.updateStatus && props.updateStatus();
      }
    }, [code, props.updateStatus]);

    useImperativeHandle(
      ref,
      () => {
        return { finish: finish };
      },
      [code]
    );

    if (status === ORDERTYPES.USING && isOwner) {
      return (
        <Button
          mode={configuration.mode}
          buttonText={configuration.buttonText}
          onClick={finish}
        />
      );
    }
    return <></>;
  }
);

export default FinishButton;
