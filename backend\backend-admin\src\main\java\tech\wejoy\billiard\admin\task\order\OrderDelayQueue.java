package tech.wejoy.billiard.admin.task.order;

import com.congeer.utils.JsonUtils;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.admin.service.DeviceService;
import tech.wejoy.billiard.admin.task.order.bo.OrderDelayMessage;
import tech.wejoy.billiard.common.dto.DeviceOperationDto;

import java.util.concurrent.DelayQueue;

@Slf4j
@Singleton
public class OrderDelayQueue {

    @Inject
    DeviceService deviceService;

    private final DelayQueue<OrderDelayMessage> delayQueue = new DelayQueue<>();

    public OrderDelayQueue() {
        Thread thread = new Thread(() -> {
            while (true) {
                try {
                    OrderDelayMessage take = delayQueue.take();
                    DeviceOperationDto dto = new DeviceOperationDto();
                    dto.setOrderNo(take.getOrderNo());
                    dto.setType(take.getType());
                    deviceService.handleOrderDevice(dto);
                    log.info("handle : {}", JsonUtils.toJson(take));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        });
        thread.setDaemon(true);
        thread.start();
    }

    public void offer(OrderDelayMessage message) {
        delayQueue.offer(message);
    }


}
