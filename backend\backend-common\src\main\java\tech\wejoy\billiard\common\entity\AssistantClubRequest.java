package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class AssistantClubRequest extends BaseEntity {

    private Long clubId;

    private Long assistantId;

}
