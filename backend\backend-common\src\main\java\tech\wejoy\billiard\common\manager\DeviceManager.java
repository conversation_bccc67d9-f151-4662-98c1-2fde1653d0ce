package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.web.bean.request.PageRequest;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.dto.DeviceQueryDto;
import tech.wejoy.billiard.common.entity.TenantAqaraAccount;
import tech.wejoy.billiard.common.entity.TenantDevice;
import tech.wejoy.billiard.common.entity.TenantSmyooAccount;
import tech.wejoy.billiard.common.enums.DeviceBrandEnum;
import tech.wejoy.billiard.common.enums.DeviceTypeEnum;
import tech.wejoy.billiard.common.repo.TenantAqaraAccountRepo;
import tech.wejoy.billiard.common.repo.TenantDeviceRepo;
import tech.wejoy.billiard.common.repo.TenantSmyooAccountRepo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class DeviceManager {

    private final TenantAqaraAccountRepo tenantAqaraAccountRepo;

    private final TenantSmyooAccountRepo tenantSmyooAccountRepo;

    private final TenantDeviceRepo tenantDeviceRepo;

    public void saveAqaraAccount(TenantAqaraAccount accountInfo) {
        tenantAqaraAccountRepo.save(accountInfo);
    }

    public List<TenantDevice> getDeviceListByIds(List<Long> deviceIds) {
        return tenantDeviceRepo.find("id in ?1", deviceIds).list();
    }

    public TenantAqaraAccount getAqaraAccount(long accountId) {
        return tenantAqaraAccountRepo.findById(accountId);
    }

    public void saveDevice(TenantDevice device) {
        tenantDeviceRepo.save(device);
    }

    public List<TenantDevice> fetchDeviceByAccountAndBrand(Long accountId, DeviceBrandEnum brand) {
        return tenantDeviceRepo.find("accountId = ?1 and brand = ?2", Sort.by("id"), accountId, brand).list();
    }

    public Page<TenantAqaraAccount> getAqaraAccountList(List<Long> tenantId, String account, PageRequest page) {
        String where = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tenantId)) {
            where += " and tenantId in :tenantId";
            params.put("tenantId", tenantId);
        }
        if (account != null) {
            where += " and account like concat('%', :account, '%')";
            params.put("account", account);
        }
        return tenantAqaraAccountRepo.page(page.toPage(), where, Sort.by("id"), params);
    }

    public Page<TenantDevice> fetchDeviceList(DeviceQueryDto dto) {
        String where = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (dto.getAccountId() != null) {
            where += " and accountId = :accountId";
            params.put("accountId", dto.getAccountId());
        }
        if (dto.getTenantId() != null) {
            where += " and tenantId = :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getBrand() != null) {
            where += " and brand in :brand";
            params.put("brand", dto.getBrand());
        }
        if (dto.getOnline() != null) {
            where += " and online in :online";
            params.put("online", dto.getOnline());
        }
        if (dto.getOpen() != null) {
            where += " and open in :open";
            params.put("open", dto.getOpen());
        }
        if (dto.getType() != null) {
            where += " and type in :type";
            params.put("type", dto.getType());
        }
        return tenantDeviceRepo.page(dto.toPage(), where, Sort.by("id"), params);
    }

    public TenantDevice fetchById(Long id) {
        return tenantDeviceRepo.findById(id);
    }

    public TenantDevice fetchByDeviceIdAndResourceId(String deviceId, String resourceId) {
        return tenantDeviceRepo.find("deviceId = ?1 and resourceId = ?2", deviceId, resourceId).firstResult();
    }

    public TenantAqaraAccount fetchAccountByAccount(String account) {
        return tenantAqaraAccountRepo.find("account = ?1", account).firstResult();
    }

    public void removeDeviceByIds(Set<Long> removeIds) {
        if (CollectionUtils.isEmpty(removeIds)) {
            return;
        }
        tenantDeviceRepo.delete("id in ?1", removeIds);
    }

    public TenantSmyooAccount getSmyooAccount(long accountId) {
        return tenantSmyooAccountRepo.findById(accountId);
    }

    public void saveSmyooAccount(TenantSmyooAccount account) {
        tenantSmyooAccountRepo.save(account);
    }

    public Page<TenantSmyooAccount> getSmyooAccountList(List<Long> tenantId, String phone, PageRequest dto) {
        String where = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(tenantId)) {
            where += " and tenantId in :tenantId";
            params.put("tenantId", tenantId);
        }
        if (phone != null) {
            where += " and phone like concat('%', :phone, '%')";
            params.put("phone", phone);
        }
        return tenantSmyooAccountRepo.page(dto.toPage(), where, Sort.by("id"), params);
    }

    public List<TenantDevice> fetchDeviceByTenantAndBrand(long tenantId, DeviceBrandEnum deviceBrandEnum) {
        return tenantDeviceRepo.find("tenantId = ?1 and brand = ?2", tenantId, deviceBrandEnum).list();
    }

    public List<TenantDevice> fetchByIdsAndType(List<Long> deviceIds, DeviceTypeEnum type) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return List.of();
        }
        return tenantDeviceRepo.find("id in ?1 and type = ?2", deviceIds, type).list();
    }

}
