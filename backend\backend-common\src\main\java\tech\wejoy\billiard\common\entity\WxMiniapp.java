package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class WxMiniapp extends BaseEntity {

    private String name;

    private String appId;

    private String appSecret;

    private String mchId;

    private String partnerMchId;

    private String partnerAppId;

    private String mchKey;

    private String notifyUrl;

    private byte[] certData;

    private byte[] keyData;

}
