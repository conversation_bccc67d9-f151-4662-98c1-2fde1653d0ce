package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.dto.AdminRoleQueryDto;
import tech.wejoy.billiard.common.dto.AdminUserQueryDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.PermissionTypeEnum;
import tech.wejoy.billiard.common.repo.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@Transactional(rollbackOn = Exception.class)
@RequiredArgsConstructor
public class AdminUserManager {

    private final AdminUserRepo adminUserRepo;

    private final AdminRoleRepo adminRoleRepo;

    private final AdminUserRoleRelRepo adminUserRoleRelRepo;

    private final AdminUserClubRelRepo adminUserClubRelRepo;

    private final AdminUserPermissionRelRepo adminUserPermissionRelRepo;

    private final AdminRolePermissionRelRepo adminRolePermissionRelRepo;

    public AdminUser fetchByUsername(String username) {
        return adminUserRepo.find("username", username).firstResult();
    }

    public AdminUser fetchById(Long userId) {
        return adminUserRepo.findById(userId);
    }

    public void save(AdminUser adminUser) {
        adminUserRepo.save(adminUser);
    }

    public AdminUser getUserByOpenId(String openId) {
        return adminUserRepo.find("openId", openId).firstResult();
    }

    public List<AdminUser> getUserByTenantId(Long id) {
        return adminUserRepo.find("tenantId", Sort.by("id"), id).list();
    }

    public List<AdminUser> fetchByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return adminUserRepo.find("id in ?1", ids).list();
    }

    public AdminUser fetchByPhone(String phone) {
        return adminUserRepo.find("phone", phone).firstResult();
    }

    public List<AdminRole> fetchRolesByUserId(Long userId) {
        List<AdminUserRoleRel> relList = adminUserRoleRelRepo.find("userId", userId).list();
        List<String> roles = relList.stream().map(AdminUserRoleRel::getRole).toList();
        return adminRoleRepo.find("code in ?1", roles).list();
    }

    public List<String> fetchPermissionByRoles(List<String> roles) {
        List<AdminRolePermissionRel> relList = adminRolePermissionRelRepo.find("role in ?1", roles).list();
        return relList.stream().map(AdminRolePermissionRel::getPermission).toList();
    }

    public List<String> fetchPermissionByUserId(Long userId, PermissionTypeEnum type) {
        if (type == null) {
            List<AdminUserPermissionRel> relList = adminUserPermissionRelRepo.find("userId", userId).list();
            return relList.stream().map(AdminUserPermissionRel::getPermission).toList();
        }
        List<AdminUserPermissionRel> relList = adminUserPermissionRelRepo.find("userId = ?1 and type = ?2", userId, type).list();
        return relList.stream().map(AdminUserPermissionRel::getPermission).toList();
    }

    public Page<AdminUser> page(AdminUserQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (dto.getTenantId() != null && dto.getTenantId() != 0) {
            query += " and tenantId = :tenantId and clubId is not null and clubId != 0";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getClubId() != null && dto.getClubId() != 0) {
            query += " and clubId = :clubId";
            params.put("clubId", dto.getClubId());
        }
        if (dto.getPhone() != null) {
            query += " and phone like concat('%',:phone,'%')";
            params.put("phone", dto.getPhone());
        }
        return adminUserRepo.page(dto.toPage(), query, Sort.by("id"), params);
    }

    public AdminRole fetchRoleByCode(String code) {
        return adminRoleRepo.find("code", code).firstResult();
    }

    public void saveRole(AdminRole system) {
        adminRoleRepo.save(system);
    }

    public AdminRole fetchRoleById(Long id) {
        return adminRoleRepo.findById(id);
    }

    public void deleteRole(Long id) {
        AdminRole role = adminRoleRepo.findById(id);
        adminRoleRepo.deleteById(id);
        adminUserRoleRelRepo.delete("role", role.getCode());
        adminRolePermissionRelRepo.delete("role", role.getCode());
    }

    public void deleteAdmin(Long id) {
        adminUserRepo.deleteById(id);
        adminUserRoleRelRepo.delete("userId", id);
        adminUserPermissionRelRepo.delete("userId", id);
    }

    public Page<AdminRole> pageRole(AdminRoleQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (dto.getTenantId() != null && dto.getTenantId() != 0) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getType() != null) {
            query += " and type = :type";
            params.put("type", dto.getType());
        }
        return adminRoleRepo.page(dto.toPage(), query, Sort.by("id"), params);
    }

    public void saveRolePermission(String role, List<String> permissions) {
        adminRolePermissionRelRepo.delete("role", role);
        List<AdminRolePermissionRel> relList = permissions.stream().map(v -> {
            AdminRolePermissionRel rel = new AdminRolePermissionRel();
            rel.setRole(role);
            rel.setPermission(v);
            return rel;
        }).toList();
        adminRolePermissionRelRepo.persist(relList);
    }

    public void saveUserRole(Long id, List<String> roles) {
        adminUserRoleRelRepo.delete("userId", id);
        List<AdminUserRoleRel> relList = roles.stream().map(v -> {
            AdminUserRoleRel rel = new AdminUserRoleRel();
            rel.setUserId(id);
            rel.setRole(v);
            return rel;
        }).toList();
        adminUserRoleRelRepo.persist(relList);
    }

    public List<AdminRole> getRoleByIds(List<Long> roles) {
        if (CollectionUtils.isEmpty(roles)) {
            return List.of();
        }
        return adminRoleRepo.find("id in ?1", roles).list();
    }

    public void saveUserRoleRel(AdminUserRoleRel roleRel) {
        adminUserRoleRelRepo.save(roleRel);
    }

    public void saveUserClub(Long id, List<Long> clubIds) {
        adminUserClubRelRepo.delete("userId", id);
        List<AdminUserClubRel> relList = clubIds.stream().map(v -> {
            AdminUserClubRel rel = new AdminUserClubRel();
            rel.setUserId(id);
            rel.setClubId(v);
            return rel;
        }).toList();
        adminUserClubRelRepo.persist(relList);
    }

    public List<Long> fetchClubIdsByUserId(Long userId) {
        List<AdminUserClubRel> relList = adminUserClubRelRepo.find("userId", userId).list();
        return relList.stream().map(AdminUserClubRel::getClubId).toList();
    }

    public List<AdminRole> fetchRolesByUserIds(List<Long> userIds, PermissionTypeEnum permissionTypeEnum) {
        List<AdminUserRoleRel> relList = adminUserRoleRelRepo.find("userId in ?1", userIds).list();
        List<String> roles = relList.stream().map(AdminUserRoleRel::getRole).toList();
        if (permissionTypeEnum == null) {
            return adminRoleRepo.find("code in ?1", roles).list();
        }
        return adminRoleRepo.find("code in ?1 and type = ?2", roles, permissionTypeEnum).list();
    }

    public List<AdminUserRoleRel> fetchUserRoleRelByUserIds(List<Long> userIds) {
        return adminUserRoleRelRepo.find("userId in ?1", userIds).list();
    }

    public List<AdminRole> fetchRolesByTenantId(Long tenantId, PermissionTypeEnum type) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null && tenantId != 0) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", tenantId);
        }
        if (type != null) {
            query += " and type = :type";
            params.put("type", type);
        }
        return adminRoleRepo.find(query, Sort.by("id"), params).list();
    }

    public void deleteUserRoleRelByUserId(Long id) {
        adminUserRoleRelRepo.delete("userId", id);
    }

    public void deleteUserClubRelByUserId(Long id) {
        adminUserClubRelRepo.delete("userId", id);
    }

    public void deleteRolePermission(String code) {
        adminRolePermissionRelRepo.delete("role", code);
    }

    public List<AdminUserRoleRel> fetchUserByRole(String code) {
        return adminUserRoleRelRepo.find("role", code).list();
    }

    public void deleteUserPermissionRelByUserId(Long id) {
        adminUserPermissionRelRepo.delete("userId", id);
    }

}
