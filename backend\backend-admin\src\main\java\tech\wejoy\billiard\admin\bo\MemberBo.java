package tech.wejoy.billiard.admin.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.GenderEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class MemberBo {

    @Schema(description = "用户ID")
    private Long id;

    @Schema(description = "用户手机号")
    private String phone;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像")
    private String avatar;

    @Schema(description = "用户性别")
    private GenderEnum gender;

    @Schema(description = "用户生日")
    private LocalDate birth;

    @Schema(description = "用户余额")
    private BigDecimal balance = BigDecimal.ZERO;

    @Schema(description = "赠送余额")
    private BigDecimal bonus = BigDecimal.ZERO;

    @Schema(description = "用户总充值金额")
    private BigDecimal totalAmount = BigDecimal.ZERO;

    @Schema(description = "用户总消费金额")
    private BigDecimal spentAmount = BigDecimal.ZERO;

    @Schema(description = "用户总消费次数")
    private Integer orderCount;

    @Schema(description = "用户上次消费时间")
    private LocalDateTime lastOrderAt;

}
