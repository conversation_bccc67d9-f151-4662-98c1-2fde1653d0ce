{"version": 3, "file": "app.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;ACrDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/._src_app.tsx", "webpack://frontend-client/./src/app.tsx?c9a4"], "sourcesContent": ["import { store } from \"@/stores\";\nimport { getUpdateManager, showModal, useLaunch } from '@tarojs/taro';\nimport { Provider } from \"react-redux\";\nimport Taro from \"@tarojs/taro\";\nimport { useDidHide, useDidShow } from '@tarojs/taro';\nimport { useEffect } from 'react';\n// 全局样式\nimport './app.css';\nimport useAccount from \"./hooks/useAccount\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction App(_ref) {\n  var children = _ref.children;\n  var _useAccount = useAccount(),\n    getCode = _useAccount.getCode;\n  // 可以使用所有的 React Hooks\n  useEffect(function () {});\n\n  // 对应 onShow\n  useDidShow(function () {});\n\n  // 对应 onHide\n  useDidHide(function () {});\n  useLaunch(function () {\n    if (process.env.TARO_ENV === 'weapp') {\n      // 检测用户是否已经登录过 如果没有登录过前往登录\n      var isLogin = Taro.getStorageSync('isLogin');\n      if (isLogin) {\n        getCode();\n      }\n      var updateManager = getUpdateManager();\n      updateManager.onCheckForUpdate(function (res) {\n        // 请求完新版本信息的回调\n      });\n      updateManager.onUpdateReady(function () {\n        showModal({\n          title: '更新提示',\n          content: '新版本已经准备好，是否重启应用？',\n          success: function success(res) {\n            if (res.confirm) {\n              // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启\n              updateManager.applyUpdate();\n            }\n          }\n        });\n      });\n    }\n  });\n  // children 是将要会渲染的页面\n  return /*#__PURE__*/_jsx(Provider, {\n    store: store,\n    children: children\n  });\n}\nexport default App;", "import '@tarojs/plugin-platform-weapp/dist/runtime'\nimport '@tarojs/plugin-html/dist/runtime'\n\nimport { window } from '@tarojs/runtime'\nimport { createReactApp } from '@tarojs/plugin-framework-react/dist/runtime'\nimport { initPxTransform } from '@tarojs/taro'\n\nimport component from \"!!../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=app!./app.tsx\"\n\nimport * as React from 'react'\nimport ReactDOM from 'react-dom'\n\nvar config = {\"pages\":[\"pages/index/index\",\"pages/venue/index\",\"pages/recharge/index\",\"pages/login/index\",\"pages/order/index\",\"pages/feedback/index\",\"pages/confirmation/index\",\"pages/result/index\",\"pages/settings/index\",\"pages/match/create\",\"pages/match/index\",\"pages/coupons/index\",\"pages/assistant/settings/index\",\"pages/assistant/request/index\",\"pages/assistant/agreement/index\",\"pages/assistant/list/index\",\"pages/assistant/detail/index\",\"pages/assistant/confirmation/index\",\"pages/assistant/club/index\",\"pages/assistant/todo/index\",\"pages/competition/detail/index\",\"pages/competition/rules/index\",\"pages/competition/schedule/index\",\"pages/competition/score/index\",\"pages/competition/live/index\",\"pages/competition/rank/index\"],\"permission\":{\"scope.userLocation\":{\"desc\":\"你的位置信息将用于小程序位置接口的效果展示\"}},\"requiredPrivateInfos\":[\"getLocation\"],\"window\":{\"backgroundTextStyle\":\"light\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTitleText\":\"\",\"navigationBarTextStyle\":\"black\"}};\nwindow.__taroAppConfig = config\nvar inst = App(createReactApp(component, React, ReactDOM, config))\n\ninitPxTransform({\n  designWidth: 750,\n  deviceRatio: {\"640\":1.17,\"750\":1,\"828\":0.905},\n  baseFontSize: 20,\n  unitPrecision: undefined,\n  targetUnit: undefined\n})\n"], "names": [], "sourceRoot": ""}