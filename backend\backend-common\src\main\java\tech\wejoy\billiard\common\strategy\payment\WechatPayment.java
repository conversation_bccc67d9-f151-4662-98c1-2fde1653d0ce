package tech.wejoy.billiard.common.strategy.payment;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.inject.spi.CDI;
import tech.wejoy.billiard.common.bo.PayParamBo;
import tech.wejoy.billiard.common.bo.PayResultBo;
import tech.wejoy.billiard.common.bo.RefundResultBo;
import tech.wejoy.billiard.common.entity.AssistantOrder;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.BillTypeEnum;
import tech.wejoy.billiard.common.enums.ThirdPayTypeEnum;
import tech.wejoy.billiard.common.manager.BillManager;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.service.PaymentService;
import tech.wejoy.billiard.common.strategy.Payment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class WechatPayment extends Payment {

    BillManager billManager;

    ClientUserManager clientUserManager;

    PaymentService paymentService;

    public WechatPayment() {
        this.billManager = CDI.current().select(BillManager.class).get();
        this.clientUserManager = CDI.current().select(ClientUserManager.class).get();
        this.paymentService = CDI.current().select(PaymentService.class).get();
    }

    @Override
    public PayResultBo pay(OrderInfo order, Object params) {
        BillInfo bill = createWxOrder(order);
        PayParamBo payParamBo = paymentService.createPayment(bill.getBillNo(), bill.getAmount(), order.getUserId(), "开台费用");
        PayResultBo payResultBo = new PayResultBo();
        payResultBo.setSuccess(false);
        payResultBo.setAmount(order.getTotalAmount());
        payResultBo.setNeedPay(true);
        payResultBo.setExtra(payParamBo);
        order.setRealAmount(order.getTotalAmount());
        order.setPayAmount(order.getTotalAmount());
        return payResultBo;
    }

    @Override
    public PayResultBo pay(AssistantOrder order, Object params) {
        BillInfo bill = createWxOrder(order);
        PayParamBo payParamBo = paymentService.createPayment(bill.getBillNo(), bill.getAmount(), order.getUserId(), "助教费用");
        PayResultBo payResultBo = new PayResultBo();
        payResultBo.setSuccess(false);
        payResultBo.setAmount(order.getTotalAmount());
        payResultBo.setNeedPay(true);
        payResultBo.setExtra(payParamBo);
        order.setRealAmount(order.getTotalAmount());
        order.setPayAmount(order.getTotalAmount());
        return payResultBo;
    }

    protected BillInfo createWxOrder(OrderInfo order) {
        BillInfo bill = new BillInfo();
        bill.setUserId(order.getUserId());
        bill.setClubId(order.getClubId());
        bill.setTenantId(order.getTenantId());
        bill.setType(BillTypeEnum.ORDER);
        bill.setAmount(order.getTotalAmount());
        bill.setOrderNo(order.getOrderNo());
        bill.setPayInfo(JsonUtils.toJson(order));
        bill.setThirdPayType(ThirdPayTypeEnum.WECHAT);
        billManager.createBill(bill);
        return bill;
    }

    protected BillInfo createWxOrder(AssistantOrder order) {
        BillInfo bill = new BillInfo();
        bill.setUserId(order.getUserId());
        bill.setClubId(order.getClubId());
        bill.setTenantId(order.getTenantId());
        bill.setType(BillTypeEnum.ASSISTANT_ORDER);
        bill.setAmount(order.getTotalAmount());
        bill.setOrderNo(order.getOrderNo());
        bill.setPayInfo(JsonUtils.toJson(order));
        bill.setThirdPayType(ThirdPayTypeEnum.WECHAT);
        billManager.createBill(bill);
        return bill;
    }

    @Override
    public RefundResultBo refund(OrderInfo order, BigDecimal refundAmount) {
        BillInfo billInfo = billManager.fetchByOrderNo(order.getOrderNo());
        RefundResultBo ret = new RefundResultBo();
        if (billInfo == null) {
            ret.setSuccess(false);
            ret.setMessage("账单不存在");
            return ret;
        }
        try {
            String refundResult = paymentService.refund(billInfo.getBillNo(), refundAmount, billInfo.getPayAmount(), "退款");
            ret.setSuccess(true);
            ret.setAmount(refundAmount);
            billInfo.setRefundInfo(refundResult);
        } catch (Exception e) {
            ret.setSuccess(false);
            ret.setMessage("退款失败: " + e.getMessage());
            return ret;
        }
        billInfo.setRefundTime(LocalDateTime.now());
        billInfo.setRefundAmount(refundAmount);
        billManager.save(billInfo);
        return ret;
    }

    @Override
    public RefundResultBo refund(AssistantOrder order, BigDecimal refundAmount) {
        BillInfo billInfo = billManager.fetchByOrderNo(order.getOrderNo());
        RefundResultBo ret = new RefundResultBo();
        if (billInfo == null) {
            ret.setSuccess(false);
            ret.setMessage("账单不存在");
            return ret;
        }
        try {
            String refundResult = paymentService.refund(billInfo.getBillNo(), refundAmount, billInfo.getPayAmount(), "退款");
            ret.setSuccess(true);
            ret.setAmount(refundAmount);
            billInfo.setRefundInfo(refundResult);
        } catch (Exception e) {
            ret.setSuccess(false);
            ret.setMessage("退款失败: " + e.getMessage());
            return ret;
        }
        billInfo.setRefundTime(LocalDateTime.now());
        billInfo.setRefundAmount(refundAmount);
        billManager.save(billInfo);
        return ret;
    }

}
