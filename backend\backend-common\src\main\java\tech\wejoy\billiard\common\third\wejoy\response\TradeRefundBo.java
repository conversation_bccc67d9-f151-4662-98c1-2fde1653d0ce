package tech.wejoy.billiard.common.third.wejoy.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TradeRefundBo {
    private Long id;
    private Long orderId;
    private String orderNo;
    private String refundNo;
    private String thirdRefundNo;
    private BigDecimal refundAmount;
    private String refundReason;
    private LocalDateTime refundTime;
    private String notifyUrl;
    private String resultInfo;
}
