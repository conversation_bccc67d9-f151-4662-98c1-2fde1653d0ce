package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class DealQueryDto extends PageRequest {

    @QueryParam("name")
    private String name;

    @QueryParam("clubId")
    private Long clubId;

    @Schema(hidden = true)
    private List<Long> dealIds;

}
