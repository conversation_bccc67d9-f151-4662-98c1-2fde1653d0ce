package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class PlanGiftRecord extends BaseEntity {

    private Long clubId;

    private Long tenantId;

    private Long operatorId;

    private Long userId;

    private String phone;

    private Long planId;

    private BigDecimal amount;

    private BigDecimal bonusAmount;

    private String remark;

}
