package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.entity.DistrictInfo;
import tech.wejoy.billiard.common.enums.DistrictLevelEnum;

import java.util.List;

@Data
@Schema(description = "行政区域")
public class DistrictBo {

    @Schema(description = "编码")
    private String code;

    @Schema(description = "名称")
    private String name;

    private Double latitude;

    private Double longitude;

    @Schema(description = "级别")
    private DistrictLevelEnum level;

    @Schema(description = "子级")
    private List<DistrictBo> children;

    public static DistrictBo from(DistrictInfo info) {
        if (info == null) {
            return null;
        }
        DistrictBo bo = new DistrictBo();
        bo.setCode(info.getCode());
        bo.setName(info.getName());
        bo.setLatitude(info.getLatitude());
        bo.setLongitude(info.getLongitude());
        bo.setLevel(info.getLevel());
        return bo;
    }

}
