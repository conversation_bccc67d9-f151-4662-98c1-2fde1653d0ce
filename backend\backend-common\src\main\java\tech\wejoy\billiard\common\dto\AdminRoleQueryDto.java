package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tech.wejoy.billiard.common.enums.PermissionTypeEnum;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdminRoleQueryDto extends PageRequest {

    @QueryParam("tenantId")
    private Long tenantId;

    @QueryParam("type")
    private PermissionTypeEnum type;

}
