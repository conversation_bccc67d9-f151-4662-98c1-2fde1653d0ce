"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/order/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/order/index!./src/pages/order/index.tsx":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/order/index!./src/pages/order/index.tsx ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_InfiniteLoading_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/InfiniteLoading/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/InfiniteLoading/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_InfiniteLoading__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/InfiniteLoading */ "./node_modules/@nutui/nutui-react-taro/dist/esm/infiniteloading.taro-6r-XMUlU.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _components_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/card */ "./src/components/card.tsx");
/* harmony import */ var _components_ui_tab__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tab */ "./src/components/ui/tab.tsx");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var _hooks_useOrder__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/useOrder */ "./src/hooks/useOrder.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! clsx */ "./node_modules/clsx/dist/clsx.mjs");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/api/bussiness/assistant */ "./src/api/bussiness/assistant.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");




















var CardItem = function CardItem(props) {
  var item = props.item;
  var statusName = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    var maps = ["支付中", "已支付", "使用中", "已完成", "已取消", "已退款", "已超时"];
    return maps[item.status];
  }, [item]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
    className: "rounded-lg bg-bgt gap-2 flex flex-col p-3 text-sm",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
      className: "flex justify-between pb-1",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.Text, {
        className: "font-semibold",
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().redirectTo({
            url: "/pages/venue/index?id=".concat(item.clubId)
          });
        },
        children: [item.clubName, ">"]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.Text, {
        className: (0,clsx__WEBPACK_IMPORTED_MODULE_13__["default"])({
          "text-white": item.status !== 4,
          "text-muted-foreground": item.status === 4
        }),
        children: statusName
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
      className: "flex",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_card__WEBPACK_IMPORTED_MODULE_2__.Card, {
        className: "rounded-none p-0 text-xs",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
          className: "flex flex-col gap-1 text-muted-foreground ml-2 h-full justify-between",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
            className: "flex items-center gap-1",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
              className: "i-ph-note w-4 h-4"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.Text, {
              children: item.orderNo
            })]
          }), item.tableName && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
            className: "flex items-center gap-1",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
              className: "i-ph-picnic-table w-4 h-4"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.Text, {
              children: item.tableName
            })]
          }), item.assistantName && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
            className: "flex items-center gap-1",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
              className: "i-ph-picnic-table w-4 h-4"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.Text, {
              children: item.assistantName
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
            className: "flex items-center gap-1",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
              className: "i-ph-clock w-4 h-4"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.Text, {
              children: [dayjs__WEBPACK_IMPORTED_MODULE_8___default()(item.startTime).format("MM-DD HH:mm"), " ~", " ", dayjs__WEBPACK_IMPORTED_MODULE_8___default()(item.endTime).format("MM-DD HH:mm")]
            })]
          })]
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
        className: "flex text-primary items-end",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
          className: "text-_0d75rem_",
          children: "\xA5"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
          className: "",
          children: (0,_utils__WEBPACK_IMPORTED_MODULE_6__.numberFixed)(item.totalAmount)
        })]
      })]
    })]
  });
};
var AssistantList = function AssistantList() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)([]),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_14__["default"])(_useState, 2),
    list = _useState2[0],
    setList = _useState2[1];
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_6__.menuRect)();
  var pagination = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)({
    size: 30,
    current: 0
  });
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(-1),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_14__["default"])(_useState3, 2),
    total = _useState4[0],
    setTotal = _useState4[1];
  var getList = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_15__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().mark(function _callee(initialize) {
      var _pagination$current, size, current, params, _ref2, _ref2$data, records, total;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _pagination$current = pagination.current, size = _pagination$current.size, current = _pagination$current.current;
            params = JSON.parse(JSON.stringify({
              size: size,
              current: current
            }));
            _context.next = 4;
            return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__["default"].getOrderList(params);
          case 4:
            _ref2 = _context.sent;
            _ref2$data = _ref2.data;
            records = _ref2$data.records;
            total = _ref2$data.total;
            setList(function (items) {
              if (initialize) return records;
              return [].concat((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_17__["default"])(items), (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_17__["default"])(records));
            });
            setTotal(total);
          case 10:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [setList, setTotal]);
  var onLoad = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_15__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().mark(function _callee2() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          pagination.current.current++;
          _context2.next = 3;
          return getList();
        case 3:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [getList]);
  var hasMore = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    if (total < 0) return true;
    return list.length < total && total > 0;
  }, [list, total]);
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    pagination.current.current = 0;
    getList(true);
  }, []);
  var onRefresh = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_15__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().mark(function _callee3() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          pagination.current.current = 0;
          getList(true);
        case 2:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  })), [getList]);
  var navigate = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(function (id) {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().navigateTo({
      url: "/pages/assistant/confirmation/index?orderId=".concat(id)
    });
  }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
    id: "target",
    style: {
      height: "calc(100vh - 44PX - ".concat(rect.bottom + 5, "PX - env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1))")
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_nutui_nutui_react_taro_dist_esm_InfiniteLoading__WEBPACK_IMPORTED_MODULE_18__.I, {
      loadMoreText: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
        className: "pt-2 pb-3",
        children: "\u6CA1\u6709\u66F4\u591A\u4E86"
      }),
      onLoadMore: onLoad,
      hasMore: hasMore,
      loadingText: "loading",
      target: "target",
      pullRefresh: true,
      onRefresh: onRefresh,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
        className: "flex-col flex gap-3 text-white px-3 py-2",
        children: list.map(function (item) {
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
            onClick: function onClick() {
              return navigate(item.orderNo);
            },
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(CardItem, {
              item: item
            })
          });
        })
      })
    })
  });
};
var OrderList = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_9__.memo)(function () {
  var _useOrder = (0,_hooks_useOrder__WEBPACK_IMPORTED_MODULE_5__["default"])({
      current: 0,
      size: 10
    }),
    setParams = _useOrder.setParams,
    params = _useOrder.params,
    list = _useOrder.list,
    getList = _useOrder.getList,
    setList = _useOrder.setList,
    total = _useOrder.total;
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_6__.menuRect)();
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_14__["default"])(_useState5, 2),
    count = _useState6[0],
    setCount = _useState6[1];
  (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_7__.useDidShow)(function () {
    if (count > 0) {
      onRefresh();
    }
    setCount(function (count) {
      return count + 1;
    });
  });
  var onLoad = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_15__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().mark(function _callee4() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          setParams(function (params) {
            return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_19__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_19__["default"])({}, params), {}, {
              current: params.current + 1
            });
          });
        case 1:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  })), [getList, setParams]);
  var onRefresh = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_15__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().mark(function _callee5() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_16__["default"])().wrap(function _callee5$(_context5) {
      while (1) switch (_context5.prev = _context5.next) {
        case 0:
          setList([]);
          setParams(function (params) {
            return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_19__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_19__["default"])({}, params), {}, {
              current: 0
            });
          });
        case 2:
        case "end":
          return _context5.stop();
      }
    }, _callee5);
  })), [setList, setParams, getList, params]);
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    getList();
  }, [params]);
  var hasMore = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    return total > list.length;
  }, [total, list]);
  var navigate = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(function (item) {
    if ([_constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.USING, _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.PAID].includes(item.status)) {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().navigateTo({
        url: "/pages/confirmation/index?id=".concat(item.tableId)
      });
    }
  }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
    id: "target",
    style: {
      height: "calc(100vh - 44PX - ".concat(rect.bottom + 5, "PX - env(safe-area-inset-bottom) * var(--nutui-safe-area-multiple, 1))")
    },
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_nutui_nutui_react_taro_dist_esm_InfiniteLoading__WEBPACK_IMPORTED_MODULE_18__.I, {
      loadMoreText: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
        className: "pt-2 pb-3",
        children: "\u6CA1\u6709\u66F4\u591A\u4E86"
      }),
      onLoadMore: onLoad,
      onRefresh: onRefresh,
      hasMore: hasMore,
      loadingText: "loading",
      target: "target",
      pullRefresh: true,
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
        className: "flex-col flex gap-3 text-white px-3 py-2",
        children: list.map(function (item) {
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
            onClick: function onClick() {
              return navigate(item);
            },
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(CardItem, {
              item: item
            })
          });
        })
      })
    })
  });
});
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_6__.menuRect)();
  var router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_7__.useRouter)();
  var tabs = [{
    title: "我的订单",
    component: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(OrderList, {})
  }, {
    title: "助教订单",
    component: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(AssistantList, {})
  }];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(function () {
      var _router$params;
      return Number(((_router$params = router.params) === null || _router$params === void 0 ? void 0 : _router$params.tab) || 0);
    }),
    _useState8 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_14__["default"])(_useState7, 2),
    current = _useState8[0],
    setCurrent = _useState8[1];
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_12__.View, {
    style: {
      paddingTop: rect.bottom + 5
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_1__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_ui_tab__WEBPACK_IMPORTED_MODULE_3__["default"], {
      route: tabs,
      current: current,
      onChange: function onChange(value) {
        return setCurrent(value);
      },
      tabClass: "flex-1 text-center"
    })]
  });
});

/***/ }),

/***/ "./src/hooks/useOrder.ts":
/*!*******************************!*\
  !*** ./src/hooks/useOrder.ts ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);






/* harmony default export */ __webpack_exports__["default"] = (function (data) {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
      return data;
    }),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__["default"])(_useState, 2),
    params = _useState2[0],
    setParams = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__["default"])(_useState3, 2),
    list = _useState4[0],
    setList = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__["default"])(_useState5, 2),
    loading = _useState6[0],
    setLoading = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),
    _useState8 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_2__["default"])(_useState7, 2),
    total = _useState8[0],
    setTotal = _useState8[1];
  var changeList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (data) {
    setList(function () {
      var items = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
      return [].concat((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_3__["default"])(items), (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_3__["default"])(data));
    });
  }, [setList]);
  var getList = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_4__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_5__["default"])().mark(function _callee() {
    var _yield$api$order$getL, _yield$api$order$getL2, records, total;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_5__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setLoading(true);
          _context.next = 3;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_0__["default"].order.getList(params);
        case 3:
          _yield$api$order$getL = _context.sent;
          _yield$api$order$getL2 = _yield$api$order$getL.data;
          records = _yield$api$order$getL2.records;
          total = _yield$api$order$getL2.total;
          setLoading(false);
          changeList(records);
          setTotal(total);
        case 10:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [setList, setLoading, params, setTotal]);
  return {
    list: list,
    setList: setList,
    getList: getList,
    loading: loading,
    setParams: setParams,
    params: params,
    total: total
  };
});

/***/ }),

/***/ "./src/pages/order/index.tsx":
/*!***********************************!*\
  !*** ./src/pages/order/index.tsx ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_order_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/order/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/order/index!./src/pages/order/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_order_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/order/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_order_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_order_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_order_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_order_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/order/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map