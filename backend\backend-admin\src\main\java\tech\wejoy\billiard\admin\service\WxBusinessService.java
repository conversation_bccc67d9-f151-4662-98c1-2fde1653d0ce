package tech.wejoy.billiard.admin.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.congeer.core.exception.BaseException;
import com.congeer.security.core.bean.SessionInfo;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.JsonUtils;
import com.congeer.web.util.WebUtils;
import io.vertx.core.http.HttpServerRequest;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Context;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.admin.bo.AdminUserBo;
import tech.wejoy.billiard.admin.bo.WxLoginBo;
import tech.wejoy.billiard.common.entity.AdminUser;
import tech.wejoy.billiard.common.entity.TenantInfo;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;

import java.io.File;
import java.time.LocalDateTime;

@ApplicationScoped
@Slf4j
public class WxBusinessService {

    @Inject
    AdminUserService userService;

    @Inject
    TenantService tenantService;

    @Context
    HttpServerRequest request;

    @Transactional(rollbackOn = Exception.class)
    public WxLoginBo login(String code, String bind, String username, String password) {
        String sessionKey = null;
        String openId = null;
        AdminUserBo bo;
        if (code != null) {
            try {
                WxMaJscode2SessionResult result = ThirdServiceHolder.wxMaService("business").getUserService().getSessionInfo(code);
                sessionKey = result.getSessionKey();
                openId = result.getOpenid();
            } catch (Exception e) {
                log.error("获取sessionKey失败:{}", e.getMessage(), e);
            }

            if (sessionKey == null || openId == null) {
                throw new BaseException("获取sessionKey失败");
            }
            AdminUser user = userService.getUserByOpenId(openId);

            if (user != null) {
                // 更新用户信息
                user.setLastLoginIp(WebUtils.getIpAddr(request));
                user.setLastLoginAt(LocalDateTime.now());
            } else if (StringUtils.isNotBlank(bind)) {
                user = tenantService.bindAccount(bind, openId);
                user.setLastLoginIp(WebUtils.getIpAddr(request));
                user.setLastLoginAt(LocalDateTime.now());
            } else if (StringUtils.isNotBlank(username) && StringUtils.isNotBlank(password)) {
                user = tenantService.bindAccount(username, password, openId);
                user.setLastLoginIp(WebUtils.getIpAddr(request));
                user.setLastLoginAt(LocalDateTime.now());
            } else {
                throw new BaseException("2001", "用户不存在");
            }
            userService.save(user);
            bo = userService.loginByOpenId(openId);
        } else {
            if (StringUtils.isBlank(username) || StringUtils.isBlank(password)) {
                throw new BaseException("参数异常");
            }
            bo = userService.fetchLoginByUsername(username);
            if (bo == null) {
                throw new BaseException("登录失败");
            }
            String encryptPassword = SecurityHolder.context().encryptPassword(password);
            if (!encryptPassword.equals(bo.getPassword())) {
                throw new BaseException("登录失败");
            }
            if (bo.getTenantId() != 0L && bo.getClubId() != 0L) {
                throw new BaseException("登录失败");
            }
        }
        SessionInfo<Long, AdminUserBo> session = SecurityHolder.context("business").login(bo);
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        WxLoginBo dto = new WxLoginBo();
        dto.setToken(session.getToken());
        dto.setTenantId(bo.getTenantId());
        if (bo.getTenantId() != null && bo.getTenantId() != 0L) {
            TenantInfo tenant = tenantService.getTenantById(bo.getTenantId());
            dto.setTenantName(tenant.getName());
        } else {
            dto.setTenantName("猩猩球社总店");
        }
        dto.setHasPhone(StringUtils.isNotBlank(bo.getPhone()));
        dto.setRoles(session.getUser().getRoles());
        dto.setPermissions(session.getUser().getPermissions());
        return dto;
    }

    @Transactional(rollbackOn = Exception.class)
    public void updatePhone(String code) {
        SessionInfo<Long, AdminUserBo> sessionInfo = SecurityHolder.session();
        AdminUserBo user = sessionInfo.getUser();
        AdminUser userById = userService.getUserById(user.getId());
        String phoneNumber = null;
        try {
            WxMaPhoneNumberInfo phoneNoInfo = ThirdServiceHolder.wxMaService("business").getUserService().getPhoneNoInfo(code);
            phoneNumber = phoneNoInfo.getPhoneNumber();
        } catch (Exception e) {
            log.error("获取手机号失败:{}", e.getMessage(), e);
        }
        if (phoneNumber == null) {
            throw new BaseException("获取手机号失败");
        }
        AdminUser u = userService.fetchByPhone(phoneNumber);
        if (u != null) {
            throw new BaseException("手机号已被绑定");
        }
        userById.setPhone(phoneNumber);
        userService.save(userById);
    }

    public File getBindQRCode(String code) {
        try {
            return ThirdServiceHolder.wxMaService("business").getQrcodeService().createWxaCode("pages/bind/index?code=" + code);
        } catch (WxErrorException e) {
            log.error("获取二维码失败:{}", e.getMessage(), e);
            throw new BaseException("获取二维码失败");
        }
    }

    public File getTableQRCode(long tableId) {
        try {
            return ThirdServiceHolder.wxMaService("user").getQrcodeService().createQrcode("pages/confirmation/index?scene=1011&id=" + tableId);
        } catch (WxErrorException e) {
            log.error("获取二维码失败:{}", e.getMessage(), e);
            throw new BaseException("获取二维码失败");
        }
    }

    public byte[] getTableQRCodeBytes(long tableId) {
        try {
            return ThirdServiceHolder.wxMaService("user").getQrcodeService().createQrcodeBytes("pages/confirmation/index?scene=1011&id=" + tableId, 430);
        } catch (WxErrorException e) {
            log.error("获取二维码失败:{}", e.getMessage(), e);
            throw new BaseException("获取二维码失败");
        }
    }

}
