package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.admin.bo.*;
import tech.wejoy.billiard.admin.dto.*;
import tech.wejoy.billiard.admin.service.DeviceService;
import tech.wejoy.billiard.common.dto.DeviceQueryDto;
import tech.wejoy.billiard.common.enums.DeviceBrandEnum;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;
import tech.wejoy.billiard.common.third.aqara.client.AqaraClient;

import java.util.List;

@Path("/device")
@Tag(name = "设备")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class DeviceApi {

    private final DeviceService deviceService;

    @POST
    @Path("/aqara/bind/auth")
    @Operation(summary = "绑定Aqara账号授权码")
    public void bindAqaraAuth(BindAqaraDto dto) {
        AqaraClient base = ThirdServiceHolder.aqaraClient("base");
        base.getAuthCode(dto.getAccount(), 0);
    }

    @POST
    @Path("/aqara/bind")
    @Operation(summary = "绑定Aqara账号")
    public void bindAqara(BindAqaraDto dto) {
        deviceService.bindAqara(dto.getTenantId(), dto.getAccount(), dto.getCode());
    }

    @GET
    @Path("/aqara/list")
    @Operation(summary = "Aqara账号列表")
    public Page<AqaraAccountBo> aqaraAccountList(@BeanParam AqaraAccountQueryDto dto) {
        return deviceService.aqaraAccountList(dto);
    }

    @POST
    @Path("/aqara/{id}/refresh")
    @Operation(summary = "刷新Aqara账号设备")
    public void refreshAqara(@PathParam("id") Long id) {
        deviceService.refreshAqaraDevice(id);
    }

    @GET
    @Path("/list")
    @Operation(summary = "设备列表")
    public Page<DeviceBo> list(@BeanParam DeviceQueryDto dto) {
        return deviceService.list(dto);
    }

    @GET
    @Path("/list/by/account")
    @Operation(summary = "根据账号获取设备列表")
    public List<DeviceBo> listByAccount(@QueryParam("accountId") Long accountId, @QueryParam("brand") DeviceBrandEnum brand) {
        return deviceService.listByAccount(accountId, brand);
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "设备详情")
    public DeviceBo detail(@PathParam("id") Long id) {
        return deviceService.detail(id);
    }

    @POST
    @Path("/{id}/open")
    @Operation(summary = "更新设备")
    public void open(@PathParam("id") Long id, DeviceOperateDto dto) {
        dto.setId(id);
        deviceService.open(dto);
    }

    @GET
    @Path("/smyoo/list")
    @Operation(summary = "Aqara账号列表")
    public Page<SmyooAccountBo> smyooAccountList(@BeanParam SmyooAccountQueryDto dto) {
        return deviceService.smyooAccountList(dto);
    }

    @POST
    @Path("/smyoo/bind")
    @Operation(summary = "绑定Smyoo账号")
    public void bindSmyoo(BindSmyooDto dto) {
        deviceService.bindSmyoo(dto.getTenantId(), dto.getPhone(), dto.getPassword());
    }

    @POST
    @Path("/smyoo/{id}/password")
    @Operation(summary = "绑定Smyoo账号")
    public void changePassword(@PathParam("id") Long id, ChangeSmyooPasswordDto dto) {
        dto.setId(id);
        deviceService.changeSmyooPassword(dto);
    }

    @POST
    @Path("/smyoo/{id}/refresh")
    @Operation(summary = "刷新Smyoo账号设备")
    public void refreshSmyoo(@PathParam("id") Long id) {
        deviceService.refreshSmyooDevice(id);
    }

    @GET
    @Path("/club/air/list")
    @Operation(summary = "获取门店空调设备列表")
    public List<DeviceBo> clubAirList(@QueryParam("clubId") Long clubId) {
        return deviceService.clubAirList(clubId);
    }

    @GET
    @Path("/club/air/bind/list")
    @Operation(summary = "获取门店空调设备列表")
    public List<ClubDeviceBindBo> clubAirBindList(@QueryParam("clubId") Long clubId) {
        return deviceService.clubAirBindList(clubId);
    }

    @GET
    @Path("/club/air/{id}")
    @Operation(summary = "获取门店空调设备详情")
    public SmyooAirDetailBo clubAirDetail(@PathParam("id") Long id) {
        return deviceService.clubAirDetail(id);
    }

    @POST
    @Path("/club/air/{id}")
    @Operation(summary = "更新门店空调设备")
    public void clubAirOperate(@PathParam("id") Long id, SmyooAirDetailBo dto) {
        dto.setId(id);
        deviceService.clubAirOperate(dto);
    }

    @POST
    @Path("/table/bind/change")
    @Operation(summary = "更换桌台绑定设备")
    public void tableBindChange(TableBindChangeDto dto) {
        deviceService.tableBindChange(dto);
    }

}
