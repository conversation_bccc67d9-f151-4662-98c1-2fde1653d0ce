"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/competition/live/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/live/index!./src/pages/competition/live/index.tsx":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/live/index!./src/pages/competition/live/index.tsx ***!
  \**************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/bussiness/competition */ "./src/api/bussiness/competition.ts");
/* harmony import */ var _components_bussiness_Title__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/bussiness/Title */ "./src/components/bussiness/Title/index.tsx");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _components_ui_tab__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tab */ "./src/components/ui/tab.tsx");
/* harmony import */ var _components_bussiness_Match__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/bussiness/Match */ "./src/components/bussiness/Match/index.tsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");














var STATUS_TYPES = /*#__PURE__*/function (STATUS_TYPES) {
  STATUS_TYPES[STATUS_TYPES["NONE"] = 0] = "NONE";
  STATUS_TYPES[STATUS_TYPES["WAITING"] = 1] = "WAITING";
  STATUS_TYPES[STATUS_TYPES["ONGOING"] = 2] = "ONGOING";
  STATUS_TYPES[STATUS_TYPES["FINISHED"] = 3] = "FINISHED";
  return STATUS_TYPES;
}(STATUS_TYPES || {});
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.menuRect)();
  var router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__.useRouter)();
  var id = router.params.id;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_9__["default"])(_useState, 2),
    detailData = _useState2[0],
    setDetailData = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(1),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_9__["default"])(_useState3, 2),
    current = _useState4[0],
    setCurrent = _useState4[1];
  var getCompetition = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().mark(function _callee() {
    var _yield$competition$ge, data;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_0__["default"].getDetail(id);
        case 2:
          _yield$competition$ge = _context.sent;
          data = _yield$competition$ge.data;
          setDetailData(data);
          return _context.abrupt("return", data);
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [setDetailData]);
  var getPlayers = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function (items) {
    return items.reduce(function (result, item) {
      var player1UserId = item.player1UserId,
        player1Name = item.player1Name,
        player1Number = item.player1Number,
        player2Number = item.player2Number,
        player2Name = item.player2Name,
        player2UserId = item.player2UserId,
        player2Score = item.player2Score,
        player1Score = item.player1Score,
        winner = item.winner;
      return [].concat((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_12__["default"])(result), [{
        player: {
          nickname: player1Name,
          userId: player1UserId,
          number: player1Number
        },
        score: player1Score,
        winner: winner
      }, {
        player: {
          userId: player2UserId,
          nickname: player2Name,
          number: player2Number
        },
        score: player2Score,
        winner: winner
      }]);
    }, []);
  }, []);
  var schedulesMatches = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    var _detailData$schedules;
    return (detailData === null || detailData === void 0 || (_detailData$schedules = detailData.schedules) === null || _detailData$schedules === void 0 ? void 0 : _detailData$schedules.map(function (schedule) {
      var _detailData$matches;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_13__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_13__["default"])({}, schedule), {}, {
        matches: (0,_utils__WEBPACK_IMPORTED_MODULE_3__.chunk)(getPlayers(detailData === null || detailData === void 0 || (_detailData$matches = detailData.matches) === null || _detailData$matches === void 0 ? void 0 : _detailData$matches.filter(function (match) {
          return match.round === schedule.round;
        })), 2)
      });
    })) || [];
  }, [detailData, getPlayers]);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    getCompetition();
  }, []);
  var tabs = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    var filterSchedulesMatches = schedulesMatches.filter(function (item) {
      return [STATUS_TYPES.ONGOING, STATUS_TYPES.FINISHED].includes(item.status);
    });
    return filterSchedulesMatches.map(function (schedule) {
      console.log(schedule.matches);
      return {
        title: schedule.title,
        route: "",
        component: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
          className: "flex flex-col gap-3 px-3",
          children: schedule.matches.map(function (players) {
            var winnerIds = players.map(function (item) {
              return item.winner;
            });
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_components_bussiness_Match__WEBPACK_IMPORTED_MODULE_7__["default"], {
                players: players,
                isLastMatch: true,
                resultIds: winnerIds,
                noNum: true
              })
            });
          })
        })
      };
    });
  }, [schedulesMatches]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)("div", {
    className: "flex flex-col h-_100vh_",
    style: {
      paddingTop: rect.bottom + 10
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_2__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_components_bussiness_Title__WEBPACK_IMPORTED_MODULE_1__["default"], {
      name: "\u6BD4\u5206\u76F4\u64AD"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_components_ui_tab__WEBPACK_IMPORTED_MODULE_6__["default"], {
      route: tabs,
      current: current,
      onChange: function onChange(value) {
        return setCurrent(value);
      }
    })]
  });
});

/***/ }),

/***/ "./src/pages/competition/live/index.tsx":
/*!**********************************************!*\
  !*** ./src/pages/competition/live/index.tsx ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_live_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/live/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/live/index!./src/pages/competition/live/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white","disableScroll":true};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_live_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/competition/live/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_live_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_live_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_live_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_live_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/competition/live/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map