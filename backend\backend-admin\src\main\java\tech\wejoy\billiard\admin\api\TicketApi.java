package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.common.bo.DouyinDealBo;
import tech.wejoy.billiard.common.bo.DouyinShopBo;
import tech.wejoy.billiard.common.bo.MeituanDealBo;
import tech.wejoy.billiard.common.bo.MeituanShopBo;
import tech.wejoy.billiard.common.dto.ChannelDealDto;
import tech.wejoy.billiard.common.dto.DealQueryDto;
import tech.wejoy.billiard.common.dto.ShopQueryDto;
import tech.wejoy.billiard.common.service.TicketService;

@Path("/ticket")
@Tag(name = "渠道券")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class TicketApi {

    private final TicketService ticketService;

    @GET
    @Path("/meituan/shop/list")
    @Operation(summary = "美团门店列表")
    public Page<MeituanShopBo> listMeituanShop(@BeanParam ShopQueryDto dto) {
        return ticketService.listMeituanShop(dto);
    }

//    @POST
//    @Path("/meituan/{id}/refresh")
//    @Operation(summary = "刷新美团门店")
//    public void refreshMeituanShop(@PathParam("id") Long accountId) {
//        ticketService.refreshMeituanShop(accountId);
//    }

    @GET
    @Path("/meituan/auth-url")
    @Operation(summary = "获取美团授权URL")
    public String getMeituanAuthUrl() {
        return ticketService.generateMeituanAuthUrl();
    }

    @POST
    @Path("/meituan/bind-auth")
    @Operation(summary = "绑定美团授权")
    public void bindMeituanAuth(
            @QueryParam("code") String code,
            @QueryParam("state") String state,
            @QueryParam("businessId") String businessId) {
        ticketService.handleMeituanAuth(code, businessId, state);
    }

    @POST
    @Path("/meituan/shop/{shopId}/bind/{clubId}")
    @Operation(summary = "绑定美团门店")
    public void bindMeituanShop(@PathParam("shopId") Long shopId, @PathParam("clubId") Long clubId) {
        ticketService.bindMeituanShop(shopId, clubId);
    }

    @POST
    @Path("/meituan/shop/{shopId}/unbind")
    @Operation(summary = "解绑美团门店")
    public void unbindMeituanShop(@PathParam("shopId") Long shopId) {
        ticketService.unbindMeituanShop(shopId);
    }

    @POST
    @Path("/meituan/shop/{shopId}/refresh")
    @Operation(summary = "刷新美团门店券")
    public void refreshMeituanShopDeal(@PathParam("shopId") Long shopId) {
        ticketService.refreshMeituanShopDeal(shopId);
    }

    @DELETE
    @Path("/meituan/shop/{shopId}")
    @Operation(summary = "删除美团门店")
    public void deleteMeituanShop(@PathParam("shopId") Long shopId) {
        ticketService.deleteMeituanShop(shopId);
    }

    @GET
    @Path("/douyin/shop/list")
    @Operation(summary = "抖音门店列表")
    public Page<DouyinShopBo> listDouyinShop(@BeanParam ShopQueryDto dto) {
        return ticketService.listDouyinShop(dto);
    }

    @POST
    @Path("/douyin/{id}/refresh")
    @Operation(summary = "刷新抖音门店")
    public void refreshDouyinShop(@PathParam("id") Long accountId) {
        ticketService.refreshDouyinShop(accountId);
        ticketService.refreshDouyinDeal(accountId);
    }

    @POST
    @Path("/douyin/shop/{shopId}/bind/{clubId}")
    @Operation(summary = "绑定抖音门店")
    public void bindDouyinShop(@PathParam("shopId") Long shopId, @PathParam("clubId") Long clubId) {
        ticketService.bindDouyinShop(shopId, clubId);
    }

    @POST
    @Path("/douyin/shop/{shopId}/unbind")
    @Operation(summary = "解绑抖音门店")
    public void unbindDouyinShop(@PathParam("shopId") Long shopId) {
        ticketService.unbindDouyinShop(shopId);
    }

    @GET
    @Path("/meituan/deal/list")
    @Operation(summary = "美团券列表")
    public Page<MeituanDealBo> listMeituanDeal(@BeanParam DealQueryDto dto) {
        return ticketService.listMeituanDeal(dto);
    }

    @GET
    @Path("/douyin/deal/list")
    @Operation(summary = "抖音券列表")
    public Page<DouyinDealBo> listDouyinDeal(@BeanParam DealQueryDto dto) {
        return ticketService.listDouyinDeal(dto);
    }

    @POST
    @Path("/meituan/deal/{id}/config")
    @Operation(summary = "配置美团券")
    public void configMeituanDeal(@PathParam("id") Long id, ChannelDealDto dto) {
        dto.setId(id);
        ticketService.configMeituanDeal(dto);
    }

    @GET
    @Path("/meituan/deal/{id}")
    @Operation(summary = "获取美团券")
    public MeituanDealBo getMeituanDeal(@PathParam("id") Long id) {
        return ticketService.getMeituanDealInfo(id);
    }

    @POST
    @Path("/douyin/deal/{id}/config")
    @Operation(summary = "配置抖音券")
    public void configDouyinDeal(@PathParam("id") Long id, ChannelDealDto dto) {
        dto.setId(id);
        ticketService.configDouyinDeal(dto);
    }

    @GET
    @Path("/douyin/deal/{id}")
    @Operation(summary = "获取抖音券")
    public DouyinDealBo getDouyinDeal(@PathParam("id") Long id) {
        return ticketService.getDouyinDealInfo(id);
    }

}
