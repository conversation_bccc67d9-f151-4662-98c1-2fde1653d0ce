package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.utils.BeanUtils;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.ClubUserOrderCountBo;
import tech.wejoy.billiard.common.bo.OrderBo;
import tech.wejoy.billiard.common.bo.UserStatsBo;
import tech.wejoy.billiard.common.dto.AdminOrderQueryDto;
import tech.wejoy.billiard.common.dto.OrderQueryDto;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.repo.OrderInfoRepo;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@Transactional(rollbackOn = Exception.class)
@RequiredArgsConstructor
public class OrderManager {

    private final OrderInfoRepo orderInfoRepo;

    public Page<OrderBo> page(OrderQueryDto dto) {
        Map<String, Object> map = BeanUtils.toMap(dto, "userId", "status", "clubId");
        String query = "userId = :userId";
        if (dto.getStatus() != null) {
            query += " and status = :status";
        }
        if (dto.getClubId() != null) {
            query += " and clubId = :clubId";
        }
        Page<OrderInfo> page = orderInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), map);
        return page.convert(v -> BeanUtils.copy(v, OrderBo.class));
    }

    public OrderInfo fetchUsingOrderByTableId(Long tableId) {
        return orderInfoRepo.find("tableId = ?1 and status in ?2", tableId, OrderStatusEnum.usingStatus).firstResult();
    }

    public List<OrderInfo> fetchUsingOrderByTableIds(List<Long> tableIds) {
        if (tableIds == null || tableIds.isEmpty()) {
            return List.of();
        }
        return orderInfoRepo.find("tableId in ?1 and status in ?2", tableIds, OrderStatusEnum.usingStatus).list();
    }

    public OrderInfo fetchOrderByNo(String orderNo) {
        return orderInfoRepo.find("orderNo", orderNo).firstResult();
    }

    public String generateOrderNo(OrderInfo orderInfo) {
        String clubId = (orderInfo.getClubId() + 30105) + "";
        String tableId = (orderInfo.getTableId() + 602040) + "";
        String orderId = (orderInfo.getId() + 12150000) + "";
        return "T" + clubId + tableId + orderId;
    }

    public void save(OrderInfo order) {
        orderInfoRepo.save(order);
    }

    public void delete(OrderInfo order) {
        orderInfoRepo.delete(order);
    }

    public List<OrderInfo> getOrderByTableId(Long tableId) {
        return orderInfoRepo.find("tableId = ?1 and status in ?2", Sort.by("startTime"), tableId, OrderStatusEnum.usingStatus).list();
    }

    public List<OrderInfo> fetchOrderByTableId(Long tableId) {
        return orderInfoRepo.find("tableId = ?1", tableId).list();
    }

    public List<OrderInfo> fetchOrderByStatus(OrderStatusEnum status) {
        return orderInfoRepo.find("status", status).list();
    }

    public List<OrderInfo> fetchFinishedOrderByClubAndDate(String orderNo, Long tenantId, Long clubId, LocalDate startDate, LocalDate endDate) {
        LocalDateTime start = startDate.atStartOfDay();
        LocalDateTime end = endDate.plusDays(1).atStartOfDay();
        Map<String, Object> map = new HashMap<>();
        map.put("start", start);
        map.put("end", end);
        map.put("status", OrderStatusEnum.FINISH);
        String query = "realEndTime >= :start and realEndTime < :end and status = :status";
        if (StringUtils.isNotBlank(orderNo)) {
            query += " and orderNo = :orderNo";
            map.put("orderNo", orderNo);
        }
        if (tenantId != null && tenantId != 0L) {
            query += " and tenantId = :tenantId";
            map.put("tenantId", tenantId);
        }
        if (clubId != null && clubId != 0L) {
            query += " and clubId = :clubId";
            map.put("clubId", clubId);
        }
        return orderInfoRepo.find(query, map).list();
    }

    public List<OrderInfo> fetchOrderByClubAndDate(String orderNo, Long tenantId, Long clubId, LocalDate startDate, LocalDate endDate) {
        Map<String, Object> map = new HashMap<>();
        String query = baseQuery(orderNo, tenantId, clubId, startDate, endDate, map);
        return orderInfoRepo.find(query, map).list();
    }

    private static String baseQuery(String orderNo, Long tenantId, Long clubId, LocalDate startDate, LocalDate endDate, Map<String, Object> map) {
        String query = "1=1";
        if (StringUtils.isNotBlank(orderNo)) {
            query += " and orderNo = :orderNo";
            map.put("orderNo", orderNo);
        }
        if (tenantId != null && tenantId != 0L) {
            query += " and tenantId = :tenantId";
            map.put("tenantId", tenantId);
        }
        if (clubId != null && clubId != 0L) {
            query += " and clubId = :clubId";
            map.put("clubId", clubId);
        }
        if (startDate != null) {
            map.put("startDate", startDate.atStartOfDay());
            query += " and ((status != 3 and status != 2 and startTime >= :startDate) or (status = 3 and realEndTime >= :startDate) or (status = 2 and endTime >= :startDate))";
        }
        if (endDate != null) {
            map.put("endDate", endDate.plusDays(1).atStartOfDay());
            query += " and ((status != 3 and startTime < :endDate) or (status = 3 and realEndTime < :endDate))";
        }
        return query;
    }

    public List<OrderInfo> fetchFinishOrderByClubAndUserIdsClubType(Long clubId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        String query = "clubId = ?1 and userId in ?2 and payType = ?3 and status = ?4";
        return orderInfoRepo.find(query, Sort.descending("createAt"), clubId, userIds, OrderPayTypeEnum.CLUB, OrderStatusEnum.FINISH).list();
    }

    public List<OrderInfo> fetchFinishOrderByClubAndUserIds(Long clubId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        String query = "clubId = ?1 and userId in ?2 and status = ?3";
        return orderInfoRepo.find(query, Sort.descending("createAt"), clubId, userIds, OrderStatusEnum.FINISH).list();
    }

    public Page<OrderInfo> fetchAdminPage(AdminOrderQueryDto dto) {
        Map<String, Object> map = BeanUtils.toMap(dto, "status", "clubId", "tenantId", "orderNo");
        String query = baseQuery(dto.getOrderNo(), dto.getTenantId(), dto.getClubId(), dto.getStartDate(), dto.getEndDate(), map);
        if (dto.getStatus() != null) {
            query += " and status = :status";
        }
        if (dto.getUserIds() != null && !dto.getUserIds().isEmpty()) {
            query += " and userId in :userIds";
            map.put("userIds", dto.getUserIds());
        }
        return orderInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), map);
    }

    public List<ClubUserOrderCountBo> fetchClubOrderCountGroupByUserIds(Long tenantId, Long clubId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        String sql = "select user_id, count(1) as count from order_info ";
        String where = "where 1=1 and user_id in :userIds and status = :status ";
        String groupBy = "group by user_id";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null) {
            where += "and tenant_id = :tenantId ";
            params.put("tenantId", tenantId);
        }
        if (clubId != null) {
            where += "and club_id = :clubId ";
            params.put("clubId", clubId);
        }
        params.put("userIds", userIds);
        params.put("status", OrderStatusEnum.FINISH);
        return orderInfoRepo.findBySql(sql + where + groupBy, params, ClubUserOrderCountBo.class);
    }

    public UserStatsBo fetchOrderStatsByTenantId(Long tenantId) {
        String sql = "select count(1) as order_count, sum(real_amount-refund_amount) as total_amount from order_info ";
        String where = "where pay_type <> 2 and status = 3";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null && tenantId != 0L) {
            where += " and tenant_id = :tenantId";
            params.put("tenantId", tenantId);
        }
        return orderInfoRepo.findBySql(sql + where, params, UserStatsBo.class).getFirst();
    }

    public OrderInfo fetchOrderById(Long id) {
        return orderInfoRepo.findById(id);
    }

    public List<Long> fetchUserIdsByTenantIdAndClubId(Long tenantId, Long clubId) {
        String sql = "select distinct user_id from order_info " +
                "where status = :status";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null) {
            sql += " and tenant_id = :tenantId";
            params.put("tenantId", tenantId);
        }
        if (clubId != null) {
            sql += " and club_id = :clubId";
            params.put("clubId", clubId);
        }
        params.put("status", OrderStatusEnum.FINISH);
        return orderInfoRepo.findBySql(sql, params, ClubUserOrderCountBo.class).stream().map(ClubUserOrderCountBo::getUserId).toList();
    }
}
