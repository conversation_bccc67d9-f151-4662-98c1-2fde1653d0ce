package tech.wejoy.billiard.common.third.aqara.response;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
public class AqaraResponse {

    private Integer code;

    private String message;

    private Object msgDetails;

    private String requestId;

    private Object result;

    public void assertSuccess() {
        if (code == null || code != 0) {
            log.warn("Aqara Response Fail, {}", JsonUtils.toJson(this));
            throw new BaseException(message);
        }
    }

    public <T> T getResult(Class<T> clz) {
        assertSuccess();
        return JsonUtils.toObject(JsonUtils.toJson(result), clz);
    }

    public <T> T getResult(TypeReference<T> typeReference) {
        assertSuccess();
        return JsonUtils.toObject(JsonUtils.toJson(result), typeReference);
    }

    public <T> List<T> getResultList(Class<T> clz) {
        assertSuccess();
        return JsonUtils.toList(JsonUtils.toJson(result), clz);
    }

}
