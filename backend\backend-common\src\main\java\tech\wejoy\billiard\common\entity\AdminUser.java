package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class AdminUser extends BaseEntity {

    private Long tenantId;

    private Long clubId;

    private String username;

    private String password;

    private String email;

    private String phone;

    private String avatar;

    private String nickname;

    private LocalDateTime lastLoginAt;

    private String lastLoginIp;

    private String openId;

    private String sessionKey;

}
