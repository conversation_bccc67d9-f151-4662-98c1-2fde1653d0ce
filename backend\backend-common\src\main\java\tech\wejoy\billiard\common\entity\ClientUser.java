package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.GenderEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClientUser extends BaseEntity {

    private String username;

    private String password;

    private String openId;

    private String email;

    private String phone;

    private String avatar;

    private GenderEnum gender;

    private IsEnum status;

    private LocalDate birth;

    private String nickname;

    private LocalDateTime lastLoginAt;

    private String lastLoginIp;

}
