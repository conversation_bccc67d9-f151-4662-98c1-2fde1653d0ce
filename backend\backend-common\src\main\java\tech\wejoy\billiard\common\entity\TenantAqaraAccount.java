package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class TenantAqaraAccount extends BaseEntity {

    private Long tenantId;

    private Long userId;

    @Column(columnDefinition = "text")
    private String account;

    @Column(columnDefinition = "text")
    private String accessToken;

    @Column(columnDefinition = "text")
    private String refreshToken;

    @Column(columnDefinition = "text")
    private String openId;

    @Column(columnDefinition = "text")
    private String expiresIn;

    private LocalDateTime expireAt;

}
