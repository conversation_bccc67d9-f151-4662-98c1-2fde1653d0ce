package tech.wejoy.billiard.common.manager;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.entity.DistrictInfo;
import tech.wejoy.billiard.common.entity.DistrictPolyline;
import tech.wejoy.billiard.common.enums.DistrictLevelEnum;
import tech.wejoy.billiard.common.repo.DistrictInfoRepo;
import tech.wejoy.billiard.common.repo.DistrictPolylineRepo;

import java.util.List;
import java.util.Map;

@ApplicationScoped
@Transactional(rollbackOn = Exception.class)
@RequiredArgsConstructor
public class DistrictManager {

    @Inject
    DistrictInfoRepo districtInfoRepo;

    @Inject
    DistrictPolylineRepo districtPolylineRepo;

    public DistrictInfo create(DistrictInfo districtInfo) {
        districtInfoRepo.persist(districtInfo);
        return districtInfo;
    }

    public List<DistrictInfo> listByLevel(DistrictLevelEnum level) {
        Map<String, Object> params = Map.of("level", level);
        return districtInfoRepo.find("level <= :level", params).list();
    }

    public List<DistrictPolyline> listPolyline() {
        return districtPolylineRepo.listAll();
    }

    public List<DistrictInfo> fetchByCodes(List<String> list) {
        return districtInfoRepo.find("code in :list", Map.of("list", list)).list();
    }
}
