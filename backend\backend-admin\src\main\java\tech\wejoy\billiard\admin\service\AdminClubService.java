package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import com.google.common.collect.Lists;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.admin.bo.AdminUserBo;
import tech.wejoy.billiard.admin.dto.DistributionDto;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.*;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.*;
import tech.wejoy.billiard.common.manager.*;
import tech.wejoy.billiard.common.service.DistrictService;
import tech.wejoy.billiard.common.service.TicketService;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.utils.PriceUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@ApplicationScoped
@RequiredArgsConstructor
public class AdminClubService {

    private final ClubManager clubManager;

    private final FileManager fileManager;

    private final DistrictService districtService;

    private final WxBusinessService wxBusinessService;

    private final DeviceManager deviceManager;

    private final TicketService ticketService;

    private final MeituanTicketManager meituanTicketManager;

    private final DouyinTicketManager douyinTicketManager;

    private final OrderManager orderManager;

    public Page<AdminClubBo> listAdmin(AdminClubQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != null && user.getTenantId() > 0) {
            dto.setTenantId(user.getTenantId());
        }
        dto.setClubIds(user.getClubIds());
        if (user.getClubId() != null && user.getClubId() > 0) {
            if (dto.getClubIds() == null) {
                dto.setClubIds(Lists.newArrayList(user.getClubId()));
            } else {
                dto.getClubIds().add(user.getClubId());
            }
        }
        return clubManager.listAdmin(dto);
    }

    public AdminClubBo detail(Long id) {
        ClubInfo clubInfo = clubManager.fetchClubById(id);
        if (clubInfo == null) {
            return null;
        }
        AdminClubBo ret = AdminClubBo.from(clubInfo);
        List<ClubTable> clubTables = clubManager.fetchTableByClub(id);
        ret.setTables(clubTables.stream().map(TableBo::from).toList());
        List<FileInfo> fileInfos = fileManager.getImageInfoByTypeAndId(FileTypeEnum.IMAGE, FileLinkEnum.CLUB, id);
        List<String> images = fileInfos.stream().map(FileInfo::getUrl).collect(Collectors.toList());
        if (images.isEmpty()) {
            images.add("https://oss.gorillaballclub.cn/images/icons/icon-y.png");
        }
        ret.setImages(images);
        return ret;
    }

    @Transactional(rollbackOn = Exception.class)
    public AdminClubBo save(AdminClubDto bo) {
        ClubInfo clubInfo;
        if (bo.getId() != null) {
            clubInfo = clubManager.fetchClubById(bo.getId());
            if (clubInfo == null) {
                throw new BaseException("门店不存在");
            }

            // 检查门店是否处于关闭状态
            if (clubInfo.getStatus() == ClubStatusEnum.CLOSED) {
                throw new BaseException("门店已关闭，无法修改信息");
            }

            clubInfo.setAddress(bo.getAddress());
            clubInfo.setPhone(bo.getPhone());
            clubInfo.setName(bo.getName());
            clubInfo.setHeadImage(bo.getHeadImage());
            clubInfo.setLatitude(bo.getLatitude());
            clubInfo.setLongitude(bo.getLongitude());
            clubInfo.setMemberMaxHours(bo.getMemberMaxHours());
            clubInfo.setServiceFeeRatio(bo.getServiceFeeRatio());
            if (bo.getStatus() != null) {
                // 不允许直接设置为关闭状态，必须通过关闭门店接口
                if (bo.getStatus() == ClubStatusEnum.CLOSED) {
                    throw new BaseException("不能直接设置门店为关闭状态，请使用关闭门店功能");
                }
                clubInfo.setStatus(bo.getStatus());
            }
        } else {
            clubInfo = BeanUtils.copy(bo, ClubInfo.class);
            clubInfo.setStatus(ClubStatusEnum.NEW);
            clubInfo.setBusinessHours("");
            clubInfo.setPrice(BigDecimal.ZERO);
            if (clubInfo.getMemberMaxHours() == null) {
                clubInfo.setMemberMaxHours(3);
            }
            clubManager.save(clubInfo);
            // 自动更新门店名称
            if (StringUtils.isBlank(clubInfo.getName())) {
                clubInfo.setName("新建门店" + clubInfo.getId());
            }
            if (bo.getTableCount() != null && bo.getTableCount() > 0) {
                clubInfo.setTableCount(bo.getTableCount());
                genTable(clubInfo.getId(), bo.getTableCount());
            } else {
                clubInfo.setTableCount(0);
            }
        }
        // 自动设置地区
        if (clubInfo.getLatitude() != null && clubInfo.getLongitude() != null) {
            DistrictBo regeo = districtService.regeo(new float[]{clubInfo.getLongitude().floatValue(), clubInfo.getLatitude().floatValue()});
            if (regeo != null) {
                clubInfo.setDistrict(regeo.getCode());
            }
        }
        if (CollectionUtils.isNotEmpty(bo.getImages())) {
            fileManager.delete(FileTypeEnum.IMAGE, FileLinkEnum.CLUB, clubInfo.getId());
            for (int i = 0; i < bo.getImages().size(); i++) {
                if (i == 0) {
                    clubInfo.setHeadImage(bo.getImages().get(i));
                }
                String image = bo.getImages().get(i);
                FileInfo file = fileManager.findByUrl(image);
                if (file != null) {
                    file.setSeq(i);
                    file.setLink(FileLinkEnum.CLUB);
                    file.setOuterId(clubInfo.getId());
                    fileManager.save(file);
                } else {
                    file = new FileInfo();
                    file.setUrl(image);
                    file.setSeq(i);
                    file.setType(FileTypeEnum.IMAGE);
                    file.setLink(FileLinkEnum.CLUB);
                    file.setOuterId(clubInfo.getId());
                    fileManager.save(file);
                }
            }
        }
        clubManager.save(clubInfo);
        return AdminClubBo.from(clubInfo);
    }

    public void distribution(DistributionDto dto) {
        ClubInfo clubInfo = clubManager.fetchClubById(dto.getClubId());
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }

        // 检查门店是否处于关闭状态
        if (clubInfo.getStatus() == ClubStatusEnum.CLOSED) {
            throw new BaseException("门店已关闭，无法分配");
        }

        clubInfo.setTenantId(dto.getTenantId());
        clubManager.save(clubInfo);
    }

    public List<AdminTableBo> listTableByClub(Long clubId) {
        List<ClubTable> clubTables = clubManager.fetchTableByClub(clubId);
        return clubTables.stream().map(AdminTableBo::from).toList();
    }

    public File getTableCode(Long id) {
        return wxBusinessService.getTableQRCode(id);
    }

    public void bindDevice(Long id, Long deviceId) {
        ClubTable table = clubManager.fetchClubTableById(id);
        if (table == null) {
            throw new BaseException("桌台不存在");
        }

        // 检查门店是否处于关闭状态
        ClubInfo clubInfo = clubManager.fetchClubById(table.getClubId());
        if (clubInfo != null && clubInfo.getStatus() == ClubStatusEnum.CLOSED) {
            throw new BaseException("门店已关闭，无法绑定设备");
        }

        TenantDevice device = deviceManager.fetchById(deviceId);
        if (device == null) {
            return;
        }
        List<ClubTableDeviceRel> rel = clubManager.fetchDeviceRelByDeviceIds(Lists.newArrayList(deviceId));
        if (device.getType() == DeviceTypeEnum.LIGHT && !rel.isEmpty()) {
            throw new BaseException("灯具只能绑定一个桌台");
        }
        clubManager.bindDevice(id, deviceId);
    }

    public List<AdminClubBo> optionsClub(Long tenantId) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        List<ClubInfo> clubAll;
        if ((user.getTenantId() != null && user.getTenantId() > 0)) {
            clubAll = clubManager.fetchByTenantId(user.getTenantId());
        } else if (tenantId != null) {
            clubAll = clubManager.fetchByTenantId(tenantId);
        } else {
            clubAll = clubManager.fetchAllClub();
        }
        Set<Long> clubIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(user.getClubIds())) {
            clubIds.addAll(user.getClubIds());
        }
        if (user.getClubId() != null && user.getClubId() > 0) {
            clubIds.add(user.getClubId());
        }
        if (CollectionUtils.isEmpty(clubIds)) {
            return clubAll.stream().map(v -> {
                AdminClubBo bo = new AdminClubBo();
                bo.setId(v.getId());
                bo.setName(v.getName());
                return bo;
            }).toList();
        }
        return clubAll.stream().filter(v -> clubIds.contains(v.getId())).map(v -> {
            AdminClubBo bo = new AdminClubBo();
            bo.setId(v.getId());
            bo.setName(v.getName());
            return bo;
        }).toList();
    }

    public List<AdminTableBo> tableOptions(Long clubId) {
        List<ClubTable> clubTables = clubManager.fetchTableByClub(clubId);
        return clubTables.stream().map(v -> {
            AdminTableBo bo = new AdminTableBo();
            bo.setId(v.getId());
            bo.setName(v.getName());
            return bo;
        }).toList();
    }

    public void unbindDevice(Long id, Long deviceId) {
        clubManager.unbindDevice(id, deviceId);
    }

    public AdminTableBo update(AdminTableBo tableBo) {
        ClubInfo clubInfo = clubManager.fetchClubById(tableBo.getClubId());
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }
        ClubTable clubTable = clubManager.fetchClubTableById(tableBo.getId());
        if (clubTable == null) {
            throw new BaseException("桌台不存在");
        }
        if (tableBo.getStatus() != null) {
            clubTable.setStatus(tableBo.getStatus());
        }
        if (tableBo.getDeposit() != null) {
            clubTable.setDeposit(tableBo.getDeposit());
        }
        if (tableBo.getTableLevel() != null) {
            clubTable.setTableLevel(tableBo.getTableLevel());
        }
        if (tableBo.getSeq() != null) {
            clubTable.setSeq(tableBo.getSeq());
        }
        if (StringUtils.isNotBlank(tableBo.getName())) {
            clubTable.setName(tableBo.getName());
        }
        if (StringUtils.isNotBlank(tableBo.getHeadImage())) {
            clubTable.setHeadImage(tableBo.getHeadImage());
        }
        if (StringUtils.isNotBlank(tableBo.getDescription())) {
            clubTable.setDescription(tableBo.getDescription());
        }
        if (CollectionUtils.isNotEmpty(tableBo.getTimeSlots())) {
            if (!checkTimeSlot(tableBo.getTimeSlots())) {
                throw new BaseException("时间段设置错误");
            }
            BigDecimal minPrice = BigDecimal.ZERO;
            for (TimeSlotBo timeSlot : tableBo.getTimeSlots()) {
                if (timeSlot.getPerPrice().compareTo(minPrice) < 0 || minPrice.compareTo(BigDecimal.ZERO) == 0) {
                    minPrice = timeSlot.getPerPrice();
                }
            }
            clubInfo.setPrice(minPrice);
            clubManager.save(clubInfo);
            clubTable.setTimeSlots(JsonUtils.toJson(tableBo.getTimeSlots()));
        }
        clubManager.saveTable(clubTable);
        return tableBo;
    }

    @Transactional(rollbackOn = Exception.class)
    public AdminTableBo create(AdminTableBo tableBo) {
        ClubInfo clubInfo = clubManager.fetchClubById(tableBo.getClubId());
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }
        ClubTable clubTable = tableBo.to();
        BigDecimal minPrice = clubInfo.getPrice();
        // 判断时间是否有重复
        if (!checkTimeSlot(tableBo.getTimeSlots())) {
            throw new BaseException("时间段设置错误");
        }
        for (TimeSlotBo timeSlot : tableBo.getTimeSlots()) {
            if (timeSlot.getPerPrice().compareTo(minPrice) < 0 || minPrice.compareTo(BigDecimal.ZERO) == 0) {
                minPrice = timeSlot.getPerPrice();
            }
        }
        clubManager.saveTable(clubTable);
        clubInfo.setTableCount(clubManager.fetchTableByClub(clubTable.getClubId()).size());
        clubManager.save(clubInfo);
        return tableBo;
    }

    public AdminStartPrepareBo startPrepare(Long id) {
        ClubTable clubTable = clubManager.fetchClubTableById(id);
        if (clubTable == null) {
            throw new BaseException("桌台不存在");
        }
        AdminStartPrepareBo ret = new AdminStartPrepareBo();
        ret.setTimePlans(getClubTimePlan());
        ret.setTable(AdminTableBo.from(clubTable));
        return ret;
    }

    private static List<ClubTimePlanBo> getClubTimePlan() {
        List<ClubTimePlanBo> timePlans = new ArrayList<>();
        ClubTimePlanBo one = new ClubTimePlanBo();
        one.setType(ClubTimeTypeEnum.HOUR);
        one.setValue(1);
        one.setName("1小时");
        timePlans.add(one);
        ClubTimePlanBo two = new ClubTimePlanBo();
        two.setType(ClubTimeTypeEnum.HOUR);
        two.setValue(2);
        two.setName("2小时");
        timePlans.add(two);
        ClubTimePlanBo three = new ClubTimePlanBo();
        three.setType(ClubTimeTypeEnum.HOUR);
        three.setValue(3);
        three.setName("3小时");
        timePlans.add(three);
        ClubTimePlanBo four = new ClubTimePlanBo();
        four.setType(ClubTimeTypeEnum.HOUR);
        four.setValue(4);
        four.setName("4小时");
        timePlans.add(four);
        return timePlans;
    }

    public StartResultBo start(AdminStartTableDto dto) {
        ClubTable table = clubManager.fetchClubTableById(dto.getTableId());
        if (table == null) {
            throw new BaseException("未找到桌台");
        }
        StartResultBo ret = new StartResultBo();
        ret.setTableId(dto.getTableId());
        Payment payment = Payment.getPayment(OrderPayTypeEnum.FREE);
        if (table.getStatus() != TableStatusEnum.IDLE) {
            ret.setResult(StartResultEnum.TABLE_OCCUPY);
            ret.setMessage("桌台不在空闲状态");
            return ret;
        }
        StartTableTimeDto checkTime = new StartTableTimeDto();
        PrepayBo prepay = payment.prepay(BeanUtils.copy(dto, StartTableDto.class), table);
        if (!prepay.isSuccess()) {
            ret.setResult(StartResultEnum.NOT_ENOUGH_BALANCE);
            ret.setMessage(prepay.getMessage());
            return ret;
        }
        checkTime.setTableId(table.getId());
        checkTime.setStartTime(dto.getStartTime());
        checkTime.setEndTime(dto.getEndTime());
        if (!checkTime(checkTime)) {
            ret.setResult(StartResultEnum.TABLE_OCCUPY);
            ret.setMessage("桌台不在空闲状态");
            return ret;
        }
        checkTime.setTableId(dto.getTableId());
        BigDecimal totalPrice = PriceUtils.calculatePrice(table, dto.getStartTime(), dto.getEndTime());
        OrderInfo order = createOrderBase(dto, table, totalPrice);
        String orderNo = orderManager.generateOrderNo(order);
        order.setOrderNo(orderNo);
        PayResultBo pay = payment.pay(order, null);
        ret.setOrderNo(orderNo);
        if (pay.isSuccess()) {
            if (LocalDateTime.now().plusMinutes(10).isAfter(dto.getStartTime())) {
                ret.setResult(StartResultEnum.STARTING);
            } else {
                ret.setResult(StartResultEnum.SUCCESS);
            }
            order.setStatus(OrderStatusEnum.PAID);
            clubManager.updateTableUsing(table.getId(), 0L, dto.getEndTime());
            ret.setStartTime(dto.getStartTime());
            order.setPayTime(LocalDateTime.now());
            orderManager.save(order);
        } else if (pay.isNeedPay()) {
            order.setStatus(OrderStatusEnum.PENDING);
            orderManager.save(order);
            ret.setResult(StartResultEnum.PENDING_PAYMENT);
            ret.setExtra(pay.getExtra());
        } else {
            orderManager.delete(order);
            ret.setResult(StartResultEnum.NOT_ENOUGH_BALANCE);
            ret.setMessage(pay.getMessage());
            return ret;
        }
        ret.setStatus(order.getStatus());
        return ret;
    }

    public boolean checkTime(StartTableTimeDto dto) {
        List<OrderInfo> orders = orderManager.getOrderByTableId(dto.getTableId());
        for (OrderInfo order : orders) {
            if (dto.getStartTime().isBefore(order.getEndTime()) && dto.getEndTime().isAfter(order.getStartTime())) {
                return false;
            }
        }
        return true;
    }

    private OrderInfo createOrderBase(AdminStartTableDto dto, ClubTable table, BigDecimal totalPrice) {
        OrderInfo order = new OrderInfo();
        order.setUserId(0L);
        order.setStartFrom(StartFromEnum.ADMIN);
        order.setTenantId(table.getTenantId());
        order.setTableId(dto.getTableId());
        order.setClubId(table.getClubId());
        order.setPayType(OrderPayTypeEnum.FREE);
        order.setStartTime(dto.getStartTime());
        order.setEndTime(dto.getEndTime());
        order.setTotalAmount(totalPrice);
        order.setRefundAmount(BigDecimal.ZERO);
        orderManager.save(order);
        return order;
    }

    @Transactional(rollbackOn = Exception.class)
    public void genTable(Long clubId, Integer tableCount) {
        ClubInfo clubInfo = clubManager.fetchClubById(clubId);
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }
        List<ClubTable> clubTables = clubManager.fetchTableByClub(clubId);
        int seq = 1;
        if (!clubTables.isEmpty()) {
            seq = clubTables.getLast().getSeq() + 1;
        }
        BigDecimal deposit = new BigDecimal(100);
        String[] number = {"零", "一", "二", "三", "四", "五", "六", "七", "八", "九", "十"};
        for (int i = 0; i < tableCount; i++) {
            ClubTable table = new ClubTable();
            table.setTenantId(clubInfo.getTenantId());
            table.setClubId(clubId);
            String numStr;
            if (seq > 99) {
                throw new BaseException("桌台数量超过99");
            }
            if (seq <= 10) {
                numStr = number[seq];
            } else if (seq < 20) {
                numStr = "十" + number[seq % 10];
            } else {
                int i1 = seq / 10;
                int i2 = seq % 10;
                numStr = number[i1] + "十";
                if (i2 != 0) {
                    numStr += number[i2];
                }
            }
            table.setName(numStr + "号桌");
            table.setSeq(seq);
            table.setStatus(TableStatusEnum.UNAVAILABLE);
            table.setHeadImage("https://oss.gorillaballclub.cn/images/icons/icon-y.png");
            table.setTableLevel(0);
            table.setDeposit(deposit);
            table.setTimeSlots("");
            clubManager.saveTable(table);
            seq++;
        }
        clubInfo.setTableCount(clubManager.fetchTableByClub(clubId).size());
        clubManager.save(clubInfo);
    }

    public File getClubTablesCode(Long id) {
        ClubInfo clubInfo = clubManager.fetchClubById(id);
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }
        List<ClubTable> tables = clubManager.fetchTableByClub(id);
        if (CollectionUtils.isEmpty(tables)) {
            throw new BaseException("桌台不存在");
        }
        try {
            File zipFile = File.createTempFile(clubInfo.getName(), ".zip");// 压缩文件
            zipFile.deleteOnExit();
            ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile));
            for (ClubTable table : tables) {
                byte[] code = wxBusinessService.getTableQRCodeBytes(table.getId());
                zipOut.putNextEntry(new ZipEntry(table.getName() + ".jpg"));
                zipOut.write(code);
            }
            zipOut.close();
            return zipFile;
        } catch (Exception e) {
            log.error("压缩文件失败:{}", e.getMessage(), e);
            throw new BaseException("压缩文件失败");
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void configTable(Long id, List<TimeSlotBo> timeSlots, TableStatusEnum status) {
        List<ClubTable> tables = clubManager.fetchTableByClub(id);
        if (CollectionUtils.isNotEmpty(timeSlots)) {
            if (!checkTimeSlot(timeSlots)) {
                throw new BaseException("时间段设置错误");
            }
            BigDecimal minPrice = BigDecimal.ZERO;
            for (TimeSlotBo timeSlot : timeSlots) {
                if (timeSlot.getPerPrice().compareTo(minPrice) < 0 || minPrice.compareTo(BigDecimal.ZERO) == 0) {
                    minPrice = timeSlot.getPerPrice();
                }
            }
            ClubInfo clubInfo = clubManager.fetchClubById(id);
            if (clubInfo == null) {
                throw new BaseException("门店不存在");
            }
            clubInfo.setPrice(minPrice);
            clubManager.save(clubInfo);
        }
        for (ClubTable table : tables) {
            if (CollectionUtils.isNotEmpty(timeSlots)) {
                table.setTimeSlots(JsonUtils.toJson(timeSlots));
            }
            if (status != null) {
                table.setStatus(status);
            }
            clubManager.saveTable(table);
        }
    }

    public boolean checkTimeSlot(List<TimeSlotBo> timeSlots) {
        if (CollectionUtils.isEmpty(timeSlots)) {
            return false;
        }
        timeSlots.sort(Comparator.comparing(TimeSlotBo::getStartTime));
        LocalTime start = timeSlots.getFirst().getStartTime();
        LocalTime nextTime = null;
        for (TimeSlotBo timeSlot : timeSlots) {
            if (timeSlot.getPerPrice().compareTo(BigDecimal.ZERO) <= 0) {
                return false;
            }
            timeSlot.setOvernight(!timeSlot.getEndTime().isAfter(timeSlot.getStartTime()));
            if (nextTime == null) {
                nextTime = timeSlot.getStartTime();
            } else if (!nextTime.equals(timeSlot.getEndTime())) {
                return false;
            } else {
                nextTime = timeSlot.getEndTime();
            }
        }
        return start.equals(nextTime);
    }

    public AdminTableBo tableDetail(Long id) {
        ClubTable table = clubManager.fetchClubTableById(id);
        if (table == null) {
            return null;
        }
        return AdminTableBo.from(table);
    }

    @Transactional(rollbackOn = Exception.class)
    public void stop(Long id) {
        ClubInfo clubInfo = clubManager.fetchClubById(id);
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }
        List<ClubTable> tables = clubManager.fetchTableByClub(id);
        for (ClubTable table : tables) {
            if (table.getStatus() == TableStatusEnum.USING) {
                throw new BaseException("桌台正在使用中");
            }
        }
        clubInfo.setStatus(ClubStatusEnum.STOP);
        clubManager.save(clubInfo);
        for (int i = 0; i < tables.size(); i++) {
            ClubTable table = tables.get(i);
            table.setStatus(TableStatusEnum.UNAVAILABLE);
            clubManager.saveTable(table);
        }
    }

    public void delete(Long id) {
        ClubTable table = clubManager.fetchClubTableById(id);
        if (table == null) {
            throw new BaseException("桌台不存在");
        }
        List<OrderInfo> orders = orderManager.fetchOrderByTableId(id);
        if (CollectionUtils.isNotEmpty(orders)) {
            throw new BaseException("球桌已经产生订单，无法删除");
        }
        clubManager.deleteTable(id);
    }

    /**
     * 设置门店为停业状态，解绑所有渠道和设备
     *
     * @param id 门店ID
     */
    @Transactional(rollbackOn = Exception.class)
    public void outOfBusiness(Long id) {
        ClubInfo clubInfo = clubManager.fetchClubById(id);
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }

        // 检查是否有桌台正在使用中
        List<ClubTable> tables = clubManager.fetchTableByClub(id);
        for (ClubTable table : tables) {
            if (table.getStatus() == TableStatusEnum.USING) {
                throw new BaseException("桌台正在使用中，无法设置为停业状态");
            }
        }

        // 解绑所有渠道
        // 1. 获取所有渠道关系
        List<ClubChannelDealRel> channelDealRels = clubManager.fetchClubChannelDealRelByClubId(id);
        Map<TicketChannelEnum, List<ClubChannelDealRel>> channelMap = channelDealRels.stream()
                .collect(Collectors.groupingBy(ClubChannelDealRel::getChannel));

        // 2. 解绑美团渠道
        if (channelMap.containsKey(TicketChannelEnum.MEITUAN)) {
            List<MeituanShop> meituanShops = meituanTicketManager.fetchMeituanShopsByClubId(id);
            for (MeituanShop shop : meituanShops) {
                try {
                    ticketService.unbindMeituanShop(shop.getId());
                } catch (Exception e) {
                    log.error("解绑美团门店失败: {}", shop.getId(), e);
                }
            }
        }

        // 3. 解绑抖音渠道
        if (channelMap.containsKey(TicketChannelEnum.DOUYIN)) {
            List<DouyinShop> douyinShops = douyinTicketManager.fetchDouyinShopsByClubId(id);
            for (DouyinShop shop : douyinShops) {
                try {
                    ticketService.unbindDouyinShop(shop.getId());
                } catch (Exception e) {
                    log.error("解绑抖音门店失败: {}", shop.getId(), e);
                }
            }
        }

        // 4. 删除所有渠道关系
        for (TicketChannelEnum channel : channelMap.keySet()) {
            clubManager.deleteClubChannelDealRelByClubId(id, channel);
        }

        // 解绑所有设备
        for (ClubTable table : tables) {
            // 获取桌台关联的所有设备
            List<ClubTableDeviceRel> deviceRels = clubManager.fetchDeviceByTableId(table.getId());
            for (ClubTableDeviceRel rel : deviceRels) {
                try {
                    clubManager.unbindDevice(table.getId(), rel.getDeviceId());
                } catch (Exception e) {
                    log.error("解绑设备失败: {} - {}", table.getId(), rel.getDeviceId(), e);
                }
            }

            // 设置桌台状态为不可用
            table.setStatus(TableStatusEnum.UNAVAILABLE);
            clubManager.saveTable(table);
        }

        // 设置门店状态为停业
        clubInfo.setStatus(ClubStatusEnum.STOP);
        clubManager.save(clubInfo);
    }

    /**
     * 关闭门店，关闭后无法修改信息，所有桌子不可用
     *
     * @param id 门店ID
     */
    @Transactional(rollbackOn = Exception.class)
    public void closeStore(Long id) {
        ClubInfo clubInfo = clubManager.fetchClubById(id);
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }

        // 检查是否有桌台正在使用中
        List<ClubTable> tables = clubManager.fetchTableByClub(id);
        for (ClubTable table : tables) {
            if (table.getStatus() == TableStatusEnum.USING) {
                throw new BaseException("桌台正在使用中，无法关闭门店");
            }
        }

        // 解绑所有设备
        for (ClubTable table : tables) {
            // 设置桌台状态为不可用
            table.setStatus(TableStatusEnum.UNAVAILABLE);
            clubManager.saveTable(table);
        }

        // 设置门店状态为关闭
        clubInfo.setStatus(ClubStatusEnum.CLOSED);
        clubManager.save(clubInfo);
    }

    /**
     * 重新打开门店，恢复状态
     *
     * @param id 门店ID
     */
    @Transactional(rollbackOn = Exception.class)
    public void reopenStore(Long id) {
        ClubInfo clubInfo = clubManager.fetchClubById(id);
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }

        if (clubInfo.getStatus() != ClubStatusEnum.CLOSED) {
            throw new BaseException("只有关闭状态的门店才能重新打开");
        }

        // 设置门店状态为正常
        clubInfo.setStatus(ClubStatusEnum.NORMAL);
        clubManager.save(clubInfo);

        // 获取所有桌台
        List<ClubTable> tables = clubManager.fetchTableByClub(id);

        // 将所有桌台设置为空闲状态
        for (ClubTable table : tables) {
            table.setStatus(TableStatusEnum.IDLE);
            clubManager.saveTable(table);
        }
    }

}
