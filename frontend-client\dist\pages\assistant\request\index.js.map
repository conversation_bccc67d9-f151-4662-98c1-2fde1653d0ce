{"version": 3, "file": "pages/assistant/request/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;ACnRA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAIA;AAAA;AACA;AAEA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAGA;;;;;;;;;;;;ACpBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/assistant/request/index.tsx?3651", "webpack://frontend-client/._src_pages_assistant_request_card.tsx", "webpack://frontend-client/._src_pages_assistant_request_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/Popup/style/css\";\nimport _Popup from \"@nutui/nutui-react-taro/dist/esm/Popup\";\nimport \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport Title from \"@/components/bussiness/Title\";\nimport Back from \"@/components/bussiness/back\";\nimport { Button } from \"@/components/ui/button\";\nimport { menuRect } from \"@/utils\";\nimport { useCallback, useEffect, useState } from \"react\";\nimport { Card } from \"./card\";\nimport api from \"@/api/bussiness\";\nimport { Address } from \"@/custom/address\";\nimport { useStore } from \"@/hooks\";\nimport { Text } from \"@tarojs/components\";\nimport Taro from \"@tarojs/taro\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default (function () {\n  var rect = menuRect();\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    list = _useState2[0],\n    setList = _useState2[1];\n  var refreshList = useCallback(function () {\n    api.assistant.getRequestList().then(function (res) {\n      setList(res.data);\n    });\n  }, []);\n  useEffect(function () {\n    refreshList();\n  }, []);\n  var cancelRequest = useCallback(/*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(id) {\n      var _yield$Taro$showModal, confirm;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return Taro.showModal({\n              title: \"提示\",\n              content: \"确认取消该申请？\",\n              confirmText: \"确定\"\n            });\n          case 2:\n            _yield$Taro$showModal = _context.sent;\n            confirm = _yield$Taro$showModal.confirm;\n            if (!confirm) {\n              _context.next = 9;\n              break;\n            }\n            _context.next = 7;\n            return api.assistant.cancelRequest(id);\n          case 7:\n            Taro.showToast({\n              title: \"取消成功\",\n              icon: \"none\"\n            });\n            refreshList();\n          case 9:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }(), [refreshList]);\n  var cancelClub = useCallback(/*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(id) {\n      var _yield$Taro$showModal2, confirm;\n      return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.next = 2;\n            return Taro.showModal({\n              title: \"提示\",\n              content: \"确认解约该门店？\",\n              confirmText: \"确定\"\n            });\n          case 2:\n            _yield$Taro$showModal2 = _context2.sent;\n            confirm = _yield$Taro$showModal2.confirm;\n            if (!confirm) {\n              _context2.next = 9;\n              break;\n            }\n            _context2.next = 7;\n            return api.assistant.cancelClub(id);\n          case 7:\n            Taro.showToast({\n              title: \"解约成功\",\n              icon: \"none\"\n            });\n            refreshList();\n          case 9:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2);\n    }));\n    return function (_x2) {\n      return _ref2.apply(this, arguments);\n    };\n  }(), [refreshList]);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    className: \"h-[100vh] flex flex-col\",\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u95E8\\u5E97\\u5165\\u9A7B\"\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"flex-1 space-y-3 p-3\",\n      children: list === null || list === void 0 ? void 0 : list.map(function (item) {\n        return /*#__PURE__*/_jsx(Card, {\n          data: item,\n          children: /*#__PURE__*/_jsx(\"div\", {\n            className: \"flex gap-2\",\n            children: item.pass ? /*#__PURE__*/_jsx(Button, {\n              sizev: \"sm\",\n              className: \"h-7 font-semibold\",\n              onClick: function onClick() {\n                return cancelClub(item.clubId);\n              },\n              children: \"\\u89E3\\u7EA6\"\n            }) : /*#__PURE__*/_jsx(Button, {\n              sizev: \"sm\",\n              className: \"h-7 font-semibold\",\n              onClick: function onClick() {\n                return cancelRequest(item.clubId);\n              },\n              children: \"\\u53D6\\u6D88\"\n            })\n          })\n        }, item.id);\n      })\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"w-full px-3\",\n      children: /*#__PURE__*/_jsx(NewClub, {\n        list: list,\n        onSubmit: refreshList\n      })\n    }), /*#__PURE__*/_jsx(_SafeArea, {\n      position: \"bottom\"\n    })]\n  });\n});\nvar NewClub = function NewClub(_ref3) {\n  var list = _ref3.list,\n    onSubmit = _ref3.onSubmit;\n  var _useState3 = useState(false),\n    _useState4 = _slicedToArray(_useState3, 2),\n    visible = _useState4[0],\n    setVisible = _useState4[1];\n  var _useState5 = useState([]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addressList = _useState6[0],\n    setAddressList = _useState6[1];\n  var _useStore = useStore(function (state) {\n      return state.auth;\n    }),\n    lat = _useStore.lat,\n    lng = _useStore.lng;\n  var changeAddress = useCallback(/*#__PURE__*/function () {\n    var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(params) {\n      var _yield$api$venues$get, data, newData;\n      return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n        while (1) switch (_context3.prev = _context3.next) {\n          case 0:\n            _context3.next = 2;\n            return api.venues.getRechargeList(params);\n          case 2:\n            _yield$api$venues$get = _context3.sent;\n            data = _yield$api$venues$get.data;\n            console.log(list);\n            newData = data.filter(function (item) {\n              return !(list !== null && list !== void 0 && list.find(function (v) {\n                return v.clubId == item.id;\n              }));\n            });\n            console.log(newData);\n            setAddressList(newData);\n            return _context3.abrupt(\"return\", data);\n          case 9:\n          case \"end\":\n            return _context3.stop();\n        }\n      }, _callee3);\n    }));\n    return function (_x3) {\n      return _ref4.apply(this, arguments);\n    };\n  }(), [setAddressList, list]);\n  var handleSubmit = useCallback(/*#__PURE__*/function () {\n    var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(id) {\n      var _yield$Taro$showModal3, confirm;\n      return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n        while (1) switch (_context4.prev = _context4.next) {\n          case 0:\n            _context4.next = 2;\n            return Taro.showModal({\n              title: \"提示\",\n              content: \"确认申请入驻该门店？\",\n              confirmText: \"确定\"\n            });\n          case 2:\n            _yield$Taro$showModal3 = _context4.sent;\n            confirm = _yield$Taro$showModal3.confirm;\n            if (!confirm) {\n              _context4.next = 10;\n              break;\n            }\n            _context4.next = 7;\n            return api.assistant.postRequest({\n              clubId: id\n            });\n          case 7:\n            setVisible(false);\n            Taro.showToast({\n              title: \"申请成功\",\n              icon: \"none\"\n            });\n            onSubmit && onSubmit();\n          case 10:\n          case \"end\":\n            return _context4.stop();\n        }\n      }, _callee4);\n    }));\n    return function (_x4) {\n      return _ref5.apply(this, arguments);\n    };\n  }(), [setVisible]);\n  var onShow = useCallback(function () {\n    changeAddress({\n      lat: lat,\n      lng: lng\n    });\n    setVisible(true);\n  }, [setVisible, list]);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    children: [/*#__PURE__*/_jsx(Button, {\n      className: \"w-full\",\n      onClick: onShow,\n      children: \"\\u5165\\u9A7B\\u65B0\\u95E8\\u5E97\"\n    }), /*#__PURE__*/_jsx(_Popup, {\n      title: /*#__PURE__*/_jsx(Text, {\n        className: \"text-white\",\n        children: \"\\u53EF\\u7528\\u95E8\\u5E97\"\n      }),\n      position: \"bottom\",\n      closeable: true,\n      visible: visible,\n      onClose: function onClose() {\n        return setVisible(false);\n      },\n      style: {\n        width: \"100%\",\n        height: \"100%\",\n        backgroundColor: \"#000\"\n      },\n      children: /*#__PURE__*/_jsx(Address, {\n        list: addressList,\n        onSearch: changeAddress,\n        club: 0,\n        onChoose: function onChoose(id) {\n          handleSubmit(id);\n          setVisible(false);\n        }\n      })\n    })]\n  });\n};", "export const Card = ({ data, children }: any) => {\r\n  return (\r\n    <div className=\"flex gap-2 p-3 bg-bgt rounded-lg\">\r\n      <div>\r\n        <img\r\n          className=\"w-[5rem] h-[5rem] rounded-lg\"\r\n          src={\r\n            data.clubImg ??\r\n            \"https://oss.gorillaballclub.cn/images/icons/icon-y.png\"\r\n          }\r\n          alt=\"\"\r\n        />\r\n      </div>\r\n      <div className=\"flex flex-col justify-between\">\r\n        <div className=\"font-semibold\">{data.clubName}</div>\r\n        <div className=\"text-sm\">{data.pass ? \"已入驻\" : \"审核中\"}</div>\r\n        <div>{children}</div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/request/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/assistant/request/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}