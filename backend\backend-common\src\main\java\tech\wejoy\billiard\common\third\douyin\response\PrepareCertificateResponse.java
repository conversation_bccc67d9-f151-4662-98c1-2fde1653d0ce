package tech.wejoy.billiard.common.third.douyin.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PrepareCertificateResponse extends ResultResponse {

    private String verifyToken;
    private List<Certificate> certificates;
    private List<Certificate> certificatesV2;

    @Data
    public static class Certificate {
        private String encryptedCode;
        private Long expireTime;
        private Sku sku;
        private Amount amount;
    }

    @Data
    public static class Sku {
        private String title;
        private String accountId;
        private Integer grouponType;
        private BigDecimal marketPrice;
        private String skuId;
        private Long soldStartTime;
        private String thirdSkuId;
    }

    @Data
    public static class Amount {
        private BigDecimal originalAmount;
        private BigDecimal listMarketAmount;
        private BigDecimal payAmount;
        private BigDecimal merchantTicketAmount;
        private BigDecimal paymentDiscountAmount;
        private BigDecimal platformDiscountAmount;
        private BigDecimal couponPayAmount;
    }

}
