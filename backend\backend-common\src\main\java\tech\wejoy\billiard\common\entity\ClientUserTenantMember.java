package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClientUserTenantMember extends BaseEntity {

    private Long userId;

    private Long tenantId;

    private Long planId;

    private BigDecimal balance;

    private BigDecimal bonus;

    private BigDecimal totalAmount;

    private Integer seq;

}
