{"version": 3, "file": "pages/match/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC7HA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/match/index.tsx?0ffa", "webpack://frontend-client/._src_pages_match_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport match from \"@/api/bussiness/match\";\nimport Back from \"@/components/bussiness/back\";\nimport Title from \"@/components/bussiness/Title\";\nimport { Button } from \"@/components/ui/button\";\nimport { MatchCard } from \"@/custom/match-card\";\nimport { useStore } from \"@/hooks\";\nimport { menuRect } from \"@/utils\";\nimport { View } from \"@tarojs/components\";\nimport Taro, { useRouter } from \"@tarojs/taro\";\nimport { useEffect, useState } from \"react\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport var MatchDetail = function MatchDetail() {\n  var rect = menuRect();\n  var router = useRouter();\n  var id = router.params.id;\n  var _useState = useState(null),\n    _useState2 = _slicedToArray(_useState, 2),\n    data = _useState2[0],\n    setData = _useState2[1];\n  var _useStore = useStore(function (state) {\n      return state.auth;\n    }),\n    user = _useStore.user;\n  var getData = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n      var _yield$match$detail, data;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            if (id) {\n              _context.next = 2;\n              break;\n            }\n            return _context.abrupt(\"return\");\n          case 2:\n            _context.next = 4;\n            return match.detail(parseInt(id));\n          case 4:\n            _yield$match$detail = _context.sent;\n            data = _yield$match$detail.data;\n            setData(data);\n          case 7:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function getData() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  useEffect(function () {\n    if (!id) {\n      return;\n    }\n    getData();\n  }, [id]);\n  return /*#__PURE__*/_jsxs(View, {\n    style: {\n      paddingTop: rect.bottom + 5\n    },\n    className: \"h-[100vh] flex flex-col p-3\",\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u7EA6\\u7403\\u8BE6\\u60C5\"\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"flex-1\",\n      children: data && /*#__PURE__*/_jsx(MatchCard, {\n        data: data\n      })\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"w-full\",\n      children: user && data && data.status === 1 && user.id !== data.userId && /*#__PURE__*/_jsx(Button, {\n        variant: data.request ? \"destructive\" : \"default\",\n        className: \"w-full h-10\",\n        onClick: function onClick() {\n          Taro.showLoading({\n            title: \"\",\n            mask: true\n          });\n          if (data.request) {\n            match.cancel(id).then(function (resp) {\n              if (resp.statusCode != 200 && resp.statusCode != 204) {\n                Taro.showToast({\n                  title: resp.data.message,\n                  icon: \"none\"\n                });\n                return;\n              }\n              Taro.showToast({\n                title: \"取消成功\",\n                icon: \"none\"\n              });\n              getData();\n            });\n            return;\n          }\n          match.request(id).then(function (resp) {\n            if (resp.statusCode != 200 && resp.statusCode != 204) {\n              Taro.hideLoading();\n              Taro.showToast({\n                title: resp.data.message,\n                icon: \"none\"\n              });\n              return;\n            }\n            Taro.hideLoading();\n            Taro.showToast({\n              title: \"应约成功\",\n              icon: \"none\"\n            });\n            getData();\n          });\n        },\n        children: !(data !== null && data !== void 0 && data.request) ? \"我要应约\" : \"取消应约\"\n      })\n    }), /*#__PURE__*/_jsx(_SafeArea, {\n      position: \"bottom\"\n    })]\n  });\n};\nexport default MatchDetail;", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/match/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/match/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}