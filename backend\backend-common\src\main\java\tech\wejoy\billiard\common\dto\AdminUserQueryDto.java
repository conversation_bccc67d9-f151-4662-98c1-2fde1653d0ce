package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdminUserQueryDto extends PageRequest {

    @QueryParam("tenantId")
    private Long tenantId;

    @QueryParam("clubId")
    private Long clubId;

    @QueryParam("phone")
    private String phone;

}
