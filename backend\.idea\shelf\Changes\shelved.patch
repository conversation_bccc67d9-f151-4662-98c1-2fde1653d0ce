Index: backend-common/src/main/java/tech/wejoy/billiard/common/entity/AssistantInfo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.entity;\r\n\r\nimport com.congeer.database.bean.BaseEntity;\r\nimport jakarta.persistence.Entity;\r\nimport lombok.Data;\r\nimport lombok.EqualsAndHashCode;\r\nimport lombok.experimental.Accessors;\r\nimport tech.wejoy.billiard.common.enums.GenderEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDate;\r\n\r\n@EqualsAndHashCode(callSuper = true)\r\n@Data\r\n@Entity\r\n@Accessors(chain = true)\r\npublic class AssistantInfo extends BaseEntity {\r\n\r\n    private Long userId;\r\n\r\n    private String name;\r\n\r\n    private String avatar;\r\n\r\n    private String phone;\r\n\r\n    private LocalDate birth;\r\n\r\n    private GenderEnum gender;\r\n\r\n    private LocalDate startWork;\r\n\r\n    private Integer level;\r\n\r\n    private BigDecimal price;\r\n\r\n    private String tags;\r\n\r\n    private Integer minMinutes;\r\n\r\n    private String realName;\r\n\r\n    private String idCard;\r\n\r\n    private String bankAccount;\r\n\r\n    private String bankName;\r\n\r\n    private String bankBranch;\r\n\r\n    private IsEnum enable;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/entity/AssistantInfo.java b/backend-common/src/main/java/tech/wejoy/billiard/common/entity/AssistantInfo.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/entity/AssistantInfo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/entity/AssistantInfo.java	(date *************)
@@ -5,6 +5,7 @@
 import lombok.Data;
 import lombok.EqualsAndHashCode;
 import lombok.experimental.Accessors;
+import tech.wejoy.billiard.common.enums.AssistantLevelEnum;
 import tech.wejoy.billiard.common.enums.GenderEnum;
 import tech.wejoy.billiard.common.enums.IsEnum;
 
@@ -31,7 +32,7 @@
 
     private LocalDate startWork;
 
-    private Integer level;
+    private AssistantLevelEnum level;
 
     private BigDecimal price;
 
@@ -51,4 +52,6 @@
 
     private IsEnum enable;
 
+    private IsEnum online;
+
 }
Index: backend-common/src/main/java/tech/wejoy/billiard/common/dto/AssistantUpdateDto.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.dto;\r\n\r\nimport lombok.Data;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.enums.GenderEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDate;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class AssistantUpdateDto {\r\n\r\n    @Schema(description = \"ID\")\r\n    private Long id;\r\n\r\n    @Schema(description = \"姓名\")\r\n    private String name;\r\n\r\n    private String phone;\r\n\r\n    @Schema(description = \"头像\")\r\n    private String avatar;\r\n\r\n    @Schema(description = \"标签\")\r\n    private List<String> tags;\r\n\r\n    @Schema(description = \"性别\")\r\n    private GenderEnum gender;\r\n\r\n    @Schema(description = \"生日\")\r\n    private LocalDate birth;\r\n\r\n    private BigDecimal price;\r\n\r\n    @Schema(description = \"入职时间\")\r\n    private LocalDate startWork;\r\n\r\n    @Schema(description = \"可用门店\")\r\n    private List<Long> clubIds;\r\n\r\n    private IsEnum enable;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AssistantUpdateDto.java b/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AssistantUpdateDto.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AssistantUpdateDto.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AssistantUpdateDto.java	(date 1748954434058)
@@ -2,6 +2,7 @@
 
 import lombok.Data;
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
+import tech.wejoy.billiard.common.enums.AssistantLevelEnum;
 import tech.wejoy.billiard.common.enums.GenderEnum;
 import tech.wejoy.billiard.common.enums.IsEnum;
 
@@ -29,6 +30,9 @@
     @Schema(description = "性别")
     private GenderEnum gender;
 
+    @Schema(description = "级别")
+    private AssistantLevelEnum level;
+
     @Schema(description = "生日")
     private LocalDate birth;
 
Index: backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminTableBo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.bo;\r\n\r\nimport com.congeer.utils.BeanUtils;\r\nimport com.congeer.utils.JsonUtils;\r\nimport lombok.Data;\r\nimport lombok.experimental.Accessors;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.entity.ClubTable;\r\nimport tech.wejoy.billiard.common.enums.TableStatusEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDateTime;\r\nimport java.util.List;\r\n\r\n@Data\r\n@Accessors(chain = true)\r\n@Schema(description = \"桌台\")\r\npublic class AdminTableBo {\r\n\r\n    @Schema(description = \"桌台ID\")\r\n    private Long id;\r\n\r\n    @Schema(description = \"俱乐部ID\")\r\n    private Long clubId;\r\n\r\n    @Schema(description = \"桌台名称\")\r\n    private String name;\r\n\r\n    @Schema(description = \"桌台描述\")\r\n    private String description;\r\n\r\n    @Schema(description = \"桌台图片\")\r\n    private String headImage;\r\n\r\n    @Schema(description = \"桌台等级\")\r\n    private Integer tableLevel;\r\n\r\n    @Schema(description = \"桌台时间段\")\r\n    private List<TimeSlotBo> timeSlots;\r\n\r\n    @Schema(description = \"桌台状态\")\r\n    private TableStatusEnum status;\r\n\r\n    @Schema(description = \"结束时间\")\r\n    private LocalDateTime endTime;\r\n\r\n    @Schema(description = \"押金\")\r\n    private BigDecimal deposit;\r\n\r\n    @Schema(description = \"排序\")\r\n    private Integer seq;\r\n\r\n    private Long userId;\r\n\r\n    public String getTimeSlotsStr() {\r\n        if (this.getTimeSlots() == null) {\r\n            return null;\r\n        }\r\n        return JsonUtils.toJson(this.getTimeSlots());\r\n    }\r\n\r\n    public static AdminTableBo from(ClubTable table) {\r\n        AdminTableBo copy = BeanUtils.copy(table, AdminTableBo.class);\r\n        if (StringUtils.isNotBlank(table.getTimeSlots())) {\r\n            copy.setTimeSlots(JsonUtils.toList(table.getTimeSlots(), TimeSlotBo.class));\r\n        }\r\n        return copy;\r\n    }\r\n\r\n    public ClubTable to() {\r\n        ClubTable copy = BeanUtils.copy(this, ClubTable.class);\r\n        if (this.getTimeSlots() != null) {\r\n            copy.setTimeSlots(JsonUtils.toJson(this.getTimeSlots()));\r\n        }\r\n        return copy;\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminTableBo.java b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminTableBo.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminTableBo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminTableBo.java	(date 1748936929365)
@@ -8,6 +8,7 @@
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
 import tech.wejoy.billiard.common.entity.ClubTable;
 import tech.wejoy.billiard.common.enums.TableStatusEnum;
+import tech.wejoy.billiard.common.enums.TableTypeEnum;
 
 import java.math.BigDecimal;
 import java.time.LocalDateTime;
@@ -27,6 +28,8 @@
     @Schema(description = "桌台名称")
     private String name;
 
+    private TableTypeEnum type;
+
     @Schema(description = "桌台描述")
     private String description;
 
Index: backend-common/src/main/java/tech/wejoy/billiard/common/entity/ClubTable.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.entity;\r\n\r\nimport com.congeer.database.bean.BaseEntity;\r\nimport jakarta.persistence.Column;\r\nimport jakarta.persistence.Entity;\r\nimport lombok.Data;\r\nimport lombok.EqualsAndHashCode;\r\nimport lombok.experimental.Accessors;\r\nimport tech.wejoy.billiard.common.enums.TableStatusEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDateTime;\r\n\r\n@EqualsAndHashCode(callSuper = true)\r\n@Data\r\n@Entity\r\n@Accessors(chain = true)\r\npublic class ClubTable extends BaseEntity {\r\n\r\n    private Long tenantId;\r\n\r\n    private Long clubId;\r\n\r\n    private String name;\r\n\r\n    @Column(columnDefinition = \"text\")\r\n    private String description;\r\n\r\n    @Column(columnDefinition = \"text\")\r\n    private String headImage;\r\n\r\n    private Integer tableLevel;\r\n\r\n    private TableStatusEnum status;\r\n\r\n    private LocalDateTime endTime;\r\n\r\n    @Column(columnDefinition = \"text\")\r\n    private String timeSlots;\r\n\r\n    private Integer seq;\r\n\r\n    private BigDecimal deposit;\r\n\r\n    private Long userId;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/entity/ClubTable.java b/backend-common/src/main/java/tech/wejoy/billiard/common/entity/ClubTable.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/entity/ClubTable.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/entity/ClubTable.java	(date 1748936929308)
@@ -7,6 +7,7 @@
 import lombok.EqualsAndHashCode;
 import lombok.experimental.Accessors;
 import tech.wejoy.billiard.common.enums.TableStatusEnum;
+import tech.wejoy.billiard.common.enums.TableTypeEnum;
 
 import java.math.BigDecimal;
 import java.time.LocalDateTime;
@@ -23,6 +24,8 @@
 
     private String name;
 
+    private TableTypeEnum type;
+
     @Column(columnDefinition = "text")
     private String description;
 
Index: backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminClubService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.admin.service;\r\n\r\nimport com.congeer.core.bean.Page;\r\nimport com.congeer.core.exception.BaseException;\r\nimport com.congeer.security.core.utils.SecurityHolder;\r\nimport com.congeer.utils.BeanUtils;\r\nimport com.congeer.utils.JsonUtils;\r\nimport com.google.common.collect.Lists;\r\nimport jakarta.enterprise.context.ApplicationScoped;\r\nimport jakarta.transaction.Transactional;\r\nimport lombok.RequiredArgsConstructor;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.apache.commons.collections4.CollectionUtils;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport tech.wejoy.billiard.admin.bo.AdminUserBo;\r\nimport tech.wejoy.billiard.admin.dto.DistributionDto;\r\nimport tech.wejoy.billiard.common.bo.*;\r\nimport tech.wejoy.billiard.common.dto.*;\r\nimport tech.wejoy.billiard.common.entity.*;\r\nimport tech.wejoy.billiard.common.enums.*;\r\nimport tech.wejoy.billiard.common.manager.*;\r\nimport tech.wejoy.billiard.common.service.DistrictService;\r\nimport tech.wejoy.billiard.common.service.TicketService;\r\nimport tech.wejoy.billiard.common.strategy.Payment;\r\nimport tech.wejoy.billiard.common.utils.PriceUtils;\r\n\r\nimport java.io.File;\r\nimport java.io.FileOutputStream;\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDateTime;\r\nimport java.time.LocalTime;\r\nimport java.util.*;\r\nimport java.util.stream.Collectors;\r\nimport java.util.zip.ZipEntry;\r\nimport java.util.zip.ZipOutputStream;\r\n\r\n@Slf4j\r\n@ApplicationScoped\r\n@RequiredArgsConstructor\r\npublic class AdminClubService {\r\n\r\n    private final ClubManager clubManager;\r\n\r\n    private final FileManager fileManager;\r\n\r\n    private final DistrictService districtService;\r\n\r\n    private final WxBusinessService wxBusinessService;\r\n\r\n    private final DeviceManager deviceManager;\r\n\r\n    private final TicketService ticketService;\r\n\r\n    private final MeituanTicketManager meituanTicketManager;\r\n\r\n    private final DouyinTicketManager douyinTicketManager;\r\n\r\n    private final OrderManager orderManager;\r\n\r\n    public Page<AdminClubBo> listAdmin(AdminClubQueryDto dto) {\r\n        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();\r\n        if (user.getTenantId() != null && user.getTenantId() > 0) {\r\n            dto.setTenantId(user.getTenantId());\r\n        }\r\n        dto.setClubIds(user.getClubIds());\r\n        if (user.getClubId() != null && user.getClubId() > 0) {\r\n            if (dto.getClubIds() == null) {\r\n                dto.setClubIds(Lists.newArrayList(user.getClubId()));\r\n            } else {\r\n                dto.getClubIds().add(user.getClubId());\r\n            }\r\n        }\r\n        return clubManager.listAdmin(dto);\r\n    }\r\n\r\n    public AdminClubBo detail(Long id) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(id);\r\n        if (clubInfo == null) {\r\n            return null;\r\n        }\r\n        AdminClubBo ret = AdminClubBo.from(clubInfo);\r\n        List<ClubTable> clubTables = clubManager.fetchTableByClub(id);\r\n        ret.setTables(clubTables.stream().map(TableBo::from).toList());\r\n        List<FileInfo> fileInfos = fileManager.getImageInfoByTypeAndId(FileTypeEnum.IMAGE, FileLinkEnum.CLUB, id);\r\n        List<String> images = fileInfos.stream().map(FileInfo::getUrl).collect(Collectors.toList());\r\n        if (images.isEmpty()) {\r\n            images.add(\"https://oss.gorillaballclub.cn/images/icons/icon-y.png\");\r\n        }\r\n        ret.setImages(images);\r\n        return ret;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public AdminClubBo save(AdminClubDto bo) {\r\n        ClubInfo clubInfo;\r\n        if (bo.getId() != null) {\r\n            clubInfo = clubManager.fetchClubById(bo.getId());\r\n            if (clubInfo == null) {\r\n                throw new BaseException(\"门店不存在\");\r\n            }\r\n\r\n            // 检查门店是否处于关闭状态\r\n            if (clubInfo.getStatus() == ClubStatusEnum.CLOSED) {\r\n                throw new BaseException(\"门店已关闭，无法修改信息\");\r\n            }\r\n\r\n            clubInfo.setAddress(bo.getAddress());\r\n            clubInfo.setPhone(bo.getPhone());\r\n            clubInfo.setName(bo.getName());\r\n            clubInfo.setHeadImage(bo.getHeadImage());\r\n            clubInfo.setLatitude(bo.getLatitude());\r\n            clubInfo.setLongitude(bo.getLongitude());\r\n            clubInfo.setMemberMaxHours(bo.getMemberMaxHours());\r\n            clubInfo.setServiceFeeRatio(bo.getServiceFeeRatio());\r\n            if (bo.getStatus() != null) {\r\n                // 不允许直接设置为关闭状态，必须通过关闭门店接口\r\n                if (bo.getStatus() == ClubStatusEnum.CLOSED) {\r\n                    throw new BaseException(\"不能直接设置门店为关闭状态，请使用关闭门店功能\");\r\n                }\r\n                clubInfo.setStatus(bo.getStatus());\r\n            }\r\n        } else {\r\n            clubInfo = BeanUtils.copy(bo, ClubInfo.class);\r\n            clubInfo.setStatus(ClubStatusEnum.NEW);\r\n            clubInfo.setBusinessHours(\"\");\r\n            clubInfo.setPrice(BigDecimal.ZERO);\r\n            if (clubInfo.getMemberMaxHours()==null) {\r\n                clubInfo.setMemberMaxHours(3);\r\n            }\r\n            clubManager.save(clubInfo);\r\n            // 自动更新门店名称\r\n            if (StringUtils.isBlank(clubInfo.getName())) {\r\n                clubInfo.setName(\"新建门店\" + clubInfo.getId());\r\n            }\r\n            if (bo.getTableCount() != null && bo.getTableCount() > 0) {\r\n                clubInfo.setTableCount(bo.getTableCount());\r\n                genTable(clubInfo.getId(), bo.getTableCount());\r\n            } else {\r\n                clubInfo.setTableCount(0);\r\n            }\r\n        }\r\n        // 自动设置地区\r\n        if (clubInfo.getLatitude() != null && clubInfo.getLongitude() != null) {\r\n            DistrictBo regeo = districtService.regeo(new float[]{clubInfo.getLongitude().floatValue(), clubInfo.getLatitude().floatValue()});\r\n            if (regeo != null) {\r\n                clubInfo.setDistrict(regeo.getCode());\r\n            }\r\n        }\r\n        if (CollectionUtils.isNotEmpty(bo.getImages())) {\r\n            fileManager.delete(FileTypeEnum.IMAGE, FileLinkEnum.CLUB, clubInfo.getId());\r\n            for (int i = 0; i < bo.getImages().size(); i++) {\r\n                if (i == 0) {\r\n                    clubInfo.setHeadImage(bo.getImages().get(i));\r\n                }\r\n                String image = bo.getImages().get(i);\r\n                FileInfo file = fileManager.findByUrl(image);\r\n                if (file != null) {\r\n                    file.setSeq(i);\r\n                    file.setLink(FileLinkEnum.CLUB);\r\n                    file.setOuterId(clubInfo.getId());\r\n                    fileManager.save(file);\r\n                } else {\r\n                    file = new FileInfo();\r\n                    file.setUrl(image);\r\n                    file.setSeq(i);\r\n                    file.setType(FileTypeEnum.IMAGE);\r\n                    file.setLink(FileLinkEnum.CLUB);\r\n                    file.setOuterId(clubInfo.getId());\r\n                    fileManager.save(file);\r\n                }\r\n            }\r\n        }\r\n        clubManager.save(clubInfo);\r\n        return AdminClubBo.from(clubInfo);\r\n    }\r\n\r\n    public void distribution(DistributionDto dto) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(dto.getClubId());\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n\r\n        // 检查门店是否处于关闭状态\r\n        if (clubInfo.getStatus() == ClubStatusEnum.CLOSED) {\r\n            throw new BaseException(\"门店已关闭，无法分配\");\r\n        }\r\n\r\n        clubInfo.setTenantId(dto.getTenantId());\r\n        clubManager.save(clubInfo);\r\n    }\r\n\r\n    public List<AdminTableBo> listTableByClub(Long clubId) {\r\n        List<ClubTable> clubTables = clubManager.fetchTableByClub(clubId);\r\n        return clubTables.stream().map(AdminTableBo::from).toList();\r\n    }\r\n\r\n    public File getTableCode(Long id) {\r\n        return wxBusinessService.getTableQRCode(id);\r\n    }\r\n\r\n    public void bindDevice(Long id, Long deviceId) {\r\n        ClubTable table = clubManager.fetchClubTableById(id);\r\n        if (table == null) {\r\n            throw new BaseException(\"桌台不存在\");\r\n        }\r\n\r\n        // 检查门店是否处于关闭状态\r\n        ClubInfo clubInfo = clubManager.fetchClubById(table.getClubId());\r\n        if (clubInfo != null && clubInfo.getStatus() == ClubStatusEnum.CLOSED) {\r\n            throw new BaseException(\"门店已关闭，无法绑定设备\");\r\n        }\r\n\r\n        TenantDevice device = deviceManager.fetchById(deviceId);\r\n        if (device == null) {\r\n            return;\r\n        }\r\n        List<ClubTableDeviceRel> rel = clubManager.fetchDeviceRelByDeviceIds(Lists.newArrayList(deviceId));\r\n        if (device.getType() == DeviceTypeEnum.LIGHT && !rel.isEmpty()) {\r\n            throw new BaseException(\"灯具只能绑定一个桌台\");\r\n        }\r\n        clubManager.bindDevice(id, deviceId);\r\n    }\r\n\r\n    public List<AdminClubBo> optionsClub(Long tenantId) {\r\n        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();\r\n        List<ClubInfo> clubAll;\r\n        if ((user.getTenantId() != null && user.getTenantId() > 0)) {\r\n            clubAll = clubManager.fetchByTenantId(user.getTenantId());\r\n        } else if (tenantId != null) {\r\n            clubAll = clubManager.fetchByTenantId(tenantId);\r\n        } else {\r\n            clubAll = clubManager.fetchAllClub();\r\n        }\r\n        Set<Long> clubIds = new HashSet<>();\r\n        if (CollectionUtils.isNotEmpty(user.getClubIds())) {\r\n            clubIds.addAll(user.getClubIds());\r\n        }\r\n        if (user.getClubId() != null && user.getClubId() > 0) {\r\n            clubIds.add(user.getClubId());\r\n        }\r\n        if (CollectionUtils.isEmpty(clubIds)) {\r\n            return clubAll.stream().map(v -> {\r\n                AdminClubBo bo = new AdminClubBo();\r\n                bo.setId(v.getId());\r\n                bo.setName(v.getName());\r\n                return bo;\r\n            }).toList();\r\n        }\r\n        return clubAll.stream().filter(v -> clubIds.contains(v.getId())).map(v -> {\r\n            AdminClubBo bo = new AdminClubBo();\r\n            bo.setId(v.getId());\r\n            bo.setName(v.getName());\r\n            return bo;\r\n        }).toList();\r\n    }\r\n\r\n    public List<AdminTableBo> tableOptions(Long clubId) {\r\n        List<ClubTable> clubTables = clubManager.fetchTableByClub(clubId);\r\n        return clubTables.stream().map(v -> {\r\n            AdminTableBo bo = new AdminTableBo();\r\n            bo.setId(v.getId());\r\n            bo.setName(v.getName());\r\n            return bo;\r\n        }).toList();\r\n    }\r\n\r\n    public void unbindDevice(Long id, Long deviceId) {\r\n        clubManager.unbindDevice(id, deviceId);\r\n    }\r\n\r\n    public AdminTableBo update(AdminTableBo tableBo) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(tableBo.getClubId());\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n        ClubTable clubTable = clubManager.fetchClubTableById(tableBo.getId());\r\n        if (clubTable == null) {\r\n            throw new BaseException(\"桌台不存在\");\r\n        }\r\n        if (tableBo.getStatus() != null) {\r\n            clubTable.setStatus(tableBo.getStatus());\r\n        }\r\n        if (tableBo.getDeposit() != null) {\r\n            clubTable.setDeposit(tableBo.getDeposit());\r\n        }\r\n        if (tableBo.getTableLevel() != null) {\r\n            clubTable.setTableLevel(tableBo.getTableLevel());\r\n        }\r\n        if (tableBo.getSeq() != null) {\r\n            clubTable.setSeq(tableBo.getSeq());\r\n        }\r\n        if (StringUtils.isNotBlank(tableBo.getName())) {\r\n            clubTable.setName(tableBo.getName());\r\n        }\r\n        if (StringUtils.isNotBlank(tableBo.getHeadImage())) {\r\n            clubTable.setHeadImage(tableBo.getHeadImage());\r\n        }\r\n        if (StringUtils.isNotBlank(tableBo.getDescription())) {\r\n            clubTable.setDescription(tableBo.getDescription());\r\n        }\r\n        if (CollectionUtils.isNotEmpty(tableBo.getTimeSlots())) {\r\n            if (!checkTimeSlot(tableBo.getTimeSlots())) {\r\n                throw new BaseException(\"时间段设置错误\");\r\n            }\r\n            BigDecimal minPrice = BigDecimal.ZERO;\r\n            for (TimeSlotBo timeSlot : tableBo.getTimeSlots()) {\r\n                if (timeSlot.getPerPrice().compareTo(minPrice) < 0 || minPrice.compareTo(BigDecimal.ZERO) == 0) {\r\n                    minPrice = timeSlot.getPerPrice();\r\n                }\r\n            }\r\n            clubInfo.setPrice(minPrice);\r\n            clubManager.save(clubInfo);\r\n            clubTable.setTimeSlots(JsonUtils.toJson(tableBo.getTimeSlots()));\r\n        }\r\n        clubManager.saveTable(clubTable);\r\n        return tableBo;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public AdminTableBo create(AdminTableBo tableBo) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(tableBo.getClubId());\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n        ClubTable clubTable = tableBo.to();\r\n        BigDecimal minPrice = clubInfo.getPrice();\r\n        // 判断时间是否有重复\r\n        if (!checkTimeSlot(tableBo.getTimeSlots())) {\r\n            throw new BaseException(\"时间段设置错误\");\r\n        }\r\n        for (TimeSlotBo timeSlot : tableBo.getTimeSlots()) {\r\n            if (timeSlot.getPerPrice().compareTo(minPrice) < 0 || minPrice.compareTo(BigDecimal.ZERO) == 0) {\r\n                minPrice = timeSlot.getPerPrice();\r\n            }\r\n        }\r\n        clubManager.saveTable(clubTable);\r\n        clubInfo.setTableCount(clubManager.fetchTableByClub(clubTable.getClubId()).size());\r\n        clubManager.save(clubInfo);\r\n        return tableBo;\r\n    }\r\n\r\n    public AdminStartPrepareBo startPrepare(Long id) {\r\n        ClubTable clubTable = clubManager.fetchClubTableById(id);\r\n        if (clubTable == null) {\r\n            throw new BaseException(\"桌台不存在\");\r\n        }\r\n        AdminStartPrepareBo ret = new AdminStartPrepareBo();\r\n        ret.setTimePlans(getClubTimePlan());\r\n        ret.setTable(AdminTableBo.from(clubTable));\r\n        return ret;\r\n    }\r\n\r\n    private static List<ClubTimePlanBo> getClubTimePlan() {\r\n        List<ClubTimePlanBo> timePlans = new ArrayList<>();\r\n        ClubTimePlanBo one = new ClubTimePlanBo();\r\n        one.setType(ClubTimeTypeEnum.HOUR);\r\n        one.setValue(1);\r\n        one.setName(\"1小时\");\r\n        timePlans.add(one);\r\n        ClubTimePlanBo two = new ClubTimePlanBo();\r\n        two.setType(ClubTimeTypeEnum.HOUR);\r\n        two.setValue(2);\r\n        two.setName(\"2小时\");\r\n        timePlans.add(two);\r\n        ClubTimePlanBo three = new ClubTimePlanBo();\r\n        three.setType(ClubTimeTypeEnum.HOUR);\r\n        three.setValue(3);\r\n        three.setName(\"3小时\");\r\n        timePlans.add(three);\r\n        ClubTimePlanBo four = new ClubTimePlanBo();\r\n        four.setType(ClubTimeTypeEnum.HOUR);\r\n        four.setValue(4);\r\n        four.setName(\"4小时\");\r\n        timePlans.add(four);\r\n        return timePlans;\r\n    }\r\n\r\n    public StartResultBo start(AdminStartTableDto dto) {\r\n        ClubTable table = clubManager.fetchClubTableById(dto.getTableId());\r\n        if (table == null) {\r\n            throw new BaseException(\"未找到桌台\");\r\n        }\r\n        StartResultBo ret = new StartResultBo();\r\n        ret.setTableId(dto.getTableId());\r\n        Payment payment = Payment.getPayment(OrderPayTypeEnum.FREE);\r\n        if (table.getStatus() != TableStatusEnum.IDLE) {\r\n            ret.setResult(StartResultEnum.TABLE_OCCUPY);\r\n            ret.setMessage(\"桌台不在空闲状态\");\r\n            return ret;\r\n        }\r\n        StartTableTimeDto checkTime = new StartTableTimeDto();\r\n        PrepayBo prepay = payment.prepay(BeanUtils.copy(dto, StartTableDto.class), table);\r\n        if (!prepay.isSuccess()) {\r\n            ret.setResult(StartResultEnum.NOT_ENOUGH_BALANCE);\r\n            ret.setMessage(prepay.getMessage());\r\n            return ret;\r\n        }\r\n        checkTime.setTableId(table.getId());\r\n        checkTime.setStartTime(dto.getStartTime());\r\n        checkTime.setEndTime(dto.getEndTime());\r\n        if (!checkTime(checkTime)) {\r\n            ret.setResult(StartResultEnum.TABLE_OCCUPY);\r\n            ret.setMessage(\"桌台不在空闲状态\");\r\n            return ret;\r\n        }\r\n        checkTime.setTableId(dto.getTableId());\r\n        BigDecimal totalPrice = PriceUtils.calculatePrice(table, dto.getStartTime(), dto.getEndTime());\r\n        OrderInfo order = createOrderBase(dto, table, totalPrice);\r\n        String orderNo = orderManager.generateOrderNo(order);\r\n        order.setOrderNo(orderNo);\r\n        PayResultBo pay = payment.pay(order, null);\r\n        ret.setOrderNo(orderNo);\r\n        if (pay.isSuccess()) {\r\n            if (LocalDateTime.now().plusMinutes(10).isAfter(dto.getStartTime())) {\r\n                ret.setResult(StartResultEnum.STARTING);\r\n            } else {\r\n                ret.setResult(StartResultEnum.SUCCESS);\r\n            }\r\n            order.setStatus(OrderStatusEnum.PAID);\r\n            clubManager.updateTableUsing(table.getId(), 0L, dto.getEndTime());\r\n            ret.setStartTime(dto.getStartTime());\r\n            order.setPayTime(LocalDateTime.now());\r\n            orderManager.save(order);\r\n        } else if (pay.isNeedPay()) {\r\n            order.setStatus(OrderStatusEnum.PENDING);\r\n            orderManager.save(order);\r\n            ret.setResult(StartResultEnum.PENDING_PAYMENT);\r\n            ret.setExtra(pay.getExtra());\r\n        } else {\r\n            orderManager.delete(order);\r\n            ret.setResult(StartResultEnum.NOT_ENOUGH_BALANCE);\r\n            ret.setMessage(pay.getMessage());\r\n            return ret;\r\n        }\r\n        ret.setStatus(order.getStatus());\r\n        return ret;\r\n    }\r\n\r\n    public boolean checkTime(StartTableTimeDto dto) {\r\n        List<OrderInfo> orders = orderManager.getOrderByTableId(dto.getTableId());\r\n        for (OrderInfo order : orders) {\r\n            if (dto.getStartTime().isBefore(order.getEndTime()) && dto.getEndTime().isAfter(order.getStartTime())) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    private OrderInfo createOrderBase(AdminStartTableDto dto, ClubTable table, BigDecimal totalPrice) {\r\n        OrderInfo order = new OrderInfo();\r\n        order.setUserId(0L);\r\n        order.setStartFrom(StartFromEnum.ADMIN);\r\n        order.setTenantId(table.getTenantId());\r\n        order.setTableId(dto.getTableId());\r\n        order.setClubId(table.getClubId());\r\n        order.setPayType(OrderPayTypeEnum.FREE);\r\n        order.setStartTime(dto.getStartTime());\r\n        order.setEndTime(dto.getEndTime());\r\n        order.setTotalAmount(totalPrice);\r\n        order.setRefundAmount(BigDecimal.ZERO);\r\n        orderManager.save(order);\r\n        return order;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void genTable(Long clubId, Integer tableCount) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(clubId);\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n        List<ClubTable> clubTables = clubManager.fetchTableByClub(clubId);\r\n        int seq = 1;\r\n        if (!clubTables.isEmpty()) {\r\n            seq = clubTables.getLast().getSeq() + 1;\r\n        }\r\n        BigDecimal deposit = new BigDecimal(100);\r\n        String[] number = {\"零\", \"一\", \"二\", \"三\", \"四\", \"五\", \"六\", \"七\", \"八\", \"九\", \"十\"};\r\n        for (int i = 0; i < tableCount; i++) {\r\n            ClubTable table = new ClubTable();\r\n            table.setTenantId(clubInfo.getTenantId());\r\n            table.setClubId(clubId);\r\n            String numStr;\r\n            if (seq > 99) {\r\n                throw new BaseException(\"桌台数量超过99\");\r\n            }\r\n            if (seq <= 10) {\r\n                numStr = number[seq];\r\n            } else if (seq < 20) {\r\n                numStr = \"十\" + number[seq % 10];\r\n            } else {\r\n                int i1 = seq / 10;\r\n                int i2 = seq % 10;\r\n                numStr = number[i1] + \"十\";\r\n                if (i2 != 0) {\r\n                    numStr += number[i2];\r\n                }\r\n            }\r\n            table.setName(numStr + \"号桌\");\r\n            table.setSeq(seq);\r\n            table.setStatus(TableStatusEnum.UNAVAILABLE);\r\n            table.setHeadImage(\"https://oss.gorillaballclub.cn/images/icons/icon-y.png\");\r\n            table.setTableLevel(0);\r\n            table.setDeposit(deposit);\r\n            table.setTimeSlots(\"\");\r\n            clubManager.saveTable(table);\r\n            seq++;\r\n        }\r\n        clubInfo.setTableCount(clubManager.fetchTableByClub(clubId).size());\r\n        clubManager.save(clubInfo);\r\n    }\r\n\r\n    public File getClubTablesCode(Long id) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(id);\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n        List<ClubTable> tables = clubManager.fetchTableByClub(id);\r\n        if (CollectionUtils.isEmpty(tables)) {\r\n            throw new BaseException(\"桌台不存在\");\r\n        }\r\n        try {\r\n            File zipFile = File.createTempFile(clubInfo.getName(), \".zip\");// 压缩文件\r\n            zipFile.deleteOnExit();\r\n            ZipOutputStream zipOut = new ZipOutputStream(new FileOutputStream(zipFile));\r\n            for (ClubTable table : tables) {\r\n                byte[] code = wxBusinessService.getTableQRCodeBytes(table.getId());\r\n                zipOut.putNextEntry(new ZipEntry(table.getName() + \".jpg\"));\r\n                zipOut.write(code);\r\n            }\r\n            zipOut.close();\r\n            return zipFile;\r\n        } catch (Exception e) {\r\n            log.error(\"压缩文件失败:{}\", e.getMessage(), e);\r\n            throw new BaseException(\"压缩文件失败\");\r\n        }\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void configTable(Long id, List<TimeSlotBo> timeSlots, TableStatusEnum status) {\r\n        List<ClubTable> tables = clubManager.fetchTableByClub(id);\r\n        if (CollectionUtils.isNotEmpty(timeSlots)) {\r\n            if (!checkTimeSlot(timeSlots)) {\r\n                throw new BaseException(\"时间段设置错误\");\r\n            }\r\n            BigDecimal minPrice = BigDecimal.ZERO;\r\n            for (TimeSlotBo timeSlot : timeSlots) {\r\n                if (timeSlot.getPerPrice().compareTo(minPrice) < 0 || minPrice.compareTo(BigDecimal.ZERO) == 0) {\r\n                    minPrice = timeSlot.getPerPrice();\r\n                }\r\n            }\r\n            ClubInfo clubInfo = clubManager.fetchClubById(id);\r\n            if (clubInfo == null) {\r\n                throw new BaseException(\"门店不存在\");\r\n            }\r\n            clubInfo.setPrice(minPrice);\r\n            clubManager.save(clubInfo);\r\n        }\r\n        for (ClubTable table : tables) {\r\n            if (CollectionUtils.isNotEmpty(timeSlots)) {\r\n                table.setTimeSlots(JsonUtils.toJson(timeSlots));\r\n            }\r\n            if (status != null) {\r\n                table.setStatus(status);\r\n            }\r\n            clubManager.saveTable(table);\r\n        }\r\n    }\r\n\r\n    public boolean checkTimeSlot(List<TimeSlotBo> timeSlots) {\r\n        if (CollectionUtils.isEmpty(timeSlots)) {\r\n            return false;\r\n        }\r\n        timeSlots.sort(Comparator.comparing(TimeSlotBo::getStartTime));\r\n        LocalTime start = timeSlots.getFirst().getStartTime();\r\n        LocalTime nextTime = null;\r\n        for (TimeSlotBo timeSlot : timeSlots) {\r\n            if (timeSlot.getPerPrice().compareTo(BigDecimal.ZERO) <= 0) {\r\n                return false;\r\n            }\r\n            timeSlot.setOvernight(!timeSlot.getEndTime().isAfter(timeSlot.getStartTime()));\r\n            if (nextTime == null) {\r\n                nextTime = timeSlot.getStartTime();\r\n            } else if (!nextTime.equals(timeSlot.getEndTime())) {\r\n                return false;\r\n            } else {\r\n                nextTime = timeSlot.getEndTime();\r\n            }\r\n        }\r\n        return start.equals(nextTime);\r\n    }\r\n\r\n    public AdminTableBo tableDetail(Long id) {\r\n        ClubTable table = clubManager.fetchClubTableById(id);\r\n        if (table == null) {\r\n            return null;\r\n        }\r\n        return AdminTableBo.from(table);\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void stop(Long id) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(id);\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n        List<ClubTable> tables = clubManager.fetchTableByClub(id);\r\n        for (ClubTable table : tables) {\r\n            if (table.getStatus() == TableStatusEnum.USING) {\r\n                throw new BaseException(\"桌台正在使用中\");\r\n            }\r\n        }\r\n        clubInfo.setStatus(ClubStatusEnum.STOP);\r\n        clubManager.save(clubInfo);\r\n        for (int i = 0; i < tables.size(); i++) {\r\n            ClubTable table = tables.get(i);\r\n            table.setStatus(TableStatusEnum.UNAVAILABLE);\r\n            clubManager.saveTable(table);\r\n        }\r\n    }\r\n\r\n    public void delete(Long id) {\r\n        ClubTable table = clubManager.fetchClubTableById(id);\r\n        if (table == null) {\r\n            throw new BaseException(\"桌台不存在\");\r\n        }\r\n        List<OrderInfo> orders = orderManager.fetchOrderByTableId(id);\r\n        if (CollectionUtils.isNotEmpty(orders)) {\r\n            throw new BaseException(\"球桌已经产生订单，无法删除\");\r\n        }\r\n        clubManager.deleteTable(id);\r\n    }\r\n\r\n    /**\r\n     * 设置门店为停业状态，解绑所有渠道和设备\r\n     * @param id 门店ID\r\n     */\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void outOfBusiness(Long id) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(id);\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n\r\n        // 检查是否有桌台正在使用中\r\n        List<ClubTable> tables = clubManager.fetchTableByClub(id);\r\n        for (ClubTable table : tables) {\r\n            if (table.getStatus() == TableStatusEnum.USING) {\r\n                throw new BaseException(\"桌台正在使用中，无法设置为停业状态\");\r\n            }\r\n        }\r\n\r\n        // 解绑所有渠道\r\n        // 1. 获取所有渠道关系\r\n        List<ClubChannelDealRel> channelDealRels = clubManager.fetchClubChannelDealRelByClubId(id);\r\n        Map<TicketChannelEnum, List<ClubChannelDealRel>> channelMap = channelDealRels.stream()\r\n                .collect(Collectors.groupingBy(ClubChannelDealRel::getChannel));\r\n\r\n        // 2. 解绑美团渠道\r\n        if (channelMap.containsKey(TicketChannelEnum.MEITUAN)) {\r\n            List<MeituanShop> meituanShops = meituanTicketManager.fetchMeituanShopsByClubId(id);\r\n            for (MeituanShop shop : meituanShops) {\r\n                try {\r\n                    ticketService.unbindMeituanShop(shop.getId());\r\n                } catch (Exception e) {\r\n                    log.error(\"解绑美团门店失败: {}\", shop.getId(), e);\r\n                }\r\n            }\r\n        }\r\n\r\n        // 3. 解绑抖音渠道\r\n        if (channelMap.containsKey(TicketChannelEnum.DOUYIN)) {\r\n            List<DouyinShop> douyinShops = douyinTicketManager.fetchDouyinShopsByClubId(id);\r\n            for (DouyinShop shop : douyinShops) {\r\n                try {\r\n                    ticketService.unbindDouyinShop(shop.getId());\r\n                } catch (Exception e) {\r\n                    log.error(\"解绑抖音门店失败: {}\", shop.getId(), e);\r\n                }\r\n            }\r\n        }\r\n\r\n        // 4. 删除所有渠道关系\r\n        for (TicketChannelEnum channel : channelMap.keySet()) {\r\n            clubManager.deleteClubChannelDealRelByClubId(id, channel);\r\n        }\r\n\r\n        // 解绑所有设备\r\n        for (ClubTable table : tables) {\r\n            // 获取桌台关联的所有设备\r\n            List<ClubTableDeviceRel> deviceRels = clubManager.fetchDeviceByTableId(table.getId());\r\n            for (ClubTableDeviceRel rel : deviceRels) {\r\n                try {\r\n                    clubManager.unbindDevice(table.getId(), rel.getDeviceId());\r\n                } catch (Exception e) {\r\n                    log.error(\"解绑设备失败: {} - {}\", table.getId(), rel.getDeviceId(), e);\r\n                }\r\n            }\r\n\r\n            // 设置桌台状态为不可用\r\n            table.setStatus(TableStatusEnum.UNAVAILABLE);\r\n            clubManager.saveTable(table);\r\n        }\r\n\r\n        // 设置门店状态为停业\r\n        clubInfo.setStatus(ClubStatusEnum.STOP);\r\n        clubManager.save(clubInfo);\r\n    }\r\n\r\n    /**\r\n     * 关闭门店，关闭后无法修改信息，所有桌子不可用\r\n     * @param id 门店ID\r\n     */\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void closeStore(Long id) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(id);\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n\r\n        // 检查是否有桌台正在使用中\r\n        List<ClubTable> tables = clubManager.fetchTableByClub(id);\r\n        for (ClubTable table : tables) {\r\n            if (table.getStatus() == TableStatusEnum.USING) {\r\n                throw new BaseException(\"桌台正在使用中，无法关闭门店\");\r\n            }\r\n        }\r\n\r\n        // 解绑所有设备\r\n        for (ClubTable table : tables) {\r\n            // 设置桌台状态为不可用\r\n            table.setStatus(TableStatusEnum.UNAVAILABLE);\r\n            clubManager.saveTable(table);\r\n        }\r\n\r\n        // 设置门店状态为关闭\r\n        clubInfo.setStatus(ClubStatusEnum.CLOSED);\r\n        clubManager.save(clubInfo);\r\n    }\r\n\r\n    /**\r\n     * 重新打开门店，恢复状态\r\n     * @param id 门店ID\r\n     */\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void reopenStore(Long id) {\r\n        ClubInfo clubInfo = clubManager.fetchClubById(id);\r\n        if (clubInfo == null) {\r\n            throw new BaseException(\"门店不存在\");\r\n        }\r\n\r\n        if (clubInfo.getStatus() != ClubStatusEnum.CLOSED) {\r\n            throw new BaseException(\"只有关闭状态的门店才能重新打开\");\r\n        }\r\n\r\n        // 设置门店状态为正常\r\n        clubInfo.setStatus(ClubStatusEnum.NORMAL);\r\n        clubManager.save(clubInfo);\r\n\r\n        // 获取所有桌台\r\n        List<ClubTable> tables = clubManager.fetchTableByClub(id);\r\n\r\n        // 将所有桌台设置为空闲状态\r\n        for (ClubTable table : tables) {\r\n            table.setStatus(TableStatusEnum.IDLE);\r\n            clubManager.saveTable(table);\r\n        }\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminClubService.java b/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminClubService.java
--- a/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminClubService.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminClubService.java	(date 1748954680392)
@@ -286,6 +286,9 @@
         if (tableBo.getTableLevel() != null) {
             clubTable.setTableLevel(tableBo.getTableLevel());
         }
+        if (tableBo.getType() != null) {
+            clubTable.setType(tableBo.getType());
+        }
         if (tableBo.getSeq() != null) {
             clubTable.setSeq(tableBo.getSeq());
         }
Index: backend-common/src/main/java/tech/wejoy/billiard/common/bo/CouponBo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.bo;\r\n\r\nimport lombok.Data;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.enums.CouponTypeEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDateTime;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class CouponBo {\r\n\r\n    private Long id;\r\n\r\n    @Schema(description = \"优惠券名称\")\r\n    private String title;\r\n\r\n    @Schema(description = \"优惠券描述\")\r\n    private String description;\r\n\r\n    @Schema(description = \"优惠券价格\")\r\n    private BigDecimal price;\r\n\r\n    @Schema(description = \"优惠券类型\")\r\n    private CouponTypeEnum type;\r\n\r\n    @Schema(description = \"优惠券有效时间\")\r\n    private Integer minutes;\r\n\r\n    @Schema(description = \"优惠券有效时段\")\r\n    private String period;\r\n\r\n    private IsEnum gift;\r\n\r\n    private List<ApiClubBo> clubs;\r\n\r\n    private LocalDateTime expireTime;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/CouponBo.java b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/CouponBo.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/CouponBo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/CouponBo.java	(date 1748936929269)
@@ -1,9 +1,12 @@
 package tech.wejoy.billiard.common.bo;
 
+import com.congeer.utils.JsonUtils;
 import lombok.Data;
+import org.apache.commons.lang3.StringUtils;
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
 import tech.wejoy.billiard.common.enums.CouponTypeEnum;
 import tech.wejoy.billiard.common.enums.IsEnum;
+import tech.wejoy.billiard.common.enums.TableTypeEnum;
 
 import java.math.BigDecimal;
 import java.time.LocalDateTime;
@@ -36,6 +39,14 @@
 
     private List<ApiClubBo> clubs;
 
+    private List<TableTypeEnum> tableType;
+
     private LocalDateTime expireTime;
 
+    public void setTableType(String tableType) {
+        if (StringUtils.isNotBlank(tableType)) {
+            this.tableType = JsonUtils.toList(tableType, TableTypeEnum.class);
+        }
+    }
+
 }
Index: backend-api/src/main/java/tech/wejoy/billiard/api/bo/WxLoginBo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.api.bo;\r\n\r\nimport lombok.Data;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\n\r\nimport java.time.LocalDate;\r\n\r\n@Data\r\n@Schema(description = \"微信登录结果\")\r\npublic class WxLoginBo {\r\n\r\n    @Schema(description = \"ID\")\r\n    private Long id;\r\n\r\n    @Schema(description = \"昵称\")\r\n    private String nickname;\r\n\r\n    @Schema(description = \"头像\")\r\n    private String avatar;\r\n\r\n    @Schema(description = \"token\")\r\n    private String token;\r\n\r\n    @Schema(description = \"是否绑定手机号\")\r\n    private boolean hasPhone;\r\n\r\n    private String phone;\r\n\r\n    private LocalDate birth;\r\n\r\n    private Long assistantId;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-api/src/main/java/tech/wejoy/billiard/api/bo/WxLoginBo.java b/backend-api/src/main/java/tech/wejoy/billiard/api/bo/WxLoginBo.java
--- a/backend-api/src/main/java/tech/wejoy/billiard/api/bo/WxLoginBo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-api/src/main/java/tech/wejoy/billiard/api/bo/WxLoginBo.java	(date 1748936929439)
@@ -2,6 +2,7 @@
 
 import lombok.Data;
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
+import tech.wejoy.billiard.common.enums.IsEnum;
 
 import java.time.LocalDate;
 
@@ -30,4 +31,6 @@
 
     private Long assistantId;
 
+    private IsEnum assistantOnline;
+
 }
Index: backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminCouponService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.admin.service;\r\n\r\nimport com.congeer.core.bean.Page;\r\nimport com.congeer.core.exception.BaseException;\r\nimport com.congeer.database.bean.BaseEntity;\r\nimport com.congeer.security.core.utils.SecurityHolder;\r\nimport com.congeer.utils.BeanUtils;\r\nimport com.congeer.utils.JsonUtils;\r\nimport jakarta.enterprise.context.ApplicationScoped;\r\nimport jakarta.inject.Inject;\r\nimport jakarta.transaction.Transactional;\r\nimport lombok.RequiredArgsConstructor;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport org.apache.commons.collections4.CollectionUtils;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport tech.wejoy.billiard.admin.bo.AdminUserBo;\r\nimport tech.wejoy.billiard.common.bo.*;\r\nimport tech.wejoy.billiard.common.dto.AdminCouponQueryDto;\r\nimport tech.wejoy.billiard.common.dto.AdminCouponSaveDto;\r\nimport tech.wejoy.billiard.common.dto.CouponPresentDto;\r\nimport tech.wejoy.billiard.common.dto.DeviceOperationDto;\r\nimport tech.wejoy.billiard.common.entity.*;\r\nimport tech.wejoy.billiard.common.enums.BillTypeEnum;\r\nimport tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\nimport tech.wejoy.billiard.common.enums.OrderStatusEnum;\r\nimport tech.wejoy.billiard.common.manager.*;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDateTime;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.stream.Collectors;\r\n\r\n@ApplicationScoped\r\n@RequiredArgsConstructor\r\n@Slf4j\r\npublic class AdminCouponService {\r\n\r\n    private final CouponManager couponManager;\r\n\r\n    private final ClientUserManager clientUserManager;\r\n\r\n    private final BillManager billManager;\r\n\r\n    private final AdminUserManager adminUserManager;\r\n\r\n    private final OrderManager orderManager;\r\n\r\n    private final ClubManager clubManager;\r\n\r\n    @Inject\r\n    DeviceService deviceService;\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public AdminCouponBo create(AdminCouponSaveDto dto) {\r\n        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();\r\n        TenantCoupon coupon = BeanUtils.copy(dto, TenantCoupon.class);\r\n        coupon.setTenantId(user.getTenantId());\r\n        validate(dto);\r\n        coupon.setDelete(IsEnum.FALSE);\r\n        couponManager.saveCoupon(coupon);\r\n        if (CollectionUtils.isNotEmpty(dto.getClubIds())) {\r\n            if (user.getTenantId() == 0) {\r\n                ClubInfo club = clubManager.fetchClubById(dto.getClubIds().getFirst());\r\n                dto.setTenantId(club.getTenantId());\r\n                coupon.setTenantId(club.getTenantId());\r\n                couponManager.saveCoupon(coupon);\r\n            }\r\n            couponManager.saveCouponClub(coupon.getId(), coupon.getTenantId(), dto.getClubIds());\r\n        }\r\n        if (coupon.getTenantId() == 0) {\r\n            throw new BaseException(\"请选择门店\");\r\n        }\r\n        return BeanUtils.copy(dto, AdminCouponBo.class);\r\n    }\r\n\r\n    public AdminCouponBo update(AdminCouponSaveDto dto) {\r\n        TenantCoupon db = couponManager.fetchCouponById(dto.getId());\r\n        if (db == null) {\r\n            throw new BaseException(\"优惠券不存在\");\r\n        }\r\n        validate(dto);\r\n        db.setTitle(dto.getTitle());\r\n        db.setDescription(dto.getDescription());\r\n        db.setPeriod(dto.getPeriod());\r\n        db.setMinutes(dto.getMinutes());\r\n        db.setSeq(dto.getSeq());\r\n        db.setType(dto.getType());\r\n        db.setPrice(dto.getPrice());\r\n        db.setUserTotalLimit(dto.getUserTotalLimit());\r\n        db.setUserDayLimit(dto.getUserDayLimit());\r\n        db.setDayLimit(dto.getDayLimit());\r\n        db.setExpireDays(dto.getExpireDays());\r\n        db.setShow(dto.getShow());\r\n        db.setStatus(dto.getStatus());\r\n        couponManager.saveCoupon(db);\r\n        if (CollectionUtils.isNotEmpty(dto.getClubIds())) {\r\n            couponManager.saveCouponClub(db.getId(), db.getTenantId(), dto.getClubIds());\r\n        }\r\n        return BeanUtils.copy(dto, AdminCouponBo.class);\r\n    }\r\n\r\n    private static void validate(AdminCouponSaveDto dto) {\r\n        if (dto.getPeriod() != null) {\r\n            try {\r\n                TimeBo period = JsonUtils.toObject(dto.getPeriod(), TimeBo.class);\r\n                if (period == null) {\r\n                    throw new BaseException(\"有效期格式错误\");\r\n                }\r\n                if (period.getStartTime() == null || period.getEndTime() == null) {\r\n                    throw new BaseException(\"有效期格式错误\");\r\n                }\r\n                if (!period.getEndTime().isAfter(period.getStartTime())) {\r\n                    period.setOvernight(true);\r\n                    dto.setPeriod(JsonUtils.toJson(period));\r\n                }\r\n            } catch (BaseException e) {\r\n                throw e;\r\n            } catch (Exception e) {\r\n                throw new BaseException(\"有效期格式错误\");\r\n            }\r\n        }\r\n    }\r\n\r\n    public void delete(Long id) {\r\n        couponManager.deleteCoupon(id);\r\n    }\r\n\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void presentCoupon(CouponPresentDto dto) {\r\n        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();\r\n        ClientUser clientUser = clientUserManager.fetchUserByPhone(dto.getPhone());\r\n        if (clientUser == null) {\r\n            throw new BaseException(\"用户不存在\");\r\n        }\r\n        TenantCoupon coupon = couponManager.fetchCouponById(dto.getCouponId());\r\n        if (coupon == null) {\r\n            throw new BaseException(\"优惠券不存在\");\r\n        }\r\n        CouponGiftRecord record = new CouponGiftRecord();\r\n        record.setCouponId(coupon.getId());\r\n        record.setUserId(clientUser.getId());\r\n        record.setRemark(dto.getRemark());\r\n        record.setCount(dto.getCount());\r\n        record.setPhone(dto.getPhone());\r\n        record.setClubId(dto.getClubId());\r\n        record.setTenantId(user.getTenantId());\r\n        record.setOperatorId(user.getId());\r\n        couponManager.saveGiftRecord(record);\r\n        for (int i = 0; i < dto.getCount(); i++) {\r\n            clientUserManager.saveUserCoupon(coupon, clientUser.getId(), record.getId() + \"\", true);\r\n        }\r\n    }\r\n\r\n    public Page<CouponGiftRecordBo> giftList(AdminCouponQueryDto dto) {\r\n        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();\r\n        if (user.getTenantId() != 0) {\r\n            dto.setTenantId(user.getTenantId());\r\n        }\r\n        Page<CouponGiftRecord> list = couponManager.fetchGiftRecordByTenantIdAndClubId(dto);\r\n        List<Long> couponIds = list.getRecords().stream().map(CouponGiftRecord::getCouponId).toList();\r\n        List<TenantCoupon> coupons = couponManager.fetchCouponByIds(couponIds);\r\n        List<Long> operatorIds = list.getRecords().stream().map(CouponGiftRecord::getOperatorId).toList();\r\n        List<AdminUser> adminUsers = adminUserManager.fetchByIds(operatorIds);\r\n        Map<Long, String> operatorMap = adminUsers.stream().collect(Collectors.toMap(BaseEntity::getId, AdminUser::getUsername));\r\n        Map<Long, String> titleMap = coupons.stream().collect(Collectors.toMap(BaseEntity::getId, TenantCoupon::getTitle));\r\n        return list.convert(v -> {\r\n            CouponGiftRecordBo bo = BeanUtils.copy(v, CouponGiftRecordBo.class);\r\n            bo.setTitle(titleMap.get(v.getCouponId()));\r\n            bo.setGiftTime(v.getCreateAt());\r\n            bo.setUserPhone(v.getPhone());\r\n            bo.setOperator(operatorMap.get(v.getOperatorId()));\r\n            return bo;\r\n        });\r\n    }\r\n\r\n    public Page<CouponBillBo> billList(AdminCouponQueryDto dto) {\r\n        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();\r\n        if (user.getTenantId() != 0) {\r\n            dto.setTenantId(user.getTenantId());\r\n        }\r\n        Page<BillInfo> page = billManager.fetchPageByByTenantIdAndClubIdAndType(dto, dto.getStartDate(), dto.getEndDate(), dto.getTenantId(), dto.getClubId(), BillTypeEnum.COUPON);\r\n        List<Long> userIds = page.getRecords().stream().map(BillInfo::getUserId).toList();\r\n        List<String> billNos = page.getRecords().stream().map(BillInfo::getBillNo).toList();\r\n        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);\r\n        List<ClientUserCoupon> coupons = clientUserManager.fetchCouponByBillNos(billNos);\r\n        Map<String, ClientUserCoupon> userCouponMap = coupons.stream().collect(Collectors.toMap(ClientUserCoupon::getBillNo, v -> v));\r\n        List<Long> couponIds = coupons.stream().map(ClientUserCoupon::getCouponId).toList();\r\n        List<TenantCoupon> tenantCoupons = couponManager.fetchCouponsByIds(couponIds);\r\n        Map<Long, TenantCoupon> couponMap = tenantCoupons.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));\r\n        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));\r\n        return page.convert(v -> {\r\n            CouponBillBo bo = new CouponBillBo();\r\n            ClientUser clientUser = userMap.get(v.getUserId());\r\n            ClientUserCoupon clientUserCoupon = userCouponMap.get(v.getBillNo());\r\n            TenantCoupon tenantCoupon = couponMap.get(clientUserCoupon.getCouponId());\r\n            bo.setTitle(tenantCoupon.getTitle());\r\n            bo.setUsed(clientUserCoupon.getUsed());\r\n            bo.setCreateTime(v.getCreateAt());\r\n            bo.setBillId(v.getId());\r\n            bo.setDescription(tenantCoupon.getDescription());\r\n            bo.setPayAmount(v.getPayAmount());\r\n            bo.setStatus(v.getStatus());\r\n            bo.setId(clientUserCoupon.getId());\r\n            bo.setNickname(clientUser.getNickname());\r\n            bo.setUserPhone(clientUser.getPhone());\r\n            return bo;\r\n        });\r\n\r\n    }\r\n\r\n    public void issueCoupon(BillInfo bill) {\r\n        String payInfo = bill.getPayInfo();\r\n        Long userId = bill.getUserId();\r\n        BigDecimal payerTotal = bill.getPayAmount();\r\n        log.info(\"issueCoupon userId: {}, payerTotal: {}, payInfo: {}\", userId, payerTotal, payInfo);\r\n        TenantCoupon coupon = JsonUtils.toObject(payInfo, TenantCoupon.class);\r\n        ClientUserCoupon userCoupon = clientUserManager.saveUserCoupon(coupon, userId, bill.getBillNo(), false);\r\n        if (StringUtils.isNotBlank(bill.getOrderNo())) {\r\n            clientUserManager.useCoupon(userCoupon.getId());\r\n            OrderInfo order = orderManager.fetchOrderByNo(bill.getOrderNo());\r\n            order.setStatus(OrderStatusEnum.PAID);\r\n            order.setPayAmount(payerTotal);\r\n            order.setPayTime(LocalDateTime.now());\r\n            orderManager.save(order);\r\n            clubManager.updateTableUsing(order.getTableId(), userId, order.getEndTime());\r\n            if (LocalDateTime.now().plusSeconds(30).isAfter(order.getStartTime())) {\r\n                DeviceOperationDto dto = new DeviceOperationDto();\r\n                dto.setOrderNo(order.getOrderNo());\r\n                dto.setType(DeviceOperationTypeEnum.START);\r\n                deviceService.handleOrderDevice(dto);\r\n            }\r\n        }\r\n    }\r\n\r\n    public void giftCouponExpired(Long id) {\r\n\r\n    }\r\n\r\n    public void userCouponExpired(Long id) {\r\n        clientUserManager.expireCoupon(id);\r\n    }\r\n\r\n    public Page<AdminUserCouponBo> clubUserList(AdminCouponQueryDto dto) {\r\n        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();\r\n        if (user.getTenantId() != 0) {\r\n            dto.setTenantId(user.getTenantId());\r\n        }\r\n        List<Long> queryUserIds = null;\r\n        if (StringUtils.isNotBlank(dto.getPhone())) {\r\n            List<ClientUser> users = clientUserManager.fetchUsersByPhone(dto.getPhone());\r\n            if (CollectionUtils.isEmpty(users)) {\r\n                return dto.empty();\r\n            }\r\n            queryUserIds = users.stream().map(BaseEntity::getId).toList();\r\n        }\r\n        List<TenantCoupon> couponList = couponManager.fetchCouponByClub(dto.getClubId());\r\n        List<Long> couponIds = couponList.stream().map(BaseEntity::getId).toList();\r\n        Page<ClientUserCoupon> page = clientUserManager.fetchCouponByCouponIds(dto, couponIds, queryUserIds);\r\n        List<Long> userIds = page.getRecords().stream().map(ClientUserCoupon::getUserId).toList();\r\n        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);\r\n        Map<Long, TenantCoupon> couponMap = couponList.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));\r\n        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));\r\n        return page.convert(v -> {\r\n            AdminUserCouponBo bo = BeanUtils.copy(v, AdminUserCouponBo.class);\r\n            TenantCoupon tenantCoupon = couponMap.get(v.getCouponId());\r\n            ClientUser clientUser = userMap.get(v.getUserId());\r\n            bo.setUserId(v.getUserId());\r\n            bo.setCreateTime(v.getCreateAt());\r\n            bo.setExpireTime(v.getExpireTime());\r\n            bo.setUsed(v.getUsed());\r\n            bo.setNickname(clientUser.getNickname());\r\n            bo.setUserPhone(clientUser.getPhone());\r\n            bo.setTitle(tenantCoupon.getTitle());\r\n            bo.setDescription(tenantCoupon.getDescription());\r\n            bo.setPrice(tenantCoupon.getPrice());\r\n            bo.setType(tenantCoupon.getType());\r\n            bo.setMinutes(tenantCoupon.getMinutes());\r\n            bo.setPeriod(tenantCoupon.getPeriod());\r\n            return bo;\r\n        });\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminCouponService.java b/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminCouponService.java
--- a/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminCouponService.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-admin/src/main/java/tech/wejoy/billiard/admin/service/AdminCouponService.java	(date 1748936929250)
@@ -56,6 +56,7 @@
     public AdminCouponBo create(AdminCouponSaveDto dto) {
         AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
         TenantCoupon coupon = BeanUtils.copy(dto, TenantCoupon.class);
+        coupon.setTableType(JsonUtils.toJson(dto.getTableType()));
         coupon.setTenantId(user.getTenantId());
         validate(dto);
         coupon.setDelete(IsEnum.FALSE);
@@ -93,6 +94,7 @@
         db.setDayLimit(dto.getDayLimit());
         db.setExpireDays(dto.getExpireDays());
         db.setShow(dto.getShow());
+        db.setTableType(JsonUtils.toJson(dto.getTableType()));
         db.setStatus(dto.getStatus());
         couponManager.saveCoupon(db);
         if (CollectionUtils.isNotEmpty(dto.getClubIds())) {
Index: backend-common/src/main/java/tech/wejoy/billiard/common/service/AssistantService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.service;\r\n\r\nimport com.congeer.core.bean.Page;\r\nimport com.congeer.core.exception.BaseException;\r\nimport com.congeer.database.bean.BaseEntity;\r\nimport com.congeer.utils.BeanUtils;\r\nimport com.congeer.utils.JsonUtils;\r\nimport com.congeer.web.bean.request.PageRequest;\r\nimport jakarta.enterprise.context.ApplicationScoped;\r\nimport jakarta.transaction.Transactional;\r\nimport lombok.RequiredArgsConstructor;\r\nimport org.apache.commons.lang3.RandomStringUtils;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport tech.wejoy.billiard.common.bo.*;\r\nimport tech.wejoy.billiard.common.dto.*;\r\nimport tech.wejoy.billiard.common.entity.*;\r\nimport tech.wejoy.billiard.common.enums.*;\r\nimport tech.wejoy.billiard.common.manager.AssistantManager;\r\nimport tech.wejoy.billiard.common.manager.ClientUserManager;\r\nimport tech.wejoy.billiard.common.manager.ClubManager;\r\nimport tech.wejoy.billiard.common.manager.MemberManager;\r\nimport tech.wejoy.billiard.common.strategy.Payment;\r\nimport tech.wejoy.billiard.common.utils.PriceUtils;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDateTime;\r\nimport java.util.ArrayList;\r\nimport java.util.List;\r\nimport java.util.Map;\r\nimport java.util.function.Function;\r\nimport java.util.stream.Collectors;\r\n\r\n@ApplicationScoped\r\n@RequiredArgsConstructor\r\npublic class AssistantService {\r\n\r\n    private final AssistantManager assistantManager;\r\n\r\n    private final ClubManager clubManager;\r\n\r\n    private final MemberManager memberManager;\r\n\r\n    private final ClientUserManager clientUserManager;\r\n\r\n    public Page<AssistantBo> list(PageRequest dto) {\r\n        return assistantManager.list(dto).convert(v -> BeanUtils.copy(v, AssistantBo.class));\r\n    }\r\n\r\n    public AssistantBo detail(Long id) {\r\n        AssistantInfo detail = assistantManager.fetchById(id);\r\n        if (detail == null) {\r\n            return null;\r\n        }\r\n        AssistantBo ret = BeanUtils.copy(detail, AssistantBo.class);\r\n        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(detail.getId());\r\n        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();\r\n        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);\r\n        ret.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));\r\n        return ret;\r\n    }\r\n\r\n    public boolean checkTime(StartAssistantTimeDto dto) {\r\n        List<AssistantOrder> orders = assistantManager.getUsingOrderByAssistantId(dto.getAssistantId());\r\n        for (AssistantOrder order : orders) {\r\n            if (dto.getStartTime().isBefore(order.getEndTime()) && dto.getEndTime().isAfter(order.getStartTime())) {\r\n                return false;\r\n            }\r\n        }\r\n        return true;\r\n    }\r\n\r\n    public PriceCalBo calPrice(StartAssistantTimeDto dto) {\r\n        AssistantInfo assistant = assistantManager.fetchAssistantById(dto.getAssistantId());\r\n        if (assistant == null) {\r\n            throw new BaseException(\"未找到助教\");\r\n        }\r\n        BigDecimal price = PriceUtils.calculatePrice(assistant.getPrice(), dto.getStartTime(), dto.getEndTime());\r\n        PriceCalBo bo = new PriceCalBo();\r\n        bo.setPrice(price);\r\n        return bo;\r\n    }\r\n\r\n    public AssistantPrepareBo startPrepare(AssistantPrepareDto dto) {\r\n        AssistantInfo detail = assistantManager.fetchById(dto.getAssistantId());\r\n        if (detail == null) {\r\n            throw new BaseException(\"未找到助教\");\r\n        }\r\n        AssistantPrepareBo ret = new AssistantPrepareBo();\r\n        AssistantBo bo = BeanUtils.copy(detail, AssistantBo.class);\r\n        ret.setAssistant(bo);\r\n        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(detail.getId());\r\n        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();\r\n        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);\r\n        bo.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));\r\n        ret.setTimePlans(PriceUtils.getAssistantTimePlan());\r\n        Long userId = dto.getUserId();\r\n        ClientUserMember member = memberManager.getMemberByUserId(userId);\r\n        WalletDetailBo wallet = new WalletDetailBo();\r\n        wallet.setMemberBalance(member.getBalance());\r\n        ret.setWallet(wallet);\r\n        return ret;\r\n    }\r\n\r\n    public AssistantResultBo startStatus(String orderNo) {\r\n        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);\r\n        if (order == null) {\r\n            throw new BaseException(\"未找到订单\");\r\n        }\r\n        AssistantResultBo ret = new AssistantResultBo();\r\n        ret.setStatus(order.getStatus());\r\n        ret.setAssistantId(order.getAssistantId());\r\n        if (order.getStatus() == OrderStatusEnum.PENDING) {\r\n            ret.setResult(AssistantResultEnum.PENDING_PAYMENT);\r\n        } else if (order.getStatus() == OrderStatusEnum.PAID) {\r\n            ret.setResult(AssistantResultEnum.SUCCESS);\r\n        } else if (order.getStatus() == OrderStatusEnum.USING) {\r\n            ret.setResult(AssistantResultEnum.SUCCESS);\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public AssistantResultBo start(AssistantStartDto dto) {\r\n        AssistantInfo assistant = assistantManager.fetchAssistantById(dto.getAssistantId());\r\n        if (assistant == null) {\r\n            throw new BaseException(\"未找到助教\");\r\n        }\r\n        if (dto.getUserId().equals(assistant.getUserId())) {\r\n            throw new BaseException(\"不能预约自己\");\r\n        }\r\n        ClubInfo club = clubManager.fetchClubById(dto.getClubId());\r\n        if (club == null) {\r\n            throw new BaseException(\"未找到门店\");\r\n        }\r\n        AssistantResultBo ret = new AssistantResultBo();\r\n        ret.setAssistantId(assistant.getId());\r\n        Payment payment = Payment.getPayment(dto.getPayType());\r\n        StartAssistantTimeDto checkTime = new StartAssistantTimeDto();\r\n        checkTime.setAssistantId(dto.getAssistantId());\r\n        checkTime.setStartTime(dto.getStartTime());\r\n        checkTime.setEndTime(dto.getEndTime());\r\n        if (!checkTime(checkTime)) {\r\n            ret.setResult(AssistantResultEnum.OCCUPY);\r\n            ret.setMessage(\"助教当前时间已被预约\");\r\n            return ret;\r\n        }\r\n        BigDecimal totalPrice = PriceUtils.calculatePrice(assistant.getPrice(), dto.getStartTime(), dto.getEndTime());\r\n        AssistantOrder order = createOrderBase(dto, totalPrice);\r\n        order.setAssistantInfo(JsonUtils.toJson(assistant));\r\n        String orderNo = assistantManager.generateOrderNo(order);\r\n        order.setOrderNo(orderNo);\r\n        PayResultBo pay = payment.pay(order, dto.getExtra());\r\n        ret.setOrderNo(orderNo);\r\n        if (pay.isSuccess()) {\r\n            ret.setResult(AssistantResultEnum.SUCCESS);\r\n            order.setStatus(OrderStatusEnum.PAID);\r\n            ret.setStartTime(dto.getStartTime());\r\n            order.setPayTime(LocalDateTime.now());\r\n            assistantManager.saveOrder(order);\r\n        } else if (pay.isNeedPay()) {\r\n            order.setStatus(OrderStatusEnum.PENDING);\r\n            assistantManager.saveOrder(order);\r\n            ret.setResult(AssistantResultEnum.PENDING_PAYMENT);\r\n            ret.setExtra(pay.getExtra());\r\n        } else {\r\n            assistantManager.deleteOrder(order);\r\n            ret.setResult(AssistantResultEnum.NOT_ENOUGH_BALANCE);\r\n            ret.setMessage(pay.getMessage());\r\n            return ret;\r\n        }\r\n        ret.setStatus(order.getStatus());\r\n        return ret;\r\n    }\r\n\r\n    private AssistantOrder createOrderBase(AssistantStartDto dto, BigDecimal totalPrice) {\r\n        AssistantOrder order = new AssistantOrder();\r\n        order.setUserId(dto.getUserId());\r\n        order.setAssistantId(dto.getAssistantId());\r\n        order.setClubId(dto.getClubId());\r\n        order.setPayType(dto.getPayType());\r\n        order.setStartTime(dto.getStartTime());\r\n        order.setEndTime(dto.getEndTime());\r\n        order.setTotalAmount(totalPrice);\r\n        order.setRefundAmount(BigDecimal.ZERO);\r\n        order.setCode(RandomStringUtils.randomNumeric(6));\r\n        assistantManager.saveOrder(order);\r\n        return order;\r\n    }\r\n\r\n    public void apply(ApplyAssistantDto dto) {\r\n        AssistantApply assistantApply = assistantManager.fetchAssistantApplyByUserId(dto.getUserId());\r\n        if (assistantApply != null && assistantApply.getStatus() == AssistantStatusEnum.APPLY) {\r\n            throw new BaseException(\"您已经申请过，请等待审核\");\r\n        } else if (assistantApply != null && assistantApply.getStatus() == AssistantStatusEnum.PASS) {\r\n            throw new BaseException(\"您已经是助教了\");\r\n        }\r\n        AssistantApply apply = BeanUtils.copy(dto, AssistantApply.class);\r\n        apply.setTags(JsonUtils.toJson(dto.getTags()));\r\n        apply.setStatus(AssistantStatusEnum.APPLY);\r\n        assistantManager.saveApply(apply);\r\n    }\r\n\r\n    public Page<AssistantOrderBo> pageOrder(AssistantOrderQueryDto dto) {\r\n        AssistantBo self = getAssistantByUserId(dto.getUserId());\r\n        Page<AssistantOrder> sourcePage = assistantManager.pageOrder(dto);\r\n        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        if (page.getTotal() == 0L) {\r\n            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        }\r\n\r\n        List<Long> assistantIds = page.getRecords().stream().map(AssistantOrderBo::getAssistantId).toList();\r\n        List<AssistantInfo> assistants = assistantManager.fetchAssistantByIds(assistantIds);\r\n        Map<Long, AssistantInfo> assistantMap = assistants.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n\r\n        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();\r\n        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);\r\n        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n        page.getRecords().forEach(v -> {\r\n            v.setClubName(clubMap.get(v.getClubId()).getName());\r\n            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());\r\n            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {\r\n                v.setTotalAmount(BigDecimal.ZERO);\r\n            } else {\r\n                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));\r\n            }\r\n            if (assistantMap.get(v.getAssistantId()) != null) {\r\n                v.setAssistantName(assistantMap.get(v.getAssistantId()).getName());\r\n            }\r\n            if (v.getStatus() == OrderStatusEnum.FINISH) {\r\n                v.setEndTime(v.getRealEndTime());\r\n            }\r\n            if (self != null && self.getId().equals(v.getAssistantId())) {\r\n                v.setCode(null);\r\n            }\r\n        });\r\n        return page;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public OrderFinishResultBo finishOrder(String orderNo) {\r\n        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);\r\n        if (order == null) {\r\n            throw new BaseException(\"订单不存在\");\r\n        }\r\n        AssistantInfo assistantInfo = assistantManager.fetchAssistantById(order.getAssistantId());\r\n        OrderFinishResultBo result = new OrderFinishResultBo();\r\n        if (order.getStatus() == OrderStatusEnum.FINISH) {\r\n            result.setOrderNo(order.getOrderNo());\r\n            result.setStartTime(order.getRealStartTime());\r\n            result.setEndTime(order.getRealEndTime());\r\n            return result;\r\n        }\r\n        if (order.getStatus() != OrderStatusEnum.USING) {\r\n            throw new BaseException(\"订单无法完成\");\r\n        }\r\n        if (order.getRefundTime() != null) {\r\n            throw new BaseException(\"订单完成中\");\r\n        }\r\n        LocalDateTime endTime = LocalDateTime.now();\r\n        if (endTime.isBefore(order.getRealStartTime().plusMinutes(5))) {\r\n            throw new BaseException(\"订单时间不足5分钟\");\r\n        }\r\n        BigDecimal realPrice = PriceUtils.calculatePrice(assistantInfo.getPrice(), order.getRealStartTime(), endTime);\r\n        BigDecimal refundAmount = order.getTotalAmount().subtract(realPrice);\r\n        if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {\r\n            RefundResultBo refund = Payment.getPayment(order.getPayType()).refund(order, refundAmount);\r\n            if (refund.isSuccess()) {\r\n                order.setRefundTime(endTime);\r\n                order.setRefundAmount(refund.getAmount());\r\n            }\r\n        }\r\n        order.setStatus(OrderStatusEnum.FINISH);\r\n        order.setRealEndTime(endTime);\r\n        assistantManager.saveOrder(order);\r\n        result.setOrderNo(orderNo);\r\n        result.setStartTime(order.getRealStartTime());\r\n        result.setEndTime(endTime);\r\n        result.setAmount(refundAmount);\r\n        return result;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public OrderStartResultBo startOrder(String orderNo, String code) {\r\n        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);\r\n        if (order == null) {\r\n            throw new BaseException(\"订单不存在\");\r\n        }\r\n        if (!order.getCode().equals(code)) {\r\n            throw new BaseException(\"验证码错误\");\r\n        }\r\n        if (order.getStatus() == OrderStatusEnum.USING) {\r\n            throw new BaseException(\"订单已开始\");\r\n        }\r\n        if (order.getStatus() != OrderStatusEnum.PAID) {\r\n            throw new BaseException(\"订单无法开始\");\r\n        }\r\n        order.setRealStartTime(LocalDateTime.now());\r\n        order.setStatus(OrderStatusEnum.USING);\r\n        assistantManager.saveOrder(order);\r\n        OrderStartResultBo result = new OrderStartResultBo();\r\n        result.setOrderNo(orderNo);\r\n        result.setStartTime(order.getRealStartTime());\r\n        result.setEndTime(order.getEndTime());\r\n        return result;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void cancelOrder(String orderNo) {\r\n        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);\r\n        if (order == null) {\r\n            throw new BaseException(\"订单不存在\");\r\n        }\r\n        if (order.getStatus() != OrderStatusEnum.PENDING\r\n                && order.getStatus() != OrderStatusEnum.PAID) {\r\n            throw new BaseException(\"订单无法取消\");\r\n        }\r\n        if (order.getStatus() == OrderStatusEnum.PAID) {\r\n            RefundResultBo refund = Payment.getPayment(order.getPayType()).cancel(order, order.getRealAmount());\r\n            if (refund.isSuccess()) {\r\n                order.setRefundTime(LocalDateTime.now());\r\n                order.setRefundAmount(refund.getAmount());\r\n            }\r\n        }\r\n        order.setStatus(OrderStatusEnum.CANCEL);\r\n        assistantManager.saveOrder(order);\r\n    }\r\n\r\n    public AssistantBo getAssistantByUserId(Long id) {\r\n        AssistantInfo assistant = assistantManager.fetchAssistantByUserId(id);\r\n        if (assistant == null) {\r\n            return null;\r\n        }\r\n        return BeanUtils.copy(assistant, AssistantBo.class);\r\n\r\n    }\r\n\r\n    public void payOrder(BillInfo bill) {\r\n        String payInfo = bill.getPayInfo();\r\n        BigDecimal payerTotal = bill.getPayAmount();\r\n        AssistantOrder payInfoOrder = JsonUtils.toObject(payInfo, AssistantOrder.class);\r\n        AssistantOrder order = assistantManager.fetchOrderByNo(payInfoOrder.getOrderNo());\r\n        order.setStatus(OrderStatusEnum.PAID);\r\n        order.setPayAmount(payerTotal);\r\n        order.setPayTime(LocalDateTime.now());\r\n        assistantManager.saveOrder(order);\r\n    }\r\n\r\n    public Page<AdminAssistantBo> adminList(AdminAssistantQueryDto dto) {\r\n        Page<AssistantInfo> list = assistantManager.adminList(dto);\r\n        return list.convert(v -> BeanUtils.copy(v, AdminAssistantBo.class));\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void pass(Long id) {\r\n        AssistantApply apply = assistantManager.fetchAssistantApplyById(id);\r\n        if (apply == null) {\r\n            throw new BaseException(\"申请不存在\");\r\n        }\r\n        if (apply.getStatus() != AssistantStatusEnum.APPLY) {\r\n            throw new BaseException(\"申请状态错误\");\r\n        }\r\n        apply.setStatus(AssistantStatusEnum.PASS);\r\n        assistantManager.saveApply(apply);\r\n        AssistantInfo assistant = BeanUtils.copy(apply, AssistantInfo.class);\r\n        assistant.setId(null);\r\n        assistant.setPrice(BigDecimal.ZERO);\r\n        assistant.setEnable(IsEnum.FALSE);\r\n        assistantManager.saveAssistant(assistant);\r\n    }\r\n\r\n    public void reject(Long id) {\r\n        AssistantApply apply = assistantManager.fetchAssistantApplyById(id);\r\n        if (apply == null) {\r\n            throw new BaseException(\"申请不存在\");\r\n        }\r\n        if (apply.getStatus() != AssistantStatusEnum.APPLY) {\r\n            throw new BaseException(\"申请状态错误\");\r\n        }\r\n        apply.setStatus(AssistantStatusEnum.REJECT);\r\n        assistantManager.saveApply(apply);\r\n    }\r\n\r\n    public Page<AssistantApplyBo> adminApplyList(AdminAssistantApplyQueryDto dto) {\r\n        Page<AssistantApply> list = assistantManager.adminApplyList(dto);\r\n        return list.convert(v -> BeanUtils.copy(v, AssistantApplyBo.class));\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void update(AssistantUpdateDto dto) {\r\n        AssistantInfo assistant = assistantManager.fetchAssistantById(dto.getId());\r\n        if (assistant == null) {\r\n            throw new BaseException(\"未找到助教\");\r\n        }\r\n        if (StringUtils.isNotBlank(dto.getAvatar())) {\r\n            assistant.setAvatar(dto.getAvatar());\r\n        }\r\n        if (StringUtils.isNotBlank(dto.getName())) {\r\n            assistant.setName(dto.getName());\r\n        }\r\n        if (dto.getGender() != null) {\r\n            assistant.setGender(dto.getGender());\r\n        }\r\n        if (StringUtils.isNotBlank(dto.getPhone())) {\r\n            assistant.setPhone(dto.getPhone());\r\n        }\r\n        if (dto.getBirth() != null) {\r\n            assistant.setBirth(dto.getBirth());\r\n        }\r\n        if (dto.getPrice() != null) {\r\n            assistant.setPrice(dto.getPrice());\r\n        }\r\n        if (dto.getStartWork() != null) {\r\n            assistant.setStartWork(dto.getStartWork());\r\n        }\r\n        if (dto.getEnable() != null) {\r\n            assistant.setEnable(dto.getEnable());\r\n        }\r\n        if (dto.getTags() != null) {\r\n            assistant.setTags(JsonUtils.toJson(dto.getTags()));\r\n        }\r\n        assistantManager.saveAssistant(assistant);\r\n        if (dto.getClubIds() != null) {\r\n            assistantManager.deleteClubIdsByAssistantId(assistant.getId());\r\n            List<ClubInfo> clubInfos = clubManager.fetchClubByIds(dto.getClubIds());\r\n            Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n            for (Long addId : dto.getClubIds()) {\r\n                AssistantClubRel rel = new AssistantClubRel();\r\n                rel.setAssistantId(assistant.getId());\r\n                rel.setClubId(addId);\r\n                rel.setTenantId(clubMap.get(addId).getTenantId());\r\n                assistantManager.saveClubRel(rel);\r\n            }\r\n        }\r\n    }\r\n\r\n    public AssistantOrderDetailBo orderDetail(String orderNo, Long userId) {\r\n        AssistantBo self = getAssistantByUserId(userId);\r\n        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);\r\n        if (order == null) {\r\n            throw new BaseException(\"订单不存在\");\r\n        }\r\n        AssistantOrderDetailBo ret = new AssistantOrderDetailBo();\r\n        AssistantInfo assistant = assistantManager.fetchAssistantById(order.getAssistantId());\r\n        AssistantBo bo = BeanUtils.copy(assistant, AssistantBo.class);\r\n        ret.setAssistant(bo);\r\n        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(assistant.getId());\r\n        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();\r\n        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);\r\n        bo.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));\r\n        ret.setOrder(BeanUtils.copy(order, AssistantOrderBo.class));\r\n        if (self != null && self.getId().equals(order.getAssistantId())) {\r\n            ret.getOrder().setCode(null);\r\n        }\r\n        ClientUser user = clientUserManager.fetchUserById(order.getUserId());\r\n        if (user != null) {\r\n            ret.getOrder().setUserNickname(user.getNickname());\r\n            ret.getOrder().setUserPhone(user.getPhone());\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    public AdminAssistantBo adminDetail(Long id) {\r\n        AssistantInfo detail = assistantManager.fetchAssistantById(id);\r\n        if (detail == null) {\r\n            return null;\r\n        }\r\n        AdminAssistantBo ret = BeanUtils.copy(detail, AdminAssistantBo.class);\r\n        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(detail.getId());\r\n        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();\r\n        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);\r\n        ret.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));\r\n        return ret;\r\n    }\r\n\r\n    public Page<AssistantOrderBo> adminOrderList(AdminAssistantOrderQueryDto dto) {\r\n        Page<AssistantOrder> sourcePage = assistantManager.adminOrderList(dto);\r\n        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        if (page.getTotal() == 0L) {\r\n            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        }\r\n\r\n        List<Long> assistantIds = page.getRecords().stream().map(AssistantOrderBo::getAssistantId).toList();\r\n        List<AssistantInfo> assistants = assistantManager.fetchAssistantByIds(assistantIds);\r\n        Map<Long, AssistantInfo> assistantMap = assistants.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n\r\n        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();\r\n        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);\r\n        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n        page.getRecords().forEach(v -> {\r\n            v.setClubName(clubMap.get(v.getClubId()).getName());\r\n            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());\r\n            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {\r\n                v.setTotalAmount(BigDecimal.ZERO);\r\n            } else {\r\n                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));\r\n            }\r\n            if (assistantMap.get(v.getAssistantId()) != null) {\r\n                v.setAssistantName(assistantMap.get(v.getAssistantId()).getName());\r\n            }\r\n            if (v.getStatus() == OrderStatusEnum.FINISH) {\r\n                v.setEndTime(v.getRealEndTime());\r\n            }\r\n        });\r\n        return page;\r\n    }\r\n\r\n    public Page<AssistantOrderBo> pageTodo(AssistantOrderQueryDto dto) {\r\n        AssistantBo self = getAssistantByUserId(dto.getUserId());\r\n        if (self == null) {\r\n            throw new BaseException(\"未找到助教\");\r\n        }\r\n        dto.setAssistantId(self.getId());\r\n        dto.setUserId(null);\r\n        dto.setStatus(List.of(OrderStatusEnum.PAID, OrderStatusEnum.USING));\r\n        Page<AssistantOrder> sourcePage = assistantManager.pageOrder(dto);\r\n        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        if (page.getTotal() == 0L) {\r\n            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        }\r\n\r\n        List<Long> userIds = page.getRecords().stream().map(AssistantOrderBo::getUserId).toList();\r\n        List<ClientUser> users = clientUserManager.fetchUserByIds(userIds);\r\n        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n\r\n        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();\r\n        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);\r\n        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n        page.getRecords().forEach(v -> {\r\n            v.setClubName(clubMap.get(v.getClubId()).getName());\r\n            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());\r\n            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {\r\n                v.setTotalAmount(BigDecimal.ZERO);\r\n            } else {\r\n                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));\r\n            }\r\n            ClientUser user = userMap.get(v.getUserId());\r\n            if (user != null) {\r\n                v.setUserNickname(user.getNickname());\r\n                v.setUserPhone(user.getPhone());\r\n            }\r\n            if (v.getStatus() == OrderStatusEnum.FINISH) {\r\n                v.setEndTime(v.getRealEndTime());\r\n            }\r\n            v.setCode(null);\r\n        });\r\n        return page;\r\n    }\r\n\r\n    public Page<AssistantOrderBo> pageDone(AssistantOrderQueryDto dto) {\r\n        AssistantBo self = getAssistantByUserId(dto.getUserId());\r\n        if (self == null) {\r\n            throw new BaseException(\"未找到助教\");\r\n        }\r\n        dto.setAssistantId(self.getId());\r\n        dto.setUserId(null);\r\n        dto.setStatus(List.of(OrderStatusEnum.FINISH));\r\n        Page<AssistantOrder> sourcePage = assistantManager.pageOrder(dto);\r\n        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        if (page.getTotal() == 0L) {\r\n            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));\r\n        }\r\n\r\n        List<Long> userIds = page.getRecords().stream().map(AssistantOrderBo::getUserId).toList();\r\n        List<ClientUser> users = clientUserManager.fetchUserByIds(userIds);\r\n        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n\r\n        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();\r\n        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);\r\n        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n        page.getRecords().forEach(v -> {\r\n            v.setClubName(clubMap.get(v.getClubId()).getName());\r\n            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());\r\n            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {\r\n                v.setTotalAmount(BigDecimal.ZERO);\r\n            } else {\r\n                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));\r\n            }\r\n            ClientUser user = userMap.get(v.getUserId());\r\n            if (user != null) {\r\n                v.setUserNickname(user.getNickname());\r\n                v.setUserPhone(user.getPhone());\r\n            }\r\n            if (v.getStatus() == OrderStatusEnum.FINISH) {\r\n                v.setEndTime(v.getRealEndTime());\r\n            }\r\n            v.setCode(null);\r\n        });\r\n        return page;\r\n    }\r\n\r\n    public List<AssistantBo> clubAssistant(Long clubId) {\r\n        List<AssistantInfo> assistants = assistantManager.fetchAssistantByClub(clubId);\r\n        return BeanUtils.copyList(assistants, AssistantBo.class);\r\n    }\r\n\r\n    public AssistantOrderBo adminOrderDetail(Long id) {\r\n        AssistantOrder order = assistantManager.fetchOrderById(id);\r\n        if (order == null) {\r\n            return null;\r\n        }\r\n        AssistantOrderBo ret = BeanUtils.copy(order, AssistantOrderBo.class);\r\n        AssistantInfo assistant = assistantManager.fetchAssistantById(order.getAssistantId());\r\n        ret.setAssistantName(assistant.getName());\r\n        ClubInfo club = clubManager.fetchClubById(order.getClubId());\r\n        ret.setClubName(club.getName());\r\n        ClientUser user = clientUserManager.fetchUserById(order.getUserId());\r\n        ret.setUserNickname(user.getNickname());\r\n        ret.setUserPhone(user.getPhone());\r\n        return ret;\r\n    }\r\n\r\n    public List<AssistantClubRequestBo> clubRequestByUserId(Long userId) {\r\n        AssistantBo self = getAssistantByUserId(userId);\r\n        List<AssistantClubRequest> requests = assistantManager.getAssistantClubRequestByAssistantId(self.getId());\r\n        List<AssistantClubRel> relList = assistantManager.fetchAssistantClubByAssistantId(self.getId());\r\n        List<AssistantClubRequestBo> requestList = BeanUtils.copyList(requests, AssistantClubRequestBo.class);\r\n        List<AssistantClubRequestBo> passList = BeanUtils.copyList(relList, AssistantClubRequestBo.class);\r\n        passList.forEach(v -> v.setPass(true));\r\n        List<AssistantClubRequestBo> ret = new ArrayList<>(requestList);\r\n        ret.addAll(passList);\r\n        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(ret.stream().map(AssistantClubRequestBo::getClubId).toList());\r\n        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n        for (AssistantClubRequestBo v : ret) {\r\n            ClubInfo clubInfo = clubMap.get(v.getClubId());\r\n            if (clubInfo == null) {\r\n                continue;\r\n            }\r\n            v.setClubName(clubInfo.getName());\r\n            v.setClubImg(clubInfo.getHeadImage());\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    public List<AssistantClubRequestBo> adminRequestList(ClubRequestDto dto) {\r\n        if (dto.getClubId() == null) {\r\n            return new ArrayList<>();\r\n        }\r\n        List<AssistantClubRequest> requests = assistantManager.getAssistantClubRequestByClubId(dto.getClubId());\r\n        return updateAssistantInfo(BeanUtils.copyList(requests, AssistantClubRequestBo.class));\r\n    }\r\n\r\n    public List<AssistantClubRequestBo> adminPassList(ClubRequestDto dto) {\r\n        if (dto.getClubId() == null) {\r\n            return new ArrayList<>();\r\n        }\r\n        List<AssistantClubRel> relList = assistantManager.fetchAssistantClubByClubId(dto.getClubId());\r\n        return updateAssistantInfo(BeanUtils.copyList(relList, AssistantClubRequestBo.class));\r\n    }\r\n\r\n    private List<AssistantClubRequestBo> updateAssistantInfo(List<AssistantClubRequestBo> list) {\r\n        List<Long> assistantIds = list.stream().map(AssistantClubRequestBo::getAssistantId).toList();\r\n        List<AssistantInfo> assistants = assistantManager.fetchAssistantByIds(assistantIds);\r\n        Map<Long, AssistantInfo> assistantMap = assistants.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));\r\n        list.forEach(v -> {\r\n            AssistantInfo assistant = assistantMap.get(v.getAssistantId());\r\n            if (assistant != null) {\r\n                v.setAssistantName(assistant.getName());\r\n                v.setAssistantAvatar(assistant.getAvatar());\r\n                v.setAssistantPhone(assistant.getPhone());\r\n            }\r\n        });\r\n        return list;\r\n    }\r\n\r\n    public void clubRequest(AssistantClubRequestDto dto) {\r\n        AssistantBo self = getAssistantByUserId(dto.getUserId());\r\n        AssistantClubRequest req = assistantManager.fetchAssistantRequestByAssistantIdAndClubId(self.getId(), dto.getClubId());\r\n        if (req != null) {\r\n            throw new BaseException(\"已经申请过\");\r\n        }\r\n        AssistantClubRequest request = new AssistantClubRequest();\r\n        request.setAssistantId(self.getId());\r\n        request.setClubId(dto.getClubId());\r\n        assistantManager.saveAssistantClubRequest(request);\r\n    }\r\n\r\n    public void cancelClubRequest(Long clubId, Long userId) {\r\n        AssistantBo self = getAssistantByUserId(userId);\r\n        AssistantClubRequest request = assistantManager.fetchAssistantRequestByClubIdAndAssistantId(clubId, self.getId());\r\n        if (request == null) {\r\n            throw new BaseException(\"申请不存在\");\r\n        }\r\n        assistantManager.deleteAssistantClubRequest(request.getId());\r\n    }\r\n\r\n    public void passRequest(Long id) {\r\n        AssistantClubRequest request = assistantManager.fetchAssistantRequestById(id);\r\n        if (request == null) {\r\n            throw new BaseException(\"申请不存在\");\r\n        }\r\n        AssistantClubRel rel = new AssistantClubRel();\r\n        rel.setAssistantId(request.getAssistantId());\r\n        rel.setClubId(request.getClubId());\r\n        assistantManager.saveClubRel(rel);\r\n        assistantManager.deleteAssistantClubRequest(id);\r\n    }\r\n\r\n    public void rejectRequest(Long id) {\r\n        assistantManager.deleteAssistantClubRequest(id);\r\n    }\r\n\r\n    public void cancelPass(Long id) {\r\n        assistantManager.deleteClubRel(id);\r\n    }\r\n\r\n    public void cancelClub(Long clubId, Long userId) {\r\n        AssistantBo self = getAssistantByUserId(userId);\r\n        AssistantClubRel rel = assistantManager.fetchAssistantClubRelByClubIdAndAssistantId(clubId, self.getId());\r\n        if (rel == null) {\r\n            throw new BaseException(\"关系不存在\");\r\n        }\r\n        assistantManager.deleteClubRel(rel.getId());\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/service/AssistantService.java b/backend-common/src/main/java/tech/wejoy/billiard/common/service/AssistantService.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/service/AssistantService.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/service/AssistantService.java	(date 1748937031864)
@@ -85,6 +85,9 @@
         if (detail == null) {
             throw new BaseException("未找到助教");
         }
+        if (detail.getOnline().isFalse()) {
+            throw new BaseException("助教已下线");
+        }
         AssistantPrepareBo ret = new AssistantPrepareBo();
         AssistantBo bo = BeanUtils.copy(detail, AssistantBo.class);
         ret.setAssistant(bo);
@@ -128,6 +131,9 @@
         if (dto.getUserId().equals(assistant.getUserId())) {
             throw new BaseException("不能预约自己");
         }
+        if (assistant.getOnline().isFalse()) {
+            throw new BaseException("助教已下线");
+        }
         ClubInfo club = clubManager.fetchClubById(dto.getClubId());
         if (club == null) {
             throw new BaseException("未找到门店");
@@ -365,6 +371,8 @@
         assistant.setId(null);
         assistant.setPrice(BigDecimal.ZERO);
         assistant.setEnable(IsEnum.FALSE);
+        assistant.setOnline(IsEnum.TRUE);
+        assistant.setLevel(AssistantLevelEnum.PRIMARY);
         assistantManager.saveAssistant(assistant);
     }
 
@@ -712,4 +720,13 @@
         assistantManager.deleteClubRel(rel.getId());
     }
 
+    public void online(Long userId, IsEnum isEnum) {
+        AssistantInfo assistant = assistantManager.fetchAssistantByUserId(userId);
+        if (assistant == null) {
+            throw new BaseException("未找到助教");
+        }
+        assistant.setOnline(isEnum);
+        assistantManager.saveAssistant(assistant);
+    }
+
 }
Index: backend-api/src/main/java/tech/wejoy/billiard/api/api/AssistantApi.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.api.api;\r\n\r\nimport com.congeer.core.bean.Page;\r\nimport com.congeer.core.exception.BaseException;\r\nimport com.congeer.security.core.utils.SecurityHolder;\r\nimport com.congeer.security.quarkus.annotation.Authorized;\r\nimport com.congeer.web.bean.request.PageRequest;\r\nimport jakarta.ws.rs.*;\r\nimport jakarta.ws.rs.core.MediaType;\r\nimport jakarta.ws.rs.core.Response;\r\nimport lombok.RequiredArgsConstructor;\r\nimport org.eclipse.microprofile.openapi.annotations.Operation;\r\nimport org.eclipse.microprofile.openapi.annotations.tags.Tag;\r\nimport tech.wejoy.billiard.api.bo.WxUserBo;\r\nimport tech.wejoy.billiard.api.service.WxUserService;\r\nimport tech.wejoy.billiard.common.bo.*;\r\nimport tech.wejoy.billiard.common.dto.*;\r\nimport tech.wejoy.billiard.common.enums.OrderPayTypeEnum;\r\nimport tech.wejoy.billiard.common.service.AssistantService;\r\n\r\nimport java.io.File;\r\nimport java.util.List;\r\n\r\n@Path(\"/assistant\")\r\n@Tag(name = \"助教\")\r\n@Produces(MediaType.APPLICATION_JSON)\r\n@RequiredArgsConstructor\r\npublic class AssistantApi {\r\n\r\n    private final AssistantService assistantService;\r\n\r\n    private final WxUserService wxUserService;\r\n\r\n    @GET\r\n    @Path(\"/list\")\r\n    @Operation(summary = \"助教列表\")\r\n    public Page<AssistantBo> list(PageRequest dto) {\r\n        return assistantService.list(dto);\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/{id}\")\r\n    @Operation(summary = \"助教详情\")\r\n    public AssistantBo detail(@PathParam(\"id\") Long id) {\r\n        return assistantService.detail(id);\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/{id}/code\")\r\n    @Operation(summary = \"获取助教码\")\r\n    @Produces(\"image/jpeg\")\r\n    public Response getQrcode(@PathParam(\"id\") Long id) {\r\n        File entity = wxUserService.getAssistantQrcode(id);\r\n        return Response.ok(entity)\r\n                .header(\"Content-Disposition\", \"attachment; filename=\" + entity.getName())\r\n                .build();\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/club/request\")\r\n    @Operation(summary = \"俱乐部助教申请列表\")\r\n    public List<AssistantClubRequestBo> clubRequest() {\r\n        return assistantService.clubRequestByUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/club/request\")\r\n    @Operation(summary = \"俱乐部助教申请\")\r\n    public void clubRequest(AssistantClubRequestDto dto) {\r\n        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n        assistantService.clubRequest(dto);\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/club/request/{id}/cancel\")\r\n    @Operation(summary = \"取消俱乐部助教申请\")\r\n    public void cancelClubRequest(@PathParam(\"id\") Long id) {\r\n        assistantService.cancelClubRequest(id, SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/club/{id}/cancel\")\r\n    @Operation(summary = \"取消俱乐部助教申请\")\r\n    public void cancelClubPass(@PathParam(\"id\") Long id) {\r\n        assistantService.cancelClub(id, SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/time/check\")\r\n    @Operation(summary = \"检查时间\")\r\n    @Authorized\r\n    public boolean checkTime(StartAssistantTimeDto dto) {\r\n        return assistantService.checkTime(dto);\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/price/cal\")\r\n    @Operation(summary = \"计算价格\")\r\n    @Authorized\r\n    public PriceCalBo calPrice(StartAssistantTimeDto dto) {\r\n        return assistantService.calPrice(dto);\r\n    }\r\n\r\n\r\n    @POST\r\n    @Path(\"/prepare\")\r\n    @Operation(summary = \"预约准备信息\")\r\n    @Authorized\r\n    public AssistantPrepareBo startPrepare(AssistantPrepareDto dto) {\r\n        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n        return assistantService.startPrepare(dto);\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/start\")\r\n    @Operation(summary = \"预约助教\")\r\n    @Authorized\r\n    public AssistantResultBo start(AssistantStartDto dto) {\r\n        if (dto.getPayType() == null || dto.getPayType() == OrderPayTypeEnum.NONE) {\r\n            throw new BaseException(\"请选择支付方式\");\r\n        }\r\n        if (dto.getAssistantId() == null) {\r\n            throw new BaseException(\"请选择助教\");\r\n        }\r\n        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n        return assistantService.start(dto);\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/start/status\")\r\n    @Operation(summary = \"开始状态\")\r\n    @Authorized\r\n    public AssistantResultBo startStatus(@QueryParam(\"orderNo\") String orderNo) {\r\n        return assistantService.startStatus(orderNo);\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/apply\")\r\n    @Operation(summary = \"申请助教\")\r\n    @Authorized\r\n    public void apply(ApplyAssistantDto dto) {\r\n        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n        dto.setPhone(SecurityHolder.<Long, WxUserBo>session().getUser().getPhone());\r\n        assistantService.apply(dto);\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/order/list\")\r\n    @Operation(summary = \"订单列表\")\r\n    @Authorized\r\n    public Page<AssistantOrderBo> pageOrder(@BeanParam AssistantOrderQueryDto dto) {\r\n        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n        return assistantService.pageOrder(dto);\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/order/todo\")\r\n    @Operation(summary = \"助教未完成订单列表\")\r\n    @Authorized\r\n    public Page<AssistantOrderBo> pageTodo(@BeanParam AssistantOrderQueryDto dto) {\r\n        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n        return assistantService.pageTodo(dto);\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/order/done\")\r\n    @Operation(summary = \"助教完成订单列表\")\r\n    @Authorized\r\n    public Page<AssistantOrderBo> pageDone(@BeanParam AssistantOrderQueryDto dto) {\r\n        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n        return assistantService.pageDone(dto);\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/order/{no}/finish\")\r\n    @Operation(summary = \"完成订单\")\r\n    @Authorized\r\n    public OrderFinishResultBo finishOrder(@PathParam(\"no\") String orderNo) {\r\n        return assistantService.finishOrder(orderNo);\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/order/{no}/start/{code}\")\r\n    @Operation(summary = \"开始订单\")\r\n    @Authorized\r\n    public OrderStartResultBo startOrder(@PathParam(\"no\") String orderNo, @PathParam(\"code\") String code) {\r\n        return assistantService.startOrder(orderNo, code);\r\n    }\r\n\r\n    @POST\r\n    @Path(\"/order/{no}/cancel\")\r\n    @Operation(summary = \"取消订单\")\r\n    @Authorized\r\n    public void cancelOrder(@PathParam(\"no\") String orderNo) {\r\n        assistantService.cancelOrder(orderNo);\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/order/{no}\")\r\n    @Operation(summary = \"订单详情\")\r\n    @Authorized\r\n    public AssistantOrderDetailBo orderDetail(@PathParam(\"no\") String orderNo) {\r\n\r\n        return assistantService.orderDetail(orderNo, SecurityHolder.<Long, WxUserBo>session().getUserId());\r\n    }\r\n\r\n    @GET\r\n    @Path(\"/club/{id}\")\r\n    @Operation(summary = \"俱乐部助教列表\")\r\n    public List<AssistantBo> clubAssistant(@PathParam(\"id\") Long clubId) {\r\n        return assistantService.clubAssistant(clubId);\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-api/src/main/java/tech/wejoy/billiard/api/api/AssistantApi.java b/backend-api/src/main/java/tech/wejoy/billiard/api/api/AssistantApi.java
--- a/backend-api/src/main/java/tech/wejoy/billiard/api/api/AssistantApi.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-api/src/main/java/tech/wejoy/billiard/api/api/AssistantApi.java	(date 1748954408842)
@@ -1,5 +1,6 @@
 package tech.wejoy.billiard.api.api;
 
+import com.congeer.core.bean.BaseEnum;
 import com.congeer.core.bean.Page;
 import com.congeer.core.exception.BaseException;
 import com.congeer.security.core.utils.SecurityHolder;
@@ -15,6 +16,7 @@
 import tech.wejoy.billiard.api.service.WxUserService;
 import tech.wejoy.billiard.common.bo.*;
 import tech.wejoy.billiard.common.dto.*;
+import tech.wejoy.billiard.common.enums.IsEnum;
 import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
 import tech.wejoy.billiard.common.service.AssistantService;
 
@@ -211,4 +213,12 @@
         return assistantService.clubAssistant(clubId);
     }
 
+    @PUT
+    @Path("/online/{value}")
+    @Operation(summary = "上线")
+    @Authorized
+    public void online(@PathParam("value") Integer value) {
+        assistantService.online(SecurityHolder.<Long, WxUserBo>session().getUserId(), BaseEnum.ordinalOf(IsEnum.class, value));
+    }
+
 }
Index: backend-common/src/main/java/tech/wejoy/billiard/common/entity/TenantCoupon.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.entity;\r\n\r\nimport com.congeer.database.bean.BaseEntity;\r\nimport jakarta.persistence.Column;\r\nimport jakarta.persistence.Entity;\r\nimport lombok.Data;\r\nimport lombok.EqualsAndHashCode;\r\nimport lombok.experimental.Accessors;\r\nimport tech.wejoy.billiard.common.enums.CouponTypeEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\n\r\n@EqualsAndHashCode(callSuper = true)\r\n@Data\r\n@Entity\r\n@Accessors(chain = true)\r\npublic class TenantCoupon extends BaseEntity {\r\n\r\n    private Long tenantId;\r\n\r\n    @Column(columnDefinition = \"text\")\r\n    private String title;\r\n\r\n    @Column(columnDefinition = \"text\")\r\n    private String description;\r\n\r\n    private BigDecimal price;\r\n\r\n    private CouponTypeEnum type;\r\n\r\n    private Integer minutes;\r\n\r\n    @Column(columnDefinition = \"text\")\r\n    private String period;\r\n\r\n    private Integer expireDays;\r\n\r\n    private Integer userTotalLimit;\r\n\r\n    private Integer userDayLimit;\r\n\r\n    private Integer dayLimit;\r\n\r\n    private Integer seq;\r\n\r\n    private IsEnum status;\r\n\r\n    private IsEnum show;\r\n\r\n    private IsEnum delete;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/entity/TenantCoupon.java b/backend-common/src/main/java/tech/wejoy/billiard/common/entity/TenantCoupon.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/entity/TenantCoupon.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/entity/TenantCoupon.java	(date 1748936929422)
@@ -29,6 +29,9 @@
 
     private CouponTypeEnum type;
 
+    @Column(columnDefinition = "text")
+    private String tableType;
+
     private Integer minutes;
 
     @Column(columnDefinition = "text")
Index: backend-common/src/main/java/tech/wejoy/billiard/common/dto/AdminCouponSaveDto.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.dto;\r\n\r\nimport lombok.Data;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.enums.CouponTypeEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class AdminCouponSaveDto {\r\n\r\n    private Long id;\r\n\r\n    private Long tenantId;\r\n\r\n    @Schema(description = \"可用门店id列表\")\r\n    private List<Long> clubIds;\r\n\r\n    @Schema(description = \"优惠券名称\")\r\n    private String title;\r\n\r\n    @Schema(description = \"优惠券描述\")\r\n    private String description;\r\n\r\n    @Schema(description = \"优惠券价格\")\r\n    private BigDecimal price;\r\n\r\n    @Schema(description = \"优惠券类型\")\r\n    private CouponTypeEnum type;\r\n\r\n    @Schema(description = \"优惠券有效时间\")\r\n    private Integer minutes;\r\n\r\n    @Schema(description = \"优惠券有效时段\")\r\n    private String period;\r\n\r\n    @Schema(description = \"优惠券过期天数\")\r\n    private Integer expireDays;\r\n\r\n    @Schema(description = \"用户限制\")\r\n    private Integer userTotalLimit;\r\n\r\n    @Schema(description = \"每日限制\")\r\n    private Integer userDayLimit;\r\n\r\n    @Schema(description = \"每日限制\")\r\n    private Integer dayLimit;\r\n\r\n    @Schema(description = \"排序\")\r\n    private Integer seq;\r\n\r\n    @Schema(description = \"上架状态\")\r\n    private IsEnum status;\r\n\r\n    @Schema(description = \"是否展示\")\r\n    private IsEnum show;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AdminCouponSaveDto.java b/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AdminCouponSaveDto.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AdminCouponSaveDto.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/dto/AdminCouponSaveDto.java	(date 1748936929300)
@@ -4,6 +4,7 @@
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
 import tech.wejoy.billiard.common.enums.CouponTypeEnum;
 import tech.wejoy.billiard.common.enums.IsEnum;
+import tech.wejoy.billiard.common.enums.TableTypeEnum;
 
 import java.math.BigDecimal;
 import java.util.List;
@@ -57,4 +58,6 @@
     @Schema(description = "是否展示")
     private IsEnum show;
 
+    private List<TableTypeEnum> tableType;
+
 }
Index: backend-common/src/main/java/tech/wejoy/billiard/common/enums/TableTypeEnum.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/enums/TableTypeEnum.java b/backend-common/src/main/java/tech/wejoy/billiard/common/enums/TableTypeEnum.java
new file mode 100644
--- /dev/null	(date 1748936311188)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/enums/TableTypeEnum.java	(date 1748936311188)
@@ -0,0 +1,14 @@
+package tech.wejoy.billiard.common.enums;
+
+
+import com.congeer.core.bean.BaseEnum;
+
+public enum TableTypeEnum implements BaseEnum<TableTypeEnum> {
+    NONE,
+    SILVER_LEG,
+    GOLD_LEG,
+    ROSE_GOLD,
+    SNOOKER,
+    JOE;
+
+}
Index: backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminAssistantBo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.bo;\r\n\r\nimport com.congeer.utils.JsonUtils;\r\nimport lombok.Data;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.enums.GenderEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDate;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class AdminAssistantBo {\r\n\r\n    @Schema(description = \"ID\")\r\n    private Long id;\r\n\r\n    @Schema(description = \"姓名\")\r\n    private String name;\r\n\r\n    private String phone;\r\n\r\n    private String realName;\r\n\r\n    private String idCard;\r\n\r\n    @Schema(description = \"头像\")\r\n    private String avatar;\r\n\r\n    @Schema(description = \"标签\")\r\n    private List<String> tags;\r\n\r\n    @Schema(description = \"性别\")\r\n    private GenderEnum gender;\r\n\r\n    @Schema(description = \"生日\")\r\n    private LocalDate birth;\r\n\r\n    private BigDecimal price;\r\n\r\n    @Schema(description = \"入职时间\")\r\n    private LocalDate startWork;\r\n\r\n    @Schema(description = \"可用门店\")\r\n    private List<ApiClubBo> clubs;\r\n    \r\n    private IsEnum enable;\r\n\r\n    public void setTags(String tags) {\r\n        if (tags != null) {\r\n            this.tags = JsonUtils.toList(tags, String.class);\r\n        }\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminAssistantBo.java b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminAssistantBo.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminAssistantBo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminAssistantBo.java	(date 1748936929337)
@@ -44,9 +44,11 @@
 
     @Schema(description = "可用门店")
     private List<ApiClubBo> clubs;
-    
+
     private IsEnum enable;
 
+    private IsEnum online;
+
     public void setTags(String tags) {
         if (tags != null) {
             this.tags = JsonUtils.toList(tags, String.class);
Index: backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminCouponBo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.bo;\r\n\r\nimport lombok.Data;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.enums.CouponTypeEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class AdminCouponBo {\r\n\r\n    private Long id;\r\n\r\n    @Schema(description = \"优惠券名称\")\r\n    private String title;\r\n\r\n    @Schema(description = \"优惠券描述\")\r\n    private String description;\r\n\r\n    @Schema(description = \"优惠券价格\")\r\n    private BigDecimal price;\r\n\r\n    @Schema(description = \"优惠券类型\")\r\n    private CouponTypeEnum type;\r\n\r\n    @Schema(description = \"优惠券有效时间\")\r\n    private Integer minutes;\r\n\r\n    @Schema(description = \"优惠券有效时段\")\r\n    private String period;\r\n\r\n    @Schema(description = \"优惠券过期天数\")\r\n    private Integer expireDays;\r\n\r\n    @Schema(description = \"用户限制\")\r\n    private Integer userTotalLimit;\r\n\r\n    @Schema(description = \"每日限制\")\r\n    private Integer userDayLimit;\r\n\r\n    @Schema(description = \"每日限制\")\r\n    private Integer dayLimit;\r\n\r\n    @Schema(description = \"排序\")\r\n    private Integer seq;\r\n\r\n    @Schema(description = \"上架状态\")\r\n    private IsEnum status;\r\n\r\n    @Schema(description = \"是否展示\")\r\n    private IsEnum show;\r\n\r\n    private List<Long> clubIds;\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminCouponBo.java b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminCouponBo.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminCouponBo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AdminCouponBo.java	(date 1748936929318)
@@ -1,9 +1,13 @@
 package tech.wejoy.billiard.common.bo;
 
+import com.congeer.utils.BeanUtils;
+import com.congeer.utils.JsonUtils;
 import lombok.Data;
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
+import tech.wejoy.billiard.common.entity.TenantCoupon;
 import tech.wejoy.billiard.common.enums.CouponTypeEnum;
 import tech.wejoy.billiard.common.enums.IsEnum;
+import tech.wejoy.billiard.common.enums.TableTypeEnum;
 
 import java.math.BigDecimal;
 import java.util.List;
@@ -54,4 +58,22 @@
 
     private List<Long> clubIds;
 
+    private List<TableTypeEnum> tableType;
+
+    public static AdminCouponBo from(TenantCoupon obj) {
+        AdminCouponBo copy = BeanUtils.copy(obj, AdminCouponBo.class);
+        if (obj.getTableType() != null) {
+            copy.setTableType(JsonUtils.toList(obj.getTableType(), TableTypeEnum.class));
+        }
+        return copy;
+    }
+
+    public TenantCoupon to() {
+        TenantCoupon copy = BeanUtils.copy(this, TenantCoupon.class);
+        if (this.getTableType() != null) {
+            copy.setTableType(JsonUtils.toJson(this.getTableType()));
+        }
+        return copy;
+    }
+
 }
Index: backend-common/src/main/java/tech/wejoy/billiard/common/enums/AssistantLevelEnum.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/enums/AssistantLevelEnum.java b/backend-common/src/main/java/tech/wejoy/billiard/common/enums/AssistantLevelEnum.java
new file mode 100644
--- /dev/null	(date 1748936311207)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/enums/AssistantLevelEnum.java	(date 1748936311207)
@@ -0,0 +1,10 @@
+package tech.wejoy.billiard.common.enums;
+
+import com.congeer.core.bean.BaseEnum;
+
+public enum AssistantLevelEnum implements BaseEnum<AssistantLevelEnum> {
+    NONE,
+    PRIMARY,
+    INTERMEDIATE,
+    SENIOR;
+}
Index: backend-common/src/main/java/tech/wejoy/billiard/common/bo/AssistantBo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.bo;\r\n\r\nimport com.congeer.utils.JsonUtils;\r\nimport lombok.Data;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.enums.GenderEnum;\r\nimport tech.wejoy.billiard.common.enums.IsEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDate;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class AssistantBo {\r\n\r\n    @Schema(description = \"ID\")\r\n    private Long id;\r\n\r\n    @Schema(description = \"姓名\")\r\n    private String name;\r\n\r\n    @Schema(description = \"头像\")\r\n    private String avatar;\r\n\r\n    @Schema(description = \"标签\")\r\n    private List<String> tags;\r\n\r\n    @Schema(description = \"性别\")\r\n    private GenderEnum gender;\r\n\r\n    @Schema(description = \"生日\")\r\n    private LocalDate birth;\r\n\r\n    private BigDecimal price;\r\n\r\n    @Schema(description = \"入职时间\")\r\n    private LocalDate startWork;\r\n\r\n    @Schema(description = \"可用门店\")\r\n    private List<ApiClubBo> clubs;\r\n\r\n    private IsEnum enable;\r\n\r\n    public void setTags(String tags) {\r\n        if (tags != null) {\r\n            this.tags = JsonUtils.toList(tags, String.class);\r\n        }\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AssistantBo.java b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AssistantBo.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AssistantBo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/AssistantBo.java	(date 1748936929238)
@@ -3,6 +3,7 @@
 import com.congeer.utils.JsonUtils;
 import lombok.Data;
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
+import tech.wejoy.billiard.common.enums.AssistantLevelEnum;
 import tech.wejoy.billiard.common.enums.GenderEnum;
 import tech.wejoy.billiard.common.enums.IsEnum;
 
@@ -36,11 +37,15 @@
     @Schema(description = "入职时间")
     private LocalDate startWork;
 
+    private AssistantLevelEnum level;
+
     @Schema(description = "可用门店")
     private List<ApiClubBo> clubs;
 
     private IsEnum enable;
 
+    private IsEnum online;
+
     public void setTags(String tags) {
         if (tags != null) {
             this.tags = JsonUtils.toList(tags, String.class);
Index: backend-common/src/main/java/tech/wejoy/billiard/common/bo/TableBo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.common.bo;\r\n\r\nimport com.congeer.utils.BeanUtils;\r\nimport com.congeer.utils.JsonUtils;\r\nimport lombok.Data;\r\nimport lombok.experimental.Accessors;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport org.eclipse.microprofile.openapi.annotations.media.Schema;\r\nimport tech.wejoy.billiard.common.entity.ClubTable;\r\nimport tech.wejoy.billiard.common.enums.TableStatusEnum;\r\n\r\nimport java.math.BigDecimal;\r\nimport java.time.LocalDateTime;\r\nimport java.util.List;\r\n\r\n@Data\r\n@Accessors(chain = true)\r\n@Schema(description = \"桌台\")\r\npublic class TableBo {\r\n\r\n    @Schema(description = \"桌台ID\")\r\n    private Long id;\r\n\r\n    @Schema(description = \"桌台名称\")\r\n    private String name;\r\n\r\n    @Schema(description = \"桌台描述\")\r\n    private String description;\r\n\r\n    @Schema(description = \"桌台图片\")\r\n    private String headImage;\r\n\r\n    @Schema(description = \"桌台等级\")\r\n    private Integer tableLevel;\r\n\r\n    @Schema(description = \"桌台时间段\")\r\n    private List<TimeSlotBo> timeSlots;\r\n\r\n    @Schema(description = \"桌台状态\")\r\n    private TableStatusEnum status;\r\n\r\n    @Schema(description = \"结束时间\")\r\n    private LocalDateTime endTime;\r\n\r\n    @Schema(description = \"押金\")\r\n    private BigDecimal deposit;\r\n\r\n    private Long userId;\r\n\r\n    public static TableBo from(ClubTable table) {\r\n        TableBo copy = BeanUtils.copy(table, TableBo.class);\r\n        if (StringUtils.isNotBlank(table.getTimeSlots())) {\r\n            copy.setTimeSlots(JsonUtils.toList(table.getTimeSlots(), TimeSlotBo.class));\r\n        }\r\n        return copy;\r\n    }\r\n\r\n    public ClubTable to() {\r\n        ClubTable copy = BeanUtils.copy(this, ClubTable.class);\r\n        if (this.getTimeSlots() != null) {\r\n            copy.setTimeSlots(JsonUtils.toJson(this.getTimeSlots()));\r\n        }\r\n        return copy;\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/TableBo.java b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/TableBo.java
--- a/backend-common/src/main/java/tech/wejoy/billiard/common/bo/TableBo.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-common/src/main/java/tech/wejoy/billiard/common/bo/TableBo.java	(date 1748936929411)
@@ -8,6 +8,7 @@
 import org.eclipse.microprofile.openapi.annotations.media.Schema;
 import tech.wejoy.billiard.common.entity.ClubTable;
 import tech.wejoy.billiard.common.enums.TableStatusEnum;
+import tech.wejoy.billiard.common.enums.TableTypeEnum;
 
 import java.math.BigDecimal;
 import java.time.LocalDateTime;
@@ -30,6 +31,8 @@
     @Schema(description = "桌台图片")
     private String headImage;
 
+    private TableTypeEnum type;
+
     @Schema(description = "桌台等级")
     private Integer tableLevel;
 
Index: backend-api/src/main/java/tech/wejoy/billiard/api/service/WxUserService.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package tech.wejoy.billiard.api.service;\r\n\r\nimport cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;\r\nimport cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;\r\nimport com.congeer.core.exception.BaseException;\r\nimport com.congeer.security.core.bean.SessionInfo;\r\nimport com.congeer.security.core.utils.SecurityHolder;\r\nimport com.congeer.utils.JsonUtils;\r\nimport com.congeer.web.util.WebUtils;\r\nimport io.vertx.core.http.HttpServerRequest;\r\nimport jakarta.enterprise.context.ApplicationScoped;\r\nimport jakarta.inject.Inject;\r\nimport jakarta.transaction.Transactional;\r\nimport jakarta.ws.rs.core.Context;\r\nimport lombok.extern.slf4j.Slf4j;\r\nimport me.chanjar.weixin.common.error.WxErrorException;\r\nimport org.apache.commons.lang3.StringUtils;\r\nimport tech.wejoy.billiard.api.bo.WxLoginBo;\r\nimport tech.wejoy.billiard.api.bo.WxUserBo;\r\nimport tech.wejoy.billiard.common.bo.AssistantBo;\r\nimport tech.wejoy.billiard.common.entity.ClientUser;\r\nimport tech.wejoy.billiard.common.enums.GenderEnum;\r\nimport tech.wejoy.billiard.common.service.AssistantService;\r\nimport tech.wejoy.billiard.common.service.ClientUserService;\r\nimport tech.wejoy.billiard.common.third.ThirdServiceHolder;\r\n\r\nimport java.io.File;\r\nimport java.time.LocalDateTime;\r\n\r\n@ApplicationScoped\r\n@Slf4j\r\npublic class WxUserService {\r\n\r\n    @Inject\r\n    ClientUserService clientUserService;\r\n\r\n    @Inject\r\n    AssistantService assistantService;\r\n\r\n    @Context\r\n    HttpServerRequest request;\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public WxLoginBo login(String code) {\r\n        String sessionKey = null;\r\n        String openId = null;\r\n        try {\r\n            WxMaJscode2SessionResult result = ThirdServiceHolder.wxMaService(\"user\").getUserService().getSessionInfo(code);\r\n            sessionKey = result.getSessionKey();\r\n            openId = result.getOpenid();\r\n        } catch (Exception e) {\r\n            log.error(\"获取sessionKey失败:{}\", e.getMessage(), e);\r\n        }\r\n\r\n        if (sessionKey == null || openId == null) {\r\n            throw new BaseException(\"获取sessionKey失败\");\r\n        }\r\n        ClientUser user = clientUserService.getUserByOpenId(openId);\r\n\r\n        if (user != null) {\r\n            // 更新用户信息\r\n            user.setLastLoginIp(WebUtils.getIpAddr(request));\r\n            user.setLastLoginAt(LocalDateTime.now());\r\n            clientUserService.save(user);\r\n        } else {\r\n            // 创建用户\r\n            user = new ClientUser();\r\n            user.setOpenId(openId);\r\n            user.setUsername(\"wx_\" + openId);\r\n            user.setLastLoginIp(WebUtils.getIpAddr(request));\r\n            user.setLastLoginAt(LocalDateTime.now());\r\n            String[] nicks = new String[]{\"高杆左塞\", \"低杆右塞\", \"长台吸库\", \"大力轻推\", \"爆杆响袋\"};\r\n            int index = (int) (Math.random() * nicks.length);\r\n            user.setNickname(nicks[index] + openId.substring(openId.length() - 4).toUpperCase());\r\n            user.setAvatar(\"\");\r\n            user.setGender(GenderEnum.NONE);\r\n            clientUserService.createUser(user);\r\n        }\r\n        SessionInfo<Long, WxUserBo> session = SecurityHolder.login(WxUserBo.from(user));\r\n        log.info(\"用户登录成功:{}\", JsonUtils.toJson(session));\r\n        WxLoginBo dto = new WxLoginBo();\r\n        dto.setId(user.getId());\r\n        dto.setToken(session.getToken());\r\n        dto.setNickname(user.getNickname());\r\n        dto.setAvatar(user.getAvatar());\r\n        dto.setHasPhone(StringUtils.isNotBlank(user.getPhone()));\r\n        if (user.getPhone() != null) {\r\n            dto.setPhone(user.getPhone().replaceAll(\"(\\\\d{3})\\\\d{4}(\\\\d{4})\", \"$1****$2\"));\r\n        }\r\n        AssistantBo assistant = assistantService.getAssistantByUserId(user.getId());\r\n        if (assistant != null) {\r\n            dto.setAssistantId(assistant.getId());\r\n        }\r\n        dto.setBirth(user.getBirth());\r\n        return dto;\r\n    }\r\n\r\n    @Transactional(rollbackOn = Exception.class)\r\n    public void updatePhone(String code) {\r\n        SessionInfo<Long, WxUserBo> sessionInfo = SecurityHolder.session();\r\n        WxUserBo user = sessionInfo.getUser();\r\n        String phoneNumber = null;\r\n        try {\r\n            WxMaPhoneNumberInfo phoneNoInfo = ThirdServiceHolder.wxMaService(\"user\").getUserService().getPhoneNoInfo(code);\r\n            phoneNumber = phoneNoInfo.getPhoneNumber();\r\n        } catch (Exception e) {\r\n            log.error(\"获取手机号失败:{}\", e.getMessage(), e);\r\n        }\r\n        if (phoneNumber == null) {\r\n            throw new BaseException(\"获取手机号失败\");\r\n        }\r\n        ClientUser userByPhone = clientUserService.getUserByPhone(phoneNumber);\r\n        if (userByPhone != null) {\r\n            throw new BaseException(\"手机号已被绑定\");\r\n        }\r\n        ClientUser userByOpenId = clientUserService.getUserByOpenId(user.getOpenId());\r\n        userByOpenId.setPhone(phoneNumber);\r\n        clientUserService.save(userByOpenId);\r\n    }\r\n\r\n    public File getAssistantQrcode(Long id) {\r\n        try {\r\n            return ThirdServiceHolder.wxMaService(\"user\").getQrcodeService().createQrcode(\"pages/assistant/detail/index?id=\" + id);\r\n        } catch (WxErrorException e) {\r\n            log.error(\"获取二维码失败:{}\", e.getMessage(), e);\r\n            throw new BaseException(\"获取二维码失败\");\r\n        }\r\n    }\r\n\r\n    public File getCompetitionQrcode(Long id) {\r\n        try {\r\n            return ThirdServiceHolder.wxMaService(\"user\").getQrcodeService().createQrcode(\"pages/competition/detail/index?id=\" + id);\r\n        } catch (WxErrorException e) {\r\n            log.error(\"获取二维码失败:{}\", e.getMessage(), e);\r\n            throw new BaseException(\"获取二维码失败\");\r\n        }\r\n    }\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/backend-api/src/main/java/tech/wejoy/billiard/api/service/WxUserService.java b/backend-api/src/main/java/tech/wejoy/billiard/api/service/WxUserService.java
--- a/backend-api/src/main/java/tech/wejoy/billiard/api/service/WxUserService.java	(revision e9aec1e2aeb73da6a3fb32796c34f22a349aa6de)
+++ b/backend-api/src/main/java/tech/wejoy/billiard/api/service/WxUserService.java	(date 1748936929262)
@@ -90,6 +90,7 @@
         AssistantBo assistant = assistantService.getAssistantByUserId(user.getId());
         if (assistant != null) {
             dto.setAssistantId(assistant.getId());
+            dto.setAssistantOnline(assistant.getOnline());
         }
         dto.setBirth(user.getBirth());
         return dto;
