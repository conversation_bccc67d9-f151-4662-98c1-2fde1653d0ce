package tech.wejoy.billiard.common.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.DateUtils;
import com.congeer.utils.HttpUtils;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.*;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;
import tech.wejoy.billiard.common.manager.*;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;
import tech.wejoy.billiard.common.third.douyin.client.DouyinClient;
import tech.wejoy.billiard.common.third.douyin.request.*;
import tech.wejoy.billiard.common.third.douyin.response.*;
import tech.wejoy.billiard.common.third.meituan.client.MeituanClient;
import tech.wejoy.billiard.common.third.meituan.request.*;
import tech.wejoy.billiard.common.third.meituan.response.*;
import tech.wejoy.billiard.common.third.meituan.utils.SignUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class TicketService {

    private final MeituanTicketManager meituanTicketManager;

    private final DouyinTicketManager douyinTicketManager;

    private final ClubManager clubManager;

    private final ClientUserManager clientUserManager;

    private final ConfigManager configManager;

    // Meituan OAuth 授权回调处理
    public void handleMeituanAuth(String code, String businessId, String state) {
        // 从配置中获取美团应用信息（生产环境）
        MeituanConfig config = configManager.getMeituan("default");
        if (config == null) {
            throw new BaseException("美团配置不存在");
        }

        if (StringUtils.isBlank(code) || StringUtils.isBlank(config.getAppKey())) {
            throw new BaseException("美团授权参数不完整");
        }

        log.info("处理美团授权回调: code={}, developerId={}, businessId={}",
                code, config.getAppKey(), businessId);

        // 查找匹配的美团账号
        MeituanAccount account = new MeituanAccount();

        // 获取美团客户端
        MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");

        // 使用授权码换取访问令牌
        TokenResponse tokenResponse = meituanClient.getOAuthToken(code, businessId, state);
        if (tokenResponse == null || tokenResponse.getData() == null) {
            throw new BaseException("获取美团访问令牌失败");
        }

        // 更新账号的令牌信息
        account.setAccessToken(tokenResponse.getData().getAccessToken());
        account.setRefreshToken(tokenResponse.getData().getRefreshToken());
        account.setExpiresIn(String.valueOf(tokenResponse.getData().getExpireIn()));
        account.setExpireAt(java.time.LocalDateTime.now().plusSeconds(tokenResponse.getData().getExpireIn() - 60));
        // 设置关联的美团店铺ID（opBizCode）
        if (tokenResponse.getData().getOpBizCode() != null) {
            // 如果有定义的字段存储opBizCode，可以这里设置
            account.setBid(tokenResponse.getData().getOpBizCode());
            log.info("关联美团店铺ID: {}, 店铺名称: {}",
                    tokenResponse.getData().getOpBizCode(), tokenResponse.getData().getOpBizName());
        }

        // 保存美团账号信息
        meituanTicketManager.saveMeituanAccount(account);

        MeituanShop shop = new MeituanShop();
        shop.setAccountId(account.getId());
        shop.setName(tokenResponse.getData().getOpBizName());
        shop.setUuid(tokenResponse.getData().getOpBizCode());
        meituanTicketManager.saveMeituanShop(shop);
    }

    /**
     * 生成美团授权URL
     */
    public String generateMeituanAuthUrl() {
        // 从配置中获取美团应用信息（生产环境）
        MeituanConfig config = configManager.getMeituan("default");
        if (config == null) {
            throw new BaseException("美团配置不存在");
        }

        // 准备需要签名的参数
        Map<String, String> params = new HashMap<>();

        // 必要参数
        params.put("developerId", config.getAppKey());
        params.put("businessId", "58"); // 美团餐饮业务的businessId

        // 添加必要参数
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
        String state = "new_auth_" + timestamp;

        params.put("timestamp", timestamp);
        params.put("charset", "UTF-8");
        params.put("state", state);

        // 计算签名
        String sign = SignUtils.getSign(config.getAppSecret(), params);
        params.put("sign", sign);
        // 构建授权URL
        StringBuilder authUrl = new StringBuilder("https://open-erp.meituan.com/general/auth?");

        // 添加参数到URL
        boolean first = true;
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (!first) {
                authUrl.append("&");
            }
            first = false;

            authUrl.append(entry.getKey()).append("=").append(entry.getValue());
        }

        return authUrl.toString();
    }


    @Transactional(rollbackOn = Exception.class)
    public TicketBo check(TicketCheckDto dto) {
        if (dto.getTicketNo().startsWith("https://v.douyin.com")) {
            return checkDouyin(dto);
        }
        try {
            return checkExistMeituan(dto);
        } catch (BaseException e) {
            if (e.getCode().equals("NotExist")) {
                return checkDouyin(dto);
            }
            throw e;
        }
    }

    private TicketBo checkDouyin(TicketCheckDto dto) {
        String ticketNo = dto.getTicketNo();
        String code = null;
        String encryptedData = null;
        if (ticketNo.startsWith("https://")) {
            encryptedData = getObjectId(ticketNo);
        } else {
            code = ticketNo.trim();
        }
        DouyinTicket ticket = douyinTicketManager.fetchDouyinTicketByCode(code, encryptedData);
        DouyinDeal deal;
        if (ticket == null) {
            ClubTable table = clubManager.fetchClubTableById(dto.getTableId());
            DouyinShop douyinShop = douyinTicketManager.fetchDouyinShopByClubId(table.getClubId());
            DouyinClient douyinClient = ThirdServiceHolder.douyinClient("default");
            PrepareCertificateRequest request = new PrepareCertificateRequest();
            request.setCode(code);
            request.setEncryptedData(encryptedData);
            request.setPoiId(douyinShop.getPoiId());
            PrepareCertificateResponse response = douyinClient.prepareCertificate(request);
            if (response.getErrorCode() != 0) {
                throw new BaseException(response.getDescription());
            }
            PrepareCertificateResponse.Certificate first = response.getCertificates().getFirst();
            deal = douyinTicketManager.getDouyinDealBySkuId(first.getSku().getSkuId());
            if (deal == null || deal.getStatus() == IsEnum.FALSE) {
                throw new BaseException("未设置当前券，请联系客服");
            }
            ticket = saveTicket(first, deal, code, encryptedData);
        } else {
            ticket.setUsed(IsEnum.FALSE);
            deal = douyinTicketManager.getDouyinDealBySkuId(ticket.getSkuId());
        }
        ClientUserTicket clientUserTicket = saveUserTicket(ticket, dto.getUserId());
        return toTicketBo(clientUserTicket, deal);
    }

    private static TicketBo toTicketBo(ClientUserTicket clientUserTicket, ChannelDeal deal) {
        TicketBo ticketBo = new TicketBo();
        ticketBo.setChannel(clientUserTicket.getChannel());
        ticketBo.setId(clientUserTicket.getId());
        ticketBo.setUserId(clientUserTicket.getUserId());
        ticketBo.setEndTime(clientUserTicket.getEndTime());
        ticketBo.setType(deal.getType());
        ticketBo.setMinutes(deal.getMinutes());
        ticketBo.setName(deal.getName());
        ticketBo.setPeriod(deal.getPeriod());
        ticketBo.setStatus(IsEnum.TRUE);
        return ticketBo;
    }

    private static String getObjectId(String ticketNo) {
        HttpUtils.HttpResult send = HttpUtils.get(ticketNo).send();
        String location = send.getHeader("location");
        if (StringUtils.isBlank(location)) {
            throw new BaseException("券不存在");
        }
        int i = location.indexOf("?");
        if (i == -1) {
            throw new BaseException("券不存在");
        }
        String[] split = location.substring(i + 1).split("&");
        Map<String, String> map = new HashMap<>();
        for (String s : split) {
            String[] kv = s.split("=");
            if (kv.length != 2) {
                continue;
            }
            map.put(kv[0], kv[1]);
        }
        ticketNo = map.get("object_id");
        return ticketNo;
    }

    private TicketBo checkExistMeituan(TicketCheckDto dto) {
        String ticketNo = dto.getTicketNo().replace(" ", "");
        MeituanTicket ticket = meituanTicketManager.fetchMeituanTicketByCode(ticketNo);
        ClubTable table = clubManager.fetchClubTableById(dto.getTableId());
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShopByClubId(table.getClubId());
        MeituanDeal deal;
        if (meituanShop != null) {
            String meituanShopUuid = meituanShop.getUuid();
            MeituanAccount meituanAccount = meituanTicketManager.fetchMeituanAccountById(meituanShop.getAccountId());
            MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");
            ReceiptPrepareRequest request = new ReceiptPrepareRequest();
            request.setReceiptCode(ticketNo);
            request.setOpenShopUuid(meituanShopUuid);
            ReceiptValidateResponse resp = meituanClient.receiptPrepare(meituanAccount, request);
            if (resp == null) {
                throw new BaseException("券不存在");
            }
            deal = meituanTicketManager.getMeituanDealByDealId(meituanShop.getAccountId(), resp.getDealId());
            if (deal == null || deal.getStatus() == IsEnum.FALSE) {
                throw new BaseException("未设置当前券，请联系客服");
            }
            if (ticket == null || !Objects.equals(ticket.getDealId(), deal.getDealId())) {
                ticket = saveTicket(resp, deal);
            }
            if (ticket.getUsed() == IsEnum.TRUE) {
                throw new BaseException("券已使用");
            }
            ClientUserTicket clientUserTicket = saveUserTicket(ticket, dto.getUserId());
            return toTicketBo(clientUserTicket, deal);
        } else {
            throw new BaseException("未设置当前券，请联系客服");
        }
    }

    private ClientUserTicket saveUserTicket(DouyinTicket douyinTicket, Long userId) {
        ClientUserTicket ticket = clientUserManager.fetchTicketByChannelId(TicketChannelEnum.DOUYIN, douyinTicket.getId(), userId);
        if (ticket != null) {
            ticket.setUsed(douyinTicket.getUsed());
            return ticket;
        }
        ticket = new ClientUserTicket();
        ticket.setChannelId(douyinTicket.getId());
        ticket.setUserId(userId);
        ticket.setDealId(douyinTicket.getDealId());
        ticket.setChannel(TicketChannelEnum.DOUYIN);
        ticket.setEndTime(douyinTicket.getEndDate());
        ticket.setUsed(douyinTicket.getUsed());
        clientUserManager.saveClientUserTicket(ticket);
        return ticket;
    }

    private ClientUserTicket saveUserTicket(MeituanTicket meituanTicket, Long userId) {
        ClientUserTicket ticket = clientUserManager.fetchTicketByChannelId(TicketChannelEnum.MEITUAN, meituanTicket.getId(), userId);
        if (ticket != null) {
            return ticket;
        }
        ticket = new ClientUserTicket();
        ticket.setChannelId(meituanTicket.getId());
        ticket.setUserId(userId);
        ticket.setDealId(meituanTicket.getDealId());
        ticket.setChannel(TicketChannelEnum.MEITUAN);
        ticket.setEndTime(meituanTicket.getEndDate());
        ticket.setUsed(meituanTicket.getUsed());
        clientUserManager.saveClientUserTicket(ticket);
        return ticket;
    }

    private DouyinTicket saveTicket(PrepareCertificateResponse.Certificate resp, DouyinDeal deal, String code, String encryptedData) {
        DouyinTicket ticket = douyinTicketManager.fetchDouyinTicketByCode(code, encryptedData);
        if (ticket != null) {
            return ticket;
        }
        ticket = new DouyinTicket();
        ticket.setCode(code);
        ticket.setEncryptedData(encryptedData);
        ticket.setDealId(deal.getId());
        ticket.setSkuId(resp.getSku().getSkuId());
        ticket.setUsed(IsEnum.FALSE);
        ticket.setEndDate(DateUtils.getLocalDateTime(resp.getExpireTime() * 1000L));
        douyinTicketManager.saveDouyinTicket(ticket);
        return ticket;
    }

    private MeituanTicket saveTicket(ReceiptValidateResponse resp, MeituanDeal deal) {
        MeituanTicket ticket = meituanTicketManager.fetchMeituanTicketByCodeAndDealId(resp.getReceiptCode(), deal.getId());
        if (ticket != null) {
            return ticket;
        }
        ticket = new MeituanTicket();
        ticket.setCode(resp.getReceiptCode());
        ticket.setDealId(deal.getId());
        ticket.setMeituanDealId(deal.getDealId());
        ticket.setDealGroupId(resp.getDealgroupId());
        ticket.setTitle(deal.getName());
        ticket.setMobile(resp.getMobile());
        ticket.setUsed(IsEnum.FALSE);
        ticket.setEndDate(DateUtils.getLocalDateTime(resp.getReceiptEndDate()));
        ticket.setBizType(resp.getBizType());
        meituanTicketManager.saveMeituanTicket(ticket);
        return ticket;
    }

    public ClientUserTicket getTicketById(Long ticketId) {
        return clientUserManager.fetchTicketById(ticketId);
    }

    public List<TicketBo> list(TicketQueryDto dto) {
        refreshUserChannelTicket(dto);
        return listLocal(dto.getTableId(), dto.getUserId());
    }

    public List<TicketBo> listLocal(long tableId, long userId) {
        ClubTable table = clubManager.fetchClubTableById(tableId);
        List<ClubChannelDealRel> clubChannelDeal = clubManager.fetchClubChannelDealRelByClubId(table.getClubId());
        if (clubChannelDeal.isEmpty()) {
            return Lists.newArrayList();
        }
        List<Long> meituanDealIds = clubChannelDeal.stream().filter(v -> v.getChannel() == TicketChannelEnum.MEITUAN).map(ClubChannelDealRel::getDealId).toList();
        List<Long> douyinDealIds = clubChannelDeal.stream().filter(v -> v.getChannel() == TicketChannelEnum.DOUYIN).map(ClubChannelDealRel::getDealId).toList();
        List<MeituanDeal> meituanDeals = meituanTicketManager.fetchMeituanDealByIds(meituanDealIds);
        List<DouyinDeal> douyinDeals = douyinTicketManager.fetchDouyinDealByIds(douyinDealIds);
        Map<Long, MeituanDeal> dealMap = meituanDeals.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        Map<Long, DouyinDeal> douyinDealMap = douyinDeals.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        List<ClientUserTicket> clientUserTickets = clientUserManager.fetchAvailableTicketByUserId(userId);
        return clientUserTickets.stream().filter(v -> (dealMap.containsKey(v.getDealId()) && v.getChannel() == TicketChannelEnum.MEITUAN)
                || (douyinDealMap.containsKey(v.getDealId()) && v.getChannel() == TicketChannelEnum.DOUYIN)).map(v -> {
            TicketBo ticketBo = new TicketBo();
            ticketBo.setChannel(v.getChannel());
            ticketBo.setId(v.getId());
            ticketBo.setUserId(v.getUserId());
            ticketBo.setEndTime(v.getEndTime());
            ticketBo.setStatus(IsEnum.TRUE);
            if (v.getChannel() == TicketChannelEnum.MEITUAN) {
                MeituanDeal deal = dealMap.get(v.getDealId());
                ticketBo.setName(deal.getName());
                ticketBo.setType(deal.getType());
                ticketBo.setMinutes(deal.getMinutes());
                ticketBo.setPeriod(deal.getPeriod());
            } else {
                DouyinDeal deal = douyinDealMap.get(v.getDealId());
                ticketBo.setName(deal.getName());
                ticketBo.setType(deal.getType());
                ticketBo.setMinutes(deal.getMinutes());
                ticketBo.setPeriod(deal.getPeriod());
            }
            return ticketBo;
        }).toList();
    }

    /**
     * 自动刷新用户所有券
     *
     * @param dto
     */
    private void refreshUserChannelTicket(TicketQueryDto dto) {
        ClubTable table = clubManager.fetchClubTableById(dto.getTableId());
        ClientUser user = clientUserManager.fetchUserById(dto.getUserId());
        if (StringUtils.isBlank(user.getPhone())) {
            return;
        }
        refreshMeituanTicket(table.getClubId(), user);
    }

    /**
     * 自动刷新用户美团券
     *
     * @param clubId
     * @param user
     */
    @Transactional(value = Transactional.TxType.REQUIRES_NEW)
    public void refreshMeituanTicket(long clubId, ClientUser user) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShopByClubId(clubId);
        if (meituanShop == null) {
            return;
        }
        String meituanShopUuid = meituanShop.getUuid();
        MeituanAccount meituanAccount = meituanTicketManager.fetchMeituanAccountById(meituanShop.getAccountId());
        MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");
        List<ClubChannelDealRel> clubChannelDeal = clubManager.fetchClubChannelDealRelByClubId(clubId, TicketChannelEnum.MEITUAN);
        if (clubChannelDeal.isEmpty()) {
            return;
        }
        List<Long> dealIds = clubChannelDeal.stream().map(ClubChannelDealRel::getDealId).toList();
        List<MeituanDeal> deals = meituanTicketManager.fetchMeituanDealByIds(dealIds);
        List<MeituanTicket> tickets = Lists.newCopyOnWriteArrayList();
        deals.parallelStream().forEach(deal -> {
            if (deal.getType() == null) {
                return;
            }
            QueryByPhoneRequest request = new QueryByPhoneRequest();
            request.setMobile(user.getPhone());
            request.setOpenShopUuid(meituanShopUuid);
            request.setDealId(deal.getDealId());
            request.setDealGroupId(deal.getDealGroupId());
            for (int i = 1; i <= 2; i++) {
                request.setPlatform(i);
                List<String> ticketNos = meituanClient.queryTicketByPhone(meituanAccount, request);
                for (String ticketNo : ticketNos) {
                    MeituanTicket ticket = new MeituanTicket();
                    ticket.setCode(ticketNo);
                    ticket.setDealId(deal.getId());
                    ticket.setMeituanDealId(deal.getDealId());
                    ticket.setDealGroupId(deal.getDealGroupId());
                    ticket.setTitle(deal.getName());
                    ticket.setMobile(user.getPhone());
                    ticket.setBizType(0);
                    ticket.setUsed(IsEnum.FALSE);
                    tickets.add(ticket);
                }
            }
        });
        List<String> codes = tickets.stream().map(MeituanTicket::getCode).toList();
        List<MeituanTicket> dbs = meituanTicketManager.fetchMeituanTicketByCodes(codes);
        Map<String, MeituanTicket> dbMaps = dbs.stream().collect(Collectors.toMap(MeituanTicket::getCode, Function.identity()));
        for (MeituanTicket ticket : tickets) {
            if (dbMaps.containsKey(ticket.getCode())) {
                MeituanTicket meituanTicket = dbMaps.get(ticket.getCode());
                if (!meituanTicket.getMobile().equals(ticket.getMobile())) {
                    meituanTicket.setMobile(ticket.getMobile());
                    meituanTicketManager.saveMeituanTicket(meituanTicket);
                }
                saveUserTicket(meituanTicket, user.getId());
            } else {
                ReceiptPrepareRequest request = new ReceiptPrepareRequest();
                request.setOpenShopUuid(meituanShopUuid);
                request.setReceiptCode(ticket.getCode());
                ReceiptValidateResponse resp = meituanClient.receiptPrepare(meituanAccount, request);
                ticket.setEndDate(DateUtils.getLocalDateTime(resp.getReceiptEndDate()));
                meituanTicketManager.saveMeituanTicket(ticket);
                saveUserTicket(ticket, user.getId());
            }
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void consumeTicket(OrderInfo order, ClientUserTicket ticket) {
        switch (ticket.getChannel()) {
            case MEITUAN:
                consumeMeituan(order, ticket);
                break;
            case DOUYIN:
                consumeDouyin(order, ticket);
                break;
            default:
                throw new BaseException("不支持的渠道");
        }
    }


    private void consumeDouyin(OrderInfo order, ClientUserTicket ticket) {
        DouyinShop douyinShop = douyinTicketManager.fetchDouyinShopByClubId(order.getClubId());
        DouyinTicket douyinTicket = douyinTicketManager.fetchDouyinTicketById(ticket.getChannelId());
        DouyinClient douyinClient = ThirdServiceHolder.douyinClient("default");
        DouyinDeal deal = douyinTicketManager.fetchDouyinDealById(douyinTicket.getDealId());
        PrepareCertificateRequest prepare = new PrepareCertificateRequest();
        prepare.setCode(douyinTicket.getCode());
        prepare.setEncryptedData(douyinTicket.getEncryptedData());
        prepare.setPoiId(douyinShop.getPoiId());
        PrepareCertificateResponse prepareResponse = douyinClient.prepareCertificate(prepare);
        if (prepareResponse.getErrorCode() != 0) {
            usedTicket(ticket, douyinTicket);
            throw new BaseException(prepareResponse.getDescription());
        }
        VerifyCertificateRequest verify = new VerifyCertificateRequest();
        verify.setVerifyToken(prepareResponse.getVerifyToken());
        verify.setPoiId(douyinShop.getPoiId());
        verify.setEncryptedCodes(Lists.newArrayList(prepareResponse.getCertificates().getFirst().getEncryptedCode()));
        VerifyCertificateResponse verifyResponse = douyinClient.verifyCertificate(verify);
        if (verifyResponse.getVerifyResults().isEmpty()) {
            throw new BaseException("券不存在");
        }
        VerifyCertificateResponse.VerifyResult result = verifyResponse.getVerifyResults().getFirst();
        if (result.getResult() != 0) {
            throw new BaseException(result.getMsg());
        }
        Map<String, Object> desc = new HashMap<>();
        desc.put("ticket", ticket);
        desc.put("response", verifyResponse);
        order.setDescription(JsonUtils.toJson(desc));
        order.setPayAmount(deal.getPrice());
        order.setRealAmount(deal.getPrice());
        ticket.setUsed(IsEnum.TRUE);
        clientUserManager.saveClientUserTicket(ticket);
        douyinTicket.setUsed(IsEnum.TRUE);
        douyinTicket.setOriginCode(result.getOriginCode());
        douyinTicket.setCertificateId(result.getCertificateId());
        douyinTicket.setVerifyId(result.getVerifyId());
        douyinTicketManager.saveDouyinTicket(douyinTicket);
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public void usedTicket(ClientUserTicket ticket, DouyinTicket douyinTicket) {
        ticket.setUsed(IsEnum.TRUE);
        clientUserManager.saveClientUserTicket(ticket);
        douyinTicket.setUsed(IsEnum.TRUE);
        douyinTicketManager.saveDouyinTicket(douyinTicket);
    }

    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public String checkExistMeituan(Long clubId, ClientUserTicket ticket) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShopByClubId(clubId);
        String meituanShopUuid = meituanShop.getUuid();
        MeituanAccount meituanAccount = meituanTicketManager.fetchMeituanAccountById(meituanShop.getAccountId());
        MeituanTicket meituanTicket = meituanTicketManager.fetchMeituanTicketById(ticket.getChannelId());
        MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");
        ReceiptPrepareRequest prepare = new ReceiptPrepareRequest();
        prepare.setOpenShopUuid(meituanShopUuid);
        prepare.setReceiptCode(meituanTicket.getCode());
        try {
            meituanClient.receiptPrepare(meituanAccount, prepare);
        } catch (BaseException e) {
            if (e.getCode().equals("Used")) {
                ticket.setUsed(IsEnum.TRUE);
                clientUserManager.saveClientUserTicket(ticket);
                meituanTicket.setUsed(IsEnum.TRUE);
                meituanTicketManager.saveMeituanTicket(meituanTicket);
            } else if (e.getCode().equals("NotExist") || e.getCode().equals("Expired")) {
                clientUserManager.deleteClientUserTicketByChannelId(ticket.getChannelId());
                meituanTicketManager.deleteMeituanTicketById(ticket.getChannelId());
            }
            return e.getMessage();
        }
        return null;
    }

    private void consumeMeituan(OrderInfo order, ClientUserTicket ticket) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShopByClubId(order.getClubId());
        String meituanShopUuid = meituanShop.getUuid();
        MeituanAccount meituanAccount = meituanTicketManager.fetchMeituanAccountById(meituanShop.getAccountId());
        MeituanTicket meituanTicket = meituanTicketManager.fetchMeituanTicketById(ticket.getChannelId());
        MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");
        String errMsg = checkExistMeituan(order.getClubId(), ticket);
        if (errMsg != null) {
            throw new BaseException(errMsg);
        }
        ReceiptConsumeRequest request = new ReceiptConsumeRequest();
        request.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        request.setReceiptCode(meituanTicket.getCode());
        request.setOpenShopUuid(meituanShopUuid);
        request.setCount(1);
        request.setAppShopAccount("system");
        request.setAppShopAccountName("system");
        ReceiptConsumeResponse response = meituanClient.receiptConsume(meituanAccount, request);
        Map<String, Object> desc = new HashMap<>();
        desc.put("ticket", ticket);
        desc.put("response", response);
        order.setDescription(JsonUtils.toJson(desc));
        order.setPayAmount(response.getDealPrice());
        order.setRealAmount(response.getDealPrice());
        ticket.setUsed(IsEnum.TRUE);
        meituanTicket.setUsed(IsEnum.TRUE);
        meituanTicketManager.saveMeituanTicket(meituanTicket);
        clientUserManager.saveClientUserTicket(ticket);
    }

    @Transactional(rollbackOn = Exception.class)
    public void refreshMeituanShop(Long accountId) {
        MeituanAccount meituanAccount = meituanTicketManager.fetchMeituanAccountById(accountId);
        MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");
        List<MeituanShop> meituanShops = meituanTicketManager.fetchMeituanShopByAccount(accountId);
        Map<String, MeituanShop> shopMap = meituanShops.stream().collect(Collectors.toMap(MeituanShop::getUuid, Function.identity()));
        List<Shop> shops = meituanClient.queryShopScope(meituanAccount);
        for (Shop meituanShop : shops) {
            MeituanShop shop = shopMap.get(meituanShop.getOpenShopUuid());
            if (shop == null) {
                shop = new MeituanShop();
                shop.setUuid(meituanShop.getOpenShopUuid());
                shop.setAccountId(accountId);
            }
            shop.setCity(meituanShop.getCityname());
            shop.setBranchName(meituanShop.getBranchname());
            shop.setName(meituanShop.getShopname());
            shop.setAddress(meituanShop.getShopaddress());
            meituanTicketManager.saveMeituanShop(shop);
        }

    }

    @Transactional(rollbackOn = Exception.class)
    public void bindMeituanShop(Long shopId, Long clubId) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShop(shopId);
        if (meituanShop == null) {
            throw new BaseException("门店不存在");
        }
        if (meituanShop.getClubId() != null && meituanShop.getClubId() != 0L) {
            throw new BaseException("门店已绑定");
        }
        meituanShop.setClubId(clubId);
        meituanTicketManager.saveMeituanShop(meituanShop);
        refreshMeituanDeal(meituanShop);
    }

    @Transactional(rollbackOn = Exception.class)
    public void unbindMeituanShop(Long shopId) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShop(shopId);
        if (meituanShop == null) {
            throw new BaseException("门店不存在");
        }
        if (meituanShop.getClubId() == null) {
            throw new BaseException("未绑定门店");
        }
        Long clubId = meituanShop.getClubId();
        meituanShop.setClubId(null);
        meituanTicketManager.saveMeituanShop(meituanShop);
        clubManager.deleteClubChannelDealRelByClubId(clubId, TicketChannelEnum.MEITUAN);
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteMeituanShop(Long shopId) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShop(shopId);
        if (meituanShop == null) {
            throw new BaseException("门店不存在");
        }

        // 如果已绑定门店，先解绑
        if (meituanShop.getClubId() != null && meituanShop.getClubId() != 0L) {
            Long clubId = meituanShop.getClubId();
            clubManager.deleteClubChannelDealRelByClubId(clubId, TicketChannelEnum.MEITUAN);
        }

        // 删除与店铺关联的美团券
        meituanTicketManager.deleteMeituanDealByAccountId(meituanShop.getAccountId());

        meituanTicketManager.deleteMeituanAccount(meituanShop.getAccountId());

        // 删除店铺
        meituanTicketManager.deleteMeituanShop(shopId);
    }

    @Transactional(rollbackOn = Exception.class)
    public void refreshMeituanShopDeal(Long shopId) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShop(shopId);
        if (meituanShop == null) {
            throw new BaseException("门店不存在");
        }
        if (meituanShop.getClubId() == null) {
            throw new BaseException("未绑定门店");
        }
        refreshMeituanDeal(meituanShop);
    }

    private void refreshMeituanDeal(MeituanShop meituanShop) {
        MeituanAccount meituanAccount = meituanTicketManager.fetchMeituanAccountById(meituanShop.getAccountId());
        MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");
        QueryShopTicketRequest request = new QueryShopTicketRequest();
        request.setOpenShopUuid(meituanShop.getUuid());
        request.setSource(2);
        List<Deal> deals = meituanClient.queryShopDeal(meituanAccount, request);
        List<ClubChannelDealRel> clubDeals = clubManager.fetchClubChannelDealRelByClubId(meituanShop.getClubId(), TicketChannelEnum.MEITUAN);
        Set<Long> hasDeals = clubDeals.stream().map(ClubChannelDealRel::getDealId).collect(Collectors.toSet());
        List<Long> dealIds = deals.stream().map(Deal::getDealId).toList();
        List<MeituanDeal> meituanDeals = meituanTicketManager.fetchMeituanDealByDealIds(meituanAccount.getId(), dealIds);
        Map<Long, MeituanDeal> dealMap = meituanDeals.stream().collect(Collectors.toMap(MeituanDeal::getDealId, Function.identity()));
        for (Deal deal : deals) {
            MeituanDeal meituanDeal = dealMap.get(deal.getDealId());
            if (meituanDeal == null) {
                meituanDeal = new MeituanDeal();
                meituanDeal.setDealId(deal.getDealId());
                meituanDeal.setDealGroupId(deal.getDealGroupId());
                meituanDeal.setAccountId(meituanAccount.getId());
            }
            meituanDeal.setName(deal.getTitle());
            meituanDeal.setPrice(deal.getPrice());
            meituanDeal.setMarketPrice(deal.getMarketPrice());
            meituanDeal.setSourceInfo(JsonUtils.toJson(deal));
            if (meituanDeal.getType() == null || meituanDeal.getType() == CouponTypeEnum.NONE) {
                meituanDeal.setStatus(IsEnum.FALSE);
            } else {
                meituanDeal.setStatus(IsEnum.TRUE);
            }
            meituanTicketManager.saveMeituanDeal(meituanDeal);
            if (!hasDeals.contains(meituanDeal.getId())) {
                ClubChannelDealRel clubChannelDealRel = new ClubChannelDealRel();
                clubChannelDealRel.setClubId(meituanShop.getClubId());
                clubChannelDealRel.setDealId(meituanDeal.getId());
                clubChannelDealRel.setChannel(TicketChannelEnum.MEITUAN);
                clubManager.saveClubChannelDealRel(clubChannelDealRel);
            }
        }
        clubManager.deleteClubChannelDealRelByClubId(meituanShop.getClubId(), TicketChannelEnum.MEITUAN, dealIds);
    }

    public ChannelDeal getDealByTicket(ClientUserTicket ticket) {
        return switch (ticket.getChannel()) {
            case MEITUAN -> getMeituanDeal(ticket.getDealId());
            case DOUYIN -> getDouyinDeal(ticket);
            default -> throw new BaseException("不支持的渠道");
        };
    }

    private DouyinDeal getDouyinDeal(ClientUserTicket ticket) {
        return douyinTicketManager.fetchDouyinDealById(ticket.getDealId());
    }

    private MeituanDeal getMeituanDeal(Long dealId) {
        return meituanTicketManager.fetchMeituanDealById(dealId);
    }

    @Transactional(rollbackOn = Exception.class)
    public void reverseTicket(OrderInfo order) {
        Map<String, Object> object = JsonUtils.toObject(order.getDescription(), new TypeReference<>() {
        });
        ClientUserTicket ticket = JsonUtils.toObject(JsonUtils.toJson(object.get("ticket")), ClientUserTicket.class);
        switch (ticket.getChannel()) {
            case MEITUAN:
                reverseMeituan(order, ticket);
                break;
            case DOUYIN:
                reverseDouyin(order, ticket);
                break;
            default:
                throw new BaseException("不支持的渠道");
        }
    }

    private void reverseDouyin(OrderInfo order, ClientUserTicket ticket) {
        DouyinClient douyinClient = ThirdServiceHolder.douyinClient("default");
        DouyinTicket douyinTicket = douyinTicketManager.fetchDouyinTicketById(ticket.getChannelId());
        DouyinDeal deal = douyinTicketManager.fetchDouyinDealById(douyinTicket.getDealId());
        CancelCertificateRequest request = new CancelCertificateRequest();
        request.setCertificateId(douyinTicket.getCertificateId());
        request.setVerifyId(douyinTicket.getVerifyId());
        ResultResponse resultResponse = douyinClient.cancelCertificate(request);
        if (resultResponse.getErrorCode() != 0) {
            throw new BaseException(resultResponse.getDescription());
        }
        order.setRefundInfo(JsonUtils.toJson(resultResponse));
        order.setPayAmount(deal.getPrice());
        order.setRealAmount(deal.getPrice());
    }

    private void reverseMeituan(OrderInfo order, ClientUserTicket ticket) {
        MeituanShop meituanShop = meituanTicketManager.fetchMeituanShopByClubId(order.getClubId());
        String meituanShopUuid = meituanShop.getUuid();
        MeituanAccount meituanAccount = meituanTicketManager.fetchMeituanAccountById(meituanShop.getAccountId());
        MeituanTicket meituanTicket = meituanTicketManager.fetchMeituanTicketById(ticket.getChannelId());
        MeituanClient meituanClient = ThirdServiceHolder.meituanClient("default");
        ReverseConsumeRequest request = new ReverseConsumeRequest();
        request.setDealId(meituanTicket.getMeituanDealId());
        request.setReceiptCode(meituanTicket.getCode());
        request.setOpenShopUuid(meituanShopUuid);
        request.setAppShopAccount("system");
        request.setAppShopAccountName("system");
        Object response = meituanClient.reverseConsume(meituanAccount, request);
        order.setRefundInfo(JsonUtils.toJson(response));
    }

    @Transactional(rollbackOn = Exception.class)
    public void refreshDouyinShop(Long accountId) {
        DouyinAccount douyinAccount = douyinTicketManager.fetchDouyinAccountById(accountId);
        DouyinClient douyinClient = ThirdServiceHolder.douyinClient("default");
        List<DouyinShop> douyinShops = douyinTicketManager.fetchDouyinShopByAccount(accountId);
        Map<String, DouyinShop> shopMap = douyinShops.stream().collect(Collectors.toMap(DouyinShop::getPoiId, Function.identity()));
        ShopRequest request = new ShopRequest();
        request.setAccountId(douyinAccount.getAccountId());
        ShopPage shopList = douyinClient.getShopList(request);
        for (ShopPage.Shop shop : shopList.getPois()) {
            DouyinShop douyinShop = shopMap.get(shop.getPoi().getPoiId());
            if (douyinShop == null) {
                douyinShop = new DouyinShop();
                douyinShop.setPoiId(shop.getPoi().getPoiId());
                douyinShop.setAccountId(accountId);
            }
            douyinShop.setName(shop.getPoi().getPoiName());
            douyinShop.setAddress(shop.getPoi().getAddress());
            douyinShop.setLatitude(shop.getPoi().getLatitude());
            douyinShop.setLongitude(shop.getPoi().getLongitude());
            douyinTicketManager.saveDouyinShop(douyinShop);
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void refreshDouyinDeal(Long accountId) {
        DouyinAccount douyinAccount = douyinTicketManager.fetchDouyinAccountById(accountId);
        DouyinClient douyinClient = ThirdServiceHolder.douyinClient("default");
        ProductRequest request = new ProductRequest();
        request.setAccountId(douyinAccount.getAccountId());
        ProductPage productList;
        List<DouyinDeal> douyinDeals = douyinTicketManager.fetchDouyinDealByAccountId(accountId);
        List<DouyinShop> douyinShops = douyinTicketManager.fetchDouyinShopByAccount(accountId);
        List<ClubChannelDealRel> clubDeals = clubManager.fetchClubChannelDealRel().stream().filter(v -> v.getChannel() == TicketChannelEnum.DOUYIN).toList();
        Map<Long, List<ClubChannelDealRel>> dealRelGroup = clubDeals.stream().collect(Collectors.groupingBy(ClubChannelDealRel::getDealId));
        Map<String, Long> shopMapping = douyinShops.stream().filter(v -> v.getClubId() != null).collect(Collectors.toMap(DouyinShop::getPoiId, DouyinShop::getClubId));
        Map<String, DouyinDeal> dealMap = douyinDeals.stream().collect(Collectors.toMap(DouyinDeal::getSkuId, Function.identity()));

        Set<Long> removeIds = douyinDeals.stream().map(DouyinDeal::getId).collect(Collectors.toSet());
        do {
            productList = douyinClient.getProductList(request);
            for (ProductPage.ProductInfo productInfo : productList.getProducts()) {
                ProductPage.Sku sku = productInfo.getSku();
                ProductPage.Product product = productInfo.getProduct();
                DouyinDeal douyinDeal = dealMap.get(sku.getSkuId());
                if (douyinDeal == null) {
                    douyinDeal = new DouyinDeal();
                    douyinDeal.setProductId(product.getProductId());
                    douyinDeal.setSkuId(sku.getSkuId());
                    douyinDeal.setAccountId(accountId);
                } else {
                    removeIds.remove(douyinDeal.getId());
                }
                douyinDeal.setName(sku.getSkuName());
                douyinDeal.setPrice(sku.getActualAmount().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN));
                douyinDeal.setMarketPrice(sku.getOriginAmount().divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_EVEN));
                douyinDeal.setSourceInfo(JsonUtils.toJson(productInfo));
                if (douyinDeal.getType() == null || douyinDeal.getType() == CouponTypeEnum.NONE) {
                    douyinDeal.setStatus(IsEnum.FALSE);
                } else {
                    douyinDeal.setStatus(IsEnum.TRUE);
                }
                douyinTicketManager.saveDouyinDeal(douyinDeal);
                List<ClubChannelDealRel> relList = dealRelGroup.get(douyinDeal.getId());
                Set<Long> clubIds = relList == null ? new HashSet<>() : relList.stream().map(ClubChannelDealRel::getClubId).collect(Collectors.toSet());
                for (ProductPage.Poi poi : product.getPois()) {
                    Long clubId = shopMapping.get(poi.getPoiId() + "");
                    if (clubId == null) {
                        continue;
                    }
                    if (clubIds.contains(clubId)) {
                        clubIds.remove(clubId);
                        continue;
                    }
                    ClubChannelDealRel clubChannelDealRel = new ClubChannelDealRel();
                    clubChannelDealRel.setClubId(clubId);
                    clubChannelDealRel.setDealId(douyinDeal.getId());
                    clubChannelDealRel.setChannel(TicketChannelEnum.DOUYIN);
                    clubManager.saveClubChannelDealRel(clubChannelDealRel);
                }
                clubManager.deleteDouyinClubDealRelByDealId(douyinDeal.getId(), clubIds);
            }
            request.setCursor(productList.getNextCursor());
        } while (productList.isHasMore());
    }

    @Transactional(rollbackOn = Exception.class)
    public void bindDouyinShop(Long shopId, Long clubId) {
        DouyinShop douyinShop = douyinTicketManager.fetchDouyinShop(shopId);
        if (douyinShop == null) {
            throw new BaseException("门店不存在");
        }
        if (douyinShop.getClubId() != null && douyinShop.getClubId() != 0L) {
            throw new BaseException("门店已绑定");
        }
        douyinShop.setClubId(clubId);
        douyinTicketManager.saveDouyinShop(douyinShop);
        List<DouyinDeal> douyinDeals = douyinTicketManager.fetchDouyinDealByAccountId(douyinShop.getAccountId());
        List<ClubChannelDealRel> clubDeals = clubManager.fetchClubChannelDealRelByClubId(clubId, TicketChannelEnum.DOUYIN);
        Set<Long> hasDeals = clubDeals.stream().map(ClubChannelDealRel::getDealId).collect(Collectors.toSet());
        for (DouyinDeal douyinDeal : douyinDeals) {
            if (hasDeals.contains(douyinDeal.getId())) {
                continue;
            }
            ProductPage.ProductInfo productInfo = JsonUtils.toObject(douyinDeal.getSourceInfo(), ProductPage.ProductInfo.class);
            for (ProductPage.Poi poi : productInfo.getProduct().getPois()) {
                if (douyinShop.getPoiId().equals(poi.getPoiId() + "")) {
                    ClubChannelDealRel clubChannelDealRel = new ClubChannelDealRel();
                    clubChannelDealRel.setClubId(clubId);
                    clubChannelDealRel.setDealId(douyinDeal.getId());
                    clubChannelDealRel.setChannel(TicketChannelEnum.DOUYIN);
                    clubManager.saveClubChannelDealRel(clubChannelDealRel);
                    break;
                }
            }
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void unbindDouyinShop(Long shopId) {
        DouyinShop douyinShop = douyinTicketManager.fetchDouyinShop(shopId);
        if (douyinShop == null) {
            throw new BaseException("门店不存在");
        }
        if (douyinShop.getClubId() == null) {
            throw new BaseException("未绑定门店");
        }
        douyinShop.setClubId(null);
        douyinTicketManager.saveDouyinShop(douyinShop);
        clubManager.deleteClubChannelDealRelByClubId(douyinShop.getClubId(), TicketChannelEnum.DOUYIN);
    }

    public Page<DouyinShopBo> listDouyinShop(ShopQueryDto dto) {
        Page<DouyinShop> shopPage = douyinTicketManager.fetchDouyinShopPage(dto);
        return shopPage.convert(shop -> BeanUtils.copy(shop, DouyinShopBo.class));
    }

    public Page<MeituanShopBo> listMeituanShop(ShopQueryDto dto) {
        Page<MeituanShop> shopPage = meituanTicketManager.fetchMeituanShopPage(dto);
        return shopPage.convert(shop -> BeanUtils.copy(shop, MeituanShopBo.class));
    }

    public Page<MeituanDealBo> listMeituanDeal(DealQueryDto dto) {
        Long clubId = dto.getClubId();
        if (clubId != null) {
            List<ClubChannelDealRel> clubChannelDeal = clubManager.fetchClubChannelDealRelByClubId(clubId, TicketChannelEnum.MEITUAN);
            if (clubChannelDeal.isEmpty()) {
                return Page.empty();
            }
            List<Long> dealIds = clubChannelDeal.stream().map(ClubChannelDealRel::getDealId).toList();
            dto.setDealIds(dealIds);
        }
        Page<MeituanDeal> shopPage = meituanTicketManager.fetchMeituanDealPage(dto);
        return shopPage.convert(shop -> BeanUtils.copy(shop, MeituanDealBo.class));
    }

    public Page<DouyinDealBo> listDouyinDeal(DealQueryDto dto) {
        Long clubId = dto.getClubId();
        if (clubId != null) {
            List<ClubChannelDealRel> clubChannelDeal = clubManager.fetchClubChannelDealRelByClubId(clubId, TicketChannelEnum.DOUYIN);
            if (clubChannelDeal.isEmpty()) {
                return Page.empty();
            }
            List<Long> dealIds = clubChannelDeal.stream().map(ClubChannelDealRel::getDealId).toList();
            dto.setDealIds(dealIds);
        }
        Page<DouyinDeal> shopPage = douyinTicketManager.fetchDouyinDealPage(dto);
        return shopPage.convert(shop -> BeanUtils.copy(shop, DouyinDealBo.class));
    }

    public void configMeituanDeal(ChannelDealDto dto) {
        validate(dto);
        MeituanDeal deal = meituanTicketManager.fetchMeituanDealById(dto.getId());
        deal.setMinutes(dto.getMinutes());
        deal.setPeriod(dto.getPeriod());
        deal.setType(dto.getType());
        if (deal.getType() == null || deal.getType() == CouponTypeEnum.NONE) {
            deal.setStatus(IsEnum.FALSE);
        } else {
            deal.setStatus(IsEnum.TRUE);
        }
        meituanTicketManager.saveMeituanDeal(deal);
    }

    public void configDouyinDeal(ChannelDealDto dto) {
        validate(dto);
        DouyinDeal deal = douyinTicketManager.fetchDouyinDealById(dto.getId());
        deal.setMinutes(dto.getMinutes());
        deal.setPeriod(dto.getPeriod());
        deal.setType(dto.getType());
        if (deal.getType() == null || deal.getType() == CouponTypeEnum.NONE) {
            deal.setStatus(IsEnum.FALSE);
        } else {
            deal.setStatus(IsEnum.TRUE);
        }
        douyinTicketManager.saveDouyinDeal(deal);
    }

    private static void validate(ChannelDealDto dto) {
        if (dto.getPeriod() != null) {
            try {
                TimeBo period = JsonUtils.toObject(dto.getPeriod(), TimeBo.class);
                if (period == null) {
                    throw new BaseException("有效期格式错误");
                }
                if (period.getStartTime() == null || period.getEndTime() == null) {
                    throw new BaseException("有效期格式错误");
                }
                if (!period.getEndTime().isAfter(period.getStartTime())) {
                    period.setOvernight(true);
                    dto.setPeriod(JsonUtils.toJson(period));
                }
            } catch (BaseException e) {
                throw e;
            } catch (Exception e) {
                throw new BaseException("有效期格式错误");
            }
        }
    }

    public MeituanDealBo getMeituanDealInfo(Long id) {
        MeituanDeal meituanDeal = meituanTicketManager.fetchMeituanDealById(id);
        return BeanUtils.copy(meituanDeal, MeituanDealBo.class);
    }

    public DouyinDealBo getDouyinDealInfo(Long id) {
        DouyinDeal douyinDeal = douyinTicketManager.fetchDouyinDealById(id);
        return BeanUtils.copy(douyinDeal, DouyinDealBo.class);
    }

}
