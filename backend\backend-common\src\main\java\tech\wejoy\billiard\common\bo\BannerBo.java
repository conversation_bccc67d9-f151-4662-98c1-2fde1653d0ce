package tech.wejoy.billiard.common.bo;

import com.congeer.utils.BeanUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.entity.BannerInfo;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

@Data
@Schema(description = "轮播图")
@Accessors(chain = true)
public class BannerBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "图片")
    private String image;

    @Schema(description = "链接")
    private String link;

    private String params;

    private IsEnum status;

    private BannerTypeEnum type;

    @Schema(description = "排序")
    private Integer seq;

    public static BannerBo from(BannerInfo bannerInfo) {
        return BeanUtils.copy(bannerInfo, BannerBo.class);
    }

}
