package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.ChannelEnum;

@Data
public class CouponBuyDto {

    @Schema(description = "优惠券ID")
    private Long couponId;

    @Schema(description = "门店ID")
    private Long clubId;

    @Schema(description = "支付渠道")
    private ChannelEnum channel;

    @Schema(hidden = true)
    private Long userId;

}
