package tech.wejoy.billiard.api;

import com.congeer.security.core.bean.SessionInfo;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.AdminUserBo;
import tech.wejoy.billiard.dto.LoginDto;
import tech.wejoy.billiard.service.AdminSecurityService;

@Slf4j
@Path("/")
@Tag(name = "管理员安全接口")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class AdminSecurityApi {

    private final AdminSecurityService securityService;

    @POST
    @Path("/login")
    @Operation(summary = "账号登录")
    public SessionInfo<Long, AdminUserBo> login(LoginDto dto) {
        SessionInfo<Long, AdminUserBo> session = securityService.loginByUsername(dto.getUsername(), dto.getPassword());
        return session.clearSensitive();
    }

    @POST
    @Path("/logout")
    @Operation(summary = "账号登出")
    public void logout() {
        SecurityHolder.logout();
    }

    @GET
    @Path("/session")
    @Authorized
    @Operation(summary = "获取当前登录用户")
    public AdminUserBo getSessionUser() {
        return SecurityHolder.<Long, AdminUserBo>session().getUser();
    }

}
