package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.AssistantStatusEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdminAssistantApplyQueryDto extends PageRequest {

    @QueryParam("name")
    private String name;

    @QueryParam("phone")
    private String phone;

    @QueryParam("status")
    @Separator(",")
    private List<AssistantStatusEnum> status;

}
