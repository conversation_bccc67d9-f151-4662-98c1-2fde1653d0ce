package tech.wejoy.billiard.common.utils;

import com.congeer.utils.JsonUtils;
import tech.wejoy.billiard.common.bo.AssistantTimePlanBo;
import tech.wejoy.billiard.common.bo.ClubTimePlanBo;
import tech.wejoy.billiard.common.bo.TimeSlotBo;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.enums.ClubTimeTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

public class PriceUtils {

    public static BigDecimal calculatePrice(ClubTable table, LocalDateTime start, LocalDateTime end) {
        start = start.withSecond(0);
        end = end.withSecond(0);
        List<TimeSlotBo> timeSlots = JsonUtils.toList(table.getTimeSlots(), TimeSlotBo.class);
        timeSlots.sort(Comparator.comparing(TimeSlotBo::getStartTime));
        BigDecimal price = BigDecimal.ZERO;
        for (int i = -1; i < 2; i++) {
            for (TimeSlotBo timeSlot : timeSlots) {
                LocalDate localDate = start.toLocalDate().plusDays(i);
                LocalDateTime startTime = LocalDateTime.of(localDate, timeSlot.getStartTime());
                LocalDateTime endTime;
                if (timeSlot.isOvernight()) {
                    endTime = LocalDateTime.of(localDate.plusDays(1), timeSlot.getEndTime());
                } else {
                    endTime = LocalDateTime.of(localDate, timeSlot.getEndTime());
                }
                if (!start.isBefore(startTime) && !end.isAfter(endTime)) {
                    BigDecimal perPrice = timeSlot.getPerPrice();
                    long seconds = start.until(end, ChronoUnit.SECONDS);
                    BigDecimal hours = BigDecimal.valueOf(seconds).divide(BigDecimal.valueOf(3600), 8, RoundingMode.HALF_EVEN);
                    price = perPrice.multiply(hours);
                    break;
                } else if (start.isBefore(startTime) && !end.isAfter(endTime) && end.isAfter(startTime)) {
                    BigDecimal perPrice = timeSlot.getPerPrice();
                    long seconds = startTime.until(end, ChronoUnit.SECONDS);
                    BigDecimal hours = BigDecimal.valueOf(seconds).divide(BigDecimal.valueOf(3600), 8, RoundingMode.HALF_EVEN);
                    price = price.add(perPrice.multiply(hours));
                } else if (start.isBefore(startTime) && end.isAfter(endTime)) {
                    BigDecimal perPrice = timeSlot.getPerPrice();
                    long seconds = startTime.until(endTime, ChronoUnit.SECONDS);
                    BigDecimal hours = BigDecimal.valueOf(seconds).divide(BigDecimal.valueOf(3600), 8, RoundingMode.HALF_EVEN);
                    price = price.add(perPrice.multiply(hours));
                } else if (end.isAfter(endTime) && !start.isAfter(endTime) && start.isAfter(startTime)) {
                    BigDecimal perPrice = timeSlot.getPerPrice();
                    long seconds = start.until(endTime, ChronoUnit.SECONDS);
                    BigDecimal hours = BigDecimal.valueOf(seconds).divide(BigDecimal.valueOf(3600), 8, RoundingMode.HALF_EVEN);
                    price = price.add(perPrice.multiply(hours));
                }
            }
        }
        return price.setScale(2, RoundingMode.HALF_EVEN);
    }

    public static BigDecimal calculatePrice(BigDecimal perPrice, LocalDateTime start, LocalDateTime end) {
        start = start.withSecond(0);
        end = end.withSecond(0);
        long seconds = start.until(end, ChronoUnit.SECONDS);
        BigDecimal hours = BigDecimal.valueOf(seconds).divide(BigDecimal.valueOf(3600), 8, RoundingMode.HALF_EVEN);
        if (hours.compareTo(BigDecimal.ONE) <= 0) {
            hours = BigDecimal.ONE;
        }
        return perPrice.multiply(hours).setScale(2, RoundingMode.HALF_EVEN);
    }

    public static List<ClubTimePlanBo> getClubTimePlan() {
        List<ClubTimePlanBo> timePlans = new ArrayList<>();
        ClubTimePlanBo one = new ClubTimePlanBo();
        one.setType(ClubTimeTypeEnum.HOUR);
        one.setValue(1);
        one.setName("1小时");
        timePlans.add(one);
        ClubTimePlanBo two = new ClubTimePlanBo();
        two.setType(ClubTimeTypeEnum.HOUR);
        two.setValue(2);
        two.setName("2小时");
        timePlans.add(two);
        ClubTimePlanBo three = new ClubTimePlanBo();
        three.setType(ClubTimeTypeEnum.HOUR);
        three.setValue(3);
        three.setName("3小时");
        timePlans.add(three);
        ClubTimePlanBo four = new ClubTimePlanBo();
        four.setType(ClubTimeTypeEnum.HOUR);
        four.setValue(4);
        four.setName("4小时");
        timePlans.add(four);
        return timePlans;
    }

    public static List<AssistantTimePlanBo> getAssistantTimePlan() {
        List<AssistantTimePlanBo> timePlans = new ArrayList<>();
        AssistantTimePlanBo one = new AssistantTimePlanBo();
        one.setType(ClubTimeTypeEnum.HOUR);
        one.setValue(1);
        one.setName("1小时");
        timePlans.add(one);
        AssistantTimePlanBo two = new AssistantTimePlanBo();
        two.setType(ClubTimeTypeEnum.HOUR);
        two.setValue(2);
        two.setName("2小时");
        timePlans.add(two);
        AssistantTimePlanBo three = new AssistantTimePlanBo();
        three.setType(ClubTimeTypeEnum.HOUR);
        three.setValue(3);
        three.setName("3小时");
        timePlans.add(three);
        AssistantTimePlanBo four = new AssistantTimePlanBo();
        four.setType(ClubTimeTypeEnum.HOUR);
        four.setValue(4);
        four.setName("4小时");
        timePlans.add(four);
        return timePlans;
    }

}
