package tech.wejoy.billiard.common.strategy.payment;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.inject.spi.CDI;
import tech.wejoy.billiard.common.bo.PayResultBo;
import tech.wejoy.billiard.common.bo.PrepayBo;
import tech.wejoy.billiard.common.bo.RefundResultBo;
import tech.wejoy.billiard.common.bo.TimeSlotBo;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.StartFromEnum;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.MemberManager;
import tech.wejoy.billiard.common.service.ClientUserService;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.strategy.payment.model.ClubPayParams;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

public class MemberPayment extends Payment {

    ClientUserService clientUserService;

    ClubManager clubManager;

    MemberManager memberManager;

    public MemberPayment() {
        this.clientUserService = CDI.current().select(ClientUserService.class).get();
        this.clubManager = CDI.current().select(ClubManager.class).get();
        this.memberManager = CDI.current().select(MemberManager.class).get();
    }

    @Override
    public PrepayBo prepay(StartTableDto dto, ClubTable table) {
        if (dto.getFrom() == StartFromEnum.QRCODE) {
            dto.setStartTime(LocalDateTime.now());
            dto.setEndTime(calEndTime(dto, table));
        }
        return super.prepay(dto, table);
    }

    private LocalDateTime calEndTime(StartTableDto dto, ClubTable table) {
        List<TimeSlotBo> timeSlots = JsonUtils.toList(table.getTimeSlots(), TimeSlotBo.class);
        LocalDateTime start = dto.getStartTime().withSecond(0);
        ClubInfo club = clubManager.fetchClubById(table.getClubId());
        Integer memberMaxTime = club.getMemberMaxHours();
        if (memberMaxTime == null) {
            memberMaxTime = 10;
        }
        ClientUserMember member = memberManager.getMemberByUserId(dto.getUserId());
        LocalDateTime endTime = calEndTime(timeSlots, start, member.getBalance(), memberMaxTime);
        return endTime.withSecond(0);
    }

    @Override
    public PayResultBo pay(OrderInfo order, Object params) {
        ClubPayParams customParams = null;
        if (params != null) {
            customParams = JsonUtils.toObject(JsonUtils.toJson(params), ClubPayParams.class);
        }
        ClientUserMember member = clientUserService.getMemberByUserId(order.getUserId());
        PayResultBo payResultBo = new PayResultBo();
        if (member == null || member.getPlanId() == 0L) {
            payResultBo.setSuccess(false);
            payResultBo.setNeedPay(false);
            return payResultBo;
        }
        if (customParams == null) {
            MemberPlan plan = memberManager.getMemberPlanById(member.getPlanId());
            BigDecimal payAmount = order.getTotalAmount().multiply(plan.getDiscount()).setScale(2, RoundingMode.HALF_EVEN);
            if (member.getBalance().compareTo(payAmount) < 0) {
                payResultBo.setSuccess(false);
                payResultBo.setNeedPay(false);
                payResultBo.setMessage("余额不足");
                return payResultBo;
            }
            member.setBalance(member.getBalance().subtract(payAmount));
            clientUserService.updateMember(member);
            payResultBo.setAmount(payAmount);
            payResultBo.setSuccess(true);
            order.setPayAmount(payAmount);
            order.setRealAmount(payAmount);
        }
        return payResultBo;
    }

    @Override
    public RefundResultBo refund(OrderInfo order, BigDecimal refundAmount) {
        RefundResultBo ret = new RefundResultBo();
        ClientUserMember member = memberManager.getMemberByUserId(order.getUserId());
        if (member == null) {
            ret.setSuccess(false);
            ret.setMessage("用户不存在");
            return ret;
        }
        MemberPlan plan = memberManager.getMemberPlanById(member.getPlanId());
        refundAmount = refundAmount.multiply(plan.getDiscount()).setScale(2, RoundingMode.HALF_EVEN);
        member.setBalance(member.getBalance().add(refundAmount));
        clientUserService.updateMember(member);
        ret.setSuccess(true);
        ret.setAmount(refundAmount);
        return ret;
    }

}
