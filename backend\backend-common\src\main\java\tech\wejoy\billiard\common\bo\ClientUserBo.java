package tech.wejoy.billiard.common.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.GenderEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class ClientUserBo {

    private Long id;

    private LocalDateTime createdAt;

    private String username;

    private String email;

    private String phone;

    private String avatar;

    private GenderEnum gender;

    private IsEnum status;

    private LocalDate birth;

    private String nickname;

    private LocalDateTime lastLoginAt;

    private String lastLoginIp;

    private Long orderCount;

}
