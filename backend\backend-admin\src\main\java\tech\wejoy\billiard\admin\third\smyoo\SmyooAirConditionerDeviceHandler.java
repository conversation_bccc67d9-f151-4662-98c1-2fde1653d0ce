package tech.wejoy.billiard.admin.third.smyoo;

import tech.wejoy.billiard.common.entity.TenantDevice;
import tech.wejoy.billiard.common.enums.AirConditionerOpTypeEnum;

public class SmyooAirConditionerDeviceHandler extends SmyooDeviceHandler {

    @Override
    public boolean doStart(TenantDevice device) {
        return operator(device, AirConditionerOpTypeEnum.POWER, 1);
    }

    @Override
    public boolean doStop(TenantDevice device) {
        return operator(device, AirConditionerOpTypeEnum.POWER, 0);
    }

    @Override
    protected boolean doWarn(TenantDevice device) {
        return true;
    }

}
