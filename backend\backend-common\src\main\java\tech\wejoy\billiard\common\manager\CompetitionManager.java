package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.dto.AdminCompetitionQueryDto;
import tech.wejoy.billiard.common.dto.CompetitionQueryDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.CompetitionStatusEnum;
import tech.wejoy.billiard.common.repo.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class CompetitionManager {

    private final CompetitionMatchRepo competitionMatchRepo;

    private final CompetitionInfoRepo competitionInfoRepo;

    private final CompetitionApplyRepo competitionApplyRepo;

    private final CompetitionAwardRepo competitionAwardRepo;

    private final CompetitionScheduleRepo competitionScheduleRepo;

    private final CompetitionBusinessApplyRepo competitionBusinessApplyRepo;

    public Page<CompetitionInfo> list(CompetitionQueryDto dto) {
        String query = "status != 0";
        Map<String, Object> params = new HashMap<>();
        if (dto.getStatus() != null) {
            query += " and status = :status";
            params.put("status", dto.getStatus());
        }
        if (dto.getCityCode() != null) {
            query += " and city = :city";
            params.put("city", dto.getCityCode());
        }
        if (dto.getLevel() != null) {
            query += " and level = :level";
            params.put("level", dto.getLevel());
        }
        return competitionInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public void saveApply(CompetitionApply apply) {
        competitionApplyRepo.save(apply);
    }

    public List<CompetitionApply> findApplyByCompetitionId(Long competitionId) {
        return competitionApplyRepo.list("competitionId = ?1", Sort.by("rank", "number"), competitionId);
    }

    public CompetitionInfo findCompetitionById(Long competitionId) {
        return competitionInfoRepo.findById(competitionId);
    }

    public List<CompetitionAward> fetchAwardsByCompetitionId(Long id) {
        return competitionAwardRepo.list("competitionId = ?1", Sort.by("seq"), id);
    }

    public List<CompetitionMatch> fetchMatchesByCompetitionId(Long id) {
        return competitionMatchRepo.list("competitionId = ?1", Sort.by("id"), id);
    }

    public List<CompetitionSchedule> fetchSchedulesByCompetitionId(Long id) {
        return competitionScheduleRepo.list("competitionId = ?1", Sort.by("round"), id);
    }

    public CompetitionApply findApplyByCompetitionIdAndUserId(Long id, Long userId) {
        return competitionApplyRepo.find("competitionId = ?1 and userId = ?2", id, userId).firstResult();
    }

    public Page<CompetitionInfo> adminList(AdminCompetitionQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            query += " and status in :status";
            params.put("status", dto.getStatus());
        }
        if (CollectionUtils.isNotEmpty(dto.getLevel())) {
            query += " and level in :level";
            params.put("level", dto.getLevel());
        }
        if (dto.getTenantId() != null) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getClubId() != null) {
            query += " and clubId = :clubId";
            params.put("clubId", dto.getClubId());
        }
        return competitionInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public void saveBusinessApply(CompetitionBusinessApply competitionInfo) {
        competitionBusinessApplyRepo.save(competitionInfo);
    }

    public Page<CompetitionBusinessApply> applyList(AdminCompetitionQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            query += " and level in :level";
            params.put("level", dto.getLevel());
        }
        if (dto.getTenantId() != null) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        return competitionBusinessApplyRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public Page<CompetitionInfo> myList(CompetitionQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (dto.getStatus() != null) {
            query += " and status = :status";
            params.put("status", dto.getStatus());
        }
        if (dto.getCityCode() != null) {
            query += " and city = " + dto.getCityCode();
            params.put("city", dto.getCityCode());
        }
        if (dto.getLevel() != null) {
            query += " and level = " + dto.getLevel();
            params.put("level", dto.getLevel());
        }
        if (CollectionUtils.isNotEmpty(dto.getIds())) {
            query += " and id in :ids";
            params.put("ids", dto.getIds());
        }
        return competitionInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public void saveMatches(List<CompetitionMatch> matches) {
        competitionMatchRepo.persist(matches);
    }

    public CompetitionMatch fetchMatchById(Long id) {
        return competitionMatchRepo.findById(id);
    }

    public CompetitionSchedule fetchScheduleById(Long scheduleId) {
        return competitionScheduleRepo.findById(scheduleId);
    }

    public void saveMatch(CompetitionMatch competitionMatch) {
        competitionMatchRepo.save(competitionMatch);
    }

    public CompetitionSchedule fetchScheduleByCompetitionIdAndRound(Long id, int nextRound) {
        return competitionScheduleRepo.find("competitionId = ?1 and round = ?2", id, nextRound).firstResult();
    }

    public CompetitionMatch fetchMatchByScheduleIdAndNumber(Long id, int nextNumber) {
        return competitionMatchRepo.find("scheduleId = ?1 and number = ?2", id, nextNumber).firstResult();
    }

    public void saveCompetition(CompetitionInfo competitionInfo) {
        competitionInfoRepo.save(competitionInfo);
    }

    public CompetitionBusinessApply findBusinessApplyById(Long applyId) {
        return competitionBusinessApplyRepo.findById(applyId);
    }

    public void saveAwards(List<CompetitionAward> awards) {
        competitionAwardRepo.persist(awards);
    }

    public List<CompetitionApply> fetchApplyByUserId(Long userId) {
        return competitionApplyRepo.list("userId = ?1", userId);
    }

    public void saveSchedules(List<CompetitionSchedule> schedules) {
        competitionScheduleRepo.persist(schedules);
    }

    public void deleteBusinessApply(CompetitionBusinessApply apply) {
        competitionBusinessApplyRepo.delete(apply);
    }

    public void saveSchedule(CompetitionSchedule schedule) {
        competitionScheduleRepo.save(schedule);
    }

    public List<CompetitionInfo> fetchCompetitionByStatus(CompetitionStatusEnum status) {
        return competitionInfoRepo.list("status = ?1", status);
    }

    public List<CompetitionMatch> fetchMatchesByScheduleId(Long scheduleId) {
        return competitionMatchRepo.list("scheduleId = ?1", scheduleId);
    }

}
