package tech.wejoy.billiard.common.third.douyin.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ShopPage extends DouyinPage {

    private Shop[] pois;

    @Data
    public static class Shop {
        private Poi poi;
        private RootAccount root_account;
    }

    @Data
    public static class Poi {
        private String poiId;
        private String poiName;
        private String address;
        private Double latitude;
        private Double longitude;
    }

    @Data
    public static class RootAccount {
        private String accountId;
        private String accountName;
    }

}
