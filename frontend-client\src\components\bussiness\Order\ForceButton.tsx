import Taro from "@tarojs/taro";
import But<PERSON>, { MODES } from "./Button";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import api from "@/api/bussiness";
import { ORDERTYPES, OTHERFINISHTYPES } from "@/constants";
interface IForceButtonProps {
  code: string;
  status: ORDERTYPES;
  isOwner: boolean;
  getPayType: () => any;
  validatePhone: () => Promise<boolean>;
  updateStatus?: () => Promise<void>;
}

interface IForceButtonRef {
  finish: () => Promise<void>;
}

const ForceButton = forwardRef<IForceButtonRef, IForceButtonProps>(
  (props, ref) => {
    const { code, status, isOwner, getPayType, validatePhone, updateStatus } =
      props;

    const configuration = {
      buttonText: "支付全部",
      mode: MODES.Dark,
    };

    const handleResult = useCallback(
      async (response) => {
        const { result, orderNo } = response;
        if (result === ORDERTYPES.PENDING) {
          const { extra } = response;
          await Taro.requestPayment({
            ...extra,
            package: extra.packageStr,
            success: () => updateStatus && updateStatus(),
            fail: () =>
              api.order
                .cancelOrder(orderNo)
                .then(() => updateStatus && updateStatus()),
          });
        } else {
          updateStatus && updateStatus();
        }
      },
      [updateStatus]
    );

    const pay = useCallback(async () => {
      const hasPhone = await validatePhone();
      if (!hasPhone) return;

      const payType: PAY_TYPE = getPayType();

      const { data } = await api.order.otherFinishOrder(code, {
        type: OTHERFINISHTYPES.ALL,
        payType,
      });
      await handleResult(data)
    }, [getPayType, validatePhone,handleResult]);

    useImperativeHandle(
      ref,
      () => {
        return { finish: pay };
      },
      [code]
    );

    if (!isOwner && status === ORDERTYPES.USING) {
      return (
        <Button
          mode={configuration.mode}
          buttonText={configuration.buttonText}
          onClick={() => pay()}
        />
      );
    }
    return <></>;
  }
);

export default ForceButton;
