import Taro from "@tarojs/taro";
import Button, { MODES } from "./Button";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import api from "@/api/bussiness";
import { ORDERTYPES, OTHERFINISHTYPES } from "@/constants";
interface IForceButtonProps {
  code: string;
  status: ORDERTYPES;
  isOwner: boolean;
  getPayType: () => any;
  validatePhone: () => Promise<boolean>;
  updateStatus?: () => Promise<void>;
  enableTableGrabbing?: boolean; // 抢台费开关
}

interface IForceButtonRef {
  finish: () => Promise<void>;
}

const ForceButton = forwardRef<IForceButtonRef, IForceButtonProps>(
  (props, ref) => {
    const { code, status, isOwner, getPayType, validatePhone, updateStatus, enableTableGrabbing } =
      props;

    const configuration = {
      buttonText: "支付全部",
      mode: MODES.Dark,
    };

    const handleResult = useCallback(
      async (response) => {
        const { result, orderNo } = response;
        if (result === ORDERTYPES.PENDING) {
          const { extra } = response;
          await Taro.requestPayment({
            ...extra,
            package: extra.packageStr,
            success: () => {
              Taro.showToast({
                title: "支付全部成功",
                icon: "success",
                duration: 2000,
              });
              updateStatus && updateStatus();
            },
            fail: () => {
              Taro.showToast({
                title: "支付已取消",
                icon: "none",
                duration: 2000,
              });
              api.order
                .cancelOrder(orderNo)
                .then(() => updateStatus && updateStatus());
            },
          });
        } else {
          Taro.showToast({
            title: "支付全部成功",
            icon: "success",
            duration: 2000,
          });
          updateStatus && updateStatus();
        }
      },
      [updateStatus]
    );

    const pay = useCallback(async () => {
      const hasPhone = await validatePhone();
      if (!hasPhone) return;

      // 显示确认对话框
      const { confirm } = await Taro.showModal({
        title: "确认抢台费支付",
        content: "您将支付全部台费，原订单用户将获得全额退款。确认继续吗？",
        confirmText: "确认支付",
        cancelText: "取消",
      });

      if (!confirm) return;

      const payType: PAY_TYPE = getPayType();

      try {
        Taro.showLoading({ title: "处理中...", mask: true });
        const { data } = await api.order.otherFinishOrder(code, {
          type: OTHERFINISHTYPES.ALL,
          payType,
        });
        Taro.hideLoading();
        await handleResult(data);
      } catch (error) {
        Taro.hideLoading();
        Taro.showToast({
          title: "支付失败，请重试",
          icon: "none",
          duration: 2000,
        });
      }
    }, [getPayType, validatePhone, handleResult, code]);

    useImperativeHandle(
      ref,
      () => {
        return { finish: pay };
      },
      [code]
    );

    // 检查是否显示抢台费按钮：非订单拥有者 && 订单使用中 && 门店开启抢台费功能
    if (!isOwner && status === ORDERTYPES.USING && enableTableGrabbing) {
      return (
        <Button
          mode={configuration.mode}
          buttonText={configuration.buttonText}
          onClick={() => pay()}
        />
      );
    }
    return <></>;
  }
);

export default ForceButton;
