<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f35e8ea5-63ea-4465-bbd4-226926e7818f" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/src/components/bussiness/Order/AverageButton.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/bussiness/Order/ForceButton.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/bussiness/order.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/bussiness/order.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/api/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/api/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/bussiness/DurationTime/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/bussiness/DurationTime/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/bussiness/Order/CancelButton.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/bussiness/Order/CancelButton.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/bussiness/Order/FinishButton.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/bussiness/Order/FinishButton.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/bussiness/Order/Panel.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/bussiness/Order/Panel.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/bussiness/Order/QuickStartButton.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/bussiness/Order/QuickStartButton.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/bussiness/PaymentMethod/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/bussiness/PaymentMethod/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/constants/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/constants/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/assistant/detail/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/assistant/detail/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/confirmation/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/confirmation/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/types/business.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/types/business.d.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 6
}]]></component>
  <component name="ProjectId" id="2y2uUpsoKqmJ6viRSkWFY0Q3vJN" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "feat/new",
    "last_opened_file_path": "C:/Users/<USER>/GitWork/partime/billiaard/frontend-client",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.stylelint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.stylelint": "C:\\Users\\<USER>\\GitWork\\partime\\billiaard\\frontend-client\\node_modules\\stylelint",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "yarn",
    "ts.external.directory.path": "C:\\Users\\<USER>\\GitWork\\partime\\billiaard\\frontend-client\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-WS-251.25410.117" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f35e8ea5-63ea-4465-bbd4-226926e7818f" name="更改" comment="" />
      <created>1749045007803</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749045007803</updated>
      <workItem from="1749045009299" duration="349000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>