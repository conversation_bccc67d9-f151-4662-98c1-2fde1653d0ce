package tech.wejoy.billiard.common.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CompetitionCreateDto {

    private Long applyId;

    private String title;

    private LocalDateTime startTime;

    private LocalDateTime signUpStartTime;

    private LocalDateTime signUpEndTime;

    private BigDecimal entryFee;

    private Integer maxPlayer;

    private List<CompetitionAwardDto> awards;

    private List<CompetitionScheduleDto> schedules;

}
