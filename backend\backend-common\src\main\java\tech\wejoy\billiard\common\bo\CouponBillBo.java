package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CouponBillBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "账单ID")
    private Long billId;

    @Schema(description = "优惠券名称")
    private String title;

    @Schema(description = "优惠券描述")
    private String description;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "用户手机号")
    private String userPhone;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "订单状态")
    private BillStatusEnum status;

    @Schema(description = "是否使用")
    private IsEnum used;

}
