"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/competition/rules/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/rules/index!./src/pages/competition/rules/index.tsx":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/rules/index!./src/pages/competition/rules/index.tsx ***!
  \****************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/bussiness/competition */ "./src/api/bussiness/competition.ts");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _components_bussiness_Title__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/bussiness/Title */ "./src/components/bussiness/Title/index.tsx");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _hooks_useCity__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useCity */ "./src/hooks/useCity.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");














var items = [{
  label: "比赛特点",
  value: "全程指引选手比赛流程。"
}, {
  label: "用法特点",
  value: "查看比赛进度，报名，支付台费，匹配对手，个人数据记录等。"
}, {
  label: "操作流程",
  value: "比赛开始后，打开APP，等待自动发给您的操作流程对局，按照对局显示到指定球桌参与比赛即可。"
}, {
  label: "重点说明",
  value: "注意查看猩猩球社APP发送的系统通知。"
}];
var StoreInfo = function StoreInfo(props) {
  var item = props.item;
  var _useCity = (0,_hooks_useCity__WEBPACK_IMPORTED_MODULE_8__["default"])(false),
    openLocation = _useCity.openLocation;
  var makePhoneCall = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().mark(function _callee() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          if (!(item !== null && item !== void 0 && item.phone)) {
            _context.next = 4;
            break;
          }
          _context.next = 3;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().makePhoneCall({
            phoneNumber: item.phone
          });
        case 3:
          return _context.abrupt("return", _context.sent);
        case 4:
          throw Error("当前没有可用的手机号码");
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [item === null || item === void 0 ? void 0 : item.phone]);
  var clipboardData = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function () {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().setClipboardData({
      data: item.address,
      success: function success() {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().showToast({
          title: "复制成功",
          icon: "none"
        });
      }
    });
  }, [item]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
    className: "flex flex-1 justify-start p-3 w-full gap-2 rounded-lg bg-bgt",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
      className: "left flex flex-1 flex-col gap-1",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
        className: "text-lg font-bold",
        children: item.name
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
        className: "text-sm text-muted-foreground flex-1 flex items-end",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
          className: "flex items-center",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
            className: "i-ph-map-pin-fill w-3 h-3 mr-1"
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
            className: "h-4 leading-4 div-xs",
            children: item.address
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_7__.Copy, {
            className: "align-middle ml-1",
            onClick: function onClick() {
              clipboardData();
            },
            size: 12
          })]
        })
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
      className: "right flex gap-2 items-end",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
        className: "text-muted-foreground text-sm flex flex-col justify-center items-center space-y-1",
        onClick: function onClick() {
          return openLocation({
            longitude: item.longitude,
            latitude: item.latitude,
            name: item.name,
            address: item.address
          });
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "i-ph-navigation-arrow-fill w-4 h-4"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "text-sm",
          children: "\u5BFC\u822A"
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
        className: "text-muted-foreground text-sm flex flex-col justify-center items-center space-y-1",
        onClick: function onClick() {
          return makePhoneCall();
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "i-ph-phone-fill w-4 h-4"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "text-sm",
          children: "\u7535\u8BDD"
        })]
      })]
    })]
  });
};
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__.useRouter)();
  var id = router.params.id;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_12__["default"])(_useState, 2),
    detailData = _useState2[0],
    setDetailData = _useState2[1];
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_3__.menuRect)();
  var getCompetition = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().mark(function _callee2() {
    var _yield$competition$ge, data;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_0__["default"].getDetail(id);
        case 2:
          _yield$competition$ge = _context2.sent;
          data = _yield$competition$ge.data;
          setDetailData(data);
          return _context2.abrupt("return", data);
        case 6:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [setDetailData]);
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    getCompetition();
  }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
    className: "flex h-_100vh_ flex-col",
    style: {
      paddingTop: rect.bottom + 10
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_1__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_bussiness_Title__WEBPACK_IMPORTED_MODULE_2__["default"], {
      name: "\u8D5B\u4E8B\u89C4\u7A0B"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      className: "px-3 mb-3",
      children: detailData && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(StoreInfo, {
        item: detailData.club
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
      className: "px-3 flex flex-col gap-3 flex-1 overflow-y-auto",
      children: [detailData && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
        className: "rounded-lg bg-bgt flex flex-col p-3",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "text-sm border-b pb-2 mb-2 sticky",
          children: detailData.title
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
          className: "flex flex-col text-xs",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
            children: ["\u62A5\u540D\u6761\u4EF6: ", detailData.entryFee ? "付费报名" : "免费报名"]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
            children: ["\u62A5\u540D\u4EBA\u6570: ", detailData.maxPlayer, "\u4EBA"]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
            children: ["\u5F00\u8D5B\u65F6\u95F4:", " ", dayjs__WEBPACK_IMPORTED_MODULE_5___default()(detailData.startTime).format("YYYY-MM-DD HH:mm")]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
            children: ["\u62A5\u540D\u65F6\u95F4:", " ", [detailData.signUpStartTime, detailData.signUpEndTime].map(function (item) {
              return dayjs__WEBPACK_IMPORTED_MODULE_5___default()(item).format("YYYY-MM-DD HH:mm");
            }).join(" ~ ")]
          })]
        })]
      }), detailData && Array.isArray(detailData.awards) && detailData.awards.length && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
        className: "rounded-lg bg-bgt flex flex-col p-3",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "text-sm border-b pb-2 mb-2 sticky",
          children: "\u6BD4\u8D5B\u5956\u52B1"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "flex flex-col gap-3",
          children: detailData.awards.map(function (awardItem, index) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
              className: "flex gap-3 text-xs",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
                className: "flex-1",
                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
                  children: awardItem.title
                }), !!awardItem.planBonus && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
                  children: ["\u53F0\u8D39\u5956\u52B1: ", awardItem.planBonus, "\u5143(\u8D5B\u540E\u81EA\u52A8\u53D1\u653E\u81F3\u4F1A\u5458\u5361)"]
                }), !!awardItem.bonus && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
                  children: ["\u73B0\u91D1\u5956\u52B1: ", awardItem.bonus, "\u5143(\u8D5B\u540E\u7531\u5E97\u957F\u53D1\u653E)"]
                }), awardItem.content && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
                  children: ["\u5B9E\u7269\u5956\u52B1: ", awardItem.content]
                })]
              })
            }, index);
          })
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
        className: "rounded-lg bg-bgt p-3 flex flex-col",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "text-sm border-b pb-2 mb-2 sticky",
          children: "APP\u4F7F\u7528\u65B9\u6CD5"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
          className: "flex flex-col gap-1",
          children: items.map(function (item) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
              className: "flex text-xs gap-1",
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)("div", {
                children: [item.label, ":"]
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
                className: "flex-1 flex-wrap",
                children: item.value
              })]
            });
          })
        })]
      })]
    })]
  });
});

/***/ }),

/***/ "./src/pages/competition/rules/index.tsx":
/*!***********************************************!*\
  !*** ./src/pages/competition/rules/index.tsx ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_rules_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/rules/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/rules/index!./src/pages/competition/rules/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white","disableScroll":true};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_rules_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/competition/rules/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_rules_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_rules_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_rules_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_rules_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/competition/rules/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map