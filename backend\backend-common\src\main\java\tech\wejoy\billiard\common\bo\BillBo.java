package tech.wejoy.billiard.common.bo;

import jakarta.persistence.Column;
import lombok.Data;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.enums.BillTypeEnum;
import tech.wejoy.billiard.common.enums.ThirdPayTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BillBo {

    private Long id;

    private Long tenantId;

    private Long clubId;

    private String clubName;

    private Long userId;

    private String phone;

    private String billNo;

    private String orderNo;

    private BillTypeEnum type;

    private ThirdPayTypeEnum thirdPayType;

    private String thirdPayNo;

    @Column(columnDefinition = "text")
    private String payInfo;

    private BigDecimal amount;

    private BigDecimal totalAmount;

    private BigDecimal payAmount;

    private BigDecimal refundAmount;

    private BillStatusEnum status;

    private LocalDateTime payTime;

    private LocalDateTime pushTime;

    private LocalDateTime refundTime;

    private String resultInfo;

    private String refundInfo;

    private LocalDateTime createAt;

}
