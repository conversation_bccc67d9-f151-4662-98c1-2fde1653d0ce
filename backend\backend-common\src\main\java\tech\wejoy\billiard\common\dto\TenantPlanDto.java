package tech.wejoy.billiard.common.dto;

import lombok.Data;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.util.List;

@Data
public class TenantPlanDto {

    private Long id;

    private Long tenantId;

    private List<Long> clubIds;

    private String name;

    private BigDecimal totalAmount;

    private BigDecimal payAmount;

    private String remark;

    private IsEnum status;

    private IsEnum show;

    private Integer seq;

    private Integer paySeq;

}
