package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class MemberPresentDto {

    @Schema(description = "门店ID")
    private Long clubId;

    @Schema(description = "手机号码")
    private String phone;

    private BigDecimal amount = BigDecimal.ZERO;

    private BigDecimal bonusAmount = BigDecimal.ZERO;

    @Schema(description = "备注")
    private String remark;

}
