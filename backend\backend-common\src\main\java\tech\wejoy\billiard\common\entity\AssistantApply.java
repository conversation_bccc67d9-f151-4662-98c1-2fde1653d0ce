package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.AssistantStatusEnum;
import tech.wejoy.billiard.common.enums.GenderEnum;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class AssistantApply extends BaseEntity {

    private Long userId;

    private String name;

    private String avatar;

    private String phone;

    private LocalDate birth;

    private GenderEnum gender;

    private LocalDate startWork;

    private String tags;

    private String realName;

    private String idCard;

    private AssistantStatusEnum status;

}
