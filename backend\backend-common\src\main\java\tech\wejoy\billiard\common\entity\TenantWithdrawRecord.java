package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.WithdrawStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class TenantWithdrawRecord extends BaseEntity {

    private Long tenantId;

    private String orderNo;

    private String bank;

    private String branch;

    private String name;

    private String account;

    private BigDecimal amount;

    private BigDecimal fee;

    private BigDecimal realAmount;

    private LocalDateTime withdrawTime;

    private WithdrawStatusEnum status;

}
