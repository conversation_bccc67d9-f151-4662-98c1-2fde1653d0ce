package tech.wejoy.billiard.api;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.DistrictBo;
import tech.wejoy.billiard.entity.DistrictInfo;
import tech.wejoy.billiard.entity.DistrictPolyline;
import tech.wejoy.billiard.enums.DistrictLevelEnum;
import tech.wejoy.billiard.manager.DistrictManager;
import tech.wejoy.billiard.service.DistrictService;

import java.util.List;

@Path("/district")
@Tag(name = "地区")
@Produces(MediaType.APPLICATION_JSON)
public class DistrictApi {

    @Inject
    DistrictManager manager;

    @Inject
    DistrictService districtService;

    @GET
    @Path("/tree")
    @Operation(summary = "全部地区")
    public DistrictBo list() {
        return districtService.getTree();
    }

    @GET
    @Path("/regeo")
    @Operation(summary = "逆地理编码")
    public DistrictBo regeo(@QueryParam("lat") Float latitude, @QueryParam("lng") Float longitude) {
        return districtService.regeo(new float[]{longitude, latitude});
    }

    @POST
    @Path("/cache")
    public void initPolyline() {
        List<DistrictInfo> districtInfos = manager.listByLevel(DistrictLevelEnum.DISTRICT);
        districtService.setDistrictList(districtInfos);
        List<DistrictPolyline> districtPolylineList = manager.listPolyline();
        districtService.setPolyline(districtPolylineList);
    }

}
