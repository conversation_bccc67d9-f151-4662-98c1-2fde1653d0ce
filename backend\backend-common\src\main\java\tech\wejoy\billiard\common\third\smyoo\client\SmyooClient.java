package tech.wejoy.billiard.common.third.smyoo.client;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.HttpUtils;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.enterprise.inject.spi.CDI;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.entity.TenantSmyooAccount;
import tech.wejoy.billiard.common.manager.DeviceManager;
import tech.wejoy.billiard.common.third.smyoo.request.*;
import tech.wejoy.billiard.common.third.smyoo.response.*;

import java.time.LocalDateTime;
import java.util.LinkedHashMap;

@RequiredArgsConstructor
public class SmyooClient {

    private final String clientId;

    private final String clientSecret;

    private final String deviceId;

    public String request(String url, SmyooRequest request) {
        return request(url, request, null);
    }

    public String request(String url, SmyooRequest request, TenantSmyooAccount account) {
        request.setDeviceId(deviceId);
        LinkedHashMap<String, String> headers = new LinkedHashMap<>();
        headers.put("Content-Type", "application/json");
        if (account != null) {
            headers.put("Cookie", "BpeSessionId=" + getSessionId(account));
        }
        String json = JsonUtils.toJson(request);
        HttpUtils.HttpResult response = HttpUtils.post("https://auth.smyoo.com" + url).header(headers).body(json).send();
        SmyooResponse<Void> resp = JsonUtils.toObject(response.body(), new TypeReference<>() {
        });
        if (resp.getResultCode() != 0) {
            throw new BaseException(resp.getResultMsg());
        }
        return response.body();
    }

    @Transactional(value = Transactional.TxType.REQUIRES_NEW)
    public String getSessionId(TenantSmyooAccount account) {
        if (account.getExpireAt() != null && LocalDateTime.now().isBefore(account.getExpireAt())) {
            return account.getSessionId();
        }
        LoginOpenResponse open = loginOpen(account);
        LoginTicketResponse ticket = loginTicket(open.getTicket());
        account.setSessionId(ticket.getSessionId());
        account.setExpiresIn(open.getCookieExpireTime() + "");
        account.setExpireAt(LocalDateTime.now().plusHours(2));
        CDI.current().select(DeviceManager.class).get().saveSmyooAccount(account);
        return account.getSessionId();
    }

    public LoginOpenResponse loginOpen(TenantSmyooAccount account) {
        String url = "/v1/account/synloginopen";
        LoginOpenRequest req = new LoginOpenRequest();
        req.setClientId(clientId);
        req.setClientSecret(clientSecret);
        req.setPhone(account.getPhone());
        req.setPassword(account.getPassword());
        String response = request(url, req);
        SmyooResponse<LoginOpenResponse> resp = JsonUtils.toObject(response, new TypeReference<>() {
        });
        return resp.getData();
    }

    public LoginTicketResponse loginTicket(String ticket) {
        String url = "/api/gfriend/synloginticket";
        LoginTicketRequest req = new LoginTicketRequest();
        req.setTicket(ticket);
        String response = request(url, req);
        SmyooResponse<LoginTicketResponse> resp = JsonUtils.toObject(response, new TypeReference<>() {
        });
        return resp.getData();
    }

    public StatusChangeResponse statusChanged(TenantSmyooAccount account) {
        String url = "/api/gfriend/statuschanged";
        SmyooRequest req = new SmyooRequest();
        String response = request(url, req, account);
        SmyooResponse<StatusChangeResponse> resp = JsonUtils.toObject(response, new TypeReference<>() {
        });
        return resp.getData();
    }

    public QueryDevicesResponse queryDevices(TenantSmyooAccount account) {
        String url = "/api/gfriend/querydevices";
        SmyooRequest req = new SmyooRequest();
        String response = request(url, req, account);
        SmyooResponse<QueryDevicesResponse> resp = JsonUtils.toObject(response, new TypeReference<>() {
        });
        return resp.getData();
    }

    public GetIrDeviceResponse irDeviceGetData(TenantSmyooAccount account, DeviceRequest req) {
        String url = "/api/gfriend/irdevicegetdata";
        String response = request(url, req, account);
        SmyooResponse<GetIrDeviceResponse> resp = JsonUtils.toObject(response, new TypeReference<>() {
        });
        return resp.getData();
    }

    public void irDeviceSetData(TenantSmyooAccount account, DeviceSetRequest req) {
        String url = "/api/gfriend/irdevicesetdata";
        request(url, req, account);
    }

}
