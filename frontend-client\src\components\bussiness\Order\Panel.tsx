import { ORDERTYPES } from "@/constants";
import { toPrice } from "@/utils";
import { Cell, CellGroup } from "@nutui/nutui-react-taro";
import { View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import { forwardRef, useCallback, useImperativeHandle, useMemo } from "react";

interface IPanelProps {
  order: any;
}
export interface IPanelRef {
  statusName: string | boolean;
  clipboardData: any;
}
const Panel = forwardRef<IPanelRef, IPanelProps>((props, ref) => {
  const { order } = props;
  const { orderNo: code, status, startTime, endTime, realAmount } = order;
  // 复制订单编号

  const realPayment = useMemo(() => toPrice(realAmount, 2), [realAmount]);

  console.log(realPayment)

  const statusName = useMemo(() => {
    const maps = [
      { code: ORDERTYPES.PENDING, name: "待支付" },
      { code: ORDERTYPES.PAID, name: "已支付" },
      { code: ORDERTYPES.USING, name: "使用中" },
      { code: ORDERTYPES.CANCEL, name: "已取消" },
      { code: ORDERTYPES.FINISH, name: "已完成" },
    ];
    const current = maps.find((item) => item.code === status);
    if (current) return current.name;
    return false;
  }, [status]);

  const clipboardData = useCallback(() => {
    Taro.setClipboardData({
      data: code,
      success: () => {
        Taro.showToast({ title: "复制成功", icon: "none" });
      },
    });
  }, [code]);

  useImperativeHandle(
    ref,
    () => {
      return { statusName, clipboardData };
    },
    [statusName]
  );

  // 订单状态的判断
  return (
    <CellGroup>
      <Cell
        title="订单编号"
        extra={
          <View
            className="flex items-center text-sm"
            onClick={() => {
              clipboardData();
            }}
          >
            {code}
          </View>
        }
      />
      {statusName && <Cell title="订单状态" extra={statusName} />}
      <Cell title="开始时间" extra={startTime} />
      <Cell title="结束时间" extra={endTime} />
      <Cell title="当前消费" extra={<div className="text-primary">{realPayment}</div>} />
    </CellGroup>
  );
});

export default Panel;
