package tech.wejoy.billiard.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.BannerBo;
import tech.wejoy.billiard.bo.BaseConfigBo;
import tech.wejoy.billiard.bo.FeedbackBo;
import tech.wejoy.billiard.dto.BannerAdminQueryDto;
import tech.wejoy.billiard.dto.FeedbackQueryDto;
import tech.wejoy.billiard.service.ConfigService;

import java.util.List;

@Path("/config")
@Tag(name = "设置相关")
@Produces(MediaType.APPLICATION_JSON)
@Authorized
@RequiredArgsConstructor
public class ConfigApi {

    private final ConfigService configService;

    @GET
    @Path("/feedback/list")
    @Operation(summary = "获取意见反馈列表")
    public Page<FeedbackBo> listFeedback(@BeanParam FeedbackQueryDto dto) {
        return configService.listFeedback(dto);
    }

    @GET
    @Path("/feedback/{id}")
    @Operation(summary = "获取意见反馈详情")
    public FeedbackBo getFeedback(@PathParam("id") Long id) {
        return configService.getFeedback(id);
    }

    @GET
    @Path("/banner/list")
    @Operation(summary = "获取banner列表")
    public Page<BannerBo> listBanner(@BeanParam BannerAdminQueryDto dto) {
        return configService.listBanner(dto);
    }

    @POST
    @Path("/banner")
    @Operation(summary = "创建banner")
    public BannerBo createBanner(BannerBo bo) {
        return configService.createBanner(bo);
    }

    @PUT
    @Path("/banner/{id}")
    @Operation(summary = "更新banner")
    public BannerBo updateBanner(@PathParam("id") Long id, BannerBo bo) {
        bo.setId(id);
        return configService.updateBanner(bo);
    }

    @DELETE
    @Path("/banner/{id}")
    @Operation(summary = "删除banner")
    public void deleteBanner(@PathParam("id") Long id) {
        configService.deleteBanner(id);
    }

    @GET
    @Path("/")
    @Operation(summary = "获取设置")
    public List<BaseConfigBo> getConfig() {
        return configService.getAllConfig();
    }

    @PUT
    @Path("/")
    @Operation(summary = "更新设置")
    public void updateConfig(List<BaseConfigBo> dto) {
        configService.updateConfig(dto);
    }

}
