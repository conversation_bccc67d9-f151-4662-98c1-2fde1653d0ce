interface IRegeo {
  code: string;
  name: string;
  latitude: number;
  longitude: number;
  keyword?:string;
}

interface IApplication {
  name: string;
  icon: string;
  link: string;
}

interface ITable {
  userId?: number;
  id: number;
  name: number;
  description: string;
  headImage: string;
  timeSlots: ITimeSlot[];
  status: TABLE_STATUS;
  endTime: string;
  deposit: number;
  uid?: number
}

interface ITimeSlot {
  type: number;
  startTime: string;
  endTime: string;
  perPrice: string;
}

interface ICoupon{
  id:number;
  title:string;
  description:string;
  type: number;
  price:number;
  minutes:number;
  period:string;
  expireTime:string;
  clubs:{
    id:number;
    name:string;
  }[]
}

interface IVenue {
  minutes: number;
  id: number;
  name: string;
  description: string;
  headImage: string;
  images: string[];
  address: string;
  phone: string;
  status: VENUE_STATUS;
  openAt: string;
  tags: string[];
  district: string;
  latitude: number;
  longitude: number;
  distance: number;
  tableCount: number;
  price: string;
  tables: ITable[];
  coupons:ICoupon[];
  matches: any[];
  assistants: any[];
  enableTableGrabbing?: boolean; // 抢台费开关
}

declare enum TABLE_STATUS {
  UNAVAILABLE,
  IDLE,
  USING,
  LOCKED,
  MAINTAINING,
}


declare enum VENUE_STATUS {
  NONE,
  REMODELING,
  NORMAL,
  CLOSED,
}

declare enum ORDERTYPES {
  PENDING,
  PAID,
  USING,
  FINISH,
  CANCEL,
  REFUND,
  TIMEOUT,
}

declare enum RECHARGE_TYPE {
  MEMBER = 0,
  CLUB = 1,
}

declare enum PAY_TYPE {
  NONE,
  WECHAT,
  SINGLE,
  NUMBER,
  COUPON,
  MEITUAN,
  DOUYIN,
}

interface IOrder {
  id: number;
  orderNo: string;
  userId: number;
  clubId: number;
  clubName:string;
  tableId: number;
  tableName?:string;
  assistantName?:string;
  description: string;
  status: ORDERTYPES;
  payType: PAY_TYPE;
  startTime: string;
  endTime: string;
  totalAmount: number;
  payAmount: number;
  refundAmount: number;
  realAmount: number;
  realStartTime: string;
  realEndTime: string;
  startFrom: number
}

interface IPlans {
  id: number;
  name: string;
  clubNames: string[];
  totalAmount: number;
  payAmount: number;
  remark: string;
}

interface IRecharge {
  id: number;
  name: string;
  description: string;
  headImage: string;
  images: string[];
  address: string;
  balance?: number;
}

interface IWallet {
  clubTotalBalance: number;
  memberBalance: number;
  coins: number;
  points: number;
}

interface IClubWallet {
  id: number;
  clubId: number;
  balance: number;
}

interface IWalletData extends IWallet {
  list: IClubWallet[];
}

declare enum TIMETYPES{
  customization = 0,
  normal = 1
}

interface ICustomizationTimePlan {
  type: TIMETYPES.customization,
  name: string;
}

interface INormalTimePlan {
  type:TIMETYPES.normal,
  name:string;
  value:number
}

type TimePlan  = ICustomizationTimePlan | INormalTimePlan


interface PageContentProps {
  scenario: RECHARGE_TYPE | string;
  id: string;
  addressName?: string;
  onChooseItem?:(item)=>void
  onChangeVisible?: (visible: boolean) => void;
}
