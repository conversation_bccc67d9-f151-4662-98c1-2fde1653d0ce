package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class CompetitionAward extends BaseEntity {

    private Long competitionId;

    private String title;

    private String content;

    private BigDecimal bonus;

    private BigDecimal planBonus;

    private Integer rankStart;

    private Integer rankEnd;

    private Integer seq;

    private Integer score;

    private Long couponId;

    private String object;

}
