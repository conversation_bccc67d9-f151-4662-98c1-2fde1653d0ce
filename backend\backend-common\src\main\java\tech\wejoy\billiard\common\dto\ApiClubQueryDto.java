package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class ApiClubQueryDto extends PageRequest {

    @QueryParam("keyword")
    private String keyword;

    @QueryParam("cityCode")
    private String city;

    @QueryParam("queryLat")
    private Double queryLat;

    @QueryParam("queryLng")
    private Double queryLng;

    @QueryParam("userLat")
    private Double userLat;

    @QueryParam("userLng")
    private Double userLng;

}
