{"version": 3, "file": "pages/login/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC1FA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/login/index.tsx?84ae", "webpack://frontend-client/._src_pages_login_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport { Button, View, Image } from \"@tarojs/components\";\nimport { menuRect } from \"@/utils\";\nimport useAccount from \"@/hooks/useAccount\";\nimport Taro, { useRouter } from \"@tarojs/taro\";\nimport { useCallback, useEffect, useState } from \"react\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default (function () {\n  var rect = menuRect();\n  var _useRouter = useRouter(),\n    params = _useRouter.params;\n  var autoLogin = params.autoLogin,\n    tips = params.tips;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isLogin = _useState2[0],\n    setIsLogin = _useState2[1];\n  var _useAccount = useAccount(),\n    getCode = _useAccount.getCode;\n  var login = useCallback(function () {\n    setIsLogin(true);\n    getCode().then(function () {\n      var redirectURI = Taro.getStorageSync(\"redirectURL\") || \"pages/index/index\";\n      Taro.redirectTo({\n        url: \"\".concat(redirectURI),\n        complete: function complete() {\n          Taro.removeStorageSync(\"redirectURL\");\n          setIsLogin(false);\n        }\n      });\n    });\n  }, [getCode]);\n  useEffect(function () {\n    if (autoLogin) {\n      login();\n    } else if (tips) {\n      Taro.showModal({\n        title: \"提示\",\n        content: \"该功能需要授权登录后才可访问\",\n        confirmText: \"授权登录\",\n        success: function success(result) {\n          if (result.confirm) {\n            login();\n          } else {\n            Taro.navigateBack({\n              fail: function fail() {\n                Taro.redirectTo({\n                  url: \"/pages/index/index\"\n                });\n              }\n            });\n          }\n        },\n        fail: function fail() {\n          Taro.navigateBack();\n        }\n      });\n    }\n  }, [autoLogin]);\n  return /*#__PURE__*/_jsx(View, {\n    className: \"text-white h-[100vh] bg-primary\",\n    children: /*#__PURE__*/_jsxs(View, {\n      className: \"flex flex-col h-full\",\n      style: {\n        paddingTop: rect.bottom + 5\n      },\n      children: [/*#__PURE__*/_jsx(View, {\n        className: \"description flex-1 flex items-center\",\n        children: /*#__PURE__*/_jsx(Image, {\n          mode: \"aspectFit\",\n          className: \"w-full\",\n          src: \"https://oss.gorillaballclub.cn/images/banner-z.png\"\n        })\n      }), /*#__PURE__*/_jsxs(View, {\n        className: \" p-10\",\n        children: [/*#__PURE__*/_jsx(Button, {\n          className: \"login text-primary bg-background h-10 flex items-center justify-center\",\n          onClick: function onClick() {\n            return login();\n          },\n          disabled: isLogin,\n          children: isLogin ? \"登录中\" : \"登录\"\n        }), /*#__PURE__*/_jsx(_SafeArea, {\n          position: \"bottom\"\n        })]\n      })]\n    })\n  });\n});", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/login/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\"};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/login/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}