package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WalletDetailBo {

    @Schema(description = "单店余额")
    private BigDecimal clubBalance;

    @Schema(description = "全国余额")
    private BigDecimal memberBalance;

    private List<CouponBo> coupons;

    private List<TicketBo> tickets;

    @Schema(description = "代币")
    private Integer coins;

    @Schema(description = "积分")
    private Integer points;

}
