package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.CompetitionMatchStatusEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class CompetitionMatch extends BaseEntity {

    private Long competitionId;

    private Long scheduleId;

    private Long tableId;

    private Long player1UserId;

    private Long player2UserId;

    private Integer player1Score;

    private Integer player2Score;

    private Integer round;

    private Integer number;

    private CompetitionMatchStatusEnum status;

    private Long winner;

    private Long loser;

}
