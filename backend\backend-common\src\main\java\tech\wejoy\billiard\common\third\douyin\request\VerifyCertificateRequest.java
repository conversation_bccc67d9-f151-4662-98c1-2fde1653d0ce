package tech.wejoy.billiard.common.third.douyin.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class VerifyCertificateRequest extends DouyinRequest {

    private String verifyToken;

    private List<String> encryptedCodes;

    private List<String> codes;

    private String orderId;

    private List<String> codeWithTimeList;

    private String code;

    private String poiId;

}
