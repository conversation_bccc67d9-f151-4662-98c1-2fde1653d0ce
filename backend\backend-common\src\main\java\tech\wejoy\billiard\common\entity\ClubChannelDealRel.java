package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClubChannelDealRel extends BaseEntity {

    private Long clubId;

    private Long dealId;

    private TicketChannelEnum channel;

}
