package tech.wejoy.billiard.common.bo;

import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.enums.ClubStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AdminClubBo {

    @Schema(description = "门店ID")
    private Long id;

    @Schema(description = "加盟商ID")
    private Long tenantId;

    @Schema(description = "门店名称")
    private String name;

    @Schema(description = "门店描述")
    private String description;

    @Schema(description = "门店头像")
    private String headImage;

    @Schema(description = "门店图片")
    private List<String> images;

    @Schema(description = "门店地址")
    private String address;

    @Schema(description = "门店电话")
    private String phone;

    @Schema(description = "门店状态")
    private ClubStatusEnum status;

    @Schema(description = "开放时间")
    private LocalDateTime openAt;

    @Schema(description = "门店标签")
    private List<String> tags;

    @Schema(description = "城市编码")
    private String district;

    @Schema(description = "门店经度")
    private Double latitude;

    @Schema(description = "营业时间")
    private List<TimeBo> businessHours;

    @Schema(description = "门店纬度")
    private Double longitude;

    @Schema(description = "桌台数量")
    private Integer tableCount;

    @Schema(description = "价格")
    private String price;

    @Schema(description = "闪灯开关")
    private IsEnum flashLight;

    @Schema(description = "会员提示")
    private IsEnum memberTips;

    @Schema(description = "桌台")
    private List<TableBo> tables;

    private Integer memberMaxHours;

    private Integer serviceFeeRatio;

    public void setBusinessHours(String businessHours) {
        if (StringUtils.isBlank(businessHours)) {
            return;
        }
        this.businessHours = JsonUtils.toList(businessHours, TimeBo.class);
    }

    public static AdminClubBo from(ClubInfo info) {
        return BeanUtils.copy(info, AdminClubBo.class);
    }

    public ClubInfo to() {
        ClubInfo copy = BeanUtils.copy(this, ClubInfo.class);
        if (this.getBusinessHours() != null) {
            copy.setBusinessHours(JsonUtils.toJson(this.getBusinessHours()));
        }
        return copy;
    }

}
