import Taro from "@tarojs/taro";
import Button, { MODES } from "./Button";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import api from "@/api/bussiness";
import { ORDERTYPES, OTHERFINISHTYPES } from "@/constants";
interface IAverageButtonProps {
  code: string;
  status: ORDERTYPES;
  isOwner: boolean;
  getPayType: () => any;
  validatePhone: () => Promise<boolean>;
  updateStatus?: () => Promise<void>;
  enableTableGrabbing?: boolean; // 抢台费开关
}

interface IAverageButtonRef {
  finish: () => Promise<void>;
}

const AverageButton = forwardRef<IAverageButtonRef, IAverageButtonProps>(
  (props, ref) => {
    const { code, status, isOwner, getPayType, validatePhone, updateStatus, enableTableGrabbing } =
      props;

    const configuration = {
      buttonText: "AA支付",
      mode: MODES.Light,
    };

    const handleResult = useCallback(
      async (response) => {
        const { result, orderNo } = response;
        if (result === ORDERTYPES.PENDING) {
          const { extra } = response;
          await Taro.requestPayment({
            ...extra,
            package: extra.packageStr,
            success: () => {
              Taro.showToast({
                title: "AA支付成功",
                icon: "success",
                duration: 2000,
              });
              updateStatus && updateStatus();
            },
            fail: () => {
              Taro.showToast({
                title: "支付已取消",
                icon: "none",
                duration: 2000,
              });
              api.order
                .cancelOrder(orderNo)
                .then(() => updateStatus && updateStatus());
            },
          });
        } else {
          Taro.showToast({
            title: "AA支付成功",
            icon: "success",
            duration: 2000,
          });
          updateStatus && updateStatus();
        }
      },
      [updateStatus]
    );

    const pay = useCallback(async () => {
      const hasPhone = await validatePhone();
      if (!hasPhone) return;

      // 显示确认对话框
      const { confirm } = await Taro.showModal({
        title: "确认AA支付",
        content: "您将支付一半台费，原订单用户将获得部分退款。确认继续吗？",
        confirmText: "确认支付",
        cancelText: "取消",
      });

      if (!confirm) return;

      const payType: PAY_TYPE = getPayType();

      try {
        Taro.showLoading({ title: "处理中...", mask: true });
        const { data } = await api.order.otherFinishOrder(code, {
          type: OTHERFINISHTYPES.HALF,
          payType,
        });
        Taro.hideLoading();
        await handleResult(data);
      } catch (error) {
        Taro.hideLoading();
        Taro.showToast({
          title: "支付失败，请重试",
          icon: "none",
          duration: 2000,
        });
      }
    }, [getPayType, validatePhone, handleResult, code]);

    useImperativeHandle(
      ref,
      () => {
        return { finish: pay };
      },
      [code]
    );

    // 检查是否显示半价抢台费按钮：非订单拥有者 && 订单使用中 && 门店开启抢台费功能
    if (!isOwner && status === ORDERTYPES.USING && enableTableGrabbing) {
      return (
        <Button
          mode={configuration.mode}
          buttonText={configuration.buttonText}
          onClick={() => pay()}
        />
      );
    }
    return <></>;
  }
);

export default AverageButton;
