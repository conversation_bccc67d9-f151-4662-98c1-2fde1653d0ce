import Taro from "@tarojs/taro";
import But<PERSON>, { MODES } from "./Button";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import api from "@/api/bussiness";
import { ORDERTYPES, OTHERFINISHTYPES } from "@/constants";
interface IAverageButtonProps {
  code: string;
  status: ORDERTYPES;
  isOwner: boolean;
  getPayType: () => any;
  validatePhone: () => Promise<boolean>;
  updateStatus?: () => Promise<void>;
}

interface IAverageButtonRef {
  finish: () => Promise<void>;
}

const AverageButton = forwardRef<IAverageButtonRef, IAverageButtonProps>(
  (props, ref) => {
    const { code, status, isOwner, getPayType, validatePhone, updateStatus } =
      props;

    const configuration = {
      buttonText: "AA支付",
      mode: MODES.Light,
    };

    const handleResult = useCallback(
      async (response) => {
        const { result, orderNo } = response;
        if (result === ORDERTYPES.PENDING) {
          const { extra } = response;
          await Taro.requestPayment({
            ...extra,
            package: extra.packageStr,
            success: () => updateStatus && updateStatus(),
            fail: () =>
              api.order
                .cancelOrder(orderNo)
                .then(() => updateStatus && updateStatus()),
          });
        } else {
          updateStatus && updateStatus();
        }
      },
      [updateStatus]
    );

    const pay = useCallback(async () => {
      const hasPhone = await validatePhone();
      if (!hasPhone) return;

      const payType: PAY_TYPE = getPayType();

      const { data } = await api.order.otherFinishOrder(code, {
        type: OTHERFINISHTYPES.HALF,
        payType,
      });
      await handleResult(data);
    }, [getPayType, handleResult]);

    useImperativeHandle(
      ref,
      () => {
        return { finish: pay };
      },
      [code]
    );

    if (!isOwner && status === ORDERTYPES.USING) {
      return (
        <Button
          mode={configuration.mode}
          buttonText={configuration.buttonText}
          onClick={() => pay()}
        />
      );
    }
    return <></>;
  }
);

export default AverageButton;
