package tech.wejoy.billiard.bo;

import lombok.Data;
import tech.wejoy.billiard.enums.WithdrawStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class WithdrawRecordBo {

    private Long id;

    private Long tenantId;

    private String bank;

    private String name;

    private String branch;

    private String account;

    private LocalDateTime withdrawTime;

    private String orderNo;

    private BigDecimal amount;

    private BigDecimal fee;

    private BigDecimal realAmount;

    private WithdrawStatusEnum status;

}
