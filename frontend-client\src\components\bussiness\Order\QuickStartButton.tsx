import Taro from "@tarojs/taro";
import Button, { MODES } from "./Button";
import { forwardRef, useCallback, useImperativeHandle } from "react";
import api from "@/api/bussiness";
import { ORDERTYPES } from "@/constants";
interface IQuickStartButtonProps {
  code: string;
  status: ORDERTYPES;
  isOwner: boolean;
  updateStatus?: () => Promise<void>;
}

interface IQuickStartButtonRef {
  quickStart: () => Promise<void>;
}

const QuickStartButton = forwardRef<
  IQuickStartButtonRef,
  IQuickStartButtonProps
>((props, ref) => {
  const { code, status,isOwner } = props;
  const configuration = {
    buttonText: "立即开台",
    mode: MODES.Dark,
  };

  const quickStart = useCallback(async () => {
    const { confirm } = await Taro.showModal({
      title: "提示",
      content: "是否确认当前操作",
      confirmText: "确定",
    });
    if (confirm) {
      await api.order.quickStart(code);
      props.updateStatus && props.updateStatus();
    }
  }, [code, props.updateStatus]);

  useImperativeHandle(
    ref,
    () => {
      return { quickStart };
    },
    [code]
  );

  if (status === ORDERTYPES.PAID && isOwner) {
    return (
      <Button
        mode={configuration.mode}
        buttonText={configuration.buttonText}
        onClick={quickStart}
      />
    );
  }
  return <></>;
});

export default QuickStartButton;
