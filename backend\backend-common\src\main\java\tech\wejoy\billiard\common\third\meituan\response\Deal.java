package tech.wejoy.billiard.common.third.meituan.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class Deal {
    private Long dealId; // 套餐id
    private Long dealGroupId; // 团购id
    private String beginDate; // 团购开始售卖时间
    private String endDate; // 团购结束售卖时间
    private String title; // 套餐名称
    private BigDecimal price; // 套餐价格
    private BigDecimal marketPrice; // 套餐原价
    private String receiptBeginDate; // 团购券开始服务时间
    private String receiptEndDate; // 团购券结束服务时间
    private Integer saleStatus; // 售卖状态
    private String dealType; // 团购类型
    private Integer dealGroupStatus; // 团购状态
}