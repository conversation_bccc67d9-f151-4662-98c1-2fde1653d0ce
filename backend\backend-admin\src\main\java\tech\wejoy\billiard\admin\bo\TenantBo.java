package tech.wejoy.billiard.admin.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.IsEnum;

@Data
@Schema(description = "加盟商")
public class TenantBo {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "logo")
    private String logo;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "状态")
    private IsEnum status;

    @Schema(description = "联系方式")
    private String phone;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "门店数量")
    private int clubCount;

}
