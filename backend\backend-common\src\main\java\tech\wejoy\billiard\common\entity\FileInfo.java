package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.FileLinkEnum;
import tech.wejoy.billiard.common.enums.FileTypeEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class FileInfo extends BaseEntity {

    private FileLinkEnum link;

    private FileTypeEnum type;

    private Long outerId;

    private String name;

    @Column(columnDefinition = "TEXT")
    private String object;

    @Column(columnDefinition = "TEXT")
    private String url;

    private Integer seq;

}
