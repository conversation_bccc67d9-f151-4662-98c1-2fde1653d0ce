package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class DouyinShop extends BaseEntity {

    private Long accountId;
    private String poiId;
    private String name;
    private String address;
    @Column(columnDefinition = "numeric(10,6)")
    private Double latitude;
    @Column(columnDefinition = "numeric(10,6)")
    private Double longitude;
    private Long clubId;

}
