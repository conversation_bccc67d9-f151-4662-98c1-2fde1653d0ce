package tech.wejoy.billiard.common.third.meituan.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryByPhoneRequest extends MeituanRequest {

    private String mobile;

    private Long dealGroupId;

    private Long dealId;

    private Integer offset = 0;

    private Integer limit = 30;

    private String openShopUuid;

    private Integer platform = 1;

}
