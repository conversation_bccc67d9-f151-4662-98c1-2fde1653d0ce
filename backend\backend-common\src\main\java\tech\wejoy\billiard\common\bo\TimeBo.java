package tech.wejoy.billiard.common.bo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "时间段")
public class TimeBo {

    @Schema(description = "日期")
    private List<Integer> days;

    @Schema(description = "开始时间")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    private LocalTime endTime;

    private boolean overnight;

    @JsonIgnore
    public LocalDateTime getStart(LocalDate date) {
        if (startTime == null) {
            return null;
        }
        return LocalDateTime.of(date, startTime);
    }

    @JsonIgnore
    public LocalDateTime getStart(LocalDateTime dateTime) {
        return getStart(getDate(dateTime));
    }

    @JsonIgnore
    public LocalDateTime getEnd(LocalDate date) {
        if (endTime == null) {
            return null;
        }
        if (overnight) {
            date = date.plusDays(1);
        }
        return LocalDateTime.of(date, endTime);
    }

    @JsonIgnore
    public LocalDateTime getEnd(LocalDateTime dateTime) {
        return getEnd(getDate(dateTime));
    }

    @JsonIgnore
    public LocalDate getDate(LocalDateTime dateTime) {
        boolean nextDay = isNextDay(dateTime.toLocalTime());
        LocalDate date = dateTime.toLocalDate();
        if (nextDay) {
            date = date.plusDays(-1);
        }
        return date;
    }

    @JsonIgnore
    public boolean isNextDay(LocalTime time) {
        return overnight && !time.isAfter(endTime);
    }

}
