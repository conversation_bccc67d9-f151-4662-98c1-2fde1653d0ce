package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CouponBo {

    private Long id;

    @Schema(description = "优惠券名称")
    private String title;

    @Schema(description = "优惠券描述")
    private String description;

    @Schema(description = "优惠券价格")
    private BigDecimal price;

    @Schema(description = "优惠券类型")
    private CouponTypeEnum type;

    @Schema(description = "优惠券有效时间")
    private Integer minutes;

    @Schema(description = "优惠券有效时段")
    private String period;

    private IsEnum gift;

    private List<ApiClubBo> clubs;

    private LocalDateTime expireTime;

}
