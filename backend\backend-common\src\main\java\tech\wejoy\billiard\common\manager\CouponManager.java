package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.dto.AdminCouponQueryDto;
import tech.wejoy.billiard.common.entity.CouponGiftRecord;
import tech.wejoy.billiard.common.entity.TenantClubCouponRel;
import tech.wejoy.billiard.common.entity.TenantCoupon;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.repo.CouponGiftRecordRepo;
import tech.wejoy.billiard.common.repo.TenantClubCouponRelRepo;
import tech.wejoy.billiard.common.repo.TenantCouponRepo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class CouponManager {

    private final TenantCouponRepo tenantCouponRepo;

    private final TenantClubCouponRelRepo tenantClubCouponRelRepo;

    private final CouponGiftRecordRepo couponGiftRecordRepo;

    public void saveCoupon(TenantCoupon tenantCoupon) {
        tenantCouponRepo.save(tenantCoupon);
    }

    public List<TenantCoupon> fetchCouponByClub(Long id) {
        List<TenantClubCouponRel> relList = tenantClubCouponRelRepo.find("clubId", id).list();
        if (relList.isEmpty()) {
            return List.of();
        }
        return tenantCouponRepo.find("id in ?1 and delete = ?2", Sort.by("seq"),
                relList.stream().map(TenantClubCouponRel::getCouponId).toList(), IsEnum.FALSE).list();
    }

    public List<TenantCoupon> fetchShowCouponByClub(Long id) {
        List<TenantClubCouponRel> relList = tenantClubCouponRelRepo.find("clubId", id).list();
        if (relList.isEmpty()) {
            return List.of();
        }
        List<Long> ids = relList.stream().map(TenantClubCouponRel::getCouponId).toList();
        return tenantCouponRepo.find("id in ?1 and status = ?2 and show = ?3 and delete = ?4", Sort.by("seq"),
                ids, IsEnum.TRUE, IsEnum.TRUE, IsEnum.FALSE).list();
    }

    public TenantCoupon fetchCouponById(Long couponId) {
        return tenantCouponRepo.findById(couponId);
    }

    public List<TenantCoupon> fetchCouponByTenant(Long tenantId) {
        return tenantCouponRepo.find("tenantId = ?1 and and delete = ?2", Sort.by("seq"), tenantId, IsEnum.FALSE).list();
    }

    public void saveCouponClub(Long id, Long tenantId, List<Long> clubIds) {
        tenantClubCouponRelRepo.delete("couponId", id);
        for (Long clubId : clubIds) {
            TenantClubCouponRel rel = new TenantClubCouponRel();
            rel.setClubId(clubId);
            rel.setTenantId(tenantId);
            rel.setCouponId(id);
            tenantClubCouponRelRepo.save(rel);
        }
    }

    public void deleteCoupon(Long id) {
        TenantCoupon coupon = fetchCouponById(id);
        if (coupon == null) {
            throw new RuntimeException("优惠券不存在");
        }
        coupon.setDelete(IsEnum.TRUE);
        saveCoupon(coupon);
    }

    public List<TenantCoupon> fetchCouponsByIds(List<Long> couponIds) {
        return tenantCouponRepo.find("id in ?1", couponIds).list();
    }

    public void saveGiftRecord(CouponGiftRecord record) {
        couponGiftRecordRepo.save(record);
    }

    public Page<CouponGiftRecord> fetchGiftRecordByTenantIdAndClubId(AdminCouponQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (dto.getTenantId() != null) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getClubId() != null) {
            query += " and clubId = :clubId";
            params.put("clubId", dto.getClubId());
        }
        if (dto.getStartDate() != null) {
            query += " and createAt >= :startDate";
            params.put("startDate", dto.getStartDate().atStartOfDay());
        }
        if (dto.getEndDate() != null) {
            query += " and createAt <= :endDate";
            params.put("endDate", dto.getEndDate().plusDays(1).atStartOfDay());
        }
        if (StringUtils.isNotBlank(dto.getPhone())) {
            query += " and phone like concat('%', :phone, '%')";
            params.put("phone", dto.getPhone());
        }
        long count = couponGiftRecordRepo.find(query, Sort.descending("createAt"), params).count();
        if (count == 0) {
            return dto.empty();
        }
        List<CouponGiftRecord> list = couponGiftRecordRepo.find(query, params)
                .page(dto.getCurrent(), dto.getSize()).list();
        return dto.of(count, list);
    }

    public List<TenantCoupon> fetchCouponByIds(List<Long> couponIds) {
        if (CollectionUtils.isEmpty(couponIds)) {
            return List.of();
        }
        return tenantCouponRepo.find("id in ?1", couponIds).list();
    }

    public List<TenantClubCouponRel> fetchCouponRelByCouponIds(List<Long> couponIds) {
        return tenantClubCouponRelRepo.find("couponId in ?1", couponIds).list();
    }

}
