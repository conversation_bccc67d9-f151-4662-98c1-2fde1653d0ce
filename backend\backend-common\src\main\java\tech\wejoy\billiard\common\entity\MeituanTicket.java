package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class MeituanTicket extends BaseEntity {

    private String code;

    private Long dealId;

    private Long meituanDealId;

    private Long dealGroupId;

    private String title;

    private String mobile;

    private LocalDateTime endDate;

    private Integer bizType;

    private IsEnum used;

}
