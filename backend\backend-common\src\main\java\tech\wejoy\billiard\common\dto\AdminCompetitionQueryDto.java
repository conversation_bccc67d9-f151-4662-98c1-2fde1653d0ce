package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.CompetitionLevelEnum;
import tech.wejoy.billiard.common.enums.CompetitionStatusEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdminCompetitionQueryDto extends PageRequest {

    @QueryParam("status")
    @Schema(description = "比赛状态")
    @Separator(",")
    private List<CompetitionStatusEnum> status;

    @QueryParam("level")
    @Schema(description = "比赛级别")
    @Separator(",")
    private List<CompetitionLevelEnum> level;

    @Schema(hidden = true)
    private Long tenantId;

    @QueryParam("clubId")
    @Schema(description = "门店ID")
    private Long clubId;

}
