// package tech.wejoy.billiard.api;
//
// import com.congeer.utils.HttpUtils;
// import com.congeer.utils.JsonUtils;
// import com.google.common.collect.Lists;
// import jakarta.transaction.Transactional;
// import jakarta.ws.rs.GET;
// import jakarta.ws.rs.Path;
// import jakarta.ws.rs.Produces;
// import jakarta.ws.rs.core.MediaType;
// import lombok.RequiredArgsConstructor;
// import tech.wejoy.billiard.entity.DistrictInfo;
// import tech.wejoy.billiard.entity.DistrictPolyline;
// import tech.wejoy.billiard.enums.DistrictLevelEnum;
// import tech.wejoy.billiard.repo.DistrictInfoRepo;
// import tech.wejoy.billiard.repo.DistrictPolylineRepo;
//
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
//
// @Path("/district")
// @Produces(MediaType.APPLICATION_JSON)
// @RequiredArgsConstructor
// public class InitDataApi {
//
//     private final DistrictInfoRepo repo;
//
//     private final DistrictPolylineRepo polylineRepo;
//
//     @Path("/init/ployline")
//     @GET
//     public String initDistrictPolyline() {
//         List<DistrictInfo> list = list();
//         String url = "https://restapi.amap.com/v3/config/district";
//         String key = "82c50632321f61882f526eefb86073af";
//         String params = "key=" + key + "&subdistrict=0&extensions=all&keywords=";
//
//         for (DistrictInfo districtInfo : list) {
//             String requestUrl = url + "?" + params + districtInfo.getCode();
//             HashMap<String, String> headers = new HashMap<>();
//             headers.put("Content-Type", "application/json");
//             HttpUtils.HttpResult response = HttpUtils.get(requestUrl).header(headers).send();
//             String string = response.body();
//             Map object = JsonUtils.toObject(string, Map.class);
//             persist(object);
//         }
//         return "init district success";
//     }
//
//     void persist(Map object) {
//         List districts = (List) object.get("districts");
//         System.out.println(districts.size());
//
//         for (Object district : districts) {
//             Map districtMap = (Map) district;
//             DistrictPolyline polyline = new DistrictPolyline();
//             polyline.setCode((String) districtMap.get("adcode"));
//             String center = (String) districtMap.get("center");
//             String[] split = center.split(",");
//             polyline.setLongitude(Double.parseDouble(split[0]));
//             polyline.setLatitude(Double.parseDouble(split[1]));
//             polyline.setPolyline(districtMap.get("polyline").toString());
//             doPersist(polyline);
//         }
//
//     }
//
//     @Transactional(rollbackOn = Exception.class)
//     List<DistrictInfo> list() {
//         return repo.find("level <= ?1", DistrictLevelEnum.CITY).list();
//     }
//
//     @Transactional(rollbackOn = Exception.class)
//     void doPersist(DistrictPolyline polyline) {
//         polylineRepo.persist(polyline);
//     }
//
//     @GET
//     @Path("/init/district")
//     public String initDistrict() {
//         String url = "https://restapi.amap.com/v3/config/district";
//         String key = "82c50632321f61882f526eefb86073af";
//
//         String params = "key=" + key + "&subdistrict=3&extensions=base";
//         url += "?" + params;
//         HashMap<String, String> headers = new HashMap<>();
//         headers.put("Content-Type", "application/json");
//         HttpUtils.HttpResult response = HttpUtils.get(url).header(headers).send();
//         String string = response.body();
//         Map object = JsonUtils.toObject(string, Map.class);
//         List<DistrictInfo> districtInfo = createDistrictInfo(object);
//         districtInfo.forEach(d -> d.setParentId(0L));
//         bfsPersist(districtInfo);
//         return "init district success";
//     }
//
//     // bfs
//     void bfsPersist(List<DistrictInfo> districtInfo) {
//         List<DistrictInfo> next = new ArrayList<>();
//         Lists.partition(districtInfo, 100).forEach(this::doPersist);
//         for (DistrictInfo info : districtInfo) {
//             if (info.getChildren() != null) {
//                 info.getChildren().forEach(d -> d.setParentId(info.getId()));
//                 next.addAll(info.getChildren());
//             }
//         }
//         if (!next.isEmpty()) {
//             bfsPersist(next);
//         }
//     }
//
//     @Transactional(rollbackOn = Exception.class)
//     void doPersist(List<DistrictInfo> districtInfo) {
//         repo.persist(districtInfo);
//     }
//
//     private List<DistrictInfo> createDistrictInfo(Map object) {
//         List<DistrictInfo> ret = new ArrayList<>();
//         List districts = (List) object.get("districts");
//         for (Object district : districts) {
//             Map districtMap = (Map) district;
//             DistrictInfo districtInfo = new DistrictInfo();
//             districtInfo.setCode((String) districtMap.get("adcode"));
//             districtInfo.setName((String) districtMap.get("name"));
//             districtInfo.setLevel(DistrictLevelEnum.valueOf(((String) districtMap.get("level")).toUpperCase()));
//             String center = (String) districtMap.get("center");
//             String[] split = center.split(",");
//             districtInfo.setLongitude(Double.parseDouble(split[0]));
//             districtInfo.setLatitude(Double.parseDouble(split[1]));
//             if (districtMap.get("districts") != null) {
//                 districtInfo.setChildren(createDistrictInfo(districtMap));
//             }
//             ret.add(districtInfo);
//         }
//         return ret;
//     }
//
// }
