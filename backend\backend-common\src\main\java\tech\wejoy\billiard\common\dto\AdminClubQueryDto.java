package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.ClubStatusEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AdminClubQueryDto extends PageRequest {

    @QueryParam("name")
    private String name;

    @QueryParam("city")
    private String city;

    @QueryParam("status")
    @Separator(",")
    private List<ClubStatusEnum> status;

    @QueryParam("tenantId")
    private Long tenantId;

    @Schema(hidden = true)
    private List<Long> clubIds;

}
