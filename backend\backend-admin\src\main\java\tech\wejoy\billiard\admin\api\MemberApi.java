package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.admin.bo.MemberBo;
import tech.wejoy.billiard.admin.dto.ClubDatePeriodDto;
import tech.wejoy.billiard.admin.dto.ClubMemberQueryDto;
import tech.wejoy.billiard.admin.dto.ClubMemberRankQueryDto;
import tech.wejoy.billiard.admin.service.AdminMemberService;
import tech.wejoy.billiard.common.bo.TenantPlanBillBo;
import tech.wejoy.billiard.common.bo.TenantPlanBillStatsBo;
import tech.wejoy.billiard.common.bo.TenantPlanBo;
import tech.wejoy.billiard.common.bo.TenantPlanGiftRecordBo;
import tech.wejoy.billiard.common.dto.AdminTenantPlanQueryDto;
import tech.wejoy.billiard.common.dto.MemberPresentDto;
import tech.wejoy.billiard.common.dto.TenantPlanDto;

import java.util.List;

@Path("/member")
@Slf4j
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "会员")
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class MemberApi {

    private final AdminMemberService memberService;

    @GET
    @Path("/rank")
    @Operation(summary = "会员排名")
    @Permission({"business_member_rank:read", "business_member_rank:write"})
    public Page<MemberBo> rank(@BeanParam ClubMemberRankQueryDto dto) {
        return memberService.rank(dto);
    }

    @GET
    @Path("/list")
    @Operation(summary = "会员列表")
    @Permission({"business_member:read", "business_member:write"})
    public Page<MemberBo> list(@BeanParam ClubMemberQueryDto dto) {
        return memberService.list(dto);
    }

    @GET
    @Path("/config/list")
    @Operation(summary = "单店卡列表")
    @Permission({"business_member:read", "business_member:write"})
    public List<TenantPlanBo> configList(@BeanParam ClubMemberQueryDto dto) {
        return memberService.configList(dto);
    }

    @POST
    @Path("/config")
    @Operation(summary = "创建单店卡")
    @Permission({"business_member:write"})
    public TenantPlanBo createMember(TenantPlanDto dto) {
        return memberService.createMember(dto);
    }

    @PUT
    @Path("/config/{id}")
    @Operation(summary = "更新单店卡")
    @Permission({"business_member:write"})
    public TenantPlanBo updateMember(@PathParam("id") Long id, TenantPlanDto dto) {
        dto.setId(id);
        return memberService.updateMember(dto);
    }

    @DELETE
    @Path("/config/{id}")
    @Operation(summary = "删除单店卡")
    @Permission({"business_member:write"})
    public void deleteMember(@PathParam("id") Long id) {
        memberService.deleteMember(id);
    }

    @POST
    @Path("/present")
    @Operation(summary = "赠送单店卡")
    @Permission({"business_member:write"})
    public void presentMember(MemberPresentDto dto) {
        memberService.presentMember(dto);
    }

    @GET
    @Path("/gift/list")
    @Operation(summary = "赠送记录")
    @Permission({"business_member:read", "business_member:write"})
    public Page<TenantPlanGiftRecordBo> giftList(@BeanParam AdminTenantPlanQueryDto dto) {
        return memberService.giftList(dto);
    }

    @GET
    @Path("/bill/list")
    @Operation(summary = "购买记录")
    @Permission({"business_member:read", "business_member:write"})
    public Page<TenantPlanBillBo> billList(@BeanParam AdminTenantPlanQueryDto dto) {
        return memberService.billList(dto);
    }

    @GET
    @Path("/bill/stats")
    @Operation(summary = "购买统计")
    @Permission({"business_member:read", "business_member:write"})
    public TenantPlanBillStatsBo billStats(@BeanParam ClubDatePeriodDto dto) {
        return memberService.billStats(dto);
    }

}
