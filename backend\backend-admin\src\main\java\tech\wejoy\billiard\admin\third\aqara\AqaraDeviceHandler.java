package tech.wejoy.billiard.admin.third.aqara;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.JsonUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.admin.third.DeviceHandler;
import tech.wejoy.billiard.common.entity.TenantAqaraAccount;
import tech.wejoy.billiard.common.entity.TenantDevice;
import tech.wejoy.billiard.common.enums.DeviceTypeEnum;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;
import tech.wejoy.billiard.common.third.aqara.client.AqaraClient;
import tech.wejoy.billiard.common.third.aqara.request.ResourceWrite;
import tech.wejoy.billiard.common.third.aqara.response.ResourceValue;
import tech.wejoy.billiard.common.third.aqara.response.ResourceWriteResp;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public abstract class AqaraDeviceHandler extends DeviceHandler {

    private final static Map<DeviceTypeEnum, DeviceHandler> handlerMap = new ConcurrentHashMap<>();

    protected AqaraClient aqaraClient;


    protected AqaraDeviceHandler() {
        this.aqaraClient = ThirdServiceHolder.aqaraClient("base");
    }

    public static DeviceHandler getHandler(DeviceTypeEnum type) {
        if (Objects.requireNonNull(type) == DeviceTypeEnum.LIGHT) {
            return handlerMap.computeIfAbsent(DeviceTypeEnum.LIGHT, k -> new AqaraLightDeviceHandler());
        }
        return null;
    }

    protected boolean operator(TenantDevice device, String value) {
        String deviceId = device.getDeviceId();
        TenantAqaraAccount aqaraAccount = deviceManager.getAqaraAccount(device.getAccountId());
        if (aqaraAccount == null) {
            throw new BaseException("账号未找到");
        }
//        ResourceValueQuery query = new ResourceValueQuery();
//        query.setSubjectId(deviceId);
//        query.setResourceIds(Lists.newArrayList(device.getResourceId()));
//        log.info("query:{}", JsonUtils.toJson(query));
//        List<ResourceValue> resourceValues = aqaraClient.queryResourceValue(aqaraAccount, query);
//        if (!resourceValues.isEmpty() &1188& resourceValues.getFirst().getValue().equals(value)) {
//            return true;
//        }
        ResourceWrite resourceWrite = new ResourceWrite();
        resourceWrite.setSubjectId(deviceId);
        ResourceValue resourceValue = new ResourceValue();
        resourceValue.setResourceId(device.getResourceId());
        resourceValue.setValue(value);
        resourceWrite.setResources(Lists.newArrayList(resourceValue));
        log.info("operator:{}", JsonUtils.toJson(resourceWrite));
        List<ResourceWriteResp> resp = aqaraClient.writeResourceValue(aqaraAccount, resourceWrite);
        if (CollectionUtils.isEmpty(resp) || !resp.getFirst().getErrorCode().equals("0")) {
            log.error("设备操作失败:{}", resp);
            return false;
        }
        return true;
    }

}
