"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/assistant/detail/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/detail/index!./src/pages/assistant/detail/index.tsx":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/detail/index!./src/pages/assistant/detail/index.tsx ***!
  \**************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SafeArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/safearea.taro-BJpvFsMf.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Swiper_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Swiper/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Swiper/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Swiper__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Swiper */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Swiper.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SwiperItem_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SwiperItem/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SwiperItem/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SwiperItem__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SwiperItem */ "./node_modules/@nutui/nutui-react-taro/dist/esm/swiperitem.taro-BleF1EOs.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Dialog/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog */ "./node_modules/@nutui/nutui-react-taro/dist/esm/dialog.taro-1Vukbvap.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/api/bussiness/assistant */ "./src/api/bussiness/assistant.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _components_bussiness_Card_Assistant__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/bussiness/Card/Assistant */ "./src/components/bussiness/Card/Assistant.tsx");
/* harmony import */ var _components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/bussiness/Order/Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var _components_bussiness_Posters_assistant__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/bussiness/Posters/assistant */ "./src/components/bussiness/Posters/assistant.tsx");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_13__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");


























var PostersButton = function PostersButton(props) {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(false),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_15__["default"])(_useState, 2),
    visible = _useState2[0],
    setVisible = _useState2[1];
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
    className: "w-1s3",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_11__["default"], {
      buttonText: "\u751F\u6210\u6D77\u62A5",
      mode: _components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_11__.MODES.Light,
      onClick: function onClick() {
        return setVisible(true);
      }
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_16__.C, {
      theme: {
        "--nutui-gray-7": "#fff",
        "--nutui-gray-6": "#fff",
        "--nutui-dialog-header-font-weight": "600",
        "--nutui-dialog-header-font-size": "1.25rem",
        "--nutui-dialog-padding": "20rpx",
        "--nutui-dialog-content-max-height": "auto"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_17__.B, {
        visible: visible,
        onClose: function onClose() {
          return setVisible(false);
        },
        hideCancelButton: true,
        hideConfirmButton: true,
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_components_bussiness_Posters_assistant__WEBPACK_IMPORTED_MODULE_12__["default"], (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_18__["default"])({}, props))
      })
    })]
  });
};
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_7__.useRouter)();
  var id = router.params.id;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_15__["default"])(_useState3, 2),
    detail = _useState4[0],
    setDetail = _useState4[1];
  var _Taro$getSystemInfoSy = _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().getSystemInfoSync(),
    size = _Taro$getSystemInfoSy.windowWidth;
  var getDetail = (0,react__WEBPACK_IMPORTED_MODULE_8__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_19__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_20__["default"])().mark(function _callee(id) {
      var _yield$api$getOne, data;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_20__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_9__["default"].getOne(id);
          case 2:
            _yield$api$getOne = _context.sent;
            data = _yield$api$getOne.data;
            setDetail(data);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [setDetail]);
  (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(function () {
    getDetail(id);
  }, []);
  var diffDay = (0,react__WEBPACK_IMPORTED_MODULE_8__.useCallback)(function (date, util) {
    function numberToChinese(year) {
      var cnNumbers = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
      var cnUnits = ["年", "十", "百"];
      var stringifiedYear = String(year);
      var cnYear = "";
      for (var i = 0; i < stringifiedYear.length; i++) {
        var digit = Number(stringifiedYear[i]);
        if (i === 0 && digit === 0) {
          cnYear += cnNumbers[digit];
        } else if (i === 1) {
          cnYear += cnUnits[1];
          cnYear += cnNumbers[digit];
        } else if (i === 2 && digit !== 0) {
          cnYear += cnUnits[2];
          cnYear += cnNumbers[digit];
        }
      }
      return cnYear;
    }
    return numberToChinese(dayjs__WEBPACK_IMPORTED_MODULE_13___default()().diff(date, util));
  }, []);
  var content = (0,react__WEBPACK_IMPORTED_MODULE_8__.useMemo)(function () {
    if (!detail) return [];
    return [{
      value: detail.name,
      fontWeight: "bord",
      fonSize: 12,
      color: "#333"
    }, {
      value: "".concat(diffDay(dayjs__WEBPACK_IMPORTED_MODULE_13___default()(detail.birth), "year"), "\u5C81\uFF5C\u4ECE\u4E1A ").concat(diffDay(dayjs__WEBPACK_IMPORTED_MODULE_13___default()(detail.startWork), "year"), " \u5E74"),
      fontWeight: "normal",
      fonSize: 10,
      color: "#666"
    }, {
      value: detail.tags.join("、"),
      fontWeight: "normal",
      fonSize: 10,
      color: "#666"
    }];
  }, [detail]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
    className: "flex h-_100vh_ flex-col overflow-auto",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_5__["default"], {}), detail && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
      className: "flex flex-1 flex-col",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
        className: "relative flex-1",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_nutui_nutui_react_taro_dist_esm_Swiper__WEBPACK_IMPORTED_MODULE_21__["default"], {
          height: size,
          autoPlay: true,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_nutui_nutui_react_taro_dist_esm_SwiperItem__WEBPACK_IMPORTED_MODULE_22__.S, {
            className: "w-full rounded-md",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_23__.Image, {
              src: detail.avatar || "https://oss.gorillaballclub.cn/images/big-logo-y.png",
              style: {
                height: size
              },
              className: "w-full"
            })
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
          className: "px-3 relative space-y-3",
          style: {
            top: -(size / 5)
          },
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
            className: "flex flex-col gap-1 p-3 bg-bgt rounded-md mb-3",
            children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_components_bussiness_Card_Assistant__WEBPACK_IMPORTED_MODULE_10__["default"], {
              item: detail
            })
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
            className: "py-3 flex px-3 justify-between text-sm",
            onClick: function onClick() {
              return _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().navigateTo({
                url: "/pages/assistant/club/index?id=".concat(id)
              });
            },
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
              children: "\u7B7E\u7EA6\u5546\u5BB6"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
              className: "flex gap-2 items-center",
              children: [detail.clubs.length, "\u5BB6 ", /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_6__.ArrowRight, {})]
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
            className: "px-3 space-y-2d5 text-sm",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
              children: "\u4E0B\u5355\u987B\u77E5"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
              className: "text-xs text-muted-foreground space-y-1d5",
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
                children: "1. \u9009\u62E9\u52A9\u6559\u4E0B\u5355\u6700\u4F4E1\u5C0F\u65F6\u8D77\u6B65\uFF1B"
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
                children: "2. \u901A\u8FC7\u5BA2\u670D\u4E0B\u5355\u80FD\u63D0\u9AD8\u6210\u529F\u7387\uFF0C\u6BD4\u5982\u52A9\u6559\u662F\u5426\u53EF\u4EE5\u63A5\u5355\uFF1B"
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
                children: "3. \u7528\u6237\u5168\u989D\u4ED8\u6B3E\u540E\uFF0C\u56E0\u52A9\u6559\u539F\u56E0\u53D6\u6D88\u8BA2\u5355\uFF0C\u5E73\u53F0\u5168\u989D\u9000\u6B3E\uFF1B"
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
                children: "4. \u7528\u6237\u5168\u989D\u4ED8\u6B3E\u540E\uFF0C\u8BA2\u5355\u5DF2\u5F00\u59CB\u6BCF1\u4E2A\u5C0F\u65F6\u4E3A\u4E00\u4E2A\u65F6\u95F4\u8282\u70B9\uFF0C\u4E0D\u8DB31\u5C0F\u65F6\u6309\u71671\u5C0F\u65F6\u8BA1\u7B97\uFF1B"
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
                children: "5. \u9884\u7EA6\u52A9\u6559\u53EA\u80FD\u9009\u62E9\u52A9\u6559\u7B7E\u7EA6\u7403\u9986\u4E0B\u5355"
              })]
            })]
          })]
        })]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
      className: "px-3",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsxs)("div", {
        className: "flex gap-2",
        children: [detail && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(PostersButton, {
          avatar: detail.avatar || "https://oss.gorillaballclub.cn/images/big-logo-y.png",
          content: content,
          code: detail.id
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)("div", {
          className: "flex-1",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_11__["default"], {
            buttonText: "\u7ACB\u5373\u9884\u7EA6",
            onClick: function onClick() {
              return _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().navigateTo({
                url: "/pages/assistant/confirmation/index?id=".concat(id)
              });
            }
          })
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_14__.jsx)(_nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_24__.S, {
        position: "bottom"
      })]
    })]
  });
});

/***/ }),

/***/ "./src/components/bussiness/Posters/assistant.tsx":
/*!********************************************************!*\
  !*** ./src/components/bussiness/Posters/assistant.tsx ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api */ "./src/api/index.ts");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _Order_Button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Order/Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");







var WIDTH = 240;
var HEIGHT = 400;
var QRSIZE = 80;
var PADDING = 20;
/**
 * |------------|
 * |    cover   |
 * |------------|
 * | name  [QR] |
 * | descript   |
 * | tags       |
 * |------------|
 */
/* harmony default export */ __webpack_exports__["default"] = (function (props) {
  var avatar = props.avatar,
    content = props.content,
    code = props.code;
  var ctx = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();
  var tempFilePath = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)();
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function () {
    ctx.current = _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().createCanvasContext("canvas");
    draw();
  }, []);
  var toCenter = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (width) {
    return WIDTH / 2 - width / 2;
  }, []);
  var drawImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function (ctx, image, x, y, width, height) {
    ctx.drawImage(image.path, x, y, width, height);
  }, []);
  var draw = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().mark(function _callee2() {
    var _ctx$current4, cover, coverHeight, URI, qrcodeX, qrcodeY, qrCode, qrCodeHeight;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!ctx.current) {
            _context2.next = 18;
            break;
          }
          // 绘制底色
          ctx.current.setFillStyle("white");
          ctx.current.fillRect(0, 0, WIDTH, HEIGHT);

          // 绘制封面
          _context2.next = 5;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().getImageInfo({
            src: avatar
          });
        case 5:
          cover = _context2.sent;
          coverHeight = WIDTH;
          drawImage(ctx.current, cover, 0, 0, WIDTH, coverHeight);

          // 绘制文本
          content.forEach(function (item, index) {
            var _ctx$current, _ctx$current2, _ctx$current3;
            var fontWeight = item.fontWeight,
              value = item.value,
              fonSize = item.fonSize,
              color = item.color;
            (_ctx$current = ctx.current) === null || _ctx$current === void 0 || _ctx$current.setFontSize(fonSize);
            if (ctx.current) {
              ctx.current.font = fontWeight || 'normal';
            }
            (_ctx$current2 = ctx.current) === null || _ctx$current2 === void 0 || _ctx$current2.setFillStyle(color);
            (_ctx$current3 = ctx.current) === null || _ctx$current3 === void 0 || _ctx$current3.fillText(value, PADDING, coverHeight + PADDING * 2 + index * fonSize * 1.5);
          });

          // 绘制QRCode
          URI = "".concat(_api__WEBPACK_IMPORTED_MODULE_0__.BASE_URI, "/assistant/").concat(code, "/code");
          qrcodeX = WIDTH - QRSIZE - PADDING;
          qrcodeY = coverHeight + PADDING;
          _context2.next = 14;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().getImageInfo({
            src: URI
          });
        case 14:
          qrCode = _context2.sent;
          qrCodeHeight = qrCode.height * (QRSIZE / qrCode.width);
          drawImage(ctx.current, qrCode, qrcodeX, qrcodeY, QRSIZE, qrCodeHeight);
          (_ctx$current4 = ctx.current) === null || _ctx$current4 === void 0 || _ctx$current4.draw(true, /*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().mark(function _callee() {
            var result;
            return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().wrap(function _callee$(_context) {
              while (1) switch (_context.prev = _context.next) {
                case 0:
                  _context.next = 2;
                  return _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().canvasToTempFilePath({
                    canvasId: 'canvas'
                  });
                case 2:
                  result = _context.sent;
                  tempFilePath.current = result.tempFilePath;
                case 4:
                case "end":
                  return _context.stop();
              }
            }, _callee);
          })));
        case 18:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [drawImage, code, toCenter, content, avatar]);
  var save = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().mark(function _callee3() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          if (!tempFilePath.current) {
            _context3.next = 3;
            break;
          }
          _context3.next = 3;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().showShareImageMenu({
            path: tempFilePath.current
          });
        case 3:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  })), []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
      id: "poster",
      className: "flex justify-center overflow-hidden rounded-md mb-4",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("canvas", {
        "canvas-id": "canvas",
        width: "".concat(WIDTH, "px"),
        height: "".concat(HEIGHT, "px")
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Order_Button__WEBPACK_IMPORTED_MODULE_3__["default"], {
      buttonText: "\u4FDD\u5B58\u81F3\u76F8\u518C",
      mode: _Order_Button__WEBPACK_IMPORTED_MODULE_3__.MODES.Dark,
      onClick: function onClick() {
        return save();
      }
    })]
  });
});

/***/ }),

/***/ "./src/pages/assistant/detail/index.tsx":
/*!**********************************************!*\
  !*** ./src/pages/assistant/detail/index.tsx ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/detail/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/detail/index!./src/pages/assistant/detail/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white","disableScroll":true};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/assistant/detail/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/assistant/detail/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map