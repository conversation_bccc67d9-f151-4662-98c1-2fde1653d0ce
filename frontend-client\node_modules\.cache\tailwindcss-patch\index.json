["!visible", "absolute", "after:border-none", "align-middle", "bg-[rgba(0,0,0,0.70)]", "bg-background", "bg-bgf", "bg-bgs", "bg-bgt", "bg-bgt/25", "bg-bgt/75", "bg-blue-500", "bg-card", "bg-destructive", "bg-muted-foreground", "bg-muted-foreground/15", "bg-primary", "bg-secondary", "bg-transparent", "bg-white", "border", "border-2", "border-[2rpx]", "border-[gray]", "border-b", "border-background/75", "border-bgf", "border-input", "border-muted-foreground/20", "border-muted-foreground/50", "border-muted-foreground/75", "border-none", "border-primary", "border-primary/75", "border-r", "border-solid", "border-t", "border-white/25", "bottom-0", "bottom-10", "bottom-5", "break-all", "disabled:bg-primary", "disabled:opacity-50", "disabled:pointer-events-none", "filter", "first-letter:text-sm", "first-letter:text-xs", "fixed", "flex", "flex-1", "flex-col", "flex-nowrap", "flex-shrink-0", "flex-wrap", "font-bold", "font-normal", "font-semibold", "gap-1", "gap-1.5", "gap-10", "gap-2", "gap-3", "gap-4", "gap-[10PX]", "gap-x-2", "gap-y-3", "grid", "grid-cols-2", "grid-cols-4", "grid-cols-5", "grid-flow-col", "h-10", "h-11", "h-12", "h-14", "h-20", "h-3", "h-3.5", "h-4", "h-48", "h-5", "h-6", "h-7", "h-8", "h-9", "h-[100%]", "h-[100vh]", "h-[11rem]", "h-[4.6rem]", "h-[5rem]", "h-[6.5rem]", "h-full", "hidden", "hover:bg-accent", "hover:bg-destructive/90", "hover:bg-primary/90", "hover:bg-secondary/80", "hover:text-accent-foreground", "hover:underline", "i-ant-design-pay-circle-filled", "i-iconoir-clock", "i-iconoir-credit-card-solid", "i-iconoir-home-simple-door", "i-iconoir-profile-circle", "i-iconoir-trophy", "i-mdi-account-card-outline", "i-mdi-cog", "i-mdi-inbox-full", "i-mdi-list-box-outline", "i-mdi-message-question-outline", "i-ph-clock", "i-ph-crown-simple-fill", "i-ph-map-pin-fill", "i-ph-navigation-arrow-fill", "i-ph-note", "i-ph-phone-fill", "i-ph-picnic-table", "i-ph-qr-code", "i-ph-ticket-fill", "i-simple-icons-wechat", "inline-block", "inline-flex", "italic", "items-center", "items-end", "justify-between", "justify-center", "justify-end", "justify-start", "leading-4", "leading-none", "leading-relaxed", "left-0", "left-[50%]", "line-through", "mb-1", "mb-10", "mb-2", "mb-3", "mb-4", "min-h-[4.6rem]", "min-h-[6rem]", "min-w-[7rem]", "ml-1", "ml-2", "ml-3", "ml-4", "mr-1", "mr-2", "mt-2", "mt-3", "mx-2", "mx-3", "opacity-75", "outline", "overflow-auto", "overflow-hidden", "overflow-x-auto", "overflow-y-auto", "p-0", "p-1", "p-10", "p-2", "p-3", "p-3.5", "p-4", "p-6", "pb-1", "pb-10", "pb-2", "pb-3", "pb-6", "pl-4", "pt-0", "pt-2", "pt-3", "pt-[6rem]", "px-1", "px-1.5", "px-2", "px-3", "px-4", "px-8", "px-[10PX]", "py-1", "py-1.5", "py-10", "py-2", "py-3", "py-4", "relative", "right-3", "rotate-180", "rounded-[0.15rem]", "rounded-full", "rounded-lg", "rounded-md", "rounded-none", "rounded-sm", "rounded-t-lg", "rounded-xl", "scale-y-75", "self-stretch", "shadow", "space-x-1", "space-x-3", "space-y-1", "space-y-1.5", "space-y-2", "space-y-2.5", "space-y-3", "space-y-[2rpx]", "sticky", "table", "text-2xl", "text-[#f5f5f5]", "text-[0.75rem]", "text-[24rpx]", "text-background", "text-base", "text-bgs", "text-bgt", "text-card-foreground", "text-center", "text-destructive-foreground", "text-foreground", "text-foreground/95", "text-green-500", "text-lg", "text-muted-foreground", "text-muted-foreground/50", "text-muted-foreground/80", "text-muted/75", "text-pink-500", "text-primary", "text-primary-foreground", "text-red-500", "text-secondary-foreground", "text-sm", "text-white", "text-xl", "text-xs", "text-yellow-500", "top-3", "top-4", "top-[50%]", "tracking-tight", "tracking-wide", "transition-all", "transition-transform", "translate-x-[-50%]", "translate-y-[-50%]", "underline-offset-4", "visible", "w-1/2", "w-1/3", "w-10", "w-12", "w-16", "w-2/3", "w-20", "w-3", "w-3.5", "w-4", "w-5", "w-7", "w-8", "w-9", "w-[100%]", "w-[1px]", "w-[20%]", "w-[30%]", "w-[4.5rem]", "w-[4.6rem]", "w-[5rem]", "w-full", "whitespace-nowrap", "z-10", "z-[10]", "block", "cursor-pointer", "mt-1", "text-left", "underline"]