package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class TenantProfitAccount extends BaseEntity {

    private Long tenantId;

    private BigDecimal balance;

    private BigDecimal fee;

    private BigDecimal total;

}
