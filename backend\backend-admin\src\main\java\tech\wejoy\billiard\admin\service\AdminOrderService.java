package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.database.bean.BaseEntity;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.admin.bo.OrderStatsBo;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.AdminOrderQueryDto;
import tech.wejoy.billiard.common.dto.DeviceOperationDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;
import tech.wejoy.billiard.common.manager.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class AdminOrderService {

    private final OrderManager orderManager;

    private final ClientUserManager clientUserManager;

    private final ClubManager clubManager;

    private final DouyinTicketManager douyinTicketManager;

    private final MeituanTicketManager meituanTicketManager;

    private final CouponManager couponManager;

    private final AdminUserManager adminUserManager;

    @Inject
    DeviceService deviceService;

    public OrderStatsBo statsOrder(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
        List<OrderInfo> orderInfos = orderManager.fetchOrderByClubAndDate(null, tenantId, clubId, start, end);
        OrderStatsBo ret = new OrderStatsBo();
        for (OrderInfo orderInfo : orderInfos) {
            switch (orderInfo.getStatus()) {
                case CANCEL:
                    ret.setCanceled(ret.getCanceled() + 1);
                    break;
                case FINISH:
                    ret.setFinished(ret.getFinished() + 1);
                    break;
                case USING:
                    ret.setUsing(ret.getUsing() + 1);
                    break;
                default:
                    break;
            }
        }
        return ret;
    }

    public Page<AdminOrderBo> page(AdminOrderQueryDto dto) {
        if (dto.getPhone() != null && !dto.getPhone().isEmpty()) {
            List<ClientUser> users = clientUserManager.fetchListByPhone(dto.getPhone());
            dto.setUserIds(users.stream().map(ClientUser::getId).toList());
        }
        Page<OrderInfo> orderPage = orderManager.fetchAdminPage(dto);

        List<Long> adminUserIds = orderPage.getRecords().stream().filter(v -> v.getPayType() == OrderPayTypeEnum.FREE).map(BaseEntity::getCreateBy).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(adminUserIds);
        Map<Long, AdminUser> adminUserMap = adminUsers.stream().collect(Collectors.toMap(BaseEntity::getId, u -> u));
        List<Long> userIds = orderPage.getRecords().stream().map(OrderInfo::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        List<Long> tableIds = orderPage.getRecords().stream().map(OrderInfo::getTableId).toList();
        List<ClubTable> tables = clubManager.fetchTableByIds(tableIds);
        List<Long> clubIds = orderPage.getRecords().stream().map(OrderInfo::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, c -> c));
        Map<Long, ClubTable> tableMap = tables.stream().collect(Collectors.toMap(BaseEntity::getId, t -> t));
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, u -> u));
        return orderPage.convert(orderInfo -> {
            AdminOrderBo orderBo = BeanUtils.copy(orderInfo, AdminOrderBo.class);
            orderBo.setTotalAmount(orderInfo.getRealAmount().subtract(orderInfo.getRefundAmount()));
            ClientUser clientUser = userMap.get(orderInfo.getUserId());
            if (clientUser != null) {
                orderBo.setUserPhone(clientUser.getPhone());
                orderBo.setNickname(clientUser.getNickname());
            }
            AdminUser adminUser = adminUserMap.get(orderInfo.getCreateBy());
            if (adminUser != null && orderInfo.getPayType() == OrderPayTypeEnum.FREE) {
                orderBo.setUserPhone(adminUser.getPhone());
                orderBo.setNickname(adminUser.getUsername());
            }
            orderBo.setTableName(tableMap.get(orderInfo.getTableId()).getName());
            orderBo.setClubName(clubMap.get(orderInfo.getClubId()).getName());
            return orderBo;
        });
    }

    public void payOrder(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        OrderInfo payInfoOrder = JsonUtils.toObject(payInfo, OrderInfo.class);
        OrderInfo order = orderManager.fetchOrderByNo(payInfoOrder.getOrderNo());
        order.setStatus(OrderStatusEnum.PAID);
        order.setPayAmount(payerTotal);
        order.setPayTime(LocalDateTime.now());
        orderManager.save(order);
        clubManager.updateTableUsing(order.getTableId(), userId, order.getEndTime());
        if (LocalDateTime.now().plusSeconds(30).isAfter(order.getStartTime())) {
            DeviceOperationDto dto = new DeviceOperationDto();
            dto.setOrderNo(order.getOrderNo());
            dto.setType(DeviceOperationTypeEnum.START);
            deviceService.handleOrderDevice(dto);
        }
    }

    public AdminOrderDetailBo detail(Long id) {
        OrderInfo orderInfo = orderManager.fetchOrderById(id);
        AdminOrderDetailBo detail = new AdminOrderDetailBo();
        detail.setOrder(BeanUtils.copy(orderInfo, AdminOrderBo.class));
        ClientUser user = clientUserManager.fetchUserById(orderInfo.getUserId());
        if (user != null) {
            detail.setUser(BeanUtils.copy(user, ClientUserBo.class));
        }
        ClubTable table = clubManager.fetchClubTableById(orderInfo.getTableId());
        detail.setTable(AdminTableBo.from(table));
        ClubInfo club = clubManager.fetchClubById(orderInfo.getClubId());
        detail.setClub(BeanUtils.copy(club, AdminClubBo.class));
        if (orderInfo.getPayType() == OrderPayTypeEnum.MEITUAN) {
            Map object = JsonUtils.toObject(orderInfo.getDescription(), Map.class);
            ClientUserTicket ticket = JsonUtils.toObject(JsonUtils.toJson(object.get("ticket")), ClientUserTicket.class);
            if (ticket.getChannel() == TicketChannelEnum.DOUYIN) {
                DouyinDeal douyinDeal = douyinTicketManager.fetchDouyinDealById(ticket.getDealId());
                ChannelDealBo copy = BeanUtils.copy(douyinDeal, ChannelDealBo.class);
                copy.setChannel(TicketChannelEnum.DOUYIN);
                detail.setDeal(copy);
            } else {
                MeituanDeal meituanDeal = meituanTicketManager.fetchMeituanDealById(ticket.getDealId());
                ChannelDealBo copy = BeanUtils.copy(meituanDeal, ChannelDealBo.class);
                copy.setChannel(TicketChannelEnum.MEITUAN);
                detail.setDeal(copy);
            }
        } else if (orderInfo.getPayType() == OrderPayTypeEnum.DOUYIN) {
            Map object = JsonUtils.toObject(orderInfo.getDescription(), Map.class);
            ClientUserTicket ticket = JsonUtils.toObject(JsonUtils.toJson(object.get("ticket")), ClientUserTicket.class);
            DouyinDeal douyinDeal = douyinTicketManager.fetchDouyinDealById(ticket.getDealId());
            ChannelDealBo copy = BeanUtils.copy(douyinDeal, ChannelDealBo.class);
            copy.setChannel(TicketChannelEnum.DOUYIN);
            detail.setDeal(copy);
        } else if (orderInfo.getPayType() == OrderPayTypeEnum.COUPON) {
            ClientUserCoupon coupon = JsonUtils.toObject(orderInfo.getDescription(), ClientUserCoupon.class);
            if (coupon.getCouponId() != null) {
                TenantCoupon tenantCoupon = couponManager.fetchCouponById(coupon.getCouponId());
                if (tenantCoupon != null) {
                    CouponBo copy = BeanUtils.copy(tenantCoupon, CouponBo.class);
                    copy.setGift(coupon.getGift());
                    detail.setCoupon(copy);
                }
            }
        }
        return detail;
    }

    public void refundOrder(BillInfo billInfo) {
        throw new UnsupportedOperationException("暂不支持订单直接退款");
    }

}
