package tech.wejoy.billiard.common.third.douyin.response;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProductPage extends ResultResponse {
    private boolean hasMore;
    private String nextCursor;
    private List<ProductInfo> products;

    @Data
    public static class ProductInfo {
        private Product product;
        private Sku sku;
    }

    @Data
    public static class Product {
        private String categoryFullName;
        private String outId;
        private Long ownerAccountId;
        private Map<String, Object> attrKeyValueMap;
        private Long categoryId;
        private String productId;
        private Integer bizLine;
        private Long creatorAccountId;
        private Integer productType;
        private String accountName;
        private List<Poi> pois;
        private String productName;
        private Long soldEndTime;
        private Long soldStartTime;
        private List<String> telephone;
    }

    @Data
    public static class Poi {
        private Long poiId;
        private String supplierExtId;
        private Long supplierId;
    }

    @Data
    public static class Sku {
        private BigDecimal actualAmount;
        private Map<String, Object> attrKeyValueMap;
        private Integer status;
        private Long updateTime;
        private Long createTime;
        private BigDecimal originAmount;
        private String skuId;
        private String skuName;
        private Stock stock;
    }

    @Data
    public static class Stock {
        private Long limitType;
        private Long stockQty;
    }

    @Data
    public static class CommissionInfo {
        private BigDecimal platformTakeRate;
    }

}
