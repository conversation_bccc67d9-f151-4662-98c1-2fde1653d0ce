package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.DeviceRelTypeEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClubTableDeviceRel extends BaseEntity {

    private Long tenantId;

    private Long clubId;

    private Long tableId;

    private Long deviceId;

    private DeviceRelTypeEnum type;

}
