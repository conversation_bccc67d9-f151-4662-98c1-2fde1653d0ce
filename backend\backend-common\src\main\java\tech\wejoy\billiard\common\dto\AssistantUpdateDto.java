package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.GenderEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class AssistantUpdateDto {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "姓名")
    private String name;

    private String phone;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "标签")
    private List<String> tags;

    @Schema(description = "性别")
    private GenderEnum gender;

    @Schema(description = "生日")
    private LocalDate birth;

    private BigDecimal price;

    @Schema(description = "入职时间")
    private LocalDate startWork;

    @Schema(description = "可用门店")
    private List<Long> clubIds;

    private IsEnum enable;

}
