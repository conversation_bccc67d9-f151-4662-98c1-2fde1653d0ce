package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class DistrictPolyline extends BaseEntity {

    private String code;

    private Double latitude;

    private Double longitude;

    @Column(columnDefinition = "text")
    private String polyline;

}
