package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.common.bo.MatchBo;
import tech.wejoy.billiard.common.dto.MatchAdminQueryDto;
import tech.wejoy.billiard.common.service.MatchService;

@Path("/match")
@Tag(name = "约球接口")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class MatchApi {

    private final MatchService matchService;


    @GET
    @Path("/list")
    @Operation(summary = "获取约球列表")
    public Page<MatchBo> list(@BeanParam MatchAdminQueryDto dto) {
        return matchService.adminList(dto);
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "获取约球详情")
    public MatchBo get(@PathParam("id") Long id) {
        return matchService.adminDetail(id);
    }


    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除约球")
    public void delete(@PathParam("id") Long id) {
        matchService.delete(id);
    }


}
