package tech.wejoy.billiard.admin.dto;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;
import tech.wejoy.billiard.common.enums.PermissionTypeEnum;

import java.util.List;

@Data
@RegisterForReflection
public class AdminRoleDto {

    private Long id;

    private Long tenantId;

    private Long clubId;

    private String name;

    private String code;

    private String description;

    private PermissionTypeEnum type;

    private List<String> permissions;

}
