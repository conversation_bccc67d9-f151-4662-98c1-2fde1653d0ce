package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "订单信息")
public class AdminOrderBo {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "加盟商ID")
    private Long tenantId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户手机号")
    private String userPhone;

    @Schema(description = "用户昵称")
    private String nickname;

    @Schema(description = "门店ID")
    private Long clubId;

    @Schema(description = "门店名称")
    private String clubName;

    @Schema(description = "门店图片")
    private String clubImage;

    @Schema(description = "桌号ID")
    private Long tableId;

    @Schema(description = "桌台名称")
    private String tableName;

    @Schema(description = "状态")
    private OrderStatusEnum status;

    @Schema(description = "支付方式")
    private OrderPayTypeEnum payType;

    @Schema(description = "创建时间")
    private LocalDateTime createAt;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "实际盈利金额")
    private BigDecimal realAmount;

    @Schema(description = "开始时间")
    private LocalDateTime realStartTime;

    @Schema(description = "结束时间")
    private LocalDateTime realEndTime;

}
