package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.admin.bo.AdminUserBo;
import tech.wejoy.billiard.admin.bo.MemberBo;
import tech.wejoy.billiard.admin.dto.ClubDatePeriodDto;
import tech.wejoy.billiard.admin.dto.ClubMemberQueryDto;
import tech.wejoy.billiard.admin.dto.ClubMemberRankQueryDto;
import tech.wejoy.billiard.common.bo.TenantPlanBillBo;
import tech.wejoy.billiard.common.bo.TenantPlanBillStatsBo;
import tech.wejoy.billiard.common.bo.TenantPlanBo;
import tech.wejoy.billiard.common.bo.TenantPlanGiftRecordBo;
import tech.wejoy.billiard.common.dto.AdminTenantPlanQueryDto;
import tech.wejoy.billiard.common.dto.MemberPresentDto;
import tech.wejoy.billiard.common.dto.TenantPlanDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.BillTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.manager.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class AdminMemberService {

    private final ClientUserManager clientUserManager;

    private final MemberManager memberManager;

    private final OrderManager orderManager;

    private final AdminUserManager adminUserManager;

    private final BillManager billManager;

    private final ClubManager clubManager;

    public Page<MemberBo> rank(ClubMemberRankQueryDto dto) {
        List<ClientUserTenantMember> members = memberManager.getClubMembersByClubId(dto.getClubId());
        if (members.isEmpty()) {
            return Page.of(0, List.of(), dto.getOffset(), dto.getSize());
        }
        List<Long> userIds = members.stream().map(ClientUserTenantMember::getUserId).distinct().toList();
        int total = userIds.size();
        Map<Long, List<ClientUserTenantMember>> memberGroup = members.stream().collect(Collectors.groupingBy(ClientUserTenantMember::getUserId));
        List<OrderInfo> orderList = orderManager.fetchFinishOrderByClubAndUserIdsClubType(dto.getClubId(), userIds);
        Map<Long, List<OrderInfo>> orderGroup = orderList.stream().collect(Collectors.groupingBy(OrderInfo::getUserId));
        List<Long> pageUserIds = switch (dto.getOrderBy()) {
            case SPENT_COUNT_DESC -> userIds.stream().sorted((o1, o2) -> {
                List<OrderInfo> orderInfos1 = orderGroup.get(o1);
                List<OrderInfo> orderInfos2 = orderGroup.get(o2);
                Integer spent1 = orderInfos1 == null ? 0 : orderInfos1.size();
                Integer spent2 = orderInfos2 == null ? 0 : orderInfos2.size();
                return spent2.compareTo(spent1);
            }).collect(Collectors.toList());
            case SPENT_AMOUNT_DESC -> userIds.stream().sorted((o1, o2) -> {
                List<OrderInfo> orderInfos1 = orderGroup.get(o1);
                List<OrderInfo> orderInfos2 = orderGroup.get(o2);
                BigDecimal spent1 = orderInfos1 == null ? BigDecimal.ZERO : orderInfos1.stream().map(v -> v.getRealAmount().subtract(v.getRefundAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal spent2 = orderInfos2 == null ? BigDecimal.ZERO : orderInfos2.stream().map(v -> v.getRealAmount().subtract(v.getRefundAmount())).reduce(BigDecimal.ZERO, BigDecimal::add);
                return spent2.compareTo(spent1);
            }).collect(Collectors.toList());
            case NONE, RECHARGE_AMOUNT_DESC -> userIds.stream().sorted((o1, o2) -> {
                List<ClientUserTenantMember> members1 = memberGroup.get(o1);
                List<ClientUserTenantMember> members2 = memberGroup.get(o2);
                BigDecimal recharge1 = members1 == null ? BigDecimal.ZERO : members1.stream().map(ClientUserTenantMember::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal recharge2 = members2 == null ? BigDecimal.ZERO : members2.stream().map(ClientUserTenantMember::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                return recharge2.compareTo(recharge1);
            }).collect(Collectors.toList());
        };
        List<ClientUser> list = clientUserManager.list(pageUserIds, dto.getPhone());
        Map<Long, ClientUser> userMap = list.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        pageUserIds = pageUserIds.stream().filter(userMap::containsKey).skip(dto.getOffset()).limit(dto.getSize()).toList();
        return Page.of(total, pageUserIds.stream().map(userMap::get).map(convertMemberBo(memberGroup, orderGroup)).toList(), dto.getOffset(), dto.getSize());
    }

    public Page<MemberBo> list(ClubMemberQueryDto dto) {
        List<ClientUserTenantMember> members = memberManager.getClubMembersByClubId(dto.getClubId());
        List<Long> userIds = members.stream().map(ClientUserTenantMember::getUserId).distinct().toList();
        Page<ClientUser> page = clientUserManager.page(userIds, dto.getPhone(), dto.getCurrent(), dto.getSize());
        Map<Long, List<ClientUserTenantMember>> memberGroup = members.stream().collect(Collectors.groupingBy(ClientUserTenantMember::getUserId));
        userIds = page.getRecords().stream().map(ClientUser::getId).toList();
        List<OrderInfo> orderList = orderManager.fetchFinishOrderByClubAndUserIdsClubType(dto.getClubId(), userIds);
        Map<Long, List<OrderInfo>> orderGroup = orderList.stream().collect(Collectors.groupingBy(OrderInfo::getUserId));
        return page.convert(convertMemberBo(memberGroup, orderGroup));
    }

    private static Function<ClientUser, MemberBo> convertMemberBo(Map<Long, List<ClientUserTenantMember>> memberGroup, Map<Long, List<OrderInfo>> orderGroup) {
        return user -> {
            MemberBo bo = new MemberBo();
            bo.setId(user.getId());
            bo.setPhone(user.getPhone());
            bo.setNickname(user.getNickname());
            bo.setAvatar(user.getAvatar());
            bo.setGender(user.getGender());
            bo.setBirth(user.getBirth());
            List<ClientUserTenantMember> userMembers = memberGroup.get(user.getId());
            if (userMembers != null) {
                bo.setBalance(userMembers.stream().map(ClientUserTenantMember::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add));
                bo.setBonus(userMembers.stream().map(ClientUserTenantMember::getBonus).reduce(BigDecimal.ZERO, BigDecimal::add));
                bo.setTotalAmount(userMembers.stream().map(ClientUserTenantMember::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
            List<OrderInfo> orderInfos = orderGroup.get(user.getId());
            if (orderInfos != null) {
                bo.setSpentAmount(orderInfos.stream().map(v -> v.getRealAmount().subtract(v.getRefundAmount())).reduce(BigDecimal.ZERO, BigDecimal::add));
                bo.setOrderCount(orderInfos.size());
                bo.setLastOrderAt(orderInfos.getFirst().getCreateAt());
            }
            return bo;
        };
    }

    public List<TenantPlanBo> configList(ClubMemberQueryDto dto) {
        if (dto.getClubId() != null) {
            List<TenantPlan> clubPlans = memberManager.fetchTenantPlanByClub(dto.getClubId());
            return BeanUtils.copyList(clubPlans, TenantPlanBo.class);
        }
        List<TenantPlan> tenantPlans = memberManager.fetchTenantPlanByTenant(dto.getTenantId());
        return BeanUtils.copyList(tenantPlans, TenantPlanBo.class);
    }

    @Transactional(rollbackOn = Exception.class)
    public TenantPlanBo createMember(TenantPlanDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        TenantPlan plan = BeanUtils.copy(dto, TenantPlan.class);
        plan.setTenantId(user.getTenantId());
        plan.setDelete(IsEnum.FALSE);
        memberManager.saveTenantPlan(plan);
        if (CollectionUtils.isNotEmpty(dto.getClubIds())) {
            if (user.getTenantId() == 0) {
                ClubInfo club = clubManager.fetchClubById(dto.getClubIds().getFirst());
                dto.setTenantId(club.getTenantId());
                plan.setTenantId(club.getTenantId());
                memberManager.saveTenantPlan(plan);
            }
            memberManager.saveTenantClubPlanRel(plan.getId(), plan.getTenantId(), dto.getClubIds());
        }
        if (plan.getTenantId() == 0) {
            throw new BaseException("请选择门店");
        }
        return BeanUtils.copy(plan, TenantPlanBo.class);
    }

    public TenantPlanBo updateMember(TenantPlanDto dto) {
        TenantPlan plan = memberManager.getTenantPlanById(dto.getId());
        plan.setSeq(dto.getSeq());
        plan.setName(dto.getName());
        plan.setTotalAmount(dto.getTotalAmount());
        plan.setPayAmount(dto.getPayAmount());
        plan.setRemark(dto.getRemark());
        plan.setShow(dto.getShow());
        plan.setPaySeq(dto.getPaySeq());
        plan.setStatus(dto.getStatus());
        memberManager.saveTenantPlan(plan);
        if (CollectionUtils.isNotEmpty(dto.getClubIds())) {
            memberManager.saveTenantClubPlanRel(plan.getId(), plan.getTenantId(), dto.getClubIds());
        }
        return BeanUtils.copy(plan, TenantPlanBo.class);
    }

    public void deleteMember(Long id) {
        memberManager.deleteTenantPlan(id);
    }

    public void presentMember(MemberPresentDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        ClientUser clientUser = clientUserManager.fetchUserByPhone(dto.getPhone());
        if (clientUser == null) {
            throw new BaseException("用户不存在");
        }
        List<TenantPlan> tenantPlans = memberManager.fetchTenantPlanByClub(dto.getClubId());
        if (CollectionUtils.isEmpty(tenantPlans)) {
            throw new BaseException("请先设置单店卡");
        }
        TenantPlan plan = tenantPlans.getFirst();
        if (dto.getAmount().equals(BigDecimal.ZERO) && dto.getBonusAmount().equals(BigDecimal.ZERO)) {
            throw new BaseException("充值金额和赠送金额不能同时为0");
        }
        if (user.getTenantId() != 0 || user.getClubId() != 0) {
            if (dto.getAmount().compareTo(dto.getBonusAmount()) < 0) {
                throw new BaseException("充值金额不能小于赠送金额");
            }
        }

        ClientUserTenantMember clubMember = memberManager.getClubMemberByUserIdAndPlanId(clientUser.getId(), plan.getId());
        if (clubMember == null) {
            clubMember = new ClientUserTenantMember();
            clubMember.setUserId(clientUser.getId());
            clubMember.setTenantId(plan.getTenantId());
            clubMember.setPlanId(plan.getId());
            clubMember.setBalance(BigDecimal.ZERO);
            clubMember.setBonus(BigDecimal.ZERO);
            clubMember.setTotalAmount(BigDecimal.ZERO);
            clubMember.setSeq(plan.getPaySeq());
        }
        clubMember.setBalance(clubMember.getBalance().add(dto.getAmount()));
        clubMember.setBonus(clubMember.getBonus().add(dto.getBonusAmount()));
        clubMember.setTotalAmount(clubMember.getTotalAmount().add(dto.getAmount()));
        memberManager.saveClubMember(clubMember);

        PlanGiftRecord record = new PlanGiftRecord();
        record.setTenantId(plan.getTenantId());
        record.setOperatorId(user.getId());
        record.setClubId(dto.getClubId());
        record.setPlanId(plan.getId());
        record.setUserId(clientUser.getId());
        record.setRemark(dto.getRemark());
        record.setAmount(dto.getAmount());
        record.setPhone(dto.getPhone());
        record.setBonusAmount(dto.getBonusAmount());
        memberManager.savePlanGiftRecord(record);
    }

    public Page<TenantPlanGiftRecordBo> giftList(AdminTenantPlanQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        if (user.getClubId() != null && user.getClubId() != 0) {
            dto.setClubId(user.getClubId());
        }
        Page<PlanGiftRecord> list = memberManager.fetchGiftRecordByTenantIdAndClubId(dto);
        List<Long> operatorIds = list.getRecords().stream().map(PlanGiftRecord::getOperatorId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(operatorIds);
        Map<Long, String> operatorMap = adminUsers.stream().collect(Collectors.toMap(BaseEntity::getId, AdminUser::getUsername));
        return list.convert(v -> {
            TenantPlanGiftRecordBo bo = BeanUtils.copy(v, TenantPlanGiftRecordBo.class);
            bo.setGiftTime(v.getCreateAt());
            bo.setUserPhone(v.getPhone());
            bo.setOperator(operatorMap.get(v.getOperatorId()));
            return bo;
        });
    }

    public Page<TenantPlanBillBo> billList(AdminTenantPlanQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        if (user.getClubId() != null && user.getClubId() != 0) {
            dto.setClubId(user.getClubId());
        }
        Page<BillInfo> page = billManager.fetchPageByByTenantIdAndClubIdAndType(dto, dto.getStartDate(), dto.getEndDate(), dto.getTenantId(), dto.getClubId(), BillTypeEnum.TENANT_PLAN);
        List<Long> userIds = page.getRecords().stream().map(BillInfo::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        return page.convert(v -> {
            TenantPlanBillBo bo = new TenantPlanBillBo();
            ClientUser clientUser = userMap.get(v.getUserId());
            TenantPlan plan = JsonUtils.toObject(v.getPayInfo(), TenantPlan.class);
            bo.setCreateTime(v.getCreateAt());
            bo.setBillId(v.getId());
            bo.setPlanName(plan.getName());
            bo.setPayAmount(v.getPayAmount());
            bo.setStatus(v.getStatus());
            bo.setNickname(clientUser.getNickname());
            bo.setUserPhone(clientUser.getPhone());
            return bo;
        });
    }

    public TenantPlanBillStatsBo billStats(ClubDatePeriodDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != null && user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        if (user.getClubId() != null && user.getClubId() != 0) {
            dto.setClubId(user.getClubId());
        }
        TenantPlanBillStatsBo ret = memberManager.getClubMemberByTenantIdAndClubId(dto.getTenantId(), dto.getClubId());
        List<BillInfo> list = billManager.fetchPaidListByByTenantIdAndClubIdAndType(LocalDate.now(), LocalDate.now(), dto.getTenantId(), dto.getClubId(), BillTypeEnum.TENANT_PLAN);
        ret.setToday(list.stream().map(BillInfo::getPayAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        return ret;
    }

}
