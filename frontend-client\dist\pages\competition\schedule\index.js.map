{"version": 3, "file": "pages/competition/schedule/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACrFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/competition/schedule/index.tsx?1721", "webpack://frontend-client/._src_pages_competition_schedule_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport competition from \"@/api/bussiness/competition\";\nimport Back from \"@/components/bussiness/back\";\nimport Title from \"@/components/bussiness/Title\";\nimport { menuRect } from \"@/utils\";\nimport { useRouter } from \"@tarojs/taro\";\nimport { useCallback, useEffect, useState } from \"react\";\nimport dayjs from \"dayjs\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default (function () {\n  var rect = menuRect();\n  var router = useRouter();\n  var id = router.params.id;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    detailData = _useState2[0],\n    setDetailData = _useState2[1];\n  var getCompetition = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n    var _yield$competition$ge, data;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _context.next = 2;\n          return competition.getDetail(id);\n        case 2:\n          _yield$competition$ge = _context.sent;\n          data = _yield$competition$ge.data;\n          setDetailData(data);\n          return _context.abrupt(\"return\", data);\n        case 6:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  })), [setDetailData]);\n  useEffect(function () {\n    getCompetition();\n  }, []);\n  console.log(detailData === null || detailData === void 0 ? void 0 : detailData.schedules);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: \"flex flex-col\",\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u8D5B\\u4E8B\\u65E5\\u7A0B\"\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"flex-1 px-3\",\n      children: detailData && Array.isArray(detailData.schedules) && /*#__PURE__*/_jsx(\"div\", {\n        className: \"flex flex-col gap-3\",\n        children: detailData.schedules.map(function (scheduleItem) {\n          var _scheduleItem$winScor;\n          return /*#__PURE__*/_jsxs(\"div\", {\n            className: \"bg-bgt rounded-lg p-3\",\n            children: [/*#__PURE__*/_jsxs(\"div\", {\n              className: \"items-center gap-4 flex pb-2\",\n              children: [/*#__PURE__*/_jsx(\"div\", {\n                className: \"w-16 text-primary text-sm font-bold\",\n                children: /*#__PURE__*/_jsx(\"div\", {\n                  children: scheduleItem.title\n                })\n              }), /*#__PURE__*/_jsx(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/_jsxs(\"div\", {\n                  className: \"text-sm\",\n                  children: [\"\\u80DC\\u5229\\u5C40\\u6570\\uFF1A\", (_scheduleItem$winScor = scheduleItem.winScore) !== null && _scheduleItem$winScor !== void 0 ? _scheduleItem$winScor : 0, \" \\u5C40\"]\n                })\n              })]\n            }), /*#__PURE__*/_jsxs(\"div\", {\n              className: \"text-xs flex items-center gap-2 text-muted-foreground\",\n              children: [\" \", /*#__PURE__*/_jsx(\"div\", {\n                className: \"i-iconoir-clock w-3.5 h-3.5\"\n              }), [dayjs(scheduleItem.startTime).format(\"YYYY-MM-DD HH:mm\"), dayjs(scheduleItem.endTime).format(\"YYYY-MM-DD HH:mm\")].join(' ~ ')]\n            })]\n          });\n        })\n      })\n    }), /*#__PURE__*/_jsx(_SafeArea, {\n      position: \"bottom\"\n    })]\n  });\n});", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/schedule/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/competition/schedule/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}