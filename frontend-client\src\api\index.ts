import Taro, { addInterceptor, getStorageSync } from "@tarojs/taro";
import { debounce } from "lodash";

const authorizeRedirect = debounce(() => {
  const { router } = Taro.getCurrentInstance();
  const onSuccess = (result) => {
    Taro.setStorageSync("redirectURL", `/${router?.$taroPath}`);
    Taro.redirectTo({
      url: `/pages/login/index?autologin=${result.autoLogin ?? false}&tips=${
        result.tips ?? false
      }`,
    });
  };
  if (Taro.getStorageSync("isLogin")) {
    return onSuccess({ confirm: true, autoLogin: true });
  } else {
    return onSuccess({ confirm: false, tips: true });
  }
}, 800);
const interceptor = function (chain) {
  const requestParams = chain.requestParams;
  const token = getStorageSync("token");
  if (token) {
    chain.requestParams["header"] = {
      ...requestParams.header,
      "X-Token": token,
    };
  }
  return chain
    .proceed(requestParams)
    .then((res) => {
      const { statusCode, data } = res;
      let regex = /^2\d{2}$/;
      if (statusCode === 401) {
        authorizeRedirect();
      } else if (!regex.test(statusCode)) {
        Taro.showToast({
          title: data.message,
          icon: "none",
        });
      }
      return res;
    })
    .catch((error) => {
      console.log("error", error);
    });
};

addInterceptor(interceptor);

export const BASE_URI = "http://*************:18080";
// export const BASE_URI = "https://api.gorillaballclub.cn";
// export const BASE_URI = 'http://localhost:8080';

export const http = async (uri, options) => {
  return await Taro.request({
    url: BASE_URI + uri,
    ...options,
  });
};
