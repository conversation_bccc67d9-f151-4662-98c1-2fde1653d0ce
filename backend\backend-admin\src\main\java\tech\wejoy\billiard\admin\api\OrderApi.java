package tech.wejoy.billiard.admin.api;


import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.admin.bo.OrderStatsBo;
import tech.wejoy.billiard.admin.dto.ClubDatePeriodDto;
import tech.wejoy.billiard.admin.service.AdminOrderService;
import tech.wejoy.billiard.admin.service.DeviceService;
import tech.wejoy.billiard.common.bo.AdminOrderBo;
import tech.wejoy.billiard.common.bo.AdminOrderDetailBo;
import tech.wejoy.billiard.common.bo.OrderFinishResultBo;
import tech.wejoy.billiard.common.dto.AdminOrderQueryDto;
import tech.wejoy.billiard.common.dto.DeviceOperationDto;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.service.OrderService;

@Slf4j
@Path("/order")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "订单")
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class OrderApi {

    private final AdminOrderService adminOrderService;

    private final OrderService orderService;

    private final DeviceService deviceService;

    @GET
    @Path("/list")
    @Operation(summary = "订单列表")
    @Permission({"business_order:read", "business_order:write"})
    public Page<AdminOrderBo> page(AdminOrderQueryDto dto) {
        return adminOrderService.page(dto);
    }

    @GET
    @Path("/stats")
    @Operation(summary = "订单统计")
    @Permission({"business_order:read", "business_order:write"})
    public OrderStatsBo stats(ClubDatePeriodDto dto) {
        return adminOrderService.statsOrder(dto.getTenantId(), dto.getClubId(), dto.getStartDate(), dto.getEndDate());
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "订单详情")
    @Permission({"business_order:read", "business_order:write"})
    public AdminOrderDetailBo detail(@PathParam("id") Long id) {
        return adminOrderService.detail(id);
    }

    @POST
    @Path("/{id}/finish")
    @Operation(summary = "完成订单")
    @Permission({"business_order:write"})
    public OrderFinishResultBo finish(@PathParam("id") Long id) {
        OrderInfo order = orderService.getOrderById(id);
        OrderFinishResultBo finish = orderService.finish(order.getOrderNo());
        // 关闭设备
        DeviceOperationDto dto = new DeviceOperationDto();
        dto.setOrderNo(finish.getOrderNo());
        dto.setType(DeviceOperationTypeEnum.STOP);
        deviceService.handleOrderDevice(dto);
        return finish;
    }

}
