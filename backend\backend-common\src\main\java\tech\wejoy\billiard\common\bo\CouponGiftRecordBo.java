package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.time.LocalDateTime;

@Data
public class CouponGiftRecordBo {

    private Long id;

    @Schema(description = "优惠券名称")
    private String title;

    @Schema(description = "优惠券描述")
    private String description;

    private LocalDateTime giftTime;

    private Integer count;

    private String userPhone;

    private String operator;

    private String remark;

}
