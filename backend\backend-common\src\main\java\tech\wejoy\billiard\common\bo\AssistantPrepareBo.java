package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@Data
public class AssistantPrepareBo {

    @Schema(description = "助教信息")
    private AssistantBo assistant;

    @Schema(description = "时间方案")
    private List<AssistantTimePlanBo> timePlans;

    @Schema(description = "钱包信息")
    private WalletDetailBo wallet;

    @Schema(description = "订单信息")
    private AssistantOrderBo order;

}
