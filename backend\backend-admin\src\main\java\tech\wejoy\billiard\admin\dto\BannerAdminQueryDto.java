package tech.wejoy.billiard.admin.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BannerAdminQueryDto extends PageRequest {

    @QueryParam("type")
    @Separator(",")
    private List<BannerTypeEnum> type;

}
