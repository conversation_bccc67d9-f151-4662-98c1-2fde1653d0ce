package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ClubRechargePlanBo {

    @Schema(description = "id")
    private Long id;

    private String name;

    @Schema(description = "俱乐部名称")
    private List<String> clubNames;

    @Schema(description = "到账金额")
    private BigDecimal totalAmount;

    @Schema(description = "充值金额")
    private BigDecimal payAmount;

    @Schema(description = "备注")
    private String remark;

}
