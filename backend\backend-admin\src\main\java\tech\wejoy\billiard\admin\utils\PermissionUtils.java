package tech.wejoy.billiard.admin.utils;

import com.congeer.security.core.utils.SecurityHolder;
import tech.wejoy.billiard.common.enums.PermissionTypeEnum;

public class PermissionUtils {

    public static PermissionTypeEnum getPermissionType() {
        PermissionTypeEnum type = PermissionTypeEnum.ADMIN;
        if (SecurityHolder.session().getName().equals("business")) {
            type = PermissionTypeEnum.BUSINESS;
        }
        return type;
    }

}
