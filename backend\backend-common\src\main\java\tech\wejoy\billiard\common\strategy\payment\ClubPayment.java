package tech.wejoy.billiard.common.strategy.payment;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.inject.spi.CDI;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.entity.ClientUserTenantMember;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.StartFromEnum;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.MemberManager;
import tech.wejoy.billiard.common.service.ClientUserService;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.strategy.payment.model.ClubPayParams;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class ClubPayment extends Payment {

    ClientUserService clientUserService;

    ClubManager clubManager;

    MemberManager memberManager;

    public ClubPayment() {
        this.clientUserService = CDI.current().select(ClientUserService.class).get();
        this.clubManager = CDI.current().select(ClubManager.class).get();
        this.memberManager = CDI.current().select(MemberManager.class).get();
    }

    @Override
    public PrepayBo prepay(StartTableDto dto, ClubTable table) {
        if (dto.getFrom() == StartFromEnum.QRCODE) {
            dto.setStartTime(LocalDateTime.now());
            dto.setEndTime(calEndTime(dto, table));
        }
        return super.prepay(dto, table);
    }

    private LocalDateTime calEndTime(StartTableDto dto, ClubTable table) {
        List<TimeSlotBo> timeSlots = JsonUtils.toList(table.getTimeSlots(), TimeSlotBo.class);
        LocalDateTime start = dto.getStartTime().withSecond(0);
        ClubInfo club = clubManager.fetchClubById(table.getClubId());
        Integer memberMaxTime = club.getMemberMaxHours();
        if (memberMaxTime == null) {
            memberMaxTime = 10;
        }
        List<ClientUserTenantMember> members = memberManager.getClubMembersByClubIdAndUserId(club.getId(), dto.getUserId());
        BigDecimal totalBalance = members.stream().map(ClientUserTenantMember::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalBonus = members.stream().map(ClientUserTenantMember::getBonus).reduce(BigDecimal.ZERO, BigDecimal::add);
        totalBalance = totalBalance.add(totalBonus);
        LocalDateTime endTime = calEndTime(timeSlots, start, totalBalance, memberMaxTime);
        return endTime.withSecond(0);
    }

    @Override
    public PayResultBo pay(OrderInfo order, Object params) {
        ClubPayParams customParams = null;
        if (params != null) {
            customParams = JsonUtils.toObject(JsonUtils.toJson(params), ClubPayParams.class);
        }
        List<ClientUserTenantMember> clubMembers = clientUserService.getClubMembersByClubIdAndUserId(order.getClubId(), order.getUserId());
        PayResultBo payResultBo = new PayResultBo();
        if (CollectionUtils.isEmpty(clubMembers)) {
            payResultBo.setSuccess(false);
            payResultBo.setNeedPay(false);
            payResultBo.setMessage("用户不是该店会员");
            return payResultBo;
        }
        BigDecimal total = clubMembers.stream().map(ClientUserTenantMember::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        total = total.add(clubMembers.stream().map(ClientUserTenantMember::getBonus).reduce(BigDecimal.ZERO, BigDecimal::add));
        if (total.compareTo(order.getTotalAmount()) < 0) {
            payResultBo.setSuccess(false);
            payResultBo.setNeedPay(false);
            payResultBo.setMessage("余额不足");
            return payResultBo;
        }
        List<ClubPaymentRecordBo> records = new ArrayList<>();
        if (customParams == null) {
            BigDecimal payAmount = order.getTotalAmount();
            for (ClientUserTenantMember clubMember : clubMembers) {
                ClubPaymentRecordBo record = new ClubPaymentRecordBo();
                record.setId(clubMember.getId());
                record.setSeq(clubMember.getSeq());
                if (clubMember.getBalance().compareTo(payAmount) >= 0) {
                    clubMember.setBalance(clubMember.getBalance().subtract(payAmount));
                    clientUserService.updateClubMember(clubMember);
                    payResultBo.setAmount(payAmount);
                    payResultBo.setSuccess(true);
                    record.setBalance(payAmount);
                    records.add(record);
                    break;
                } else {
                    payAmount = payAmount.subtract(clubMember.getBalance());
                    record.setBalance(clubMember.getBalance());
                    clubMember.setBalance(BigDecimal.ZERO);
                }
                if (clubMember.getBonus().compareTo(payAmount) >= 0) {
                    clubMember.setBonus(clubMember.getBonus().subtract(payAmount));
                    clientUserService.updateClubMember(clubMember);
                    payResultBo.setAmount(payAmount);
                    payResultBo.setSuccess(true);
                    record.setBonus(payAmount);
                    records.add(record);
                    break;
                } else {
                    payAmount = payAmount.subtract(clubMember.getBonus());
                    record.setBonus(clubMember.getBonus());
                    clubMember.setBonus(BigDecimal.ZERO);
                }
                records.add(record);
                clientUserService.updateClubMember(clubMember);
            }
            order.setPayAmount(BigDecimal.ZERO);
            order.setRealAmount(order.getTotalAmount());
        }
        order.setDescription(JsonUtils.toJson(records));
        return payResultBo;
    }

    @Override
    public RefundResultBo refund(OrderInfo order, BigDecimal refundAmount) {
        RefundResultBo ret = new RefundResultBo();
        BigDecimal sourceRefundAmount = refundAmount;
        List<ClubPaymentRecordBo> records = JsonUtils.toList(order.getDescription(), ClubPaymentRecordBo.class);
        if (CollectionUtils.isEmpty(records)) {
            ret.setSuccess(false);
            ret.setMessage("无支付记录");
            return ret;
        }
        records.sort((o1, o2) -> o2.getSeq() - o1.getSeq());
        for (ClubPaymentRecordBo record : records) {
            ClientUserTenantMember clubMember = clientUserService.getClubMemberById(record.getId());
            if (record.getBonus().compareTo(refundAmount) >= 0) {
                clubMember.setBonus(clubMember.getBonus().add(refundAmount));
                clientUserService.updateClubMember(clubMember);
                break;
            } else {
                refundAmount = refundAmount.subtract(record.getBonus());
                clubMember.setBonus(clubMember.getBonus().add(record.getBonus()));
            }
            if (record.getBalance().compareTo(refundAmount) >= 0) {
                clubMember.setBalance(clubMember.getBalance().add(refundAmount));
                clientUserService.updateClubMember(clubMember);
                break;
            } else {
                refundAmount = refundAmount.subtract(record.getBalance());
                clubMember.setBalance(clubMember.getBalance().add(record.getBalance()));
            }
            clientUserService.updateClubMember(clubMember);
        }
        ret.setSuccess(true);
        ret.setAmount(sourceRefundAmount);
        return ret;
    }

    public OrderProfitBo profit(OrderInfo order, ClubInfo club) {
        OrderProfitBo profit = super.profit(order, club);
        profit.setServiceFee(BigDecimal.ZERO);
        profit.setProfitAmount(BigDecimal.ZERO);
        return profit;
    }

}
