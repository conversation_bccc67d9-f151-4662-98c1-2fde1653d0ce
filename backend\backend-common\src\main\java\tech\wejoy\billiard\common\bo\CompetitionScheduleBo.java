package tech.wejoy.billiard.common.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.CompetitionScheduleStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDateTime;

@Data
public class CompetitionScheduleBo {

    private Long id;

    private Long competitionId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String title;

    private Integer round;

    private Integer playerCount;

    private CompetitionScheduleStatusEnum status;

    private Integer winScore;

    private IsEnum loserGroup;

}
