package tech.wejoy.billiard.admin.third;

import jakarta.enterprise.inject.spi.CDI;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.admin.third.aqara.AqaraDeviceHandler;
import tech.wejoy.billiard.admin.third.smyoo.SmyooDeviceHandler;
import tech.wejoy.billiard.common.entity.TenantDevice;
import tech.wejoy.billiard.common.enums.DeviceBrandEnum;
import tech.wejoy.billiard.common.enums.DeviceTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.manager.DeviceManager;

@Slf4j
public abstract class DeviceHandler {

    protected DeviceManager deviceManager;

    protected DeviceHandler() {
        this.deviceManager = CDI.current().select(DeviceManager.class).get();
    }

    public static DeviceHandler getHandler(DeviceBrandEnum brand, DeviceTypeEnum type) {
        return switch (brand) {
            case NONE -> null;
            case AQARA -> AqaraDeviceHandler.getHandler(type);
            case SMYOO -> SmyooDeviceHandler.getHandler(type);
        };
    }

    public void start(TenantDevice device) {
        int max = 3;
        boolean flag = false;
        while (max-- > 0) {
            flag = doStart(device);
            if (flag) {
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("start error : {}", e.getMessage(), e);
            }
        }
        if (!flag) {
            device.setOpen(IsEnum.TRUE);
            deviceManager.saveDevice(device);
        } else {
            // TODO 通知
        }
    }

    public void stop(TenantDevice device) {
        int max = 3;
        boolean flag = false;
        while (max-- > 0) {
            flag = doStop(device);
            if (flag) {
                break;
            }
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("stop error : {}", e.getMessage(), e);
            }
        }
        if (!flag) {
            device.setOpen(IsEnum.TRUE);
            deviceManager.saveDevice(device);
        } else {
            // TODO 通知
        }
    }

    public abstract boolean doStart(TenantDevice device);

    public abstract boolean doStop(TenantDevice device);

    public void warn(TenantDevice device) {
        int max = 3;
        while (!doWarn(device) && max-- > 0) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("warn error : {}", e.getMessage(), e);
            }
        }
    }

    protected abstract boolean doWarn(TenantDevice device);

}
