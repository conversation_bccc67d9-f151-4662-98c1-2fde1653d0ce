package tech.wejoy.billiard.api;

import com.congeer.core.bean.Result;
import com.congeer.utils.JsonUtils;
import jakarta.enterprise.inject.spi.CDI;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.dto.WejoyPayNotifyDto;
import tech.wejoy.billiard.entity.WejoyConfig;
import tech.wejoy.billiard.manager.ConfigManager;
import tech.wejoy.billiard.service.BillService;
import tech.wejoy.billiard.third.wejoy.request.TradeStatusEnum;
import tech.wejoy.billiard.third.wejoy.utils.RSAUtils;

import java.math.BigDecimal;

/**
 * API for handling callbacks from third-party services
 */
@Path("/callback")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
@Slf4j
public class CallbackApi {

    private final BillService billService;

    /**
     * WeJoy payment notification callback endpoint
     *
     * @param body The raw request body
     * @param timestamp The X-Timestamp header
     * @param signature The X-Signature header
     * @param merchantId The X-Merchant-Id header
     * @return A result indicating success or failure
     */
    @POST
    @Path("/wejoy/pay")
    public Result<?> wejoyPayCallback(
            String body,
            @HeaderParam("X-Timestamp") String timestamp,
            @HeaderParam("X-Signature") String signature,
            @HeaderParam("X-Merchant-Id") String merchantId) {

        log.info("Received WeJoy payment notification: {}", body);
        log.info("X-Timestamp: {}", timestamp);
        log.info("X-Signature: {}", signature);
        log.info("X-Merchant-Id: {}", merchantId);

        // Verify the notification signature
        try {
            // Get the WeJoy configuration
            ConfigManager configManager = CDI.current().select(ConfigManager.class).get();
            WejoyConfig wejoyConfig = configManager.getWejoy("default");
            if (wejoyConfig == null) {
                log.error("WeJoy configuration not found");
                return Result.error("500", "WeJoy configuration not found");
            }

            // Verify the signature
            // The signature content is: URI + "POST" + timestamp + body
            String uri = "/callback/wejoy/pay";
            String signContent = uri + "POST" + timestamp + body;
            boolean verified = RSAUtils.verify(signContent, signature, wejoyConfig.getPublicKey());

            if (!verified) {
                log.error("Invalid signature for WeJoy payment notification");
                return Result.unAuth();
            }

            // Parse the notification body
            WejoyPayNotifyDto dto = JsonUtils.toObject(body, WejoyPayNotifyDto.class);

            // Process the payment notification
            if (dto.getStatus() == TradeStatusEnum.SUCCESS) {
                // Payment success
                BigDecimal total = BigDecimal.valueOf(dto.getAmount()).divide(BigDecimal.valueOf(100));
                billService.successBill(
                        dto.getRequestId(),  // Use requestId as the outer transaction ID
                        dto.getTradeNo(),    // Use tradeNo as the bill number
                        total,               // Total amount
                        total,               // Payer total amount
                        dto.getPayTime(),    // Payment time
                        body                 // Result info as raw JSON
                );
            } else if (dto.getRefundStatus() != null && !dto.getRefundStatus().isEmpty()) {
                // Refund notification
                BigDecimal refundAmount = BigDecimal.valueOf(dto.getRefundAmount()).divide(BigDecimal.valueOf(100));
                billService.refundBill(
                        dto.getTradeNo(),     // Bill number
                        refundAmount,         // Refund amount
                        refundAmount,         // Payer refund amount
                        body                  // Result info as raw JSON
                );
            }

            return Result.success();
        } catch (Exception e) {
            log.error("Error processing WeJoy payment notification", e);
            return Result.error("500", "Error processing notification: " + e.getMessage());
        }
    }
}
