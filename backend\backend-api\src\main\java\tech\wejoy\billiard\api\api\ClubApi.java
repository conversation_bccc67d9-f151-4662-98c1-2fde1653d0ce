package tech.wejoy.billiard.api.api;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.api.dto.StartPrepareDto;
import tech.wejoy.billiard.api.queue.ClubDeviceSend;
import tech.wejoy.billiard.api.service.ApiClubService;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.ApiClubQueryDto;
import tech.wejoy.billiard.common.dto.ApiClubRechargeQueryDto;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.dto.StartTableTimeDto;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.StartResultEnum;

import java.time.LocalDateTime;
import java.util.List;

@Path("/club")
@Tag(name = "门店")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class ClubApi {

    private final ApiClubService clubApiService;

    private final ClubDeviceSend clubDeviceSend;

    @GET
    @Path("/list")
    @Operation(summary = "分页列表")
    public Page<ApiClubBo> page(@BeanParam ApiClubQueryDto dto) {
        return clubApiService.listWithDistance(dto);
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "详情")
    public ApiClubBo detail(@PathParam("id") Long id) {
        return clubApiService.detail(id);
    }

    @GET
    @Path("/{id}/tables/status")
    @Operation(summary = "桌位状态")
    public List<TableStatusBo> tablesStatus(@PathParam("id") Long id) {
        return clubApiService.tablesStatus(id);
    }

    @POST
    @Path("/table/prepare")
    @Operation(summary = "开桌准备信息")
    @Authorized
    public StartPrepareBo startPrepare(StartPrepareDto dto) {
        return clubApiService.startPrepare(dto);
    }

    @POST
    @Path("/table/start")
    @Operation(summary = "开始使用桌台")
    @Authorized
    public StartResultBo start(StartTableDto dto) {
        if (dto.getPayType() == null || dto.getPayType() == OrderPayTypeEnum.NONE) {
            throw new BaseException("请选择支付方式");
        }
        if (dto.getTableId() == null) {
            throw new BaseException("请选择桌台");
        }
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        StartResultBo start = clubApiService.start(dto);
        if (start.getResult() == StartResultEnum.STARTING && LocalDateTime.now().plusSeconds(30).isAfter(start.getStartTime())) {
            clubDeviceSend.send(start.getOrderNo(), DeviceOperationTypeEnum.START);
        }
        return start;
    }

    @GET
    @Path("/table/start/status")
    @Operation(summary = "开始状态")
    @Authorized
    public StartResultBo startStatus(@QueryParam("orderNo") String orderNo) {
        return clubApiService.startStatus(orderNo);
    }

    @POST
    @Path("/table/time/check")
    @Operation(summary = "检查时间")
    public boolean checkTime(StartTableTimeDto dto) {
        return clubApiService.checkTime(dto);
    }

    @POST
    @Path("/table/price/cal")
    @Operation(summary = "计算价格")
    public PriceCalBo calPrice(StartTableTimeDto dto) {
        return clubApiService.calPrice(dto);
    }

    @GET
    @Path("/districts")
    @Operation(summary = "开放区域")
    public List<DistrictBo> openDistricts() {
        return clubApiService.openDistricts();
    }

    @GET
    @Path("/{id}/recharge/plans")
    @Operation(summary = "充值方案")
    public List<ClubRechargePlanBo> rechargePlans(@PathParam("id") Long id) {
        return clubApiService.rechargePlans(id);
    }

    @GET
    @Path("/recharge/list")
    @Operation(summary = "可充值门店列表")
    public List<ApiClubBo> rechargeClubList(@BeanParam ApiClubRechargeQueryDto dto) {
        return clubApiService.rechargeClubList(dto);
    }

    @GET
    @Path("/options")
    @Operation(summary = "门店选项")
    public List<ApiClubBo> options(@BeanParam ApiClubRechargeQueryDto dto) {
        return clubApiService.options(dto);
    }

}
