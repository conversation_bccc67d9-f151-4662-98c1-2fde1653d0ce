package tech.wejoy.billiard.common.strategy.payment;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.inject.spi.CDI;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.*;
import tech.wejoy.billiard.common.manager.BillManager;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.service.CouponService;
import tech.wejoy.billiard.common.service.PaymentService;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.strategy.payment.model.CouponPayParams;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class CouponPayment extends Payment {

    private CouponService couponService;

    private BillManager billManager;

    private ClientUserManager clientUserManager;

    private PaymentService paymentService;

    public CouponPayment() {
        this.couponService = CDI.current().select(CouponService.class).get();
        this.billManager = CDI.current().select(BillManager.class).get();
        this.clientUserManager = CDI.current().select(ClientUserManager.class).get();
        this.paymentService = CDI.current().select(PaymentService.class).get();
    }

    @Override
    public PrepayBo prepay(StartTableDto dto, ClubTable table) {
        CouponPayParams params = JsonUtils.toObject(JsonUtils.toJson(dto.getExtra()), CouponPayParams.class);
        if (params == null) {
            PrepayBo ret = new PrepayBo();
            ret.setSuccess(false);
            ret.setMessage("参数错误");
            return ret;
        }
        Long couponId = null;
        if (params.getUserCouponId() != null) {
            ClientUserCoupon coupon = couponService.getUserCouponById(params.getUserCouponId());
            if (coupon == null) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券不存在");
                return ret;
            }
            couponId = coupon.getCouponId();
            if (coupon.getRefund() == IsEnum.TRUE) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券已退款");
                return ret;
            }
            if (coupon.getExpired() == IsEnum.TRUE || coupon.getExpireTime().isBefore(LocalDateTime.now())) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券已过期");
                return ret;
            }
        } else if (params.getCouponId() != null) {
            couponId = params.getCouponId();
        }
        TenantCoupon source = couponService.getCouponById(couponId);
        if (source == null) {
            PrepayBo ret = new PrepayBo();
            ret.setSuccess(false);
            ret.setMessage("未找到券对应信息");
            return ret;
        }
        if (dto.getFrom() == StartFromEnum.QRCODE) {
            dto.setStartTime(LocalDateTime.now());
        } else if (dto.getStartTime() == null) {
            dto.setStartTime(LocalDateTime.now().plusMinutes(5));
        }
        TimeBo period = null;
        if (StringUtils.isNotBlank(source.getPeriod())) {
            period = JsonUtils.toObject(source.getPeriod(), TimeBo.class);
        }
        if (period != null) {
            int value = period.getDate(dto.getStartTime()).getDayOfWeek().getValue();
            if (CollectionUtils.isNotEmpty(period.getDays()) && !period.getDays().contains(value)) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券不在使用时间内");
                return ret;
            }
            if (dto.getStartTime() == null || dto.getStartTime().isBefore(period.getStart(dto.getStartTime()))) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券不在使用时间内");
                return ret;
            }
            if (dto.getStartTime().isAfter(period.getEnd(dto.getStartTime()))) {
                PrepayBo ret = new PrepayBo();
                ret.setSuccess(false);
                ret.setMessage("券不在使用时间内");
                return ret;
            }
        }
        if (source.getType() == CouponTypeEnum.HOURS) {
            dto.setEndTime(dto.getStartTime().plusMinutes(source.getMinutes()));
        } else if (source.getType() == CouponTypeEnum.PERIOD && period != null) {
            LocalDateTime endTime = dto.getStartTime().plusMinutes(source.getMinutes());
            LocalDateTime end = period.getEnd(dto.getStartTime());
            if (endTime.isAfter(end)) {
                endTime = end;
            }
            dto.setEndTime(endTime);
        }
        return super.prepay(dto, table);
    }

    protected BillInfo createWxOrder(OrderInfo order, TenantCoupon coupon) {
        BillInfo bill = new BillInfo();
        bill.setUserId(order.getUserId());
        bill.setClubId(order.getClubId());
        bill.setTenantId(order.getTenantId());
        bill.setType(BillTypeEnum.COUPON);
        bill.setAmount(coupon.getPrice());
        bill.setOrderNo(order.getOrderNo());
        bill.setPayInfo(JsonUtils.toJson(coupon));
        bill.setThirdPayType(ThirdPayTypeEnum.WECHAT);
        billManager.createBill(bill);
        return bill;
    }

    @Override
    public PayResultBo pay(OrderInfo order, Object extra) {
        CouponPayParams params = JsonUtils.toObject(JsonUtils.toJson(extra), CouponPayParams.class);
        PayResultBo ret = new PayResultBo();
        if (params.getCouponId() != null) {
            TenantCoupon coupon = couponService.getCouponById(params.getCouponId());
            Integer userTotalLimit = coupon.getUserTotalLimit();
            Integer userDayLimit = coupon.getUserDayLimit();
            if (userTotalLimit != null && userTotalLimit > 0) {
                List<ClientUserCoupon> userCoupons = clientUserManager.fetchCouponByIdAndUserId(coupon.getId(), order.getUserId());
                if (userCoupons.size() >= userTotalLimit) {
                    ret.setNeedPay(false);
                    ret.setSuccess(false);
                    ret.setMessage("已经超过购买次数");
                    return ret;
                }
            } else if (userDayLimit != null && userDayLimit > 0) {
                List<ClientUserCoupon> userCoupons = clientUserManager.fetchCouponByIdAndUserId(coupon.getId(), order.getUserId());
                long count = userCoupons.stream().filter(c -> c.getCreateAt().toLocalDate().isEqual(LocalDateTime.now().toLocalDate())).count();
                if (count >= userDayLimit) {
                    ret.setNeedPay(false);
                    ret.setSuccess(false);
                    ret.setMessage("已经超过每日购买次数，请明日再来");
                    return ret;
                }
            }
            BillInfo bill = createWxOrder(order, coupon);
            PayParamBo payParamBo = paymentService.createPayment(bill.getBillNo(), coupon.getPrice(), order.getUserId(), coupon.getTitle());
            ret.setSuccess(false);
            ret.setAmount(coupon.getPrice());
            ret.setNeedPay(true);
            ret.setExtra(payParamBo);
            order.setDescription(JsonUtils.toJson(coupon));
            order.setRealAmount(coupon.getPrice());
            order.setPayAmount(coupon.getPrice());
            return ret;
        }
        ClientUserCoupon coupon = couponService.getUserCouponById(params.getUserCouponId());
        if (coupon == null) {
            ret.setNeedPay(false);
            ret.setSuccess(false);
            ret.setMessage("券不存在");
            return ret;
        }
        TenantCoupon source = couponService.getCouponById(coupon.getCouponId());
        if (source == null) {
            ret.setSuccess(false);
            ret.setMessage("未找到券对应信息");
            return ret;
        }
        couponService.useCoupon(coupon.getId());
        order.setDescription(JsonUtils.toJson(coupon));
        if (coupon.getGift() == IsEnum.TRUE) {
            order.setPayAmount(BigDecimal.ZERO);
            order.setRealAmount(BigDecimal.ZERO);
        } else {
            order.setPayAmount(source.getPrice());
            order.setRealAmount(source.getPrice());
        }
        ret.setSuccess(true);
        return ret;
    }

    @Override
    public RefundResultBo refund(OrderInfo order, BigDecimal refundAmount) {
        // 不做退款
        RefundResultBo ret = new RefundResultBo();
        ret.setSuccess(true);
        ret.setAmount(BigDecimal.ZERO);
        return ret;
    }

    @Override
    public RefundResultBo refund(AssistantOrder order, BigDecimal refundAmount) {
        return null;
    }

    @Override
    public RefundResultBo cancel(OrderInfo order, BigDecimal refundAmount) {
        couponService.reverseCoupon(order);
        RefundResultBo ret = new RefundResultBo();
        ret.setSuccess(true);
        ret.setAmount(order.getRealAmount());
        return ret;
    }

}
