package tech.wejoy.billiard.admin.task;

import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.entity.AssistantOrder;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.manager.AssistantManager;
import tech.wejoy.billiard.common.service.AssistantService;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

@ApplicationScoped
@RequiredArgsConstructor
public class AssistantTask {

    private final AssistantService assistantService;

    private final AssistantManager assistantManager;

    @Scheduled(cron = "0 * * * * ?")
    void finishOrder() {
        List<AssistantOrder> orders = assistantManager.fetchOrderByStatus(OrderStatusEnum.USING);
        for (AssistantOrder order : orders) {
            long until = order.getStartTime().until(order.getEndTime(), ChronoUnit.MINUTES);
            if (LocalDateTime.now().isAfter(order.getRealStartTime().plusMinutes(until))) {
                assistantService.finishOrder(order.getOrderNo());
            }
        }
    }

    @Scheduled(cron = "10 * * * * ?")
    void cancelOrder() {
        List<AssistantOrder> orders = assistantManager.fetchOrderByStatus(OrderStatusEnum.PENDING);
        for (AssistantOrder order : orders) {
            if (order.getCreateAt().plusMinutes(5).isBefore(LocalDateTime.now())) {
                assistantService.cancelOrder(order.getOrderNo());
            }
        }
    }

}
