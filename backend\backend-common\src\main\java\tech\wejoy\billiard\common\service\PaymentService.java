package tech.wejoy.billiard.common.service;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.DateUtils;
import com.congeer.utils.JsonUtils;
import com.github.binarywang.wxpay.bean.request.WxPayPartnerRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.bo.PayParamBo;
import tech.wejoy.billiard.common.bo.WechatPayParamBo;
import tech.wejoy.billiard.common.dto.RefundDto;
import tech.wejoy.billiard.common.entity.ClientUser;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;
import tech.wejoy.billiard.common.third.wejoy.client.WejoyClient;
import tech.wejoy.billiard.common.third.wejoy.request.CreateTradeDto;
import tech.wejoy.billiard.common.third.wejoy.request.PayTypeEnum;
import tech.wejoy.billiard.common.third.wejoy.response.TradeCreateBo;
import tech.wejoy.billiard.common.third.wejoy.response.WejoyTradeBo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class PaymentService {

    private final ClientUserManager clientUserManager;

    /**
     * Create a payment with WeJoy, fallback to WeChat if WeJoy fails
     *
     * @param billNo      The bill number
     * @param amount      The amount to pay
     * @param userId      The user ID
     * @param description The payment description
     * @return The payment parameters
     */
    public PayParamBo createPayment(String billNo, BigDecimal amount, Long userId, String description) {
        ClientUser user = clientUserManager.fetchUserById(userId);
        if (user == null) {
            throw new BaseException("用户不存在");
        }

        try {
            // Try WeJoy first
            return createWejoyPayment(billNo, amount, user.getOpenId(), description);
        } catch (Exception e) {
            log.error("WeJoy payment failed, fallback to WeChat: {}", e.getMessage());
            // Fallback to WeChat
            return createWechatPayment(billNo, amount, user.getOpenId(), description);
        }
    }

    /**
     * Create a payment with WeJoy
     *
     * @param billNo      The bill number
     * @param amount      The amount to pay
     * @param openId      The user's OpenID
     * @param description The payment description
     * @return The payment parameters
     */
    private PayParamBo createWejoyPayment(String billNo, BigDecimal amount, String openId, String description) {
        WejoyClient client = ThirdServiceHolder.wejoyClient("client");
        CreateTradeDto request = new CreateTradeDto();
        request.setAmount(amount.multiply(BigDecimal.valueOf(100)).intValue());
        request.setPayer(openId);
        request.setExpireAt(System.currentTimeMillis() + 15 * 60 * 1000);
        request.setRequestId(UUID.randomUUID().toString());
        request.setDescription(description);
        request.setTradeType(PayTypeEnum.WX_MINI_PROGRAM);
        request.setTradeNo(billNo);
        request.setAppId(client.getAppId());
        TradeCreateBo trade = client.createTrade(request);
        return WechatPayParamBo.fromMap((Map<String, String>) trade.getParams());
    }

    /**
     * Create a payment with WeChat
     *
     * @param billNo      The bill number
     * @param amount      The amount to pay
     * @param openId      The user's OpenID
     * @param description The payment description
     * @return The payment parameters
     */
    private PayParamBo createWechatPayment(String billNo, BigDecimal amount, String openId, String description) {
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setOutTradeNo(billNo);
        request.setDescription(description);
        LocalDateTime endTime = LocalDateTime.now().plusMinutes(15);
        request.setTimeExpire(DateUtils.getDateTimeString(endTime, "yyyy-MM-dd'T'HH:mm:ss+08:00"));
        request.setAmount(new WxPayUnifiedOrderV3Request.Amount().setTotal(amount.multiply(BigDecimal.valueOf(100)).intValue()));
        request.setPayer(new WxPayUnifiedOrderV3Request.Payer().setOpenid(openId));
        return ThirdServiceHolder.wechatPay(request);
    }

    /**
     * Process a refund with WeJoy, fallback to WeChat if WeJoy fails
     *
     * @param billNo       The bill number
     * @param refundAmount The amount to refund
     * @param totalAmount  The total amount of the original payment
     * @param reason       The refund reason
     * @return The refund result as JSON string
     */
    public String refund(String billNo, BigDecimal refundAmount, BigDecimal totalAmount, String reason) {
        try {
            // Try WeJoy first
            return refundWithWejoy(billNo, refundAmount, reason);
        } catch (Exception e) {
            log.error("WeJoy refund failed, fallback to WeChat: {}", e.getMessage());
            // Fallback to WeChat
            return refundWithWechat(billNo, refundAmount, totalAmount);
        }
    }

    /**
     * Process a refund with WeJoy by trade number
     *
     * @param tradeNo      The trade number
     * @param refundAmount The amount to refund
     * @param reason       The refund reason
     * @return The refund result as JSON string
     */
    public String refundWithWejoy(String tradeNo, BigDecimal refundAmount, String reason) {
        try {
            WejoyClient client = ThirdServiceHolder.wejoyClient("client");
            RefundDto request = new RefundDto();
            request.setRefundAmount(refundAmount.multiply(BigDecimal.valueOf(100)).intValue());
            request.setRefundReason(reason);
            WejoyTradeBo result = client.refundTradeByNo(tradeNo, request);
            return JsonUtils.toJson(result);
        } catch (Exception e) {
            log.error("WeJoy refund by trade number failed: {}", e.getMessage());
            throw new BaseException("退款失败: " + e.getMessage());
        }
    }

    /**
     * Process a refund with WeChat
     *
     * @param billNo       The bill number
     * @param refundAmount The amount to refund
     * @param totalAmount  The total amount of the original payment
     * @return The refund result as JSON string
     */
    private String refundWithWechat(String billNo, BigDecimal refundAmount, BigDecimal totalAmount) {
        WxPayPartnerRefundV3Request request = new WxPayPartnerRefundV3Request();
        request.setOutRefundNo(billNo + "1");
        request.setOutTradeNo(billNo);
        WxPayRefundV3Request.Amount amount = new WxPayPartnerRefundV3Request.Amount();
        amount.setRefund(refundAmount.multiply(BigDecimal.valueOf(100)).intValue());
        amount.setTotal(totalAmount.multiply(BigDecimal.valueOf(100)).intValue());
        amount.setCurrency("CNY");
        request.setAmount(amount);
        WxPayRefundV3Result result = ThirdServiceHolder.wechatRefund(request);
        if (result == null) {
            throw new BaseException("退款失败");
        }
        return JsonUtils.toJson(result);
    }
}
