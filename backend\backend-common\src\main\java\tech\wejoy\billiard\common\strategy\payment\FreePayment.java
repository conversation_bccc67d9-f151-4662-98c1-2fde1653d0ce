package tech.wejoy.billiard.common.strategy.payment;

import jakarta.enterprise.inject.spi.CDI;
import tech.wejoy.billiard.common.bo.PayResultBo;
import tech.wejoy.billiard.common.bo.PrepayBo;
import tech.wejoy.billiard.common.bo.RefundResultBo;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.MemberManager;
import tech.wejoy.billiard.common.service.ClientUserService;
import tech.wejoy.billiard.common.strategy.Payment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class FreePayment extends Payment {

    ClientUserService clientUserService;

    ClubManager clubManager;

    MemberManager memberManager;

    public FreePayment() {
        this.clientUserService = CDI.current().select(ClientUserService.class).get();
        this.clubManager = CDI.current().select(ClubManager.class).get();
        this.memberManager = CDI.current().select(MemberManager.class).get();
    }

    @Override
    public PrepayBo prepay(StartTableDto dto, ClubTable table) {
        if (dto.getStartTime() == null) {
            dto.setStartTime(LocalDateTime.now());
        }
        return super.prepay(dto, table);
    }

    @Override
    public PayResultBo pay(OrderInfo order, Object params) {
        PayResultBo ret = new PayResultBo();
        ret.setSuccess(true);
        order.setPayAmount(BigDecimal.ZERO);
        order.setRealAmount(BigDecimal.ZERO);
        ret.setAmount(BigDecimal.ZERO);
        return ret;
    }

    @Override
    public RefundResultBo refund(OrderInfo order, BigDecimal refundAmount) {
        RefundResultBo ret = new RefundResultBo();
        ret.setSuccess(true);
        ret.setAmount(BigDecimal.ZERO);
        return ret;
    }

}
