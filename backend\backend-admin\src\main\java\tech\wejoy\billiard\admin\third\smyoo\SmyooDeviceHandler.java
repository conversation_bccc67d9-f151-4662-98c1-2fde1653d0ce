package tech.wejoy.billiard.admin.third.smyoo;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.admin.third.DeviceHandler;
import tech.wejoy.billiard.common.entity.TenantDevice;
import tech.wejoy.billiard.common.entity.TenantSmyooAccount;
import tech.wejoy.billiard.common.enums.AirConditionerOpTypeEnum;
import tech.wejoy.billiard.common.enums.DeviceTypeEnum;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;
import tech.wejoy.billiard.common.third.smyoo.client.SmyooClient;
import tech.wejoy.billiard.common.third.smyoo.request.DeviceRequest;
import tech.wejoy.billiard.common.third.smyoo.request.DeviceSetRequest;
import tech.wejoy.billiard.common.third.smyoo.response.GetIrDeviceResponse;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public abstract class SmyooDeviceHandler extends DeviceHandler {

    private final static Map<DeviceTypeEnum, DeviceHandler> handlerMap = new ConcurrentHashMap<>();

    protected SmyooClient client;

    protected SmyooDeviceHandler() {
        this.client = ThirdServiceHolder.smyooClient("base");
    }

    public static DeviceHandler getHandler(DeviceTypeEnum type) {
        if (Objects.requireNonNull(type) == DeviceTypeEnum.AIR_CONDITIONER) {
            return handlerMap.computeIfAbsent(DeviceTypeEnum.AIR_CONDITIONER, k -> new SmyooAirConditionerDeviceHandler());
        }
        return null;
    }

    protected boolean operator(TenantDevice device, AirConditionerOpTypeEnum type, int value) {
        String deviceId = device.getDeviceId();
        TenantSmyooAccount account = deviceManager.getSmyooAccount(device.getAccountId());
        if (account == null) {
            throw new BaseException("账号未找到");
        }
        try {
            DeviceRequest get = new DeviceRequest();
            get.setMcuId(deviceId);
            log.info("query:{}", JsonUtils.toJson(get));
            GetIrDeviceResponse resp = client.irDeviceGetData(account, get);
            if (resp == null) {
                throw new BaseException("设备未找到");
            }
            Map<String, Object> datapoint = resp.getDatapoint();
            if (datapoint == null) {
                throw new BaseException("设备未找到");
            }
            device.setDescription(JsonUtils.toJson(datapoint));
            deviceManager.saveDevice(device);
            switch (type) {
                case POWER:
                    if (datapoint.get("status") != null && (int) datapoint.get("status") == value) {
                        return true;
                    }
                    datapoint.put("status", value);
                    datapoint.put("key", 0);
                    break;
                case MODE:
                    if (datapoint.get("mode") != null && (int) datapoint.get("mode") == value) {
                        return true;
                    }
                    datapoint.put("mode", value);
                    break;
                case TEMP:
                    if (datapoint.get("temp") != null && (int) datapoint.get("temp") == value) {
                        return true;
                    }
                    datapoint.put("temp", value);
                    break;
                case SPEED:
                    if (datapoint.get("speed") != null && (int) datapoint.get("speed") == value) {
                        return true;
                    }
                    datapoint.put("speed", value);
                    break;
                case DIRECTION:
                    if (datapoint.get("dir") != null && (int) datapoint.get("dir") == value) {
                        return true;
                    }
                    datapoint.put("dir", value);
                    break;
                default:
                    throw new BaseException("操作类型错误");
            }
            DeviceSetRequest set = new DeviceSetRequest();
            set.setMcuId(deviceId);
            set.setDatapoint(JsonUtils.toJson(datapoint));
            log.info("operator : {}", JsonUtils.toJson(set));
            client.irDeviceSetData(account, set);
            device.setDescription(JsonUtils.toJson(datapoint));
            deviceManager.saveDevice(device);
            return true;
        } catch (Exception e) {
            log.error("operator error : {}", e.getMessage(), e);
            return false;
        }
    }

}
