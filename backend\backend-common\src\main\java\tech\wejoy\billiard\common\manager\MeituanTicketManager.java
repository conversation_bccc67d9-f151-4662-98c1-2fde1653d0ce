package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.google.common.collect.Lists;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.dto.DealQueryDto;
import tech.wejoy.billiard.common.dto.ShopQueryDto;
import tech.wejoy.billiard.common.entity.MeituanAccount;
import tech.wejoy.billiard.common.entity.MeituanDeal;
import tech.wejoy.billiard.common.entity.MeituanShop;
import tech.wejoy.billiard.common.entity.MeituanTicket;
import tech.wejoy.billiard.common.repo.MeituanAccountRepo;
import tech.wejoy.billiard.common.repo.MeituanDealRepo;
import tech.wejoy.billiard.common.repo.MeituanShopRepo;
import tech.wejoy.billiard.common.repo.MeituanTicketRepo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class MeituanTicketManager {

    private final MeituanAccountRepo meituanAccountRepo;

    private final MeituanTicketRepo meituanTicketRepo;

    private final MeituanDealRepo meituanDealRepo;

    private final MeituanShopRepo meituanShopRepo;

    public MeituanShop fetchMeituanShopByClubId(Long clubId) {
        return meituanShopRepo.find("clubId", clubId).firstResult();
    }

    public List<MeituanShop> fetchMeituanShopsByClubId(Long clubId) {
        return meituanShopRepo.find("clubId", clubId).list();
    }

    public MeituanAccount fetchMeituanAccountById(Long id) {
        return meituanAccountRepo.findById(id);
    }

    public void saveMeituanTicket(MeituanTicket meituanTicket) {
        meituanTicketRepo.save(meituanTicket);
    }

    public MeituanDeal getMeituanDealByDealId(Long id, Long dealId) {
        return meituanDealRepo.find("dealId = ?1 and accountId = ?2", dealId, id).firstResult();
    }

    public List<MeituanDeal> fetchMeituanDealByIds(List<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Lists.newArrayList();
        }
        return meituanDealRepo.find("id in ?1", dealIds).list();
    }

    public MeituanTicket fetchMeituanTicketById(Long channelId) {
        return meituanTicketRepo.findById(channelId);
    }

    public void saveMeituanShop(MeituanShop shop) {
        meituanShopRepo.save(shop);
    }

    public List<MeituanShop> fetchMeituanShopByAccount(Long accountId) {
        return meituanShopRepo.find("accountId", accountId).list();
    }

    public MeituanShop fetchMeituanShop(Long shopId) {
        return meituanShopRepo.findById(shopId);
    }

    public void saveMeituanDeal(MeituanDeal meituanDeal) {
        meituanDealRepo.save(meituanDeal);
    }

    public List<MeituanDeal> fetchMeituanDealByDealIds(Long id, List<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Lists.newArrayList();
        }
        return meituanDealRepo.find("accountId = ?1 and dealId in ?2", id, dealIds).list();
    }

    public MeituanDeal fetchMeituanDealById(Long id) {
        return meituanDealRepo.findById(id);
    }

    public List<MeituanTicket> fetchMeituanTicketByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        return meituanTicketRepo.find("code in ?1", codes).list();
    }

    public MeituanTicket fetchMeituanTicketByCode(String receiptCode) {
        return meituanTicketRepo.find("code", receiptCode).firstResult();
    }

    public List<MeituanTicket> fetchMeituanTicketByIds(List<Long> channelIds) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return Lists.newArrayList();
        }
        return meituanTicketRepo.find("id in ?1", channelIds).list();
    }

    public List<MeituanAccount> fetchMeituanAccount() {
        return meituanAccountRepo.listAll();
    }

    @Transactional(value = Transactional.TxType.REQUIRES_NEW)
    public void saveMeituanAccount(MeituanAccount account) {
        meituanAccountRepo.save(account);
    }

    public Page<MeituanShop> fetchMeituanShopPage(ShopQueryDto dto) {
        String query = "where 1=1";
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(dto.getName())) {
            query += " and name like concat('%', :name, '%')";
            params.put("name", dto.getName());
        }
        return meituanShopRepo.page(dto.toPage(), query, Sort.by("id"), params);
    }

    public Page<MeituanDeal> fetchMeituanDealPage(DealQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(dto.getName())) {
            query += " and name like concat('%', :name, '%')";
            params.put("name", dto.getName());
        }
        if (dto.getDealIds() != null) {
            query += " and id in :dealIds";
            params.put("dealIds", dto.getDealIds());
        }
        return meituanDealRepo.page(dto.toPage(), query, Sort.by("id"), params);
    }

    public void deleteMeituanTicketById(Long channelId) {
        meituanTicketRepo.deleteById(channelId);
    }

    public void deleteMeituanShop(Long shopId) {
        meituanShopRepo.deleteById(shopId);
    }

    public void deleteMeituanDealByAccountId(Long accountId) {
        meituanDealRepo.delete("accountId", accountId);
    }

    public void deleteMeituanAccount(Long accountId) {
        meituanAccountRepo.deleteById(accountId);
    }

    public MeituanTicket fetchMeituanTicketByCodeAndDealId(String receiptCode, Long id) {
        return meituanTicketRepo.find("code = ?1 and dealId = ?2", receiptCode, id).firstResult();
    }
}
