import Back from "@/components/bussiness/back";
import { menuRect } from "@/utils";
import { ArrowRight } from "@nutui/icons-react-taro";
import {
  ConfigProvider,
  Dialog,
  SafeArea,
  Swiper,
  SwiperItem,
} from "@nutui/nutui-react-taro";
import { useRouter } from "@tarojs/taro";
import { useCallback, useEffect, useMemo, useState } from "react";
import api from "@/api/bussiness/assistant";
import { IAssistant } from "../list";
import Taro from "@tarojs/taro";
import { Image } from "@tarojs/components";
import Assistant from "@/components/bussiness/Card/Assistant";
import Button, { MODES } from "@/components/bussiness/Order/Button";
import Posters from "@/components/bussiness/Posters/assistant";
import dayjs from "dayjs";
const PostersButton = (props) => {
  const [visible, setVisible] = useState(false);
  return (
    <div className="w-1/3">
      <Button
        buttonText="生成海报"
        mode={MODES.Light}
        onClick={() => setVisible(true)}
      />
      <ConfigProvider
        theme={{
          "--nutui-gray-7": "#fff",
          "--nutui-gray-6": "#fff",
          "--nutui-dialog-header-font-weight": "600",
          "--nutui-dialog-header-font-size": "1.25rem",
          "--nutui-dialog-padding": "20rpx",
          "--nutui-dialog-content-max-height": "auto",
        }}
      >
        <Dialog
          visible={visible}
          onClose={() => setVisible(false)}
          hideCancelButton
          hideConfirmButton
        >
          <Posters {...props} />
        </Dialog>
      </ConfigProvider>
    </div>
  );
};

export default () => {
  const router = useRouter();
  const id = router.params.id;
  const [detail, setDetail] = useState<IAssistant>();
  const { windowWidth: size } = Taro.getSystemInfoSync();
  const getDetail = useCallback(
    async (id) => {
      const { data } = await api.getOne(id);
      setDetail(data);
    },
    [setDetail]
  );

  useEffect(() => {
    getDetail(id);
  }, []);

  const diffDay = useCallback(
    (date: dayjs.ConfigType, util: dayjs.QUnitType) => {
      function numberToChinese(year) {
        const cnNumbers = [
          "零",
          "一",
          "二",
          "三",
          "四",
          "五",
          "六",
          "七",
          "八",
          "九",
        ];
        const cnUnits = ["年", "十", "百"];

        const stringifiedYear = String(year);
        let cnYear = "";

        for (let i = 0; i < stringifiedYear.length; i++) {
          const digit = Number(stringifiedYear[i]);
          if (i === 0 && digit === 0) {
            cnYear += cnNumbers[digit];
          } else if (i === 1) {
            cnYear += cnUnits[1];
            cnYear += cnNumbers[digit];
          } else if (i === 2 && digit !== 0) {
            cnYear += cnUnits[2];
            cnYear += cnNumbers[digit];
          }
        }

        return cnYear;
      }
      return numberToChinese(dayjs().diff(date, util));
    },
    []
  );

  const content = useMemo(() => {
    if (!detail) return [];
    return [
      {
        value: detail.name,
        fontWeight: "bord",
        fonSize: 12,
        color: "#333",
      },
      {
        value: `${diffDay(dayjs(detail.birth), "year")}岁｜从业 ${diffDay(
          dayjs(detail.startWork),
          "year"
        )} 年`,
        fontWeight: "normal",
        fonSize: 10,
        color: "#666",
      },
      {
        value: detail.tags.join("、"),
        fontWeight: "normal",
        fonSize: 10,
        color: "#666",
      },
    ];
  }, [detail]);

  return (
    <div className="flex h-[100vh] flex-col overflow-auto">
      <Back />
      {detail && (
        <div className="flex flex-1 flex-col">
          <div className="relative flex-1">
            <Swiper height={size} autoPlay={true}>
              <SwiperItem className="w-full rounded-md">
                <Image
                  src={
                    detail.avatar ||
                    "https://oss.gorillaballclub.cn/images/big-logo-y.png"
                  }
                  style={{ height: size }}
                  className="w-full"
                />
              </SwiperItem>
            </Swiper>
            <div
              className="px-3 relative space-y-3"
              style={{ top: -(size / 5) }}
            >
              <div className="flex flex-col gap-1 p-3 bg-bgt rounded-md mb-3">
                <Assistant item={detail} />
              </div>
              <div
                className="py-3 flex px-3 justify-between text-sm"
                onClick={() =>
                  Taro.navigateTo({
                    url: `/pages/assistant/club/index?id=${id}`,
                  })
                }
              >
                <div>签约商家</div>
                <div className="flex gap-2 items-center">
                  {detail.clubs.length}家 <ArrowRight />
                </div>
              </div>
              <div className="px-3 space-y-2.5 text-sm">
                <div>下单须知</div>
                <div className="text-xs text-muted-foreground space-y-1.5">
                  <div>1. 选择助教下单最低1小时起步；</div>
                  <div>2. 通过客服下单能提高成功率，比如助教是否可以接单；</div>
                  <div>
                    3. 用户全额付款后，因助教原因取消订单，平台全额退款；
                  </div>
                  <div>
                    4.
                    用户全额付款后，订单已开始每1个小时为一个时间节点，不足1小时按照1小时计算；
                  </div>
                  <div>5. 预约助教只能选择助教签约球馆下单</div>
                </div>
              </div>
            </div>
          </div>
          {/* tab */}

          {/* 签约商家 */}
        </div>
      )}
      <div className="px-3">
        <div className="flex gap-2">
          {/* 按钮栏 */}
          {detail && (
            <PostersButton
              avatar={
                detail.avatar ||
                "https://oss.gorillaballclub.cn/images/big-logo-y.png"
              }
              content={content}
              code={detail.id}
            />
          )}
          <div className="flex-1">
            <Button
              buttonText="立即预约"
              onClick={() =>
                Taro.navigateTo({
                  url: `/pages/assistant/confirmation/index?id=${id}`,
                })
              }
            />
          </div>
        </div>
        <SafeArea position="bottom"></SafeArea>
      </div>
    </div>
  );
};
