package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdminAssistantOrderQueryDto extends PageRequest {

    @QueryParam("phone")
    private String phone;

    @QueryParam("orderNo")
    private String orderNo;

    @QueryParam("clubId")
    private List<Long> clubId;

    @QueryParam("assistant")
    private String assistant;

    @QueryParam("status")
    private OrderStatusEnum status;

}
