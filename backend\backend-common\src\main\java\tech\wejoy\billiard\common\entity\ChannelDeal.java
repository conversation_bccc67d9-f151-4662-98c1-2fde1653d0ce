package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.MappedSuperclass;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@MappedSuperclass
@Accessors(chain = true)
public class ChannelDeal extends BaseEntity {

    private Long accountId;

    private String name;

    private BigDecimal price;

    private BigDecimal marketPrice;

    private CouponTypeEnum type;

    private Integer minutes;

    @Column(columnDefinition = "text")
    private String period;

    @Column(columnDefinition = "text")
    private String sourceInfo;

    private IsEnum status;

}
