import {View} from "@tarojs/components";
import clsx from "clsx";
import dayjs, {Dayjs} from "dayjs";
import {forwardRef, useCallback, useEffect, useImperativeHandle, useMemo, useState,} from "react";
import {SCANCETYPES, TIMETYPES} from "@/constants";

export interface IDurationTimeProps {
  plans: TimePlan[];
  onChange: (value) => void;
  onCalculationPayment: () => void;
  scene: number;
}

export interface IDurationTimeRef {
  getData: () => string[];
}

const supportAppointment = false;

const DurationTime = forwardRef<IDurationTimeRef, IDurationTimeProps>(
  (props, ref) => {
    const { plans, onChange, scene } = props;
    const scanCode = scene === SCANCETYPES.SCANCODE;
    const normalizePlans = useMemo(() => {
      if (Array.isArray(plans)) {
        const plansData = Array.from(plans);
        plansData.sort((a, b) => b.type - a.type);
        return plansData;
      }
      return [];
    }, [plans]);

    const [current, setCurrent] = useState(0);
    const [type, setType] = useState(0);
    const [durationTime, setDurationTime] = useState(2);
    // const [visible, setVisible] = useState(false);
    const [currentTime, setCurrentTime] = useState(() => dayjs());
    const [customizationVisible, setCustomizationVisible] = useState(false);

    // const changeCurrent = useCallback(
    //   (index) => {
    //     setCurrent(index);
    //     const day = dayjs();
    //     setCurrentTime(day.add(index, "day"));
    //   },
    //   [setCurrent, setCurrentTime, setDurationTime]
    // );

    useEffect(() => {
      const currentPlan = normalizePlans[current];
      props.onChange((currentPlan as INormalTimePlan).value);
    }, [normalizePlans]);

    const maxValue = useMemo(() => {
      const maxValue = Math.max(
        ...plans.map((planItem) =>
          planItem.type === TIMETYPES.customization ? 0 : planItem.value
        )
      );
      return maxValue;
    }, [plans]);

    // const options = useMemo(() => {
    //   const hour = 24;
    //   const defaultMinues = [
    //     { text: "0分钟", value: 0 },
    //     { text: "30分钟", value: 0.5 },
    //   ];
    //   return new Array(hour - maxValue).fill(0).map((__item, index) => ({
    //     text: `${index + maxValue + 1}小时`,
    //     value: index + maxValue + 1,
    //     children:
    //       index == hour - maxValue - 1 ? [defaultMinues[0]] : defaultMinues,
    //   }));
    // }, [maxValue]);

    const onChangeType = useCallback(
      (index: number) => {
        const currentPlan = normalizePlans[index];
        if (currentPlan.type == TIMETYPES.customization) {
          setDurationTime(maxValue + 1);
          setCustomizationVisible(true);
        } else {
          onChange(currentPlan.value);
        }
        setType(index);
      },
      [
        setType,
        setDurationTime,
        setCustomizationVisible,
        normalizePlans,
        onChange,
        maxValue,
      ]
    );

    // const onConfirmPicker = useCallback(
    //   (__options: PickerOption[], values: [number, number]) => {
    //     const total = values.reduce((total, item) => total + item, 0);
    //     console.log("onConfirmPicker", total, values);
    //     onChange(total);
    //     setDurationTime(total);
    //   },
    //   [setDurationTime, onChange]
    // );

    /**
     * 用于获取当前时间的方法
     * 支持预约模式下 使用currentTime 即用户选择时间
     * 非支持预约模式下 区分场景 (扫码场景->当前时间、其他场景->当前时间 + 5分钟)
     */
    const getCurrentTime = useCallback(() => {
      return supportAppointment
        ? currentTime
        : !scanCode
        ? dayjs().add(5, "m")
        : dayjs();
    }, [currentTime]);

    const getRange:()=>[Dayjs, Dayjs] = useCallback(()=>{
      const currentTime = getCurrentTime();
      const currentPlan = normalizePlans[type];
      if (currentPlan && currentPlan.type == TIMETYPES.customization) {
        // 自定义逻辑
        return [currentTime, currentTime.add(durationTime, "h")];
      }
      return [currentTime, currentTime.add(currentPlan.value, "h")];
    },[type, normalizePlans, durationTime, getCurrentTime])


    const range: [Dayjs, Dayjs] = useMemo(() => {
      return getRange();
    }, [getRange]);



    useEffect(() => {
      props.onCalculationPayment();
    }, [range]);

    // const getWeekDay = useCallback((day: number, index: number) => {
    //   const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    //   return index ? weekdays[day] : "今天";
    // }, []);

    // const changeTime = useCallback(() => {
    //   setVisible(true);
    // }, [setVisible]);

    // const onConfirm = useCallback(
    //   (__options: PickerOption[], values: [string, string]) => {
    //     const [hour, minute] = values;
    //     const now = dayjs();
    //     const current = currentTime.set("hour", +hour).set("minute", +minute);
    //     if (current.isAfter(now)) {
    //       setCurrentTime(current);
    //     } else {
    //       Taro.showToast({ title: "不能设置历史时间", icon: "none" });
    //     }
    //   },
    //   [currentTime, setCurrentTime]
    // );

    const getData = useCallback(() => {
      return getRange().map((item) => item.format("YYYY-MM-DD HH:mm:ss"));
    }, [getRange]);

    useImperativeHandle(
      ref,
      () => ({
        getData,
      }),
      [range]
    );

    // const times = useMemo(() => {
    //   const nums = 5;
    //   const today = dayjs();
    //   return new Array(nums)
    //     .fill("")
    //     .map((__item, index) => today.add(index, "day"));
    // }, []);
    return (
      <View className="flex items-center space-x-3">
        {normalizePlans.map((item, index) => (
          <View
            className={clsx("text-center rounded-md h-8 flex justify-center items-center p-1 text-xs flex-1", {
              "bg-primary text-background": index === type,
              "bg-bgt text-foreground": index !== type,
            })}
            onClick={() => onChangeType(index)}
          >
            {item.name}
          </View>
        ))}
      </View>
    );
    //   return (
    //     <>
    //       <View className="bg-bgt p-3 border-1 rounded-lg flex gap-4 flex-col">
    //         {supportAppointment && !scanCode && (
    //           <View className="grid grid-cols-5 gap-2 w-full">
    //             {times.map((item, index) => (
    //               <View
    //                 className={clsx(
    //                   "text-center rounded-lg bg-bgt border border-color-muted-foreground p-1",
    //                   {
    //                     "text-primary border-primary": index === current,
    //                   }
    //                 )}
    //                 onClick={() => changeCurrent(index)}
    //               >
    //                 <View className="text-sm">{item.format("MM/DD")}</View>
    //                 <View className="text-sm">
    //                   {getWeekDay(item.day(), index)}
    //                 </View>
    //               </View>
    //             ))}
    //           </View>
    //         )}
    //         <View className="flex items-center">
    //           <Text className="text-muted-foreground mr-2 text-xs">
    //             时长选择:
    //           </Text>
    //           <View className="flex items-center gap-1 flex-1">
    //             {normalizePlans.map((item, index) => (
    //               <View
    //                 className={clsx(
    //                   "text-center rounded-lg bg-bgt border border-color-muted-foreground p-1 text-xs flex-1",
    //                   {
    //                     "bg-primary border-primary text-background": index === type,
    //                   }
    //                 )}
    //                 onClick={() => onChangeType(index)}
    //               >
    //                 {item.name}
    //               </View>
    //             ))}
    //           </View>
    //         </View>
    //         {supportAppointment && !scanCode && (
    //           <View className="flex items-center">
    //             <Text className="text-muted-foreground mr-2 text-xs">
    //               时间选择:
    //             </Text>
    //             <View className="flex gap-2 flex-1">
    //               <View
    //                 className="border text-primary border-primary rounded-lg p-1 text-xs flex-1 flex items-center justify-center"
    //                 onClick={changeTime}
    //               >
    //                 {range[0].format("HH:mm")}
    //                 <Edit className="ml-2" />
    //               </View>
    //               ~
    //               <View className="border border-color-muted-foreground rounded-lg p-1 text-xs flex-1 text-center">
    //                 {range[1].format("HH:mm")}
    //               </View>
    //             </View>
    //           </View>
    //         )}
    //       </View>
    //       <DatePicker
    //         type="hour-minutes"
    //         title="日期选择"
    //         visible={visible}
    //         defaultValue={currentTime.toDate()}
    //         showChinese
    //         onClose={() => setVisible(false)}
    //         threeDimensional={false}
    //         onConfirm={onConfirm}
    //       />
    //       <Picker
    //         visible={customizationVisible}
    //         options={options}
    //         title="自定义时间"
    //         onConfirm={onConfirmPicker}
    //         onClose={() => setCustomizationVisible(false)}
    //       ></Picker>
    //     </>
    //   );
  }
);

export default DurationTime;
