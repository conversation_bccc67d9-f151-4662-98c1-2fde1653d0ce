package tech.wejoy.billiard.common.third.douyin.response;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class VerifyCertificateResponse extends ResultResponse {
    private List<VerifyResult> verifyResults;

    @Data
    public static class VerifyResult {
        private Integer result;
        private String msg;
        private String code;
        private String verifyId;
        private String orderId;
        private String certificateId;
        private String certificateNo;
        private String originCode;
        private String accountId;
        private String projectId;
        private String idCard;
        private String qrcode;
        private VerifyAmountInfo verifyAmountInfo;
    }

    @Data
    public static class VerifyAmountInfo {
        private TimesCardAmount timesCardAmount; // 弃用
        private List<TimesCardSerialAmount> timesCardSerialAmount;
    }

    @Data
    public static class TimesCardAmount {
        private long amount; // 弃用
    }

    @Data
    public static class TimesCardSerialAmount {
        private int serialNum;
        private Amount amount;
    }

    @Data
    public static class Amount {
        private int originalAmount;
        private int payAmount;
        private int merchantTicketAmount;
        private int listMarketAmount;
        private int platformDiscountAmount;
        private int paymentDiscountAmount;
        private int couponPayAmount;
    }

}
