package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.FeedbackTypeEnum;

import java.util.List;

@Data
public class FeedbackBo {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "用户id")
    private Long userId;

    private String nickname;

    private String userPhone;

    @Schema(description = "类型")
    private FeedbackTypeEnum type;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "图片")
    private List<String> images;

}
