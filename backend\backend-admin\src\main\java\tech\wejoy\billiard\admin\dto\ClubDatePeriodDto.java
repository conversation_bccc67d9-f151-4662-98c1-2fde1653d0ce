package tech.wejoy.billiard.admin.dto;

import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.dto.DatePeriodParamDto;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClubDatePeriodDto extends DatePeriodParamDto {
    @QueryParam("clubId")
    private Long clubId;
    @QueryParam("tenantId")
    @Schema(hidden = true)
    private Long tenantId;
}
