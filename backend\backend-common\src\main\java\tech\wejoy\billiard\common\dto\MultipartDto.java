package tech.wejoy.billiard.common.dto;

import jakarta.ws.rs.FormParam;
import lombok.Data;
import org.jboss.resteasy.reactive.multipart.FileUpload;
import tech.wejoy.billiard.common.enums.FileLinkEnum;
import tech.wejoy.billiard.common.enums.FileTypeEnum;

@Data
public class MultipartDto {

    @FormParam("file")
    public FileUpload file;

    @FormParam("type")
    public FileTypeEnum type;

    @FormParam("link")
    public FileLinkEnum link;

}