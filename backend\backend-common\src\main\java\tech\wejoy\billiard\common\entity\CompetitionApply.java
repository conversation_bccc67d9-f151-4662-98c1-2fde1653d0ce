package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.IsEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class CompetitionApply extends BaseEntity {

    private Long competitionId;

    private Long userId;

    private String username;

    private String idCard;

    private String phone;

    private String city;

    private IsEnum pay;

    private IsEnum enable;

    private Integer number;

    private Integer rank;

}
