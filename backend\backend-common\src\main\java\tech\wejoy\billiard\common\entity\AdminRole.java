package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.PermissionTypeEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class AdminRole extends BaseEntity {

    private Long tenantId;

    private String name;

    private String code;

    private String description;

    private PermissionTypeEnum type;

}
