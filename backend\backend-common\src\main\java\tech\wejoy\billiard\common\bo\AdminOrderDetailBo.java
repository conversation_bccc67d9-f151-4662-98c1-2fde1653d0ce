package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Data
public class AdminOrderDetailBo {

    @Schema(description = "门店")
    private AdminClubBo club;

    @Schema(description = "桌台")
    private AdminTableBo table;

    @Schema(description = "用户")
    private ClientUserBo user;

    @Schema(description = "订单")
    private AdminOrderBo order;

    @Schema(description = "团购套餐")
    private ChannelDealBo deal;

    @Schema(description = "优惠券")
    private CouponBo coupon;

}
