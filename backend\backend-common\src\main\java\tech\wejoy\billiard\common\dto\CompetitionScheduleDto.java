package tech.wejoy.billiard.common.dto;

import lombok.Data;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDateTime;

@Data
public class CompetitionScheduleDto {

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String title;

    private Integer round;

    private Integer playerCount;

    private Integer winScore;

    private IsEnum loserGroup;

}
