package tech.wejoy.billiard.api.api;

import com.congeer.core.bean.Page;
import com.congeer.security.core.bean.SessionInfo;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.common.bo.MatchBo;
import tech.wejoy.billiard.common.dto.MatchQueryDto;
import tech.wejoy.billiard.common.service.MatchService;

import java.util.List;

@Path("/match")
@Tag(name = "约球")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class MatchApi {

    private final MatchService matchService;

    @GET
    @Path("/list")
    @Operation(summary = "订单列表")
    public Page<MatchBo> page(@BeanParam MatchQueryDto dto) {
        SessionInfo<Long, WxUserBo> session = SecurityHolder.session();
        if (session != null) {
            dto.setUserId(session.getUserId());
        }
        return matchService.page(dto);
    }

    @POST
    @Path("/")
    @Operation(summary = "创建约球")
    @Authorized
    public MatchBo create(MatchBo matchBo) {
        matchBo.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return matchService.create(matchBo);
    }

    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除约球")
    @Authorized
    public void delete(@PathParam("id") Long id) {
        matchService.delete(id);
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "更新约球")
    @Authorized
    public MatchBo update(@PathParam("id") Long id, MatchBo matchBo) {
        matchBo.setId(id);
        return matchService.update(matchBo);
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "获取约球详情")
    @Authorized
    public MatchBo get(@PathParam("id") Long id) {
        SessionInfo<Long, WxUserBo> session = SecurityHolder.session();
        Long userId = null;
        if (session != null) {
            userId = session.getUserId();
        }
        return matchService.get(id, userId);
    }

    @POST
    @Path("/{id}/accept")
    @Operation(summary = "接受约球")
    @Authorized
    public void accept(@PathParam("id") Long id) {
        matchService.accept(id);
    }

    @POST
    @Path("/{id}/request")
    @Operation(summary = "请求约球")
    @Authorized
    public void request(@PathParam("id") Long id) {
        matchService.request(id, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @POST
    @Path("/{id}/cancel")
    @Operation(summary = "取消约球")
    @Authorized
    public void cancel(@PathParam("id") Long id) {
        matchService.cancel(id, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @POST
    @Path("/{id}/finish")
    @Operation(summary = "完成约球")
    @Authorized
    public void finish(@PathParam("id") Long id) {
        matchService.finish(id, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @GET
    @Path("/my/match")
    @Operation(summary = "我的约球")
    @Authorized
    public Page<MatchBo> myMatch(@BeanParam PageRequest dto) {
        return matchService.myMatch(dto, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @GET
    @Path("/my/accept")
    @Operation(summary = "我接受的约球")
    @Authorized
    public Page<MatchBo> myAccept(@BeanParam PageRequest dto) {
        return matchService.myAccept(dto, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @GET
    @Path("/club/{id}")
    @Operation(summary = "获取俱乐部约球列表")
    public List<MatchBo> clubMatch(@PathParam("id") Long clubId) {
        return matchService.listByClub(clubId);
    }

}
