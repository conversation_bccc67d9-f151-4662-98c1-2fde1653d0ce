# 抢台费功能 (Table Grabbing Fee Feature)

## 概述
抢台费功能允许其他用户为正在使用中的订单付费，从而"抢夺"桌台的使用权。该功能需要在门店级别进行开关控制。

## 功能特性

### 1. 门店级别开关控制
- 新增 `enableTableGrabbing` 字段到 `ClubInfo` 实体
- 管理员可以通过后台管理系统控制每个门店是否开启此功能
- 默认状态为关闭

### 2. 抢台类型
- **全额抢台 (ALL)**: 新用户支付全部费用，原用户获得全额退款
- **半价抢台 (HALF)**: 新用户支付一半费用，原用户获得部分退款

### 3. 安全验证
- 验证门店是否开启抢台费功能
- 验证用户不能为自己的订单付费
- 验证订单状态必须为使用中
- 验证订单使用时间必须超过5分钟

## 技术实现

### 数据库变更
```sql
-- 添加抢台费开关字段
ALTER TABLE club_info 
ADD COLUMN enable_table_grabbing INTEGER DEFAULT 0 COMMENT '抢台费开关：0-关闭，1-开启';
```

### 后端变更

#### 1. 实体类更新
- `ClubInfo.java`: 添加 `enableTableGrabbing` 字段
- `AdminClubBo.java`: 添加对应的BO字段
- `AdminClubDto.java`: 添加对应的DTO字段
- `ApiClubBo.java`: 添加对应的API响应字段

#### 2. 业务逻辑优化
`OrderService.finishOther()` 方法优化：
- 添加门店功能开关验证
- 添加用户身份验证（不能为自己付费）
- 改进错误处理和异常信息
- 优化退款逻辑
- 添加详细的业务注释

### 前端变更

#### 1. 类型定义
- `IVenue` 接口添加 `enableTableGrabbing` 字段

#### 2. API接口
- 添加 `finishOtherOrder` 方法用于调用抢台费功能

## API接口

### 抢台费接口
```
POST /order/{orderNo}/finish/other
```

**请求参数:**
```json
{
  "type": 1,     // 抢台类型：1-全额，2-半价
  "payType": 1   // 支付方式
}
```

**响应示例:**
```json
{
  "orderNo": "ORD123456",
  "amount": 50.00,
  "startTime": "2024-01-01T10:00:00",
  "endTime": "2024-01-01T12:00:00",
  "result": "PENDING",
  "extra": {
    // 支付参数
  }
}
```

## 业务流程

### 抢台费流程
1. 用户发起抢台费请求
2. 系统验证门店是否开启该功能
3. 系统验证用户身份和订单状态
4. 根据抢台类型计算费用
5. 处理原订单退款
6. 创建新订单并发起支付
7. 返回支付结果

### 错误处理
- 门店未开启功能：返回相应错误信息
- 用户为自己订单付费：返回错误信息
- 订单状态不符合：返回错误信息
- 退款失败：回滚操作并返回错误信息

## 配置说明

### 管理后台配置
管理员可以在门店管理页面中配置每个门店的抢台费开关：
- 开启：允许用户使用抢台费功能
- 关闭：禁用抢台费功能（默认）

### 前端显示逻辑
前端应根据 `enableTableGrabbing` 字段决定是否显示抢台费相关功能按钮。

## 支付逻辑修复

### 🚨 发现的关键问题
原始实现存在严重的逻辑错误：
1. **退款时机错误**: 先处理原订单退款，再处理新订单支付
2. **缺少回滚机制**: 支付失败时没有回滚原订单状态
3. **异步支付处理缺失**: PENDING状态订单支付成功后没有处理抢台费逻辑

### ✅ 修复后的正确逻辑

#### 1. 同步支付成功流程
```
验证 → 创建新订单 → 新订单支付 → 原订单退款 → 状态更新 → 桌台释放
```

#### 2. 异步支付成功流程
```
支付回调 → 检查订单类型 → 抢台费处理 → 原订单退款 → 状态更新 → 桌台释放
```

#### 3. 订单标识机制
- 使用 `description` 字段存储抢台费订单信息
- JSON格式：`{"type":"TABLE_GRAB","originalOrderNo":"xxx","grabType":"ALL/HALF"}`
- 支付回调时根据此标识进行特殊处理

### 🔧 技术实现细节

#### OrderService.finishOther() 优化
- 先验证所有前置条件
- 创建新订单并标记为抢台费订单
- 先处理新订单支付
- 支付成功后再处理原订单退款和状态更新
- 支付失败时不修改任何状态

#### OrderApiService.payOrder() 增强
- 检查订单是否为抢台费订单
- 分别处理普通订单和抢台费订单的支付成功逻辑
- 抢台费订单支付成功时自动处理原订单退款

## 注意事项

1. **数据一致性**: 抢台费操作涉及多个订单状态变更，需要确保事务一致性
2. **支付安全**: 新订单的支付流程需要严格验证
3. **退款处理**: 原订单的退款需要妥善处理，失败时需要回滚
4. **用户体验**: 提供清晰的错误提示和操作反馈
5. **异步处理**: 支付回调需要正确处理抢台费订单的特殊逻辑

## 测试建议

1. **功能测试**
   - 测试门店开关控制
   - 测试不同抢台类型的费用计算
   - 测试各种异常情况
   - **重点测试异步支付成功场景**

2. **安全测试**
   - 测试用户身份验证
   - 测试订单状态验证
   - 测试支付安全性
   - **测试支付失败时的回滚机制**

3. **性能测试**
   - 测试并发抢台情况
   - 测试大量订单处理性能

4. **边界测试**
   - 测试支付成功但退款失败的情况
   - 测试网络异常导致的状态不一致
   - 测试重复支付回调的幂等性
