package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.utils.BeanUtils;
import com.google.common.collect.Lists;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.AdminClubBo;
import tech.wejoy.billiard.common.bo.ApiClubBo;
import tech.wejoy.billiard.common.dto.AdminClubQueryDto;
import tech.wejoy.billiard.common.dto.ApiClubQueryDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.BaseConfigEnum;
import tech.wejoy.billiard.common.enums.ClubStatusEnum;
import tech.wejoy.billiard.common.enums.TableStatusEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;
import tech.wejoy.billiard.common.repo.ClubChannelDealRelRepo;
import tech.wejoy.billiard.common.repo.ClubInfoRepo;
import tech.wejoy.billiard.common.repo.ClubTableDeviceRelRepo;
import tech.wejoy.billiard.common.repo.ClubTableRepo;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@ApplicationScoped
@Transactional(rollbackOn = Exception.class)
@RequiredArgsConstructor
public class ClubManager {

    private final ClubInfoRepo clubInfoRepo;

    private final ClubTableRepo clubTableRepo;

    private final ClubTableDeviceRelRepo clubTableDeviceRelRepo;

    private final ClubChannelDealRelRepo clubChannelDealRelRepo;

    @Inject
    ConfigManager configManager;

    public ClubInfo fetchClubById(Long id) {
        return clubInfoRepo.findById(id);
    }

    public List<ClubTable> fetchTableByClub(Long clubId) {
        return clubTableRepo.find("clubId", Sort.by("seq"), clubId).list();
    }

    public Page<ApiClubBo> listWithDistance(ApiClubQueryDto dto) {
        boolean filterCity = false;
        BaseConfig baseConfig = configManager.fetchBaseConfig(BaseConfigEnum.CLUB_FILTER_CITY.getKey());
        if (baseConfig != null) {
            filterCity = Boolean.parseBoolean(baseConfig.getValue());
        }
        String select = "SELECT *";
        if (dto.getUserLat() != null && dto.getUserLng() != null) {
            select += ", earth_distance(ll_to_earth(longitude, latitude), ll_to_earth(:userLng,:userLat)) as distance ";
            if (dto.getQueryLng() == null || dto.getQueryLat() == null) {
                dto.setQueryLng(dto.getUserLng());
                dto.setQueryLat(dto.getUserLat());
            }
        }
        select += "FROM club_info";
        String where = "status not in (0,5)";
        if (StringUtils.isNotBlank(dto.getKeyword())) {
            where += " AND name LIKE concat('%', :keyword, '%')";
        } else {
            dto.setKeyword(null);
        }
        String orderBy = "";
        if (dto.getQueryLat() != null && dto.getUserLng() != null) {
            orderBy = "earth_distance(ll_to_earth(longitude, latitude), ll_to_earth(:queryLng,:queryLat))";
        }
        if (dto.getCity() != null) {
            if (filterCity) {
                where += " AND district = :city";
            } else {
                orderBy = "district is not null and district = :city DESC, " + orderBy;
            }
        }
        String sql = select + " WHERE " + where + " ORDER BY " + orderBy;
        Map<String, Object> map = BeanUtils.toMap(dto, "keyword", "city");
        if (!filterCity) {
            map.remove("city");
        }
        long count = clubInfoRepo.count(where, map);
        if (count == 0) {
            return dto.empty();
        }
        List<String> strings = Lists.newArrayList("keyword", "city");
        if (dto.getUserLat() != null && dto.getUserLng() != null) {
            strings.add("userLat");
            strings.add("userLng");
        }
        if (dto.getQueryLat() != null && dto.getQueryLng() != null) {
            strings.add("queryLat");
            strings.add("queryLng");
        }
        Map<String, Object> params = BeanUtils.toMap(dto, strings.toArray(new String[0]));
        List<ApiClubBo> list = clubInfoRepo.findBySql(sql, params, ApiClubBo.class, dto.getOffset(), dto.getSize());
        return dto.of(count, list);
    }

    public Page<AdminClubBo> listAdmin(AdminClubQueryDto dto) {
        String where = "status <> 5";
        Map<String, Object> params = BeanUtils.toMap(dto, "name", "city", "status", "tenantId");
        if (dto.getName() != null) {
            where += " AND name LIKE concat('%', :name, '%')";
        }
        if (dto.getCity() != null) {
            where += " AND district = :city";
        }
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            where += " AND status IN (:status)";
        } else {
            params.remove("status");
        }
        if (dto.getTenantId() != null && dto.getTenantId() > 0) {
            where += " AND tenantId = :tenantId";
        }
        if (CollectionUtils.isNotEmpty(dto.getClubIds())) {
            where += " AND id in :clubIds";
            params.put("clubIds", dto.getClubIds());
        }
        long count = clubInfoRepo.count(where, params);
        if (count == 0) {
            return dto.empty();
        }
        List<ClubInfo> list = clubInfoRepo.find(where, Sort.by("id"), params).range(dto.getOffset(), dto.getOffset() + dto.getSize()).list();
        List<AdminClubBo> ret = list.stream().map(AdminClubBo::from).toList();
        return dto.of(count, ret);
    }


    public List<String> fetchOpenDistricts() {
        String sql = "SELECT district FROM club_info where status not in (0, 5) order by id asc";
        Query query = clubInfoRepo.getEntityManager().createNativeQuery(sql, String.class);
        return query.getResultList();
    }

    public ClubTable fetchClubTableById(Long tableId) {
        return clubTableRepo.findById(tableId);
    }

    public void save(ClubInfo clubInfo) {
        clubInfoRepo.save(clubInfo);
    }

    public void saveTable(ClubTable clubTable) {
        clubTableRepo.save(clubTable);
    }

    public List<ClubInfo> fetchClubByIds(List<Long> clubIds) {
        return clubInfoRepo.find("id in ?1", Sort.by("id"), clubIds).list();
    }

    public List<ClubInfo> fetchClubByIdsAndKey(List<Long> clubIds, String keyword, Double lng, Double lat) {
        if (CollectionUtils.isEmpty(clubIds)) {
            return Lists.newArrayList();
        }
        String select = "SELECT * FROM club_info";
        String where = "status = :status and id in :ids";
        if (keyword != null) {
            where += " AND name LIKE concat('%', :keyword, '%')";
        }
        String orderBy = "";
        if (lng != null && lat != null) {
            orderBy = " ORDER BY earth_distance(ll_to_earth(longitude, latitude), ll_to_earth(:lng,:lat))";
        }
        String sql = select + " WHERE " + where + orderBy;
        Map<String, Object> map = new HashMap<>();
        map.put("status", ClubStatusEnum.NORMAL);
        map.put("ids", clubIds);
        if (keyword != null) {
            map.put("keyword", keyword);
        }
        if (lng != null && lat != null) {
            map.put("lng", lng);
            map.put("lat", lat);
        }
        return clubInfoRepo.findBySql(sql, map, ClubInfo.class);
    }

    public List<ClubInfo> fetchClubByKey(String keyword, Double lng, Double lat) {
        String select = "SELECT * FROM club_info";
        String where = "status = :status and id in :ids";
        if (keyword != null) {
            where += " AND name LIKE concat('%', :keyword, '%')";
        }
        String orderBy = "";
        if (lng != null && lat != null) {
            orderBy = " ORDER BY earth_distance(ll_to_earth(longitude, latitude), ll_to_earth(:lng,:lat))";
        }
        String sql = select + " WHERE " + where + orderBy;
        Map<String, Object> map = new HashMap<>();
        map.put("status", ClubStatusEnum.NORMAL);
        if (keyword != null) {
            map.put("keyword", keyword);
        }
        if (lng != null && lat != null) {
            map.put("lng", lng);
            map.put("lat", lat);
        }
        return clubInfoRepo.findBySql(sql, map, ClubInfo.class);
    }

    public List<ClubTableDeviceRel> fetchDeviceByTableId(Long tableId) {
        return clubTableDeviceRelRepo.find("tableId", tableId).list();
    }

    public List<ClubTableDeviceRel> fetchDeviceRelByDeviceIds(List<Long> deviceIds) {
        return clubTableDeviceRelRepo.find("deviceId in ?1", deviceIds).list();
    }

    public List<ClubTableDeviceRel> fetchDevicesRelByDeviceId(Long deviceId) {
        return clubTableDeviceRelRepo.find("deviceId", deviceId).list();
    }

    public ClubTableDeviceRel fetchDeviceRelByDeviceId(Long deviceId) {
        return clubTableDeviceRelRepo.find("deviceId", deviceId).firstResult();
    }

    public void bindDevice(long tableId, long deviceId) {
        ClubTable clubTable = fetchClubTableById(tableId);
        if (clubTable == null) {
            return;
        }
        ClubTableDeviceRel bind = clubTableDeviceRelRepo.find("tableId = ?1 and deviceId = ?2", tableId, deviceId).firstResult();
        if (bind != null) {
            throw new BaseException("设备已绑定到当前桌台");
        }
        ClubTableDeviceRel rel = new ClubTableDeviceRel();
        rel.setDeviceId(deviceId);
        rel.setTableId(tableId);
        rel.setClubId(clubTable.getClubId());
        rel.setTenantId(clubTable.getTenantId());
        clubTableDeviceRelRepo.save(rel);
    }

    public List<ClubInfo> fetchAllClub() {
        return clubInfoRepo.findAll(Sort.by("id")).list();
    }

    public void unbindDevice(long tableId, long deviceId) {
        clubTableDeviceRelRepo.delete("tableId = ?1 and deviceId = ?2", tableId, deviceId);
    }

    public void updateTableUsing(long tableId, long userId, LocalDateTime endTime) {
        ClubTable clubTable = fetchClubTableById(tableId);
        clubTable.setStatus(TableStatusEnum.USING);
        clubTable.setUserId(userId);
        clubTable.setEndTime(endTime);
        saveTable(clubTable);
    }

    public void updateTableIdle(long tableId) {
        ClubTable clubTable = fetchClubTableById(tableId);
        clubTable.setStatus(TableStatusEnum.IDLE);
        clubTable.setUserId(null);
        clubTable.setEndTime(null);
        saveTable(clubTable);
    }

    public List<ClubChannelDealRel> fetchClubChannelDealRelByClubId(Long clubId, TicketChannelEnum channel) {
        return clubChannelDealRelRepo.find("clubId = ?1 and channel = ?2", clubId, channel).list();
    }

    public List<ClubChannelDealRel> fetchClubChannelDealRelByClubId(Long clubId) {
        return clubChannelDealRelRepo.find("clubId = ?1", clubId).list();
    }

    public void saveClubChannelDealRel(ClubChannelDealRel clubChannelDealRel) {
        clubChannelDealRelRepo.save(clubChannelDealRel);
    }

    public List<ClubChannelDealRel> fetchClubChannelDealRel() {
        return clubChannelDealRelRepo.listAll();
    }

    public List<ClubInfo> fetchByTenantId(Long tenantId) {
        return clubInfoRepo.find("tenantId = ?1", Sort.by("id"), tenantId).list();
    }

    public List<ClubTable> fetchTableByIds(List<Long> tableIds) {
        if (CollectionUtils.isEmpty(tableIds)) {
            return List.of();
        }
        return clubTableRepo.find("id in ?1", Sort.by("seq"), tableIds).list();
    }

    public List<ClubInfo> fetchClubByTenantId(Long tenantId) {
        return clubInfoRepo.find("tenantId", tenantId).list();
    }

    public void deleteClubChannelDealRelByClubId(Long clubId, TicketChannelEnum channel) {
        clubChannelDealRelRepo.delete("clubId = ?1 and channel = ?2", clubId, channel);
    }

    public List<ClubInfo> fetchClubByTenantIds(List<Long> tenantIds) {
        if (CollectionUtils.isEmpty(tenantIds)) {
            return List.of();
        }
        return clubInfoRepo.find("tenantId in ?1", Sort.by("id"), tenantIds).list();
    }

    public void removeDeviceRelByDeviceIds(Set<Long> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return;
        }
        clubTableDeviceRelRepo.delete("deviceId in ?1", deviceIds);
    }

    public void deleteDouyinClubDealRelByDealId(Long id, Set<Long> clubIds) {
        if (CollectionUtils.isEmpty(clubIds)) {
            return;
        }
        clubChannelDealRelRepo.delete("dealId = ?1 and clubId in ?2 and channel = ?3", id, clubIds, TicketChannelEnum.DOUYIN);
    }

    public void deleteClubChannelDealRelByClubId(Long clubId, TicketChannelEnum ticketChannelEnum, List<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return;
        }
        clubChannelDealRelRepo.delete("clubId = ?1 and channel = ?2 and dealId in ?3", clubId, ticketChannelEnum, dealIds);
    }

    public List<ClubTableDeviceRel> fetchDeviceRelByTableIds(List<Long> tableIds) {
        if (CollectionUtils.isEmpty(tableIds)) {
            return List.of();
        }
        return clubTableDeviceRelRepo.find("tableId in ?1", tableIds).list();
    }

    public ClubTableDeviceRel fetchDeviceRelById(Long id) {
        return clubTableDeviceRelRepo.findById(id);
    }

    public void saveDeviceRel(ClubTableDeviceRel rel) {
        clubTableDeviceRelRepo.save(rel);
    }

    public void deleteTable(Long id) {
        clubTableRepo.deleteById(id);
    }

}
