package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.TenantPlanBillStatsBo;
import tech.wejoy.billiard.common.dto.AdminTenantPlanQueryDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.repo.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class MemberManager {

    private final ClientUserTenantMemberRepo tenantMemberRepo;

    private final ClientUserMemberRepo memberRepo;

    private final MemberPlanRepo memberPlanRepo;

    private final TenantClubPlanRelRepo tenantClubPlanRelRepo;

    private final TenantPlanRepo tenantPlanRepo;

    private final PlanGiftRecordRepo planGiftRecordRepo;

    public void createMember(ClientUserMember member) {
        memberRepo.persist(member);
    }

    public ClientUserMember getMemberByUserId(Long userId) {
        return memberRepo.find("userId", userId).firstResult();
    }

    public List<ClientUserTenantMember> getClubMembersByUserId(Long userId) {
        return tenantMemberRepo.find("userId", userId).list();
    }

    public ClientUserTenantMember getClubMemberByUserIdAndPlanId(Long userId, Long planId) {
        return tenantMemberRepo.find("userId = ?1 and planId = ?2", userId, planId).firstResult();
    }

    public void saveClubMember(ClientUserTenantMember clubMember) {
        tenantMemberRepo.save(clubMember);
    }

    public void saveMember(ClientUserMember member) {
        memberRepo.save(member);
    }

    public MemberPlan getMemberPlanById(long planId) {
        return memberPlanRepo.findById(planId);
    }

    public List<ClientUserTenantMember> getClubMembersByClubIdAndUserId(long clubId, long userId) {
        List<TenantClubPlanRel> relList = tenantClubPlanRelRepo.find("clubId", clubId).list();
        List<Long> planIds = relList.stream().map(TenantClubPlanRel::getPlanId).toList();
        if (planIds.isEmpty()) {
            return List.of();
        }
        return tenantMemberRepo.find("userId = ?1 and planId in ?2", Sort.by("seq"), userId, planIds).list();
    }

    public List<ClientUserTenantMember> getClubMembersByClubIdAndUserIds(long clubId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        List<TenantClubPlanRel> relList = tenantClubPlanRelRepo.find("clubId", clubId).list();
        List<Long> planIds = relList.stream().map(TenantClubPlanRel::getPlanId).toList();
        if (planIds.isEmpty()) {
            return List.of();
        }
        return tenantMemberRepo.find("userId in ?1 and planId in ?2", Sort.by("seq"), userIds, planIds).list();
    }

    public List<TenantPlan> fetchShowTenantPlanByClub(long clubId) {
        List<TenantClubPlanRel> relList = tenantClubPlanRelRepo.find("clubId", clubId).list();
        List<Long> planIds = relList.stream().map(TenantClubPlanRel::getPlanId).toList();
        if (planIds.isEmpty()) {
            return List.of();
        }
        return tenantPlanRepo.find("id in ?1 and show = ?2 and status = ?3 and delete = ?4",
                Sort.by("seq"), planIds, IsEnum.TRUE, IsEnum.TRUE, IsEnum.FALSE).list();
    }

    public List<TenantPlan> fetchTenantPlanByClub(long clubId) {
        List<TenantClubPlanRel> relList = tenantClubPlanRelRepo.find("clubId", clubId).list();
        List<Long> planIds = relList.stream().map(TenantClubPlanRel::getPlanId).toList();
        if (planIds.isEmpty()) {
            return List.of();
        }
        return tenantPlanRepo.find("id in ?1 and delete = ?2",
                Sort.by("seq"), planIds, IsEnum.FALSE).list();
    }

    public List<MemberPlan> getMemberPlans(int minLevel) {
        return memberPlanRepo.find("level >= ?1", Sort.by("level"), minLevel).list();
    }

    public List<TenantClubPlanRel> getClubsByPlanIds(List<Long> planIds) {
        if (CollectionUtils.isEmpty(planIds)) {
            return List.of();
        }
        return tenantClubPlanRelRepo.find("planId in ?1", planIds).list();
    }

    public List<TenantPlan> getTenantPlans() {
        return tenantPlanRepo.find("show = ?1 and status = ?2", IsEnum.TRUE, IsEnum.TRUE).list();
    }

    public TenantPlan getTenantPlanById(Long planId) {
        return tenantPlanRepo.findById(planId);
    }

    public ClientUserTenantMember getClubMemberById(Long id) {
        return tenantMemberRepo.findById(id);
    }

    public List<ClientUserTenantMember> getClubMembersByClubId(Long clubId) {
        List<TenantClubPlanRel> relList = tenantClubPlanRelRepo.find("clubId", clubId).list();
        List<Long> planIds = relList.stream().map(TenantClubPlanRel::getPlanId).toList();
        if (planIds.isEmpty()) {
            return List.of();
        }
        return tenantMemberRepo.find("planId in ?1", Sort.by("seq"), planIds).list();
    }

    public List<TenantPlan> fetchTenantPlanByTenant(Long tenantId) {
        return tenantPlanRepo.find("tenantId", Sort.by("seq"), tenantId).list();
    }

    public void saveTenantPlan(TenantPlan plan) {
        tenantPlanRepo.save(plan);
    }

    public void saveTenantClubPlanRel(Long planId, Long tenantId, List<Long> clubIds) {
        tenantClubPlanRelRepo.delete("planId", planId);
        for (Long clubId : clubIds) {
            TenantClubPlanRel rel = new TenantClubPlanRel();
            rel.setPlanId(planId);
            rel.setTenantId(tenantId);
            rel.setClubId(clubId);
            tenantClubPlanRelRepo.save(rel);
        }
    }

    public void deleteTenantPlan(Long id) {
        TenantPlan plan = getTenantPlanById(id);
        if (plan == null) {
            throw new RuntimeException("套餐不存在");
        }
        plan.setDelete(IsEnum.TRUE);
        saveTenantPlan(plan);
    }

    public void savePlanGiftRecord(PlanGiftRecord record) {
        planGiftRecordRepo.save(record);
    }

    public Page<PlanGiftRecord> fetchGiftRecordByTenantIdAndClubId(AdminTenantPlanQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (dto.getTenantId() != null) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getClubId() != null) {
            query += " and clubId = :clubId";
            params.put("clubId", dto.getClubId());
        }
        if (dto.getStartDate() != null) {
            query += " and createAt >= :startDate";
            params.put("startDate", dto.getStartDate().atStartOfDay());
        }
        if (dto.getEndDate() != null) {
            query += " and createAt <= :endDate";
            params.put("endDate", dto.getEndDate().plusDays(1).atStartOfDay());
        }
        if (StringUtils.isNotBlank(dto.getPhone())) {
            query += " and phone like concat('%', :phone, '%')";
            params.put("phone", dto.getPhone());
        }
        return planGiftRecordRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public List<PlanGiftRecord> fetchGiftRecordByTenantIdAndClubId(Long tenantId, Long clubId, LocalDate startDate, LocalDate endDate) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", tenantId);
        }
        if (clubId != null) {
            query += " and clubId = :clubId";
            params.put("clubId", clubId);
        }
        if (startDate != null) {
            query += " and createAt >= :startDate";
            params.put("startDate", startDate.atStartOfDay());
        }
        if (endDate != null) {
            query += " and createAt <= :endDate";
            params.put("endDate", endDate.plusDays(1).atStartOfDay());
        }
        return planGiftRecordRepo.find(query, Sort.descending("createAt"), params).list();
    }

    public TenantPlanBillStatsBo getClubMemberByTenantIdAndClubId(Long tenantId, Long clubId) {
        String sql = "select count(distinct m.user_id) as member_count, sum(total_amount) as total, sum(balance+bonus) as balance from client_user_tenant_member m left join tenant_club_plan_rel r on m.plan_id = r.plan_id";
        String where = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null && tenantId != 0) {
            where += " and m.tenant_id = :tenantId";
            params.put("tenantId", tenantId);
        }
        if (clubId != null && clubId != 0) {
            where += " and r.club_id = :clubId";
            params.put("clubId", clubId);
        }
        sql += " where " + where;
        List<TenantPlanBillStatsBo> result = tenantMemberRepo.findBySql(sql, params, TenantPlanBillStatsBo.class);
        return result.isEmpty() ? new TenantPlanBillStatsBo() : result.getFirst();
    }

}
