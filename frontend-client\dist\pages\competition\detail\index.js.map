{"version": 3, "file": "pages/competition/detail/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;ACraA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAFA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA;AACA;AACA;AACA;AAGA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAIA;;;;;;;;;;;;AC1IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/competition/detail/index.tsx?0b6f", "webpack://frontend-client/._src_components_bussiness_Posters_competition.tsx", "webpack://frontend-client/._src_pages_competition_detail_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _objectSpread from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport \"@nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css\";\nimport _ConfigProvider from \"@nutui/nutui-react-taro/dist/esm/ConfigProvider\";\nimport \"@nutui/nutui-react-taro/dist/esm/Dialog/style/css\";\nimport _Dialog from \"@nutui/nutui-react-taro/dist/esm/Dialog\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport competition from \"@/api/bussiness/competition\";\nimport api from \"@/api/bussiness\";\nimport Back from \"@/components/bussiness/back\";\nimport Competition from \"@/components/bussiness/Card/Competition\";\nimport Title from \"@/components/bussiness/Title\";\nimport { menuRect } from \"@/utils\";\nimport { useRouter } from \"@tarojs/taro\";\nimport { useCallback, useEffect, useMemo, useRef, useState } from \"react\";\nimport Posters from \"@/components/bussiness/Posters/competition\";\nimport dayjs from \"dayjs\";\nimport Button from \"@/components/bussiness/Form/Button\";\nimport { useFormik } from \"formik\";\nimport * as yup from \"yup\";\nimport { TextInput } from \"@/components/bussiness/Form\";\nimport Taro from \"@tarojs/taro\";\nimport { useStore } from \"@/hooks\";\nimport { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar COMPETION_STATUS = /*#__PURE__*/function (COMPETION_STATUS) {\n  COMPETION_STATUS[COMPETION_STATUS[\"NONE\"] = 0] = \"NONE\";\n  COMPETION_STATUS[COMPETION_STATUS[\"SIGN_UP\"] = 1] = \"SIGN_UP\";\n  COMPETION_STATUS[COMPETION_STATUS[\"PREPARE\"] = 2] = \"PREPARE\";\n  COMPETION_STATUS[COMPETION_STATUS[\"START\"] = 3] = \"START\";\n  COMPETION_STATUS[COMPETION_STATUS[\"AWARDS\"] = 4] = \"AWARDS\";\n  COMPETION_STATUS[COMPETION_STATUS[\"END\"] = 5] = \"END\";\n  return COMPETION_STATUS;\n}(COMPETION_STATUS || {});\nvar items = [{\n  name: \"赛事规程\",\n  path: \"/pages/competition/rules/index\"\n}, {\n  name: \"赛事日程\",\n  path: \"/pages/competition/schedule/index\"\n}, {\n  name: \"比分直播\",\n  path: \"/pages/competition/live/index\",\n  render: function render(item, detailData) {\n    var onClick = useCallback(function () {\n      Taro.navigateTo({\n        url: \"\".concat(item.path, \"?id=\").concat(detailData.id)\n      });\n    }, [item, detailData]);\n    if ([COMPETION_STATUS.START, COMPETION_STATUS.AWARDS, COMPETION_STATUS.END].includes(detailData === null || detailData === void 0 ? void 0 : detailData.status)) {\n      return /*#__PURE__*/_jsx(\"div\", {\n        className: \"p-3 w-full rounded-lg bg-bgt font-bold text-center\",\n        onClick: onClick,\n        children: item.name\n      });\n    }\n    return /*#__PURE__*/_jsx(_Fragment, {});\n  }\n}, {\n  name: \"晋级成绩\",\n  path: \"/pages/competition/score/index\",\n  render: function render(item, detailData) {\n    var onClick = useCallback(function () {\n      Taro.navigateTo({\n        url: \"\".concat(item.path, \"?id=\").concat(detailData.id)\n      });\n    }, [item, detailData]);\n    if ([COMPETION_STATUS.START, COMPETION_STATUS.AWARDS, COMPETION_STATUS.END].includes(detailData === null || detailData === void 0 ? void 0 : detailData.status)) {\n      return /*#__PURE__*/_jsx(\"div\", {\n        className: \"p-3 w-full rounded-lg bg-bgt font-bold text-center\",\n        onClick: onClick,\n        children: item.name\n      });\n    }\n    return /*#__PURE__*/_jsx(_Fragment, {});\n  }\n}, {\n  name: \"比赛榜单\",\n  path: \"/pages/competition/rank/index\",\n  render: function render(item, detailData) {\n    var onClick = useCallback(function () {\n      Taro.navigateTo({\n        url: \"\".concat(item.path, \"?id=\").concat(detailData.id)\n      });\n    }, [item, detailData]);\n    if ([COMPETION_STATUS.START, COMPETION_STATUS.AWARDS, COMPETION_STATUS.END].includes(detailData === null || detailData === void 0 ? void 0 : detailData.status)) {\n      return /*#__PURE__*/_jsx(\"div\", {\n        className: \"p-3 w-full rounded-lg bg-bgt font-bold text-center\",\n        onClick: onClick,\n        children: item.name\n      });\n    }\n    return /*#__PURE__*/_jsx(_Fragment, {});\n  }\n}, {\n  name: \"生成海报\",\n  render: function render(item, detailData) {\n    var _useState = useState(false),\n      _useState2 = _slicedToArray(_useState, 2),\n      visible = _useState2[0],\n      setVisible = _useState2[1];\n    var content = useMemo(function () {\n      if (detailData) {\n        return [{\n          value: dayjs(detailData.startTime).format(\"YYYY-MM-DD HH:mm\"),\n          fontWeight: \"bord\",\n          fonSize: 16,\n          color: \"#666\",\n          center: true\n        }, {\n          value: detailData.title,\n          fontWeight: \"bord\",\n          fonSize: 18,\n          color: \"#333\",\n          center: true\n        }];\n      }\n      return [];\n    }, [detailData]);\n    return /*#__PURE__*/_jsxs(_Fragment, {\n      children: [/*#__PURE__*/_jsx(\"div\", {\n        className: \"p-3 w-full rounded-lg bg-bgt font-bold text-center\",\n        onClick: function onClick() {\n          return setVisible(true);\n        },\n        children: item.name\n      }), /*#__PURE__*/_jsx(_ConfigProvider, {\n        theme: {\n          \"--nutui-gray-7\": \"#fff\",\n          \"--nutui-gray-6\": \"#fff\",\n          \"--nutui-dialog-header-font-weight\": \"600\",\n          \"--nutui-dialog-header-font-size\": \"1.25rem\",\n          \"--nutui-dialog-padding\": \"10rpx\",\n          \"--nutui-dialog-content-max-height\": \"auto\"\n        },\n        children: /*#__PURE__*/_jsx(_Dialog, {\n          visible: visible,\n          onClose: function onClose() {\n            return setVisible(false);\n          },\n          hideCancelButton: true,\n          hideConfirmButton: true,\n          children: detailData && /*#__PURE__*/_jsx(Posters, {\n            id: detailData.id,\n            content: content,\n            awards: detailData.awards\n          })\n        })\n      })]\n    });\n  }\n}, {\n  name: \"正赛抽签\",\n  render: function render(__item, detailData) {\n    if (((detailData === null || detailData === void 0 ? void 0 : detailData.apply) || {}).hasOwnProperty(\"number\")) {\n      return /*#__PURE__*/_jsxs(\"div\", {\n        className: \"p-3 w-full rounded-lg bg-bgt font-bold text-center\",\n        children: [\"\\u62BD\\u7B7E\\u7ED3\\u679C \", detailData.apply.number.toString().padStart(2, \"0\")]\n      });\n    }\n    return /*#__PURE__*/_jsx(_Fragment, {});\n  }\n}];\nvar SignButton = function SignButton(props) {\n  var item = props.item,\n    onLoad = props.onLoad;\n  var id = item.id;\n  var ref = useRef(null);\n  var formik = useFormik({\n    initialValues: {\n      realName: \"\",\n      idCard: \"\",\n      phone: \"\"\n    },\n    validationSchema: yup.object({\n      realName: yup.string().required(),\n      idCard: yup.string().required(\"请输入身份证\").length(18, \"身份证号必须为18位\").matches(/^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$/, \"无效的身份证号格式\"),\n      phone: yup.string().required(\"手机号不能为空\").matches(/^1[3-9]\\d{9}$/, \"手机号格式错误\")\n    }),\n    onSubmit: function onSubmit() {},\n    validateOnChange: false\n  });\n  var onCancel = useCallback(function () {\n    var _ref$current;\n    formik.resetForm();\n    (_ref$current = ref.current) === null || _ref$current === void 0 || _ref$current.setVisible(false);\n  }, [formik]);\n  var onConfirm = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n    var errorObjects, params;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _context.next = 2;\n          return formik.validateForm();\n        case 2:\n          errorObjects = _context.sent;\n          if (!(errorObjects && Object.keys(errorObjects).length)) {\n            _context.next = 5;\n            break;\n          }\n          return _context.abrupt(\"return\");\n        case 5:\n          _context.next = 7;\n          return Taro.showLoading({\n            title: \"报名中...\"\n          });\n        case 7:\n          params = _objectSpread({\n            competitionId: id\n          }, formik.values);\n          _context.next = 10;\n          return competition.signUp(params);\n        case 10:\n          _context.next = 12;\n          return Taro.hideLoading();\n        case 12:\n          _context.next = 14;\n          return Taro.showToast({\n            title: \"报名成功\",\n            icon: \"success\"\n          });\n        case 14:\n          _context.t0 = onLoad;\n          if (!_context.t0) {\n            _context.next = 18;\n            break;\n          }\n          _context.next = 18;\n          return onLoad();\n        case 18:\n          onCancel();\n        case 19:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  })), [formik.values, onCancel, onLoad]);\n  var auth = useStore(function (state) {\n    return state.auth;\n  });\n  var getPhone = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n    var _yield$api$user$getPh, data;\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          _context2.next = 2;\n          return api.user.getPhone();\n        case 2:\n          _yield$api$user$getPh = _context2.sent;\n          data = _yield$api$user$getPh.data;\n          formik.setFieldValue(\"phone\", data.phone);\n        case 5:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  })), [formik]);\n  var onVisibleChange = useCallback(function (visible) {\n    var _auth$user;\n    if (visible && (_auth$user = auth.user) !== null && _auth$user !== void 0 && _auth$user.hasPhone) getPhone();\n  }, []);\n  return /*#__PURE__*/_jsx(Button, {\n    buttonText: \"\\u6BD4\\u8D5B\\u62A5\\u540D\",\n    title: \"\\u6BD4\\u8D5B\\u62A5\\u540D\",\n    className: \"justify-center\",\n    onCancel: onCancel,\n    onConfirm: onConfirm,\n    onVisibleChange: onVisibleChange,\n    ref: ref,\n    children: /*#__PURE__*/_jsxs(\"div\", {\n      className: \"flex flex-col gap-3 px-3 text-white\",\n      children: [/*#__PURE__*/_jsx(TextInput, {\n        error: formik.errors.realName,\n        value: formik.values.realName,\n        onConfirm: function onConfirm(value) {\n          return formik.setFieldValue(\"realName\", value);\n        },\n        name: \"\\u771F\\u5B9E\\u59D3\\u540D\",\n        placeholder: \"\\u53C2\\u8D5B\\u9009\\u624B\\u7684\\u59D3\\u540D\",\n        type: \"text\"\n      }), /*#__PURE__*/_jsx(TextInput, {\n        error: formik.errors.phone,\n        value: formik.values.phone,\n        onConfirm: function onConfirm(value) {\n          return formik.setFieldValue(\"phone\", value);\n        },\n        name: \"\\u624B\\u673A\\u53F7\",\n        placeholder: \"\\u53C2\\u8D5B\\u9009\\u624B\\u7684\\u624B\\u673A\\u53F7\",\n        type: \"text\"\n      }), /*#__PURE__*/_jsx(TextInput, {\n        error: formik.errors.idCard,\n        value: formik.values.idCard,\n        onConfirm: function onConfirm(value) {\n          return formik.setFieldValue(\"idCard\", value);\n        },\n        name: \"\\u8EAB\\u4EFD\\u8BC1\",\n        placeholder: \"\\u53C2\\u8D5B\\u9009\\u624B\\u7684\\u8EAB\\u4EFD\\u8BC1\\u53F7\\u7801\",\n        type: \"text\"\n      })]\n    })\n  });\n};\nexport default (function () {\n  var router = useRouter();\n  var id = router.params.id;\n  var _useState3 = useState(),\n    _useState4 = _slicedToArray(_useState3, 2),\n    detailData = _useState4[0],\n    setDetailData = _useState4[1];\n  var rect = menuRect();\n  var getCompetition = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n    var _yield$competition$ge, data;\n    return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          _context3.next = 2;\n          return competition.getDetail(id);\n        case 2:\n          _yield$competition$ge = _context3.sent;\n          data = _yield$competition$ge.data;\n          setDetailData(data);\n          return _context3.abrupt(\"return\", data);\n        case 6:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  })), [setDetailData]);\n  var onDraw = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n    return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n      while (1) switch (_context4.prev = _context4.next) {\n        case 0:\n          _context4.next = 2;\n          return Taro.showLoading({\n            title: \"抽签中...\"\n          });\n        case 2:\n          _context4.next = 4;\n          return competition.draw(id);\n        case 4:\n          _context4.next = 6;\n          return Taro.hideLoading();\n        case 6:\n          _context4.next = 8;\n          return Taro.showToast({\n            title: \"抽签成功\",\n            icon: \"success\"\n          });\n        case 8:\n          _context4.next = 10;\n          return getCompetition();\n        case 10:\n        case \"end\":\n          return _context4.stop();\n      }\n    }, _callee4);\n  })), [getCompetition]);\n  useEffect(function () {\n    getCompetition();\n  }, []);\n  var onItemClick = useCallback(function (item) {\n    var path = item.path;\n    var commonPath = \"\".concat(path, \"?id=\").concat(id);\n    Taro.navigateTo({\n      url: commonPath\n    });\n  }, []);\n  var hasDraw = useMemo(function () {\n    if (!detailData) return false;\n    return !((detailData === null || detailData === void 0 ? void 0 : detailData.apply) || {}).hasOwnProperty(\"number\") && [COMPETION_STATUS.SIGN_UP, COMPETION_STATUS.PREPARE].includes(detailData.status) && detailData.signUp;\n  }, [detailData]);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: \"h-[100vh] flex flex-col\",\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u8D5B\\u4E8B\\u8BE6\\u60C5\"\n    }), /*#__PURE__*/_jsxs(\"div\", {\n      className: \"flex flex-col gap-3 flex-1 overflow-hidden\",\n      children: [/*#__PURE__*/_jsx(\"div\", {\n        className: \"px-3\",\n        children: detailData && /*#__PURE__*/_jsx(Competition, {\n          item: detailData\n        })\n      }), /*#__PURE__*/_jsx(\"div\", {\n        className: \"flex-1 overflow-y-auto\",\n        children: /*#__PURE__*/_jsx(\"div\", {\n          className: \"px-3 flex flex-col gap-3\",\n          children: items.map(function (item) {\n            var render = item.render;\n            if (render) {\n              return render(item, detailData);\n            }\n            return /*#__PURE__*/_jsx(\"div\", {\n              className: \"p-3 w-full rounded-lg bg-bgt font-bold text-center\",\n              onClick: function onClick(event) {\n                return onItemClick(item, event);\n              },\n              children: item.name\n            });\n          })\n        })\n      })]\n    }), /*#__PURE__*/_jsxs(\"div\", {\n      className: \"px-3\",\n      children: [detailData && detailData.status === COMPETION_STATUS.SIGN_UP && !detailData.signUp && /*#__PURE__*/_jsx(SignButton, {\n        item: detailData,\n        onLoad: getCompetition\n      }), hasDraw && /*#__PURE__*/_jsx(Button, {\n        buttonText: \"\\u62BD\\u7B7E\",\n        title: \"\\u62BD\\u7B7E\",\n        className: \"justify-center\",\n        customizationClick: onDraw\n      }), /*#__PURE__*/_jsx(_SafeArea, {\n        position: \"bottom\"\n      })]\n    })]\n  });\n});", "import Taro from \"@tarojs/taro\";\r\nimport { useCallback, useEffect, useRef } from \"react\";\r\nimport Button, { MODES } from \"../Order/Button\";\r\nimport { BASE_URI } from \"@/api\";\r\nconst DEFAULT_COVER = \"https://oss.gorillaballclub.cn/images/big-logo-y.png\";\r\nconst WIDTH = 280;\r\nconst HEIGHT = 400;\r\nconst BGCOLOR = \"#FBC202\"\r\nconst PADDING = 20;\r\nconst QRSIZE = 80;\r\nconst GAP = 8\r\nexport default (props) => {\r\n  // 裁剪图片\r\n  const ctx = useRef<Taro.CanvasContext>();\r\n  const tempFilePath = useRef<string>()\r\n  const { content, awards, id } = props;\r\n  const URI = `${BASE_URI}/competition/${id}/code`;\r\n  const clip2Cover = useCallback(async (ctx: Taro.CanvasContext, img: Taro.getImageInfo.SuccessCallbackResult, width, height) => {\r\n    ctx.setFillStyle('#fff');\r\n    ctx.fillRect(0, 0, width, HEIGHT);\r\n    ctx.save();\r\n    ctx.beginPath();\r\n    ctx.rect(0, 0, width, height / 2)\r\n    ctx.clip();\r\n    ctx.drawImage(img.path, 0, -height / 4, width, height);\r\n    ctx.restore();\r\n  }, [])\r\n\r\n  const drawAwards = useCallback((ctx: Taro.CanvasContext, currentHeight) => {\r\n    if (Array.isArray(awards) && awards.length) {\r\n      const len = awards.length;\r\n      const width = 60;\r\n      const height = 40;\r\n      const startX = (WIDTH - GAP * (len - 1)) / 2 - width / 2 * len;\r\n      awards.forEach((item, index) => {\r\n        // 均分距离 计算第一个的位置\r\n        const x = startX + index * (width + GAP);\r\n        ctx.font = `10px`\r\n        ctx.setFontSize(10)\r\n        ctx.setFillStyle(\"#000\");\r\n        // 奖金标题名称\r\n        var titleWidth = ctx?.measureText(item.title).width || 0;\r\n        ctx.fillText(item.title, x + (width - titleWidth) / 2, currentHeight)\r\n        // 奖励详情\r\n        const textValue = `现金${item.bonus}元`\r\n        const textValue2 = `${item.planBonus}元门店卡`\r\n        const fontSize = 8\r\n        ctx.font = `italic bold ${fontSize}px cursive`\r\n        ctx.setFontSize(fontSize)\r\n        const textValueWidth = ctx.measureText(textValue).width || 0\r\n        const textValue2Width = ctx.measureText(textValue2).width || 0\r\n        const textHeight = currentHeight + 20 + height / 2 - fontSize * 1.5\r\n        ctx.fillText(textValue, x + (width - textValueWidth) / 2, textHeight)\r\n        ctx.fillText(textValue2, x + (width - textValue2Width) / 2, textHeight + fontSize * 1.5)\r\n        // 当前奖励的边框\r\n        ctx.setStrokeStyle(BGCOLOR);\r\n        ctx.lineJoin = \"round\";\r\n        ctx.strokeRect(x, currentHeight + 10, width, height)\r\n      })\r\n\r\n    }\r\n  }, [awards])\r\n\r\n  const draw = useCallback(async () => {\r\n    if (ctx.current) {\r\n      const cover = await Taro.getImageInfo({ src: DEFAULT_COVER });\r\n      const coverHeight = cover.height * (WIDTH / cover.width);\r\n\r\n      // 绘制封面\r\n      await clip2Cover(ctx.current, cover, WIDTH, coverHeight)\r\n      let currentHeight = coverHeight / 2 + PADDING;\r\n      if (Array.isArray(content)) {\r\n        content.forEach((item) => {\r\n          const { fontWeight, value, fonSize, color } = item;\r\n          ctx.current?.setFontSize(fonSize);\r\n          if (ctx.current) {\r\n            ctx.current.font = `${fonSize}px`\r\n            ctx.current.font += ` ${fontWeight || 'normal'} Arial`;\r\n          }\r\n\r\n          ctx.current?.setFillStyle(color);\r\n          // 垂直居中\r\n          const lineHeight = currentHeight + fonSize * 1.5 / 2\r\n          if (item.center) {\r\n            var textWidth = ctx.current?.measureText(value).width || 0;\r\n            var x = (WIDTH / 2) - (textWidth / 2);\r\n\r\n            ctx.current?.fillText(value, x, lineHeight)\r\n          } else {\r\n            ctx.current?.fillText(value, PADDING, lineHeight)\r\n          }\r\n          currentHeight += fonSize * 1.5;\r\n        });\r\n      }\r\n      const QRCode = await Taro.getImageInfo({ src: URI });\r\n      const QRHeight = QRCode.height * (QRSIZE / QRCode.width);\r\n      var QRX = (WIDTH / 2) - (QRSIZE / 2);\r\n      ctx.current.drawImage(QRCode.path, QRX, currentHeight, QRSIZE, QRHeight);\r\n      drawAwards(ctx.current, currentHeight + QRHeight + PADDING)\r\n      ctx.current.draw(true, async () => {\r\n        const result = await Taro.canvasToTempFilePath({\r\n          canvasId: 'canvas',\r\n        })\r\n        tempFilePath.current = result.tempFilePath;\r\n      });\r\n\r\n\r\n    }\r\n  }, [clip2Cover, ctx.current, content])\r\n\r\n\r\n  const save = useCallback(async () => {\r\n    if (tempFilePath.current) {\r\n      await Taro.showShareImageMenu({\r\n        path: tempFilePath.current,\r\n      })\r\n    }\r\n  }, [])\r\n\r\n\r\n  useEffect(() => {\r\n    ctx.current = Taro.createCanvasContext(\"canvas\");\r\n    draw();\r\n  }, [])\r\n\r\n\r\n  return <>\r\n    <div id=\"poster\" className=\"flex justify-center overflow-hidden rounded-md mb-4\">\r\n      <canvas canvas-id=\"canvas\" width={`${WIDTH}px`} height={`${HEIGHT}px`}></canvas>\r\n    </div>\r\n    <Button\r\n      buttonText=\"保存至相册\"\r\n      mode={MODES.Dark}\r\n      onClick={() => save()}\r\n    />\r\n  </>\r\n\r\n\r\n}\r\n", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/detail/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/competition/detail/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}