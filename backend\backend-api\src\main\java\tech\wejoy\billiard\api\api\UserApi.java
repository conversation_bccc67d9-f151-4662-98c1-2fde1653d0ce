package tech.wejoy.billiard.api.api;

import com.congeer.core.exception.BaseException;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.ClientUserPhoneBo;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.api.dto.UpdateBirthDto;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.CouponBuyDto;
import tech.wejoy.billiard.common.dto.RechargeDto;
import tech.wejoy.billiard.common.entity.ClientUserMember;
import tech.wejoy.billiard.common.service.ClientUserService;
import tech.wejoy.billiard.common.service.MemberService;

import java.util.ArrayList;
import java.util.List;

@Path("/user")
@Tag(name = "用户相关")
@Produces(MediaType.APPLICATION_JSON)
@Authorized
@RequiredArgsConstructor
public class UserApi {

    private final ClientUserService clientUserService;

    private final MemberService memberService;

    @GET
    @Path("/wallet")
    @Operation(summary = "我的钱包")
    public WalletBo my() {
        Long userId = SecurityHolder.<Long, WxUserBo>session().getUserId();
        return clientUserService.getWalletByUserId(userId);
    }

    @GET
    @Path("/wallet/clubs")
    @Operation(summary = "单店钱包")
    public List<ClubWalletBo> clubWallets() {
        return clientUserService.getClubMembersByUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @GET
    @Path("/coupons")
    @Operation(summary = "我的优惠券")
    public List<CouponBo> coupons(@QueryParam("used") boolean used, @QueryParam("expired") boolean expired) {
        return clientUserService.getCouponsByUserId(SecurityHolder.<Long, WxUserBo>session().getUserId(), used, expired);
    }

    @GET
    @Path("/tickets")
    @Operation(summary = "我的第三方票据")
    public List<TicketBo> tickets() {
        return new ArrayList();
    }

    @GET
    @Path("/recharge/plans")
    @Operation(summary = "会员充值套餐")
    public List<MemberRechargePlanBo> rechargePlans() {
        WxUserBo user = SecurityHolder.<Long, WxUserBo>session().getUser();
        ClientUserMember member = clientUserService.getMemberByUserId(user.getId());
        int memberLevel = member == null ? 0 : member.getMemberLevel();
        return memberService.listMemberPlans(memberLevel);
    }

    @POST
    @Path("/recharge/member")
    @Operation(summary = "会员充值")
    public PayParamBo rechargeMember(RechargeDto dto) {
        WxUserBo user = SecurityHolder.<Long, WxUserBo>session().getUser();
        dto.setUserId(user.getId());
        if (dto.getPlanId() != null) {
            return clientUserService.prePlanMember(dto);
        } else if (dto.getAmount() != null) {
            return clientUserService.preRechargeMember(dto);
        }
        throw new BaseException("参数错误");
    }

    @POST
    @Path("/recharge/club")
    @Operation(summary = "门店充值")
    public PayParamBo rechargeClub(RechargeDto dto) {
        WxUserBo user = SecurityHolder.<Long, WxUserBo>session().getUser();
        return clientUserService.preRechargeClub(dto, user.getId());
    }

    @GET
    @Path("/phone")
    @Operation(summary = "获取用户手机号")
    public ClientUserPhoneBo getUserPhone() {
        Long userId = SecurityHolder.<Long, WxUserBo>session().getUserId();
        String phone = clientUserService.getUserPhone(userId);
        ClientUserPhoneBo clientUserPhoneBo = new ClientUserPhoneBo();
        clientUserPhoneBo.setPhone(phone);
        clientUserPhoneBo.setId(userId);
        return clientUserPhoneBo;
    }

    @PUT
    @Path("/{id}/birth")
    public void updateBirth(@PathParam("id") Long id, UpdateBirthDto dto) {
        clientUserService.updateBirth(id, dto.getBirth());
    }

    @POST
    @Path("/coupon")
    @Operation(summary = "购买优惠券")
    public PayParamBo buyCoupon(CouponBuyDto dto) {
        WxUserBo user = SecurityHolder.<Long, WxUserBo>session().getUser();
        dto.setUserId(user.getId());
        return clientUserService.buyCoupon(dto);
    }

}
