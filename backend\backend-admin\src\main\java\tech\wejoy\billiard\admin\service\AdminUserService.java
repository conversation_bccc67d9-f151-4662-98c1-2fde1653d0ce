package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.admin.bo.AdminRoleBo;
import tech.wejoy.billiard.admin.bo.AdminUserBo;
import tech.wejoy.billiard.admin.dto.AdminRoleDto;
import tech.wejoy.billiard.admin.dto.AdminUserDto;
import tech.wejoy.billiard.admin.enums.AdminPermissionEnum;
import tech.wejoy.billiard.admin.enums.BusinessPermissionEnum;
import tech.wejoy.billiard.common.dto.AdminRoleQueryDto;
import tech.wejoy.billiard.common.dto.AdminUserQueryDto;
import tech.wejoy.billiard.common.entity.AdminRole;
import tech.wejoy.billiard.common.entity.AdminUser;
import tech.wejoy.billiard.common.entity.AdminUserRoleRel;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.enums.PermissionTypeEnum;
import tech.wejoy.billiard.common.manager.AdminUserManager;
import tech.wejoy.billiard.common.manager.ClubManager;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@ApplicationScoped
@RequiredArgsConstructor
public class AdminUserService {

    private final AdminUserManager adminUserManager;

    private final ClubManager clubManager;

    public AdminUser fetchByUsername(String username) {
        return adminUserManager.fetchByUsername(username);
    }

    public AdminUserBo fetchLoginByUsername(String username) {
        AdminUser user = adminUserManager.fetchByUsername(username);
        return getUserBo(user, PermissionTypeEnum.ADMIN);
    }

    private AdminUserBo getUserBo(AdminUser user, PermissionTypeEnum type) {
        AdminUserBo ret = AdminUserBo.from(user);
        Long userId = user.getId();
        List<AdminRole> roles = adminUserManager.fetchRolesByUserId(userId);
        if (roles.stream().anyMatch(v -> v.getCode().equals("system"))) {
            if (type == PermissionTypeEnum.BUSINESS) {
                ret.setPermissions(getBusinessCodes());
            } else {
                ret.setPermissions(getAdminCodes());
            }
        } else if (roles.stream().anyMatch(v -> v.getCode().equals("business"))) {
            ret.setPermissions(getBusinessCodes());
        } else {
            ret.setRoles(roles.stream().map(AdminRole::getCode).toList());
            List<String> adminPermissions = adminUserManager.fetchPermissionByRoles(roles.stream().map(AdminRole::getCode).toList());
            adminPermissions = Lists.newArrayList(adminPermissions);
            adminPermissions.addAll(adminUserManager.fetchPermissionByUserId(userId, type));
            ret.setPermissions(adminPermissions);
        }
        List<Long> clubIds = adminUserManager.fetchClubIdsByUserId(userId);
        if (CollectionUtils.isNotEmpty(clubIds)) {
            ret.setClubIds(clubIds);
        }
        if (StringUtils.isBlank(user.getPhone())) {
            Set<String> permissions = Sets.newHashSet();
            for (String permission : ret.getPermissions()) {
                if (permission.endsWith(":write")) {
                    permissions.add(permission.replace(":write", ":read"));
                } else {
                    permissions.add(permission);
                }
            }
            ret.setPermissions(new ArrayList<>(permissions));
        }
        return ret;
    }

    private static List<String> getBusinessCodes() {
        List<String> permissions = Stream.of(BusinessPermissionEnum.values()).map(BusinessPermissionEnum::getCode).toList();
        List<String> permissionCodes = Lists.newArrayList();
        for (String permission : permissions) {
            permissionCodes.add(permission + ":write");
        }
        return permissionCodes;
    }

    private static List<String> getAdminCodes() {
        return Stream.of(AdminPermissionEnum.values()).map(AdminPermissionEnum::getCode).toList();
    }

    public void changePassword(Long userId, String oldPassword, String newPassword) {
        AdminUser adminUser = adminUserManager.fetchById(userId);
        if (!adminUser.getPassword().equals(oldPassword)) {
            throw new RuntimeException("旧密码错误");
        }
        adminUser.setPassword(newPassword);
        adminUserManager.save(adminUser);
    }

    public AdminUser getUserByOpenId(String openId) {
        return adminUserManager.getUserByOpenId(openId);
    }

    public AdminUserBo loginByOpenId(String openId) {
        AdminUser user = adminUserManager.getUserByOpenId(openId);
        if (user == null) {
            throw new BaseException("登录失败");
        }
        return getUserBo(user, PermissionTypeEnum.BUSINESS);
    }

    public void save(AdminUser user) {
        adminUserManager.save(user);
    }

    public void createTenantAdmin(Long tenantId, String username) {
        AdminUser adminUser = new AdminUser();
        adminUser.setTenantId(tenantId);
        adminUser.setClubId(0L);
        AdminUser exist = adminUserManager.fetchByUsername(username);
        if (exist != null) {
            throw new BaseException("已存在相同用户名用户");
        }
        adminUser.setUsername(username);
        adminUser.setPassword(SecurityHolder.context().encryptPassword("xxqs@123"));
        adminUserManager.save(adminUser);
    }

    public void createClubUser(Long tenantId, Long clubId, String username) {
        AdminUser adminUser = new AdminUser();
        adminUser.setTenantId(tenantId);
        adminUser.setClubId(clubId);
        AdminUser exist = adminUserManager.fetchByUsername(username);
        if (exist != null) {
            throw new BaseException("已存在相同用户名用户");
        }
        adminUser.setUsername(username);
        adminUser.setPassword(SecurityHolder.context().encryptPassword("xxqs@123"));
        adminUserManager.save(adminUser);
    }

    public AdminUser createTenantAdmin(Long tenantId) {
        AdminUser adminUser = new AdminUser();
        adminUser.setTenantId(tenantId);
        adminUser.setClubId(0L);
        adminUser.setUsername(UUID.randomUUID().toString().replace("-", ""));
        adminUser.setPassword(SecurityHolder.context().encryptPassword("xxqs@123"));
        adminUserManager.save(adminUser);
        return adminUser;
    }

    public AdminUser getUserById(Long id) {
        return adminUserManager.fetchById(id);
    }

    public AdminUserBo addAdmin(AdminUserDto enter) {
        AdminUserBo current = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (enter.getClubId() != null) {
            ClubInfo clubInfo = clubManager.fetchClubById(enter.getClubId());
            if (clubInfo == null) {
                throw new BaseException("门店不存在");
            }
            enter.setTenantId(clubInfo.getTenantId());
        }
        if (current.getTenantId() != 0) {
            enter.setTenantId(current.getTenantId());
        }
        checkAuth(enter.getTenantId(), enter.getClubId());
        AdminUser exist = adminUserManager.fetchByUsername(enter.getUsername());
        if (exist != null) {
            throw new BaseException("已存在相同用户名用户");
        }
        exist = adminUserManager.fetchByPhone(enter.getPhone());
        if (exist != null) {
            throw new BaseException("已存在相同手机号用户");
        }
        AdminUser user = new AdminUser();
        user.setUsername(enter.getUsername());
        user.setPhone(enter.getPhone());
        if (StringUtils.isBlank(user.getPassword())) {
            user.setPassword(SecurityHolder.context().encryptPassword("xxqs@123"));
        } else {
            user.setPassword(SecurityHolder.context().encryptPassword(user.getPassword()));
        }
        user.setTenantId(enter.getTenantId());
        user.setClubId(enter.getClubId());
        if (enter.getRoleId() != null) {
            if (enter.getRoleIds() == null) {
                enter.setRoleIds(Lists.newArrayList(enter.getRoleId()));
            } else {
                enter.getRoleIds().add(enter.getRoleId());
            }
        }
        if (enter.getClubId() != null) {
            if (enter.getClubIds() == null) {
                enter.setClubIds(Lists.newArrayList(enter.getClubId()));
            } else {
                enter.getClubIds().add(enter.getClubId());
            }
        }
        adminUserManager.save(user);
        if (CollectionUtils.isNotEmpty(enter.getRoleIds())) {
            List<AdminRole> roles = adminUserManager.getRoleByIds(enter.getRoleIds());
            adminUserManager.saveUserRole(user.getId(), roles.stream().map(AdminRole::getCode).toList());
        }
        if (CollectionUtils.isNotEmpty(enter.getClubIds())) {
            user.setClubId(enter.getClubIds().getFirst());
            adminUserManager.saveUserClub(user.getId(), enter.getClubIds());
        }
        AdminUserBo from = AdminUserBo.from(user);
        from.setRoleIds(enter.getRoleIds());
        from.setClubIds(enter.getClubIds());
        return from.clearSensitive();
    }

    private void checkAuth(Long tenantId) {
        AdminUserBo current = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (current.getTenantId() != 0 && !current.getTenantId().equals(tenantId)) {
            throw new BaseException("无权限");
        }
    }

    private void checkAuth(Long tenantId, Long clubId) {
        AdminUserBo current = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (current.getTenantId() != 0 && !current.getTenantId().equals(tenantId)) {
            throw new BaseException("无权限");
        }
        if ((clubId == null || clubId == 0) && current.getTenantId() != 0) {
            throw new BaseException("无权限");
        }
        if (clubId != null && clubId != 0) {
            ClubInfo club = clubManager.fetchClubById(clubId);
            if (club == null) {
                throw new BaseException("门店不存在");
            }
            if (!club.getTenantId().equals(tenantId) && tenantId != 0) {
                throw new BaseException("无权限");
            }
        }
    }

    public AdminUser fetchByPhone(String phoneNumber) {
        return adminUserManager.fetchByPhone(phoneNumber);
    }

    public Page<AdminUserBo> list(AdminUserQueryDto dto) {
        AdminUserBo current = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (current.getTenantId() != 0) {
            dto.setTenantId(current.getTenantId());
        }
        PermissionTypeEnum type = null;
        if (SecurityHolder.session().getName().equals("business")) {
            type = PermissionTypeEnum.BUSINESS;
        }
        Page<AdminUser> page = adminUserManager.page(dto);
        List<Long> userIds = page.getRecords().stream().map(BaseEntity::getId).toList();
        List<AdminUserRoleRel> relList = adminUserManager.fetchUserRoleRelByUserIds(userIds);
        Map<Long, List<AdminUserRoleRel>> relGroup = relList.stream().collect(Collectors.groupingBy(AdminUserRoleRel::getUserId));
        List<AdminRole> roles = adminUserManager.fetchRolesByUserIds(userIds, type);
        Map<String, AdminRole> roleMap = roles.stream().collect(Collectors.toMap(AdminRole::getCode, v -> v));

        Page<AdminUserBo> convert = page.convert(v -> AdminUserBo.from(v).clearSensitive());
        convert.getRecords().forEach(v -> {
            List<AdminUserRoleRel> userRoleRels = relGroup.get(v.getId());
            if (CollectionUtils.isNotEmpty(userRoleRels)) {
                List<String> roleCodes = userRoleRels.stream().map(AdminUserRoleRel::getRole).toList();
                v.setRoles(roleCodes);
                List<AdminRole> userRoles = roleCodes.stream().map(roleMap::get).filter(Objects::nonNull).toList();
                v.setRoleNames(userRoles.stream().map(AdminRole::getName).toList());
                v.setRoleIds(userRoles.stream().map(AdminRole::getId).toList());
            } else {
                v.setRoles(Lists.newArrayList());
                v.setRoleNames(Lists.newArrayList());
            }
        });
        return convert;
    }

    public void initRole() {
        AdminRole system = adminUserManager.fetchRoleByCode("system");
        if (system == null) {
            system = new AdminRole();
            system.setCode("system");
            system.setName("系统管理员");
            system.setDescription("系统管理员");
            system.setTenantId(0L);
            system.setType(PermissionTypeEnum.ADMIN);
            adminUserManager.saveRole(system);
        }
        AdminRole business = adminUserManager.fetchRoleByCode("business");
        if (business == null) {
            business = new AdminRole();
            business.setCode("business");
            business.setName("加盟商管理员");
            business.setDescription("加盟商管理员");
            business.setTenantId(0L);
            business.setType(PermissionTypeEnum.BUSINESS);
            adminUserManager.saveRole(business);
        }
    }

    public AdminUserBo saveAdmin(AdminUserDto enter) {
        AdminUser user = adminUserManager.fetchById(enter.getId());
        checkAuth(user.getTenantId(), user.getClubId());
        if (enter.getRoleId() != null) {
            if (enter.getRoleIds() == null) {
                enter.setRoleIds(Lists.newArrayList(enter.getRoleId()));
            } else {
                enter.getRoleIds().add(enter.getRoleId());
            }
        }
        if (enter.getClubId() != null) {
            if (enter.getClubIds() == null) {
                enter.setClubIds(Lists.newArrayList(enter.getClubId()));
            } else {
                enter.getClubIds().add(enter.getClubId());
            }
        }
        if (CollectionUtils.isNotEmpty(enter.getRoleIds())) {
            adminUserManager.deleteUserRoleRelByUserId(user.getId());
            List<AdminRole> roles = adminUserManager.getRoleByIds(enter.getRoleIds());
            adminUserManager.saveUserRole(user.getId(), roles.stream().map(AdminRole::getCode).toList());
        }
        if (CollectionUtils.isNotEmpty(enter.getClubIds())) {
            adminUserManager.deleteUserClubRelByUserId(user.getId());
            user.setClubId(enter.getClubIds().getFirst());
            adminUserManager.saveUserClub(user.getId(), enter.getClubIds());
        }
        adminUserManager.save(user);
        AdminUserBo from = AdminUserBo.from(user);
        from.setRoleIds(enter.getRoleIds());
        return from.clearSensitive();
    }

    public Page<AdminRoleBo> roleList(AdminRoleQueryDto dto) {
        AdminUserBo current = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (current.getTenantId() != 0) {
            dto.setTenantId(current.getTenantId());
        }
        if (dto.getType() == null) {
            if (SecurityHolder.session().getName().equals("business")) {
                dto.setType(PermissionTypeEnum.BUSINESS);
            }
        }
        Page<AdminRole> page = adminUserManager.pageRole(dto);
        return page.convert(v -> BeanUtils.copy(v, AdminRoleBo.class));
    }

    public AdminRoleBo addRole(AdminRoleDto enter) {
        AdminUserBo current = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (current.getTenantId() != 0 || enter.getTenantId() == null) {
            enter.setTenantId(current.getTenantId());
        }
        if (enter.getType() == null) {
            PermissionTypeEnum type = PermissionTypeEnum.ADMIN;
            if (SecurityHolder.session().getName().equals("business")) {
                type = PermissionTypeEnum.BUSINESS;
            }
            enter.setType(type);
        }
        checkAuth(enter.getTenantId());
        AdminRole role = BeanUtils.copy(enter, AdminRole.class);
        role.setTenantId(enter.getTenantId());
        if (StringUtils.isBlank(role.getCode())) {
            role.setCode(UUID.randomUUID().toString().replace("-", ""));
        }
        adminUserManager.saveRole(role);
        if (CollectionUtils.isNotEmpty(enter.getPermissions())) {
            enter.setPermissions(enter.getPermissions().stream().distinct().toList());
            adminUserManager.saveRolePermission(role.getCode(), enter.getPermissions());
        }
        AdminRoleBo copy = BeanUtils.copy(role, AdminRoleBo.class);
        copy.setPermissions(enter.getPermissions());
        return copy;
    }

    public void deleteAdmin(Long id) {
        AdminUser user = adminUserManager.fetchById(id);
        checkAuth(user.getTenantId(), user.getClubId());
        adminUserManager.deleteAdmin(id);
        adminUserManager.deleteUserRoleRelByUserId(id);
        adminUserManager.deleteUserClubRelByUserId(id);
        adminUserManager.deleteUserPermissionRelByUserId(id);
    }

    public void deleteRole(Long id) {
        AdminRole role = adminUserManager.fetchRoleById(id);
        if ("system".equals(role.getCode()) || "business".equals(role.getCode())) {
            throw new BaseException("系统角色不可修改");
        }
        checkAuth(role.getTenantId());
        List<AdminUserRoleRel> users = adminUserManager.fetchUserByRole(role.getCode());
        if (CollectionUtils.isNotEmpty(users)) {
            throw new BaseException("角色下存在用户");
        }
        adminUserManager.deleteRole(id);
        adminUserManager.deleteRolePermission(role.getCode());
    }

    public AdminRoleBo saveRole(AdminRoleDto enter) {
        AdminRole role = adminUserManager.fetchRoleById(enter.getId());
        if ("system".equals(role.getCode()) || "business".equals(role.getCode())) {
            throw new BaseException("系统角色不可修改");
        }
        checkAuth(role.getTenantId());
        if (StringUtils.isNotBlank(enter.getName())) {
            role.setName(enter.getName());
        }
        adminUserManager.saveRole(role);
        if (CollectionUtils.isNotEmpty(enter.getPermissions())) {
            enter.setPermissions(enter.getPermissions().stream().distinct().toList());
            adminUserManager.saveRolePermission(role.getCode(), enter.getPermissions());
        }
        AdminRoleBo copy = BeanUtils.copy(role, AdminRoleBo.class);
        copy.setPermissions(enter.getPermissions());
        return copy;
    }

    public AdminUserBo detail(Long id) {
        AdminUser user = adminUserManager.fetchById(id);
        if (user == null) {
            return null;
        }
        checkAuth(user.getTenantId(), user.getClubId());
        AdminUserBo from = AdminUserBo.from(user);
        List<AdminRole> roles = adminUserManager.fetchRolesByUserId(id);
        from.setRoleIds(roles.stream().map(AdminRole::getId).toList());
        from.setRoles(roles.stream().map(AdminRole::getCode).toList());
        List<Long> clubIds = adminUserManager.fetchClubIdsByUserId(id);
        from.setClubIds(clubIds);
        return from.clearSensitive();
    }

    public AdminRoleBo roleDetail(Long id) {
        AdminRole role = adminUserManager.fetchRoleById(id);
        if (role == null) {
            return null;
        }
        checkAuth(role.getTenantId());
        AdminRoleBo copy = BeanUtils.copy(role, AdminRoleBo.class);
        List<String> permissions = adminUserManager.fetchPermissionByRoles(List.of(role.getCode()));
        copy.setPermissions(permissions);
        return copy;
    }

    public void saveUserRole(AdminUserRoleRel roleRel) {
        adminUserManager.saveUserRoleRel(roleRel);
    }

    public List<AdminRoleBo> roleOptions() {
        AdminUserBo current = SecurityHolder.<Long, AdminUserBo>session().getUser();
        PermissionTypeEnum type = null;
        if (SecurityHolder.session().getName().equals("business")) {
            type = PermissionTypeEnum.BUSINESS;
        }
        List<AdminRole> roles = adminUserManager.fetchRolesByTenantId(current.getTenantId(), type);
        return roles.stream().map(v -> BeanUtils.copy(v, AdminRoleBo.class)).toList();
    }

}
