package tech.wejoy.billiard.api.api;

import com.congeer.core.exception.BaseException;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.utils.JsonUtils;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.WxLoginBo;
import tech.wejoy.billiard.api.service.BillService;
import tech.wejoy.billiard.api.service.WxUserService;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Path("/wx")
@Tag(name = "微信相关")
@Produces(MediaType.APPLICATION_JSON)
@Slf4j
@RequiredArgsConstructor
public class WxApi {

    private final WxUserService userService;

    private final BillService billService;

    @POST
    @Path("/login")
    @Operation(summary = "微信登录")
    public WxLoginBo login(@QueryParam("code") String code) {
        if (StringUtils.isBlank(code)) {
            throw new BaseException("参数异常");
        }
        return userService.login(code);
    }

    @POST
    @Path("/phone")
    @Operation(summary = "更新手机号")
    @Authorized
    public void updatePhone(@QueryParam("code") String code) {
        if (StringUtils.isBlank(code)) {
            throw new BaseException("参数异常");
        }
        userService.updatePhone(code);
    }

    @POST
    @Path("/pay/callback")
    public void payCallback(String body,
                            @HeaderParam("Wechatpay-Serial") String serial,
                            @HeaderParam("Wechatpay-Timestamp") String timestamp,
                            @HeaderParam("Wechatpay-Signature") String signature,
                            @HeaderParam("Wechatpay-Nonce") String nonce) {
        log.info(JsonUtils.toJson(body));
        log.info("serial: {}", serial);
        log.info("timestamp: {}", timestamp);
        log.info("signature: {}", signature);
        log.info("nonce: {}", nonce);
        WxPayService payService = ThirdServiceHolder.wxPayService("user");
        SignatureHeader header = new SignatureHeader();
        header.setSerial(serial);
        header.setTimeStamp(timestamp);
        header.setSignature(signature);
        header.setNonce(nonce);
        try {
            WxPayNotifyV3Result result = payService.parseOrderNotifyV3Result(body, header);
            log.info(JsonUtils.toJson(result));
            String outTradeNo = result.getResult().getOutTradeNo();
            if (!result.getResult().getAmount().getCurrency().equals("CNY") || !result.getResult().getAmount().getPayerCurrency().equals("CNY")) {
                log.error("支付币种异常");
                throw new BaseException("支付币种异常");
            }
            if ("SUCCESS".equals(result.getResult().getTradeState())) {
                BigDecimal total = new BigDecimal(result.getResult().getAmount().getTotal()).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
                LocalDateTime payTime = LocalDateTime.parse(result.getResult().getSuccessTime(), dtf);
                billService.successBill(result.getResult().getTransactionId(), outTradeNo, total, total, payTime, JsonUtils.toJson(result));
            }
        } catch (WxPayException e) {
            try {
                WxPayRefundNotifyV3Result result = payService.parseRefundNotifyV3Result(body, header);
                log.info(JsonUtils.toJson(result));
                String outTradeNo = result.getResult().getOutTradeNo();
                if ("SUCCESS".equals(result.getResult().getRefundStatus())) {
                    BigDecimal total = new BigDecimal(result.getResult().getAmount().getRefund()).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                    BigDecimal payerTotal = new BigDecimal(result.getResult().getAmount().getPayerRefund()).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                    billService.refundBill(outTradeNo, total, payerTotal, JsonUtils.toJson(result));
                }
            } catch (WxPayException e2) {
                log.error("支付回调异常", e2);
                throw new BaseException("支付回调异常");
            }
        }
    }

}
