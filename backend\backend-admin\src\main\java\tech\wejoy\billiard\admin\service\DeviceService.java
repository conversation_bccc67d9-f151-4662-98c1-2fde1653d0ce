package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.DateUtils;
import com.congeer.utils.JsonUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.admin.bo.*;
import tech.wejoy.billiard.admin.dto.*;
import tech.wejoy.billiard.admin.third.DeviceHandler;
import tech.wejoy.billiard.common.dto.DeviceOperationDto;
import tech.wejoy.billiard.common.dto.DeviceQueryDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.*;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.DeviceManager;
import tech.wejoy.billiard.common.manager.OrderManager;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;
import tech.wejoy.billiard.common.third.aqara.client.AqaraClient;
import tech.wejoy.billiard.common.third.aqara.response.AqaraDevice;
import tech.wejoy.billiard.common.third.aqara.response.AqaraResource;
import tech.wejoy.billiard.common.third.aqara.response.TokenResponse;
import tech.wejoy.billiard.common.third.smyoo.client.SmyooClient;
import tech.wejoy.billiard.common.third.smyoo.request.DeviceRequest;
import tech.wejoy.billiard.common.third.smyoo.request.DeviceSetRequest;
import tech.wejoy.billiard.common.third.smyoo.response.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@ApplicationScoped
@RequiredArgsConstructor
public class DeviceService {

    private final DeviceManager deviceManager;

    private final ClubManager clubManager;

    private final OrderManager orderManager;

    public void bindAqara(Long tenantId, String account, String code) {
        TenantAqaraAccount exist = deviceManager.fetchAccountByAccount(account);
        if (exist != null) {
            throw new BaseException("账号已存在");
        }
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        AqaraClient base = ThirdServiceHolder.aqaraClient("base");
        TokenResponse token = base.getToken(code, account, 0);
        TenantAqaraAccount accountInfo = new TenantAqaraAccount();
        accountInfo.setTenantId(tenantId);
        accountInfo.setUserId(user.getId());
        accountInfo.setAccount(account);
        accountInfo.setOpenId(token.getOpenId());
        accountInfo.setAccessToken(token.getAccessToken());
        accountInfo.setRefreshToken(token.getRefreshToken());
        accountInfo.setExpiresIn(token.getExpiresIn());
        accountInfo.setExpireAt(LocalDateTime.now().plusSeconds(Integer.parseInt(token.getExpiresIn()) - 60));
        deviceManager.saveAqaraAccount(accountInfo);
    }

    @Transactional(rollbackOn = Exception.class)
    public void handleOrderDevice(DeviceOperationDto dto) {
        switch (dto.getType()) {
            case START -> start(dto.getOrderNo());
            case STOP -> stop(dto.getOrderNo());
            case WARN -> warn(dto.getOrderNo());
        }
    }

    public void start(String orderNo) {
        OrderInfo order = orderManager.fetchOrderByNo(orderNo);
        if (order == null) {
            return;
        }
        if (order.getStatus() != OrderStatusEnum.PAID) {
            log.warn("订单状态不正确,无法开始:{}", JsonUtils.toJson(order));
            return;
        }
        List<ClubTableDeviceRel> deviceRelList = clubManager.fetchDeviceByTableId(order.getTableId());
        List<Long> deviceIds = deviceRelList.stream().map(ClubTableDeviceRel::getDeviceId).toList();
        List<TenantDevice> devices = deviceManager.getDeviceListByIds(deviceIds);
        for (TenantDevice device : devices) {
            if (device.getAuto() == IsEnum.FALSE) {
                continue;
            }
            DeviceHandler handler = DeviceHandler.getHandler(device.getBrand(), device.getType());
            if (handler == null) {
                continue;
            }
            log.info("开始设备:{},{}", device.getId(), device.getName());
            handler.start(device);
        }
        order.setStatus(OrderStatusEnum.USING);
        order.setRealStartTime(LocalDateTime.now());
        orderManager.save(order);
        clubManager.updateTableUsing(order.getTableId(), order.getUserId(), order.getEndTime());
    }

    public void stop(String orderNo) {
        OrderInfo order = orderManager.fetchOrderByNo(orderNo);
        if (order == null) {
            return;
        }
        if (order.getStatus() != OrderStatusEnum.USING && order.getStatus() != OrderStatusEnum.FINISH) {
            log.warn("订单状态不正确,无法完成:{}", JsonUtils.toJson(order));
            return;
        }
        if (order.getRealEndTime() != null) {
            log.warn("订单已经完成:{}, {}", JsonUtils.toJson(order), order.getRealEndTime());
            return;
        }
        List<ClubTableDeviceRel> deviceRelList = clubManager.fetchDeviceByTableId(order.getTableId());
        List<Long> deviceIds = deviceRelList.stream().map(ClubTableDeviceRel::getDeviceId).toList();
        List<TenantDevice> devices = deviceManager.getDeviceListByIds(deviceIds);
        for (TenantDevice device : devices) {
            if (device.getAuto() == IsEnum.FALSE) {
                continue;
            }
            DeviceHandler handler = DeviceHandler.getHandler(device.getBrand(), device.getType());
            if (handler == null) {
                continue;
            }
            if (device.getType() == DeviceTypeEnum.AIR_CONDITIONER) {
                List<ClubTableDeviceRel> relList = clubManager.fetchDevicesRelByDeviceId(device.getId());
                if (relList.isEmpty()) {
                    continue;
                }
                List<Long> tableIds = relList.stream().map(ClubTableDeviceRel::getTableId).toList();
                List<ClubTable> tables = clubManager.fetchTableByIds(tableIds);
                if (tables.isEmpty()) {
                    continue;
                }
                if (tables.stream().anyMatch(v -> v.getStatus() == TableStatusEnum.USING)) {
                    continue;
                }
            }
            log.info("关闭设备:{},{}", device.getId(), device.getName());
            handler.stop(device);
        }
        order.setStatus(OrderStatusEnum.FINISH);
        order.setRealEndTime(LocalDateTime.now());
        orderManager.save(order);
        clubManager.updateTableIdle(order.getTableId());
    }

    public void warn(String orderNo) {
        OrderInfo order = orderManager.fetchOrderByNo(orderNo);
        if (order.getStatus() != OrderStatusEnum.USING) {
            log.warn("订单状态不正确,无法警告:{}", JsonUtils.toJson(order));
            return;
        }
        List<ClubTableDeviceRel> deviceRelList = clubManager.fetchDeviceByTableId(order.getTableId());
        List<Long> deviceIds = deviceRelList.stream().map(ClubTableDeviceRel::getDeviceId).toList();
        List<TenantDevice> devices = deviceManager.getDeviceListByIds(deviceIds);
        for (TenantDevice device : devices) {
            if (device.getAuto() == IsEnum.FALSE) {
                continue;
            }
            DeviceHandler handler = DeviceHandler.getHandler(device.getBrand(), device.getType());
            if (handler == null) {
                continue;
            }
            handler.warn(device);
        }
    }

    public Page<DeviceBo> list(DeviceQueryDto dto) {
        Page<TenantDevice> tenantDevicePage = deviceManager.fetchDeviceList(dto);
        return null;
    }

    @Transactional(rollbackOn = Exception.class)
    public void refreshAqaraDevice(Long id) {
        TenantAqaraAccount account = deviceManager.getAqaraAccount(id);
        if (account == null) {
            throw new BaseException("账号不存在");
        }
        AqaraClient base = ThirdServiceHolder.aqaraClient("base");
        List<AqaraDevice> aqaraDevices = base.queryDeviceList(account);
        List<TenantDevice> devices = deviceManager.fetchDeviceByAccountAndBrand(account.getId(), DeviceBrandEnum.AQARA);
        Set<String> supportRes = Sets.newHashSet("4.1.85", "4.2.85", "4.3.85", "4.4.85");

        Set<Long> removeIds = devices.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        Map<String, TenantDevice> deviceMap = devices.stream().collect(Collectors.toMap(v -> v.getDeviceId() + "/" + v.getResourceId(), Function.identity()));
        Map<String, List<AqaraResource>> cache = new HashMap<>();
        Map<String, TenantDevice> gatewayMap = new HashMap<>();
        for (AqaraDevice aqaraDevice : aqaraDevices) {
            if (aqaraDevice.getModel().startsWith("lumi.gateway")) {
                TenantDevice device = deviceMap.get(aqaraDevice.getDid() + "/null");
                if (device == null) {
                    device = new TenantDevice();
                    device.setBrand(DeviceBrandEnum.AQARA);
                    device.setType(DeviceTypeEnum.GATEWAY);
                    device.setOpen(IsEnum.FALSE);
                    device.setDeviceId(aqaraDevice.getDid());
                    device.setAuto(IsEnum.TRUE);
                } else {
                    removeIds.remove(device.getId());
                }
                device.setTenantId(account.getTenantId());
                device.setAccountId(account.getId());
                device.setName(aqaraDevice.getDeviceName());
                device.setSourceInfo(JsonUtils.toJson(aqaraDevice));
                device.setParentId(0L);
                device.setOnline(aqaraDevice.getState() == 1 ? IsEnum.TRUE : IsEnum.FALSE);
                deviceManager.saveDevice(device);
                gatewayMap.put(aqaraDevice.getDid(), device);
            }
        }
        for (AqaraDevice aqaraDevice : aqaraDevices) {
            List<AqaraResource> resources = cache.get(aqaraDevice.getModel());
            if (resources == null) {
                resources = base.queryResourceList(account, aqaraDevice.getModel());
                cache.put(aqaraDevice.getModel(), resources);
            }
            for (AqaraResource resource : resources) {
                if (!supportRes.contains(resource.getResourceId())) {
                    continue;
                }
                TenantDevice device = deviceMap.get(aqaraDevice.getDid() + "/" + resource.getResourceId());
                if (device == null) {
                    device = new TenantDevice();
                    device.setBrand(DeviceBrandEnum.AQARA);
                    device.setType(aqaraDevice.getModelType() == 3 ? DeviceTypeEnum.LIGHT : DeviceTypeEnum.GATEWAY);
                    device.setOpen(IsEnum.FALSE);
                    device.setDeviceId(aqaraDevice.getDid());
                    device.setResourceId(resource.getResourceId());
                    device.setAuto(IsEnum.TRUE);
                } else {
                    removeIds.remove(device.getId());
                }
                TenantDevice parent = gatewayMap.get(aqaraDevice.getParentDid());
                if (parent != null) {
                    device.setParentId(parent.getId());
                } else {
                    device.setParentId(0L);
                }
                device.setTenantId(account.getTenantId());
                device.setAccountId(account.getId());
                device.setName(aqaraDevice.getDeviceName() + "-" + resource.getName());
                device.setSourceInfo(JsonUtils.toJson(aqaraDevice));
                device.setOnline(aqaraDevice.getState() == 1 ? IsEnum.TRUE : IsEnum.FALSE);
                deviceManager.saveDevice(device);
            }
        }
        deviceManager.removeDeviceByIds(removeIds);
        clubManager.removeDeviceRelByDeviceIds(removeIds);
    }

    public Page<AqaraAccountBo> aqaraAccountList(AqaraAccountQueryDto dto) {
        Page<TenantAqaraAccount> page = deviceManager.getAqaraAccountList(dto.getTenantId(), dto.getAccount(), dto);
        return page.convert(v -> {
            AqaraAccountBo bo = new AqaraAccountBo();
            bo.setId(v.getId());
            bo.setAccount(v.getAccount());
            bo.setTenantId(v.getTenantId());
            return bo;
        });
    }

    public List<DeviceBo> listByAccount(Long accountId, DeviceBrandEnum brand) {
        List<TenantDevice> devices = deviceManager.fetchDeviceByAccountAndBrand(accountId, brand);
        devices.stream().filter(v -> v.getParentId() == 0).forEach(v -> v.setParentId(v.getId()));
        devices.sort((d1, d2) -> {
            int i = d1.getParentId().compareTo(d2.getParentId());
            if (i == 0) {
                if (d1.getId().equals(d1.getParentId())) {
                    return -1;
                }
                if (d2.getId().equals(d2.getParentId())) {
                    return 1;
                }
                return d1.getId().compareTo(d2.getId());
            }
            return i;
        });

        List<Long> deviceIds = devices.stream().map(BaseEntity::getId).toList();
        List<ClubTableDeviceRel> relList = clubManager.fetchDeviceRelByDeviceIds(deviceIds);
        Map<Long, List<ClubTableDeviceRel>> relGroup = relList.stream().collect(Collectors.groupingBy(ClubTableDeviceRel::getDeviceId));
        Map<Long, TenantDevice> deviceMap = devices.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        return devices.stream().map(v -> {
            DeviceBo bo = new DeviceBo();
            bo.setId(v.getId());
            bo.setName(v.getName());
            bo.setBrand(v.getBrand());
            bo.setType(v.getType());
            bo.setOpen(v.getOpen());
            bo.setOnline(v.getOnline());
            bo.setParentId(v.getParentId());
            if (v.getParentId() != 0) {
                bo.setParentName(deviceMap.get(v.getParentId()).getName());
            }
            bo.setLinked(relGroup.containsKey(v.getId()));
            return bo;
        }).toList();
    }

    public DeviceBo detail(Long id) {
        TenantDevice device = deviceManager.fetchById(id);
        List<ClubTableDeviceRel> relList = clubManager.fetchDeviceRelByDeviceIds(Lists.newArrayList(device.getId()));
        DeviceBo bo = new DeviceBo();
        bo.setId(device.getId());
        bo.setName(device.getName());
        bo.setBrand(device.getBrand());
        bo.setType(device.getType());
        bo.setOpen(device.getOpen());
        bo.setOnline(device.getOnline());
        bo.setLinked(!relList.isEmpty());
        List<Long> clubIds = relList.stream().map(ClubTableDeviceRel::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        List<Long> tableIds = relList.stream().map(ClubTableDeviceRel::getTableId).toList();
        List<ClubTable> tables = clubManager.fetchTableByIds(tableIds);
        Map<Long, ClubTable> tableMap = tables.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        bo.setRel(relList.stream().map(v -> {
            DeviceRelBo relBo = new DeviceRelBo();
            relBo.setTableId(v.getTableId());
            relBo.setDeviceId(v.getDeviceId());
            relBo.setClubName(clubMap.get(v.getClubId()).getName());
            relBo.setClubId(v.getClubId());
            relBo.setTableName(tableMap.get(v.getTableId()).getName());
            return relBo;
        }).toList());
        return bo;
    }

    public void aqaraCallback(List<AqaraCallbackDto.Resource> data) {
        if (data == null) {
            return;
        }
        for (AqaraCallbackDto.Resource resource : data) {
            TenantDevice device = deviceManager.fetchByDeviceIdAndResourceId(resource.getSubjectId(), resource.getResourceId());
            if (device == null) {
                continue;
            }
            log.info("aqara callback: {}", JsonUtils.toJson(resource));
            DeviceHandler handler = DeviceHandler.getHandler(device.getBrand(), device.getType());
            if (handler == null) {
                continue;
            }
            if (device.getType() == DeviceTypeEnum.LIGHT) {
                ClubTableDeviceRel rel = clubManager.fetchDeviceRelByDeviceId(device.getId());
                if (rel == null) {
                    continue;
                }
                ClubTable clubTable = clubManager.fetchClubTableById(rel.getTableId());
                if (clubTable == null) {
                    continue;
                }
                if (clubTable.getStatus() == TableStatusEnum.USING && "0".equals(resource.getValue())) {
                    log.info("自动打开设备:{},{}", clubTable.getId(), device.getName());
                    //                    handler.start(device);
                } else if (clubTable.getStatus() == TableStatusEnum.IDLE && "1".equals(resource.getValue())) {
                    log.info("自动关闭设备:{},{}", clubTable.getId(), device.getName());
                    //                    handler.stop(device);
                }
            }
        }
    }

    public void open(DeviceOperateDto dto) {
        TenantDevice device = deviceManager.fetchById(dto.getId());
        if (device == null) {
            throw new BaseException("设备不存在");
        }
        DeviceHandler handler = DeviceHandler.getHandler(device.getBrand(), device.getType());
        if (handler == null) {
            throw new BaseException("设备不支持");
        }
        if (dto.isOpen()) {
            handler.start(device);
        } else {
            handler.stop(device);
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void bindSmyoo(Long tenantId, String phone, String password) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        SmyooClient client = ThirdServiceHolder.smyooClient("base");
        TenantSmyooAccount account = new TenantSmyooAccount();
        account.setTenantId(tenantId);
        account.setUserId(user.getId());
        account.setPhone(phone);
        account.setPassword(password);
        LoginOpenResponse login = client.loginOpen(account);
        LoginTicketResponse loginTicketResponse = client.loginTicket(login.getTicket());
        account.setSessionId(loginTicketResponse.getSessionId());
        account.setExpiresIn(login.getCookieExpireTime() + "");
        account.setExpireAt(LocalDateTime.now().plusHours(2));
        deviceManager.saveSmyooAccount(account);
    }


    public void refreshSmyooDevice(Long id) {
        SmyooClient client = ThirdServiceHolder.smyooClient("base");
        TenantSmyooAccount account = deviceManager.getSmyooAccount(id);
        StatusChangeResponse statusChangeResponse = client.statusChanged(account);
        LocalDateTime localDateTime = DateUtils.getLocalDateTime(statusChangeResponse.getDeviceUpdateTime(), "yyyy-MM-dd HH:mm:ss.SSS");
        if (!localDateTime.equals(account.getDeviceUpdateAt())) {
            List<TenantDevice> devices = deviceManager.fetchDeviceByAccountAndBrand(account.getId(), DeviceBrandEnum.SMYOO);
            Set<Long> removeIds = devices.stream().map(BaseEntity::getId).collect(Collectors.toSet());
            Map<String, TenantDevice> deviceMap = devices.stream().collect(Collectors.toMap(TenantDevice::getDeviceId, Function.identity()));
            account.setDeviceUpdateAt(localDateTime);
            QueryDevicesResponse resp = client.queryDevices(account);
            List<QueryDevicesResponse.McuInfo> mcuInfos = resp.getMcuInfos();
            for (QueryDevicesResponse.McuInfo mcuInfo : mcuInfos) {
                if (mcuInfo.getType() == 29) {
                    DeviceRequest req = new DeviceRequest();
                    req.setMcuId(mcuInfo.getMcuId());
                    GetIrDeviceResponse data = client.irDeviceGetData(account, req);
                    TenantDevice device = deviceMap.get(mcuInfo.getMcuId());
                    Map<String, Object> datapoint = data.getDatapoint();
                    if (device == null) {
                        device = new TenantDevice();
                        device.setBrand(DeviceBrandEnum.SMYOO);
                        device.setType(DeviceTypeEnum.AIR_CONDITIONER);
                        device.setAccountId(account.getId());
                        device.setTenantId(account.getTenantId());
                        device.setParentId(0L);
                        device.setAuto(IsEnum.TRUE);
                    } else {
                        removeIds.remove(device.getId());
                    }
                    device.setName(mcuInfo.getMcuName());
                    device.setOnline(IsEnum.of(mcuInfo.getIsOnline() == 1));
                    device.setOpen(IsEnum.of(Objects.equals(datapoint.get("status"), 1)));
                    device.setSourceInfo(JsonUtils.toJson(data));
                    device.setDescription(data.getDeviceData().getDataPoint());
                    device.setDeviceId(mcuInfo.getMcuId());
                    deviceManager.saveDevice(device);
                }
            }
            deviceManager.removeDeviceByIds(removeIds);
            clubManager.removeDeviceRelByDeviceIds(removeIds);
            account.setDeviceUpdateAt(localDateTime);
            deviceManager.saveSmyooAccount(account);
        }
    }

    public Page<SmyooAccountBo> smyooAccountList(SmyooAccountQueryDto dto) {
        Page<TenantSmyooAccount> page = deviceManager.getSmyooAccountList(dto.getTenantId(), dto.getPhone(), dto);
        return page.convert(v -> {
            SmyooAccountBo bo = new SmyooAccountBo();
            bo.setId(v.getId());
            bo.setPhone(v.getPhone());
            bo.setTenantId(v.getTenantId());
            return bo;
        });
    }

    public List<DeviceBo> clubAirList(Long clubId) {
        ClubInfo clubInfo = clubManager.fetchClubById(clubId);
        if (clubInfo == null) {
            throw new BaseException("门店不存在");
        }
        long tenantId = clubInfo.getTenantId();
        List<TenantDevice> devices = deviceManager.fetchDeviceByTenantAndBrand(tenantId, DeviceBrandEnum.SMYOO);
        return BeanUtils.copyList(devices, DeviceBo.class);
    }

    public SmyooAirDetailBo clubAirDetail(Long id) {
        TenantDevice device = deviceManager.fetchById(id);
        if (device == null) {
            throw new BaseException("设备不存在");
        }
        if (device.getBrand() != DeviceBrandEnum.SMYOO) {
            throw new BaseException("设备不支持");
        }
        if (device.getType() != DeviceTypeEnum.AIR_CONDITIONER) {
            throw new BaseException("设备不支持");
        }
        SmyooClient client = ThirdServiceHolder.smyooClient("base");
        TenantSmyooAccount account = deviceManager.getSmyooAccount(device.getAccountId());
        DeviceRequest req = new DeviceRequest();
        req.setMcuId(device.getDeviceId());
        GetIrDeviceResponse data = client.irDeviceGetData(account, req);
        SmyooAirDetailBo detail = JsonUtils.toObject(data.getDeviceData().getDataPoint(), SmyooAirDetailBo.class);
        detail.setDeviceId(device.getDeviceId());
        detail.setName(device.getName());
        detail.setAuto(device.getAuto());
        return detail;
    }

    public void clubAirOperate(SmyooAirDetailBo dto) {
        TenantDevice device = deviceManager.fetchById(dto.getId());
        if (device == null) {
            throw new BaseException("设备不存在");
        }
        if (device.getBrand() != DeviceBrandEnum.SMYOO) {
            throw new BaseException("设备不支持");
        }
        if (device.getType() != DeviceTypeEnum.AIR_CONDITIONER) {
            throw new BaseException("设备不支持");
        }
        SmyooClient client = ThirdServiceHolder.smyooClient("base");
        TenantSmyooAccount account = deviceManager.getSmyooAccount(device.getAccountId());
        DeviceRequest req = new DeviceRequest();
        req.setMcuId(device.getDeviceId());
        GetIrDeviceResponse data = client.irDeviceGetData(account, req);
        Map<String, Object> datapoint = data.getDatapoint();
        log.info("get data:{} ,{}", device.getDeviceId(), JsonUtils.toJson(data));
        datapoint.put("status", dto.getStatus());
        datapoint.put("mode", dto.getMode());
        datapoint.put("temp", dto.getTemp());
        datapoint.put("speed", dto.getSpeed());
        datapoint.put("dir", dto.getDir());
        DeviceSetRequest set = new DeviceSetRequest();
        set.setMcuId(device.getDeviceId());
        set.setDatapoint(JsonUtils.toJson(datapoint));
        log.info("set data:{}", JsonUtils.toJson(set));
        //        client.irDeviceSetData(account, set);
        device.setAuto(dto.getAuto());
        device.setOpen(IsEnum.of(dto.getStatus() == 1));
        device.setDescription(JsonUtils.toJson(datapoint));
        deviceManager.saveDevice(device);
    }

    public List<ClubDeviceBindBo> clubAirBindList(Long clubId) {
        List<ClubTable> clubTables = clubManager.fetchTableByClub(clubId);
        Map<Long, ClubTable> tableMap = clubTables.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        List<Long> tableIds = clubTables.stream().map(BaseEntity::getId).toList();
        List<ClubTableDeviceRel> relList = clubManager.fetchDeviceRelByTableIds(tableIds);
        List<Long> deviceIds = relList.stream().map(ClubTableDeviceRel::getDeviceId).toList();
        List<TenantDevice> tenantDevices = deviceManager.fetchByIdsAndType(deviceIds, DeviceTypeEnum.AIR_CONDITIONER);
        Map<Long, TenantDevice> deviceMap = tenantDevices.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        Set<Long> hasDeviceIds = tenantDevices.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        return relList.stream().filter(v -> hasDeviceIds.contains(v.getDeviceId())).map(v -> {
            ClubDeviceBindBo one = BeanUtils.copy(v, ClubDeviceBindBo.class);
            one.setTableName(tableMap.get(v.getTableId()).getName());
            one.setDeviceName(deviceMap.get(v.getDeviceId()).getName());
            return one;
        }).toList();
    }

    public void tableBindChange(TableBindChangeDto dto) {
        ClubTableDeviceRel rel = clubManager.fetchDeviceRelById(dto.getId());
        if (rel == null) {
            throw new BaseException("未找到信息");
        }
        rel.setTableId(dto.getTableId());
        rel.setDeviceId(dto.getDeviceId());
        clubManager.saveDeviceRel(rel);
    }

    @Transactional(rollbackOn = Exception.class)
    public void changeSmyooPassword(ChangeSmyooPasswordDto dto) {
        TenantSmyooAccount account = deviceManager.getSmyooAccount(dto.getId());
        if (account == null) {
            throw new BaseException("账号不存在");
        }
        account.setPassword(dto.getPassword());
        SmyooClient client = ThirdServiceHolder.smyooClient("base");
        LoginOpenResponse login = client.loginOpen(account);
        LoginTicketResponse loginTicketResponse = client.loginTicket(login.getTicket());
        account.setSessionId(loginTicketResponse.getSessionId());
        account.setExpiresIn(login.getCookieExpireTime() + "");
        account.setExpireAt(LocalDateTime.now().plusHours(2));
        deviceManager.saveSmyooAccount(account);
    }

}
