"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/confirmation/index"],{

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/Input/style/css.js":
/*!**************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/Input/style/css.js ***!
  \**************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./style.css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Input/style/style.css");


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/Input/style/style.css":
/*!*****************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/Input/style/style.css ***!
  \*****************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/input.taro-s1Zw62KR.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/input.taro-s1Zw62KR.js ***!
  \******************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   I: function() { return /* binding */ Input; }
/* harmony export */ });
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tslib.es6-iWu3F_1J.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/tslib.es6-iWu3F_1J.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./configprovider.taro-DpK4IiCE.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./typings-DV9RBfhj.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/typings-DV9RBfhj.js");
/* harmony import */ var _use_props_value_SH9krhkx_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./use-props-value-SH9krhkx.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/use-props-value-SH9krhkx.js");









function trimExtraChar(value, _char, regExp) {
  var index = value.indexOf(_char);
  if (index === -1) {
    return value;
  }
  if (_char === "-" && index !== 0) {
    return value.slice(0, index);
  }
  return value.slice(0, index + 1) + value.slice(index).replace(regExp, "");
}
function formatNumber(value) {
  var allowDot = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
  var allowMinus = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : true;
  if (allowDot) {
    value = trimExtraChar(value, ".", /\./g);
  } else {
    value = value.split(".")[0];
  }
  if (allowMinus) {
    value = trimExtraChar(value, "-", /-/g);
  } else {
    value = value.replace(/-/, "");
  }
  var regExp = allowDot ? /[^-0-9.]/g : /[^-0-9]/g;
  return value.replace(regExp, "");
}
var defaultProps = Object.assign(Object.assign({}, _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_3__.C), {
  type: "text",
  name: "",
  placeholder: void 0,
  confirmType: "done",
  align: "left",
  required: false,
  disabled: false,
  readOnly: false,
  maxLength: 9999,
  clearable: false,
  clearIcon: null,
  formatTrigger: "onChange",
  autoFocus: false
});
var Input = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function (props, ref) {
  var rtl = (0,_configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_4__.a)();
  var _useConfig = (0,_configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_4__.u)(),
    locale = _useConfig.locale;
  var _a = Object.assign(Object.assign({}, defaultProps), props),
    type = _a.type,
    name = _a.name,
    placeholder = _a.placeholder,
    align = _a.align,
    disabled = _a.disabled,
    readOnly = _a.readOnly,
    maxLength = _a.maxLength,
    clearable = _a.clearable,
    clearIcon = _a.clearIcon,
    formatTrigger = _a.formatTrigger,
    autoFocus = _a.autoFocus,
    style = _a.style,
    className = _a.className,
    onChange = _a.onChange,
    onFocus = _a.onFocus,
    onBlur = _a.onBlur,
    onClear = _a.onClear,
    formatter = _a.formatter,
    _onClick = _a.onClick,
    confirmType = _a.confirmType,
    defaultValue = _a.defaultValue,
    _value = _a.value,
    rest = (0,_tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_5__._)(_a, ["type", "name", "placeholder", "align", "disabled", "readOnly", "maxLength", "clearable", "clearIcon", "formatTrigger", "autoFocus", "style", "className", "onChange", "onFocus", "onBlur", "onClear", "formatter", "onClick", "confirmType", "defaultValue", "value"]);
  var _usePropsValue = (0,_use_props_value_SH9krhkx_js__WEBPACK_IMPORTED_MODULE_6__.u)({
      value: _value,
      defaultValue: defaultValue,
      finalValue: "",
      onChange: onChange
    }),
    _usePropsValue2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__["default"])(_usePropsValue, 2),
    value = _usePropsValue2[0],
    setValue = _usePropsValue2[1];
  var inputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__["default"])(_useState, 2),
    active = _useState2[0],
    setActive = _useState2[1];
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function () {
    return {
      clear: function clear() {
        setValue("");
      },
      focus: function focus() {
        var _a2;
        (_a2 = inputRef.current) === null || _a2 === void 0 ? void 0 : _a2.focus();
      },
      blur: function blur() {
        var _a2;
        (_a2 = inputRef.current) === null || _a2 === void 0 ? void 0 : _a2.blur();
      },
      get nativeElement() {
        return inputRef.current;
      }
    };
  });
  var inputClass = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {
    var classPrefix = "nut-input";
    return [classPrefix, "".concat(disabled ? "".concat(classPrefix, "-disabled") : "")].filter(Boolean).join(" ");
  }, [disabled]);
  var _React__default$useSt = react__WEBPACK_IMPORTED_MODULE_0___default().useState(),
    _React__default$useSt2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__["default"])(_React__default$useSt, 2),
    updateState = _React__default$useSt2[1];
  var forceUpdate = react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(function () {
    return updateState({});
  }, []);
  var updateValue = function updateValue(value2) {
    var trigger = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : "onChange";
    var val = value2;
    if (type === "number") {
      val = formatNumber(val, false, true);
    }
    if (type === "digit") {
      val = formatNumber(val, true, true);
    }
    if (formatter && trigger === formatTrigger) {
      val = formatter(val);
    }
    setValue(val);
    var eventHandler = props[trigger];
    if (eventHandler && typeof eventHandler === "function" && trigger !== "onChange") {
      eventHandler(val);
    }
    forceUpdate();
  };
  var handleFocus = function handleFocus(event) {
    if (_tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getEnv() === "WEB") {
      var val = event.target.value;
      onFocus && onFocus(val);
    } else {
      var height = (event.detail || {}).height;
      onFocus === null || onFocus === void 0 ? void 0 : onFocus(value, height);
    }
    setActive(true);
  };
  var handleInput = function handleInput(value2) {
    updateValue(value2, "onChange");
  };
  var handleBlur = function handleBlur(event) {
    var val = _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getEnv() === "WEB" ? event.target.value : value;
    updateValue(val, "onBlur");
    setTimeout(function () {
      setActive(false);
    }, 200);
  };
  var inputType = function inputType(type2) {
    if ((0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.getEnv)() === _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.ENV_TYPE.WEB) {
      if (type2 === "digit") {
        return "text";
      }
      if (type2 === "number") {
        return "tel";
      }
    } else if (type2 === "password") {
      return "text";
    }
    return type2;
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tarojs_components__WEBPACK_IMPORTED_MODULE_8__.View, {
    className: "".concat(inputClass(), "  ").concat(className || ""),
    style: style,
    onClick: function onClick(e) {
      _onClick && _onClick(e);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tarojs_components__WEBPACK_IMPORTED_MODULE_8__.Input, Object.assign({}, rest, {
    name: name,
    className: "nut-input-native",
    ref: inputRef,
    style: {
      // eslint-disable-next-line no-nested-ternary
      textAlign: rtl ?
      // eslint-disable-next-line no-nested-ternary
      align === "right" ?
      // eslint-disable-next-line no-nested-ternary
      "left" : align === "left" ? "right" : "center" : align
    },
    type: inputType(type),
    password: type === "password",
    maxlength: maxLength,
    placeholder: placeholder === void 0 ? locale.placeholder : placeholder,
    disabled: disabled || readOnly,
    value: value,
    focus: autoFocus,
    confirmType: confirmType,
    onBlur: handleBlur,
    onFocus: handleFocus,
    onInput: function onInput(e) {
      handleInput(e.currentTarget.value);
    }
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tarojs_components__WEBPACK_IMPORTED_MODULE_8__.View, {
    style: {
      display: clearable && !readOnly && active && value.length > 0 ? "flex" : "none",
      alignItems: "center",
      cursor: "pointer"
    },
    onClick: function onClick(e) {
      e.stopPropagation();
      if (!disabled) {
        setValue("");
        onClear === null || onClear === void 0 ? void 0 : onClear("");
      }
    }
  }, clearIcon || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_1__.MaskClose, {
    className: "nut-input-clear"
  })));
});
Input.displayName = "NutInput";


/***/ }),

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/confirmation/index!./src/pages/confirmation/index.tsx":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/confirmation/index!./src/pages/confirmation/index.tsx ***!
  \******************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Popup/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup */ "./node_modules/@nutui/nutui-react-taro/dist/esm/popup.taro-JynjcLCn.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SafeArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/safearea.taro-BJpvFsMf.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_CellGroup_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/CellGroup/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/CellGroup/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_CellGroup__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/CellGroup */ "./node_modules/@nutui/nutui-react-taro/dist/esm/cellgroup.taro-DTLGMR_c.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Cell/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell */ "./node_modules/@nutui/nutui-react-taro/dist/esm/cell.taro-DWLhb5m6.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog_style_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Dialog/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog */ "./node_modules/@nutui/nutui-react-taro/dist/esm/dialog.taro-1Vukbvap.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _components_bussiness_DurationTime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/bussiness/DurationTime */ "./src/components/bussiness/DurationTime/index.tsx");
/* harmony import */ var _components_bussiness_Order__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/bussiness/Order */ "./src/components/bussiness/Order/index.tsx");
/* harmony import */ var _components_bussiness_Order_AverageButton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/bussiness/Order/AverageButton */ "./src/components/bussiness/Order/AverageButton.tsx");
/* harmony import */ var _components_bussiness_Order_CancelButton__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/bussiness/Order/CancelButton */ "./src/components/bussiness/Order/CancelButton.tsx");
/* harmony import */ var _components_bussiness_Order_FinishButton__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/bussiness/Order/FinishButton */ "./src/components/bussiness/Order/FinishButton.tsx");
/* harmony import */ var _components_bussiness_Order_ForceButton__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/bussiness/Order/ForceButton */ "./src/components/bussiness/Order/ForceButton.tsx");
/* harmony import */ var _components_bussiness_Order_QuickStartButton__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/bussiness/Order/QuickStartButton */ "./src/components/bussiness/Order/QuickStartButton.tsx");
/* harmony import */ var _components_bussiness_PaymentMethod__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/bussiness/PaymentMethod */ "./src/components/bussiness/PaymentMethod/index.tsx");
/* harmony import */ var _components_bussiness_Recharge_Tab__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/bussiness/Recharge/Tab */ "./src/components/bussiness/Recharge/Tab.tsx");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var _custom_table_card__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/custom/table-card */ "./src/custom/table-card.tsx");
/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/hooks */ "./src/hooks/index.ts");
/* harmony import */ var _stores_auth__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/stores/auth */ "./src/stores/auth.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_21__);
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_22__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_23__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");





































var SceneTypes = {
  SCANCODE: "1011",
  OTHER: "1000"
};
var FROM_TYPES = /*#__PURE__*/function (FROM_TYPES) {
  FROM_TYPES[FROM_TYPES["NONE"] = 0] = "NONE";
  FROM_TYPES[FROM_TYPES["WEAPP"] = 1] = "WEAPP";
  FROM_TYPES[FROM_TYPES["QRCODE"] = 2] = "QRCODE";
  return FROM_TYPES;
}(FROM_TYPES || {});
var RESULT_TYPES = /*#__PURE__*/function (RESULT_TYPES) {
  RESULT_TYPES[RESULT_TYPES["SUCCESS"] = 0] = "SUCCESS";
  RESULT_TYPES[RESULT_TYPES["PENDING_PAYMENT"] = 1] = "PENDING_PAYMENT";
  RESULT_TYPES[RESULT_TYPES["STARTING"] = 2] = "STARTING";
  RESULT_TYPES[RESULT_TYPES["TABLE_OCCUPY"] = 3] = "TABLE_OCCUPY";
  RESULT_TYPES[RESULT_TYPES["DEVICE_OFFLINE"] = 4] = "DEVICE_OFFLINE";
  RESULT_TYPES[RESULT_TYPES["NOT_ENOUGH_BALANCE"] = 5] = "NOT_ENOUGH_BALANCE";
  return RESULT_TYPES;
}(RESULT_TYPES || {});
var PAY_TYPE = /*#__PURE__*/function (PAY_TYPE) {
  PAY_TYPE[PAY_TYPE["NONE"] = 0] = "NONE";
  PAY_TYPE[PAY_TYPE["WECHAT"] = 1] = "WECHAT";
  PAY_TYPE[PAY_TYPE["SINGLE"] = 2] = "SINGLE";
  PAY_TYPE[PAY_TYPE["NUMBER"] = 3] = "NUMBER";
  PAY_TYPE[PAY_TYPE["COUPON"] = 4] = "COUPON";
  PAY_TYPE[PAY_TYPE["MEITUAN"] = 5] = "MEITUAN";
  PAY_TYPE[PAY_TYPE["DOUYIN"] = 6] = "DOUYIN";
  PAY_TYPE[PAY_TYPE["DEPOSIT"] = 7] = "DEPOSIT";
  return PAY_TYPE;
}(PAY_TYPE || {});
var OVERTIME = 60 * 5;
var INTERVAL = 5;
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var _instance$router2, _confirmationData$clu3, _confirmationData$tab, _confirmationData$tab2, _confirmationData$tab3, _confirmationData$ord;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_23__.useState)(function () {
      var instance = _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().getCurrentInstance();
      return instance;
    }),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_useState, 1),
    instance = _useState2[0];
  var route = _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().useRouter();
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_20__.menuRect)();
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_23__.useRef)(true);
  var durationTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_23__.useRef)(null);
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_23__.useState)(FROM_TYPES.QRCODE),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_useState3, 2),
    from = _useState4[0],
    setFrom = _useState4[1];
  var paymentRef = (0,react__WEBPACK_IMPORTED_MODULE_23__.useRef)(null);
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_23__.useState)(0),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_useState5, 2),
    price = _useState6[0],
    setPrice = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_23__.useState)(0),
    _useState8 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_useState7, 2),
    durationTime = _useState8[0],
    setDurationTime = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_23__.useState)(false),
    _useState0 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_useState9, 2),
    visible = _useState0[0],
    setVisible = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_23__.useState)(),
    _useState10 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_useState1, 2),
    confirmationData = _useState10[0],
    setConfirmationData = _useState10[1];
  var dispatch = (0,_hooks__WEBPACK_IMPORTED_MODULE_26__.useDo)();
  var _useStore = (0,_hooks__WEBPACK_IMPORTED_MODULE_26__.useStore)(function (state) {
      return state.auth;
    }),
    user = _useStore.user;
  var sleep = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(function (time) {
    return new Promise(function (reslove) {
      return setTimeout(function () {
        reslove("success");
      }, time * 1000);
    });
  }, []);
  var getCheckOrder = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee(orderNo) {
      var _yield$api$venues$get, data;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_6__["default"].venues.getTableStatusByOrder({
              orderNo: orderNo
            });
          case 2:
            _yield$api$venues$get = _context.sent;
            data = _yield$api$venues$get.data;
            return _context.abrupt("return", data.result === 0 && data.status === 2);
          case 5:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), []);
  var recursiveCheck = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee2(orderNo) {
      var res;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return getCheckOrder(orderNo);
          case 2:
            res = _context2.sent;
            if (!(!res && ref.current)) {
              _context2.next = 9;
              break;
            }
            _context2.next = 6;
            return sleep(INTERVAL);
          case 6:
            _context2.next = 8;
            return recursiveCheck(orderNo);
          case 8:
            return _context2.abrupt("return", _context2.sent);
          case 9:
            return _context2.abrupt("return", res);
          case 10:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function (_x2) {
      return _ref2.apply(this, arguments);
    };
  }(), [getCheckOrder, sleep, ref]);
  var limitRecursiveCheck = function limitRecursiveCheck(orderNo) {
    ref.current = true;
    sleep(OVERTIME).then(function () {
      ref.current = false;
    });
    return recursiveCheck(orderNo);
  };
  var tableId = route.params.id;
  var scene = route.params.scene;
  (0,react__WEBPACK_IMPORTED_MODULE_23__.useEffect)(function () {
    if (scene === SceneTypes.SCANCODE) {
      setFrom(FROM_TYPES.QRCODE);
    } else {
      setFrom(FROM_TYPES.WEAPP);
    }
  }, [scene]);
  var onChangeDurationTime = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(function (value) {
    setDurationTime(value);
  }, [setDurationTime]);
  var calculationPayment = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee3() {
    var _durationTimeRef$curr, _instance$router, _getData, _getData2, startTime, endTime, _tableId, _yield$api$venues$cal, _price;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          if (!durationTimeRef.current) {
            _context3.next = 8;
            break;
          }
          _getData = (_durationTimeRef$curr = durationTimeRef.current) === null || _durationTimeRef$curr === void 0 ? void 0 : _durationTimeRef$curr.getData(), _getData2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_getData, 2), startTime = _getData2[0], endTime = _getData2[1];
          _tableId = (_instance$router = instance.router) === null || _instance$router === void 0 ? void 0 : _instance$router.params.id;
          _context3.next = 5;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_6__["default"].venues.calculationPayment({
            tableId: _tableId,
            startTime: startTime,
            endTime: endTime
          });
        case 5:
          _yield$api$venues$cal = _context3.sent;
          _price = _yield$api$venues$cal.data.price;
          setPrice(_price);
        case 8:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  })), [durationTimeRef, instance, setPrice]);
  var getConfirmationData = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/function () {
    var _ref4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee4(params) {
      var _order;
      var _yield$api$venues$cre, data;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            _context4.next = 2;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_6__["default"].venues.createTablePrepare(params);
          case 2:
            _yield$api$venues$cre = _context4.sent;
            data = _yield$api$venues$cre.data;
            setConfirmationData(data);
            if (data !== null && data !== void 0 && data.order) {
              setFrom(data.order.startFrom);
            }
            if ((data === null || data === void 0 || (_order = data.order) === null || _order === void 0 ? void 0 : _order.status) == 1) {
              limitRecursiveCheck(data === null || data === void 0 ? void 0 : data.order.orderNo).then(function () {
                ref.current && getConfirmationData({
                  from: from,
                  tableId: Number(tableId)
                });
              });
            }
          case 7:
          case "end":
            return _context4.stop();
        }
      }, _callee4);
    }));
    return function (_x3) {
      return _ref4.apply(this, arguments);
    };
  }(), [setConfirmationData, limitRecursiveCheck, ref]);
  var onGetPhoneNumber = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/function () {
    var _ref5 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee5(e) {
      var code, resp, payload;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee5$(_context5) {
        while (1) switch (_context5.prev = _context5.next) {
          case 0:
            code = e.detail.code;
            if (code) {
              _context5.next = 3;
              break;
            }
            return _context5.abrupt("return");
          case 3:
            _context5.next = 5;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_6__["default"].user.bindPhone(code);
          case 5:
            resp = _context5.sent;
            if (!(resp.statusCode !== 204 && resp.statusCode !== 200)) {
              _context5.next = 8;
              break;
            }
            return _context5.abrupt("return", _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().showToast({
              title: resp.data.message,
              icon: "none"
            }));
          case 8:
            payload = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_29__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_29__["default"])({}, user), {}, {
              hasPhone: true
            });
            dispatch((0,_stores_auth__WEBPACK_IMPORTED_MODULE_19__.login)(payload));
            _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_30__.B.close("authorizePhone");
          case 11:
          case "end":
            return _context5.stop();
        }
      }, _callee5);
    }));
    return function (_x4) {
      return _ref5.apply(this, arguments);
    };
  }(), [user]);
  var validatePhone = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee6() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          if (!(user !== null && user !== void 0 && user.hasPhone)) {
            _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_30__.B.open("authorizePhone", {
              title: "授权手机号",
              content: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)("div", {
                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)("div", {
                  className: "mb-4",
                  children: "\u4E3A\u4E86\u63D0\u4F9B\u66F4\u597D\u7684\u552E\u540E\u670D\u52A1\u6211\u4EEC\u5E0C\u671B\u83B7\u53D6\u5230\u4F60\u7684\u624B\u673A\u53F7"
                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.Button, {
                  className: "bg-primary rounded-sm text-bgt h-10 py-1 px-3 flex items-center justify-center",
                  openType: "getPhoneNumber",
                  onGetPhoneNumber: onGetPhoneNumber,
                  children: "\u6388\u6743\u7ED1\u5B9A"
                })]
              }),
              hideCancelButton: true,
              hideConfirmButton: true
            });
          }
          return _context6.abrupt("return", !!(user !== null && user !== void 0 && user.hasPhone));
        case 2:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  })), [user === null || user === void 0 ? void 0 : user.hasPhone, onGetPhoneNumber]);
  var pay = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee7() {
    var _paymentRef$current, params, payType, _paymentRef$current2, ticket, _paymentRef$current3, extra, _getData3, _durationTimeRef$curr2, _ref8, _ref9, startTime, endTime, _yield$Taro$showModal, confirm, _yield$api$venues$sta, data, result, _extra, message, orderNo;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee7$(_context7) {
      while (1) switch (_context7.prev = _context7.next) {
        case 0:
          _context7.next = 2;
          return validatePhone();
        case 2:
          if (!_context7.sent) {
            _context7.next = 32;
            break;
          }
          params = {
            from: from,
            tableId: +tableId
          };
          payType = (_paymentRef$current = paymentRef.current) === null || _paymentRef$current === void 0 ? void 0 : _paymentRef$current.type;
          if (payType === PAY_TYPE.MEITUAN) {
            ticket = (_paymentRef$current2 = paymentRef.current) === null || _paymentRef$current2 === void 0 ? void 0 : _paymentRef$current2.getCurrent();
            params.extra = {
              ticketId: ticket === null || ticket === void 0 ? void 0 : ticket.id
            };
          } else if (payType === PAY_TYPE.COUPON) {
            extra = (_paymentRef$current3 = paymentRef.current) === null || _paymentRef$current3 === void 0 ? void 0 : _paymentRef$current3.getCurrent();
            params.extra = extra;
          } else {
            _ref8 = (_getData3 = durationTimeRef === null || durationTimeRef === void 0 || (_durationTimeRef$curr2 = durationTimeRef.current) === null || _durationTimeRef$curr2 === void 0 ? void 0 : _durationTimeRef$curr2.getData()) !== null && _getData3 !== void 0 ? _getData3 : [], _ref9 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_25__["default"])(_ref8, 2), startTime = _ref9[0], endTime = _ref9[1];
            params.startTime = startTime;
            params.endTime = endTime;
          }
          _context7.next = 8;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().showModal({
            title: "提示",
            content: "是否确认支付订单？",
            confirmText: "确定"
          });
        case 8:
          _yield$Taro$showModal = _context7.sent;
          confirm = _yield$Taro$showModal.confirm;
          if (confirm) {
            _context7.next = 12;
            break;
          }
          return _context7.abrupt("return");
        case 12:
          params.payType = payType;
          if (!tableId) {
            _context7.next = 32;
            break;
          }
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().showLoading({
            title: "",
            mask: true
          });
          _context7.next = 17;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_6__["default"].venues.startTable(params);
        case 17:
          _yield$api$venues$sta = _context7.sent;
          data = _yield$api$venues$sta.data;
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().hideLoading();
          result = data.result, _extra = data.extra, message = data.message, orderNo = data.orderNo;
          if (!(result === RESULT_TYPES.STARTING || result === RESULT_TYPES.SUCCESS)) {
            _context7.next = 25;
            break;
          }
          // Taro.redirectTo({ url: `/pages/result/index?code=${orderNo}` });
          getConfirmationData({
            from: from,
            tableId: Number(tableId)
          });
          _context7.next = 31;
          break;
        case 25:
          if (!(result === RESULT_TYPES.PENDING_PAYMENT)) {
            _context7.next = 30;
            break;
          }
          _context7.next = 28;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().requestPayment((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_29__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_29__["default"])({}, _extra), {}, {
            package: _extra.packageStr,
            success: function success() {
              getConfirmationData({
                from: from,
                tableId: Number(tableId)
              });
            },
            fail: function fail() {
              _api_bussiness__WEBPACK_IMPORTED_MODULE_6__["default"].order.cancelOrder(orderNo).then(function () {
                getConfirmationData({
                  from: from,
                  tableId: Number(tableId)
                });
              });
            }
          }));
        case 28:
          _context7.next = 31;
          break;
        case 30:
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().showToast({
            title: message,
            icon: "none"
          });
        case 31:
          if (result === RESULT_TYPES.NOT_ENOUGH_BALANCE) {
            if (payType === PAY_TYPE.NUMBER || payType === PAY_TYPE.SINGLE) {
              // 引导当前用户
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().showToast({
                title: message,
                icon: "none",
                complete: function complete() {
                  setVisible(true);
                }
              });
            }
          }
        case 32:
        case "end":
          return _context7.stop();
      }
    }, _callee7);
  })), [durationTimeRef, paymentRef, from, instance, setVisible, validatePhone]);
  (0,react__WEBPACK_IMPORTED_MODULE_23__.useEffect)(function () {
    // 获取当前确认信息
    var params = {
      from: from,
      tableId: Number(tableId)
    };
    getConfirmationData(params);
    return function () {
      ref.current = false;
    };
  }, [(_instance$router2 = instance.router) === null || _instance$router2 === void 0 ? void 0 : _instance$router2.params.id]);
  var getIncludes = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(function () {
    if (paymentRef.current) {
      if (paymentRef.current.type === _constants__WEBPACK_IMPORTED_MODULE_17__.PAYTYPES.NUMBER) return [0];
      if (paymentRef.current.type === _constants__WEBPACK_IMPORTED_MODULE_17__.PAYTYPES.SINGLE) return [1];
    }
    return [];
  }, [paymentRef.current]);
  var coupons = (0,react__WEBPACK_IMPORTED_MODULE_23__.useMemo)(function () {
    if (confirmationData) {
      var _club$coupons, _wallet$coupons;
      var club = confirmationData.club,
        wallet = confirmationData.wallet;
      return [{
        title: "购买优惠券",
        list: (_club$coupons = club === null || club === void 0 ? void 0 : club.coupons) !== null && _club$coupons !== void 0 ? _club$coupons : [],
        key: 1,
        actionName: "购买并使用"
      }, {
        title: "我的优惠券",
        list: (_wallet$coupons = wallet === null || wallet === void 0 ? void 0 : wallet.coupons) !== null && _wallet$coupons !== void 0 ? _wallet$coupons : [],
        key: 2,
        actionName: "使用"
      }];
    }
    return [];
  }, [confirmationData]);
  var makePhoneCall = (0,react__WEBPACK_IMPORTED_MODULE_23__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_27__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().mark(function _callee8() {
    var _confirmationData$clu;
    var _confirmationData$clu2;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_28__["default"])().wrap(function _callee8$(_context8) {
      while (1) switch (_context8.prev = _context8.next) {
        case 0:
          if (!(confirmationData !== null && confirmationData !== void 0 && (_confirmationData$clu = confirmationData.club) !== null && _confirmationData$clu !== void 0 && _confirmationData$clu.phone)) {
            _context8.next = 4;
            break;
          }
          _context8.next = 3;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_21___default().makePhoneCall({
            phoneNumber: confirmationData === null || confirmationData === void 0 || (_confirmationData$clu2 = confirmationData.club) === null || _confirmationData$clu2 === void 0 ? void 0 : _confirmationData$clu2.phone
          });
        case 3:
          return _context8.abrupt("return", _context8.sent);
        case 4:
          throw Error("当前没有可用的手机号码");
        case 5:
        case "end":
          return _context8.stop();
      }
    }, _callee8);
  })), [confirmationData === null || confirmationData === void 0 || (_confirmationData$clu3 = confirmationData.club) === null || _confirmationData$clu3 === void 0 ? void 0 : _confirmationData$clu3.phone]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
    className: "flex flex-col h-full p-3",
    style: {
      paddingTop: rect.bottom + 5
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_16__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_32__.C, {
      theme: {
        "--nutui-cell-group-title-color": "hsl(var(--primary))",
        "--nutui-cell-title-color": "#fff",
        "--nutui-cell-background-color": "hsl(var(--background-third))",
        "--nutui-cell-group-background-color": "hsl(var(--background-third))"
      },
      children: [confirmationData && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_custom_table_card__WEBPACK_IMPORTED_MODULE_18__.TableCard, {
        table: confirmationData.table,
        className: "mb-3",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
          className: "h-full flex flex-col justify-end w-full items-end",
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
            onClick: makePhoneCall,
            className: "text-muted-foreground text-sm flex flex-col justify-center items-center space-y-1",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
              className: "i-ph-phone-fill w-4 h-4"
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
              className: "text-sm",
              children: "\u7535\u8BDD"
            })]
          })
        })
      }), confirmationData && from !== FROM_TYPES.QRCODE && !confirmationData.order && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_DurationTime__WEBPACK_IMPORTED_MODULE_7__["default"], {
        ref: durationTimeRef,
        scene: from,
        plans: confirmationData.timePlans,
        onChange: onChangeDurationTime,
        onCalculationPayment: calculationPayment
      }), (confirmationData === null || confirmationData === void 0 ? void 0 : confirmationData.order) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_Order__WEBPACK_IMPORTED_MODULE_8__.OrderPanel, {
        order: confirmationData.order
      }), confirmationData && (
      // confirmationData.wallet &&
      !confirmationData.order || confirmationData.order.userId !== (user === null || user === void 0 ? void 0 : user.id)) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_PaymentMethod__WEBPACK_IMPORTED_MODULE_14__["default"], {
        ref: paymentRef,
        scene: from,
        tableId: confirmationData.table.id,
        deposit: confirmationData.table.deposit,
        wallet: confirmationData.wallet,
        coupons: coupons,
        isOwner: !confirmationData.order,
        onPay: pay
      }), (confirmationData === null || confirmationData === void 0 || (_confirmationData$tab = confirmationData.table) === null || _confirmationData$tab === void 0 ? void 0 : _confirmationData$tab.status) === 2 && !(confirmationData !== null && confirmationData !== void 0 && confirmationData.order) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_nutui_nutui_react_taro_dist_esm_CellGroup__WEBPACK_IMPORTED_MODULE_33__.C, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_34__.C, {
          title: "\u684C\u53F0\u72B6\u6001",
          extra: "\u4F7F\u7528\u4E2D"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_34__.C, {
          title: "\u7ED3\u675F\u65F6\u95F4",
          extra: confirmationData === null || confirmationData === void 0 || (_confirmationData$tab2 = confirmationData.table) === null || _confirmationData$tab2 === void 0 ? void 0 : _confirmationData$tab2.endTime
        })]
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
      className: "fixed left-0 bottom-0 w-full px-4 py-3 bg-bgt",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
        className: "flex justify-between items-center ",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
          className: "text-xl font-semibold",
          children: !(confirmationData !== null && confirmationData !== void 0 && confirmationData.order) && from !== FROM_TYPES.QRCODE && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
            children: ["\u5B9E\u4ED8\u6B3E:", /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
              className: "inline-block text-primary first-letterctext-xs",
              children: (0,_utils__WEBPACK_IMPORTED_MODULE_20__.toPrice)(price, 2)
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
              className: "text-muted-foreground text-_24rpx_",
              children: "\u82E5\u60A8\u63D0\u524D\u5230\u5E97,\u53EF\u63D0\u524D\u5F00\u95E8\u8BA1\u8D39"
            })]
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.View, {
          className: "flex gap-2",
          children: [(confirmationData === null || confirmationData === void 0 || (_confirmationData$tab3 = confirmationData.table) === null || _confirmationData$tab3 === void 0 ? void 0 : _confirmationData$tab3.status) === 1 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_31__.Button, {
            className: "bg-primary text-bgt px-4 py-2 font-semibold",
            onClick: function onClick() {
              return pay();
            },
            children: "\u786E\u8BA4\u8BA2\u5355"
          }), (confirmationData === null || confirmationData === void 0 ? void 0 : confirmationData.order) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.Fragment, {
            children: [dayjs__WEBPACK_IMPORTED_MODULE_22___default()(confirmationData === null || confirmationData === void 0 || (_confirmationData$ord = confirmationData.order) === null || _confirmationData$ord === void 0 ? void 0 : _confirmationData$ord.startTime).diff(dayjs__WEBPACK_IMPORTED_MODULE_22___default()(), "minute") > 1 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.Fragment, {
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_Order_QuickStartButton__WEBPACK_IMPORTED_MODULE_13__["default"], {
                code: confirmationData === null || confirmationData === void 0 ? void 0 : confirmationData.order.orderNo,
                status: confirmationData.order.status,
                isOwner: confirmationData.order.userId === (user === null || user === void 0 ? void 0 : user.id),
                updateStatus: function updateStatus() {
                  return getConfirmationData({
                    from: from,
                    tableId: Number(tableId)
                  });
                }
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_Order_CancelButton__WEBPACK_IMPORTED_MODULE_10__["default"], {
                code: confirmationData === null || confirmationData === void 0 ? void 0 : confirmationData.order.orderNo,
                status: confirmationData.order.status,
                isOwner: confirmationData.order.userId === (user === null || user === void 0 ? void 0 : user.id),
                updateStatus: function updateStatus() {
                  ref.current = false;
                  return getConfirmationData({
                    from: from,
                    tableId: Number(tableId)
                  });
                }
              })]
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_Order_FinishButton__WEBPACK_IMPORTED_MODULE_11__["default"], {
              code: confirmationData === null || confirmationData === void 0 ? void 0 : confirmationData.order.orderNo,
              status: confirmationData.order.status,
              isOwner: confirmationData.order.userId === (user === null || user === void 0 ? void 0 : user.id),
              updateStatus: function updateStatus() {
                ref.current = false;
                return getConfirmationData({
                  from: from,
                  tableId: Number(tableId)
                });
              }
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_Order_ForceButton__WEBPACK_IMPORTED_MODULE_12__["default"], {
              isOwner: confirmationData.order.userId === (user === null || user === void 0 ? void 0 : user.id),
              status: confirmationData.order.status,
              getPayType: function getPayType() {
                var _paymentRef$current4;
                return (_paymentRef$current4 = paymentRef.current) === null || _paymentRef$current4 === void 0 ? void 0 : _paymentRef$current4.type;
              },
              code: confirmationData === null || confirmationData === void 0 ? void 0 : confirmationData.order.orderNo,
              validatePhone: validatePhone,
              updateStatus: function updateStatus() {
                ref.current = false;
                return getConfirmationData({
                  from: from,
                  tableId: Number(tableId)
                });
              }
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_Order_AverageButton__WEBPACK_IMPORTED_MODULE_9__["default"], {
              isOwner: confirmationData.order.userId === (user === null || user === void 0 ? void 0 : user.id),
              status: confirmationData.order.status,
              getPayType: function getPayType() {
                var _paymentRef$current5;
                return (_paymentRef$current5 = paymentRef.current) === null || _paymentRef$current5 === void 0 ? void 0 : _paymentRef$current5.type;
              },
              code: confirmationData === null || confirmationData === void 0 ? void 0 : confirmationData.order.orderNo,
              validatePhone: validatePhone,
              updateStatus: function updateStatus() {
                ref.current = false;
                return getConfirmationData({
                  from: from,
                  tableId: Number(tableId)
                });
              }
            })]
          })]
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_35__.S, {
        position: "bottom"
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_36__.P, {
      position: "bottom",
      closeable: true,
      visible: visible,
      onClose: function onClose() {
        return setVisible(false);
      },
      style: {
        width: "100%",
        height: "100%",
        backgroundColor: "#000"
      },
      children: confirmationData && visible && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_components_bussiness_Recharge_Tab__WEBPACK_IMPORTED_MODULE_15__["default"], {
        id: Number(confirmationData.club.id),
        includes: getIncludes(),
        onSuccess: function onSuccess() {
          return setVisible(false);
        }
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_32__.C, {
      theme: {
        "--nutui-gray-7": "#fff",
        "--nutui-gray-6": "#fff",
        "--nutui-dialog-header-font-weight": "600",
        "--nutui-dialog-header-font-size": "1.25rem",
        "--nutui-dialog-padding": "40rpx"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_24__.jsx)(_nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_30__.B, {
        id: "authorizePhone"
      })
    })]
  });
});

/***/ }),

/***/ "./src/components/bussiness/Card/Ticket.tsx":
/*!**************************************************!*\
  !*** ./src/components/bussiness/Card/Ticket.tsx ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");





var dayList = [{
  label: "一",
  value: 1
}, {
  label: "二",
  value: 2
}, {
  label: "三",
  value: 3
}, {
  label: "四",
  value: 4
}, {
  label: "五",
  value: 5
}, {
  label: "六",
  value: 6,
  weekend: true
}, {
  label: "日",
  value: 7,
  weekend: true
}];
/* harmony default export */ __webpack_exports__["default"] = (function (props) {
  var item = props.item;
  var daysUntilTarget = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    var today = dayjs__WEBPACK_IMPORTED_MODULE_1___default()();
    return dayjs__WEBPACK_IMPORTED_MODULE_1___default()(item.endTime).diff(today, "day");
  }, [item]);
  var conversionText = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    var type = item.type,
      _item$period = item.period,
      period = _item$period === void 0 ? "{}" : _item$period;
    if (type == 1) return "不限时段";
    var _JSON$parse = JSON.parse(period),
      _JSON$parse$days = _JSON$parse.days,
      days = _JSON$parse$days === void 0 ? [] : _JSON$parse$days,
      _JSON$parse$startTime = _JSON$parse.startTime,
      startTime = _JSON$parse$startTime === void 0 ? "00:00" : _JSON$parse$startTime,
      _JSON$parse$endTime = _JSON$parse.endTime,
      endTime = _JSON$parse$endTime === void 0 ? "00:00" : _JSON$parse$endTime;
    var range = [startTime, endTime];
    var isAll = days.length === dayList.length;
    if (isAll || !days.length) {
      return "\u6BCF\u65E5 ".concat(range.join("~"));
    }
    var weekendDays = dayList.filter(function (item) {
      return item.weekend;
    }).map(function (item) {
      return item.value;
    });
    var isWeekend = days.every(function (item) {
      return weekendDays.includes(item);
    });
    if (isWeekend) {
      return "\u6BCF\u5468\u672B ".concat(range.join("~"));
    }
    var currentDays = dayList.filter(function (item) {
      return days.includes(item.value);
    }).map(function (item) {
      return item.label;
    });
    return "\u6BCF\u5468".concat(currentDays.join("、"), " ").concat(range.join("~"));
  }, [item]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
    className: "flex h-_6d5rem_",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "flex-1 flex flex-col gap-2 bg-white text-background rounded-md p-4 justify-between",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
          className: "font-semibold text-sm",
          children: item.name
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
          className: "text-muted-foreground text-xs",
          children: conversionText
        })]
      }), daysUntilTarget > 0 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "text-muted-foreground text-xs",
        children: ["\u6709\u6548\u671F\u81F3 ", dayjs__WEBPACK_IMPORTED_MODULE_1___default()(item.endTime).format("YYYY-MM-DD"), " (\u5269\u4F59", " ", daysUntilTarget, " \u5929)"]
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "bg-primary w-_1px_ scale-y-75"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
      className: "ticketStub bg-primary flex flex-col items-center text-background text-sm rounded-md p-4 justify-between",
      onClick: props.onUse,
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        children: [(0,_utils__WEBPACK_IMPORTED_MODULE_0__.numberFixed)(item.minutes / 60), "\u5C0F\u65F6"]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "text-xs text-muteds75",
        children: item.type === 1 ? "时长券" : "时段券"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_4__.View, {
        className: "border-backgrounds75 border rounded-sm px-2 p-1 font-semibold text-xs",
        children: "\u7ACB\u5373\u4F7F\u7528"
      })]
    })]
  });
});

/***/ }),

/***/ "./src/components/bussiness/Coupon/index.tsx":
/*!***************************************************!*\
  !*** ./src/components/bussiness/Coupon/index.tsx ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Popup/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup */ "./node_modules/@nutui/nutui-react-taro/dist/esm/popup.taro-JynjcLCn.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! clsx */ "./node_modules/clsx/dist/clsx.mjs");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _Card_Coupon__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../Card/Coupon */ "./src/components/bussiness/Card/Coupon.tsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");









/* harmony default export */ __webpack_exports__["default"] = (function (props) {
  var render = props.render,
    title = props.title,
    tabs = props.tabs,
    ckey = props.ckey,
    _onUse = props.onUse;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_5__["default"])(_useState, 2),
    visible = _useState2[0],
    setVisible = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(function () {
      return ckey !== null && ckey !== void 0 ? ckey : tabs[0].key;
    }),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_5__["default"])(_useState3, 2),
    currentKey = _useState4[0],
    setCurrentKey = _useState4[1];
  var currentTab = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(function () {
    return tabs.find(function (item) {
      return item.key === currentKey;
    });
  }, [currentKey, tabs]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
      onClick: function onClick() {
        return setVisible(true);
      },
      children: render ? render() : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
        children: "\u4F7F\u7528\u4F18\u60E0\u5238"
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_6__.P, {
      title: title,
      position: "bottom",
      closeable: true,
      visible: visible,
      onClose: function onClose() {
        return setVisible(false);
      },
      style: {
        width: "100%",
        height: "100%",
        backgroundColor: "#000",
        overflow: "hidden",
        display: visible ? "flex" : "none",
        flexDirection: "column"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)("div", {
        className: "bg-bgs flex-1 overflow-hidden flex flex-col text-white",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
          className: "flex border-b border-muted-foregrounds50 ",
          children: tabs.map(function (tabItem) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
              className: (0,clsx__WEBPACK_IMPORTED_MODULE_7__["default"])("flex-1 transition-all flex justify-center", {
                "text-primary ": currentKey === tabItem.key
              }),
              onClick: function onClick() {
                return setCurrentKey(tabItem.key);
              },
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
                className: (0,clsx__WEBPACK_IMPORTED_MODULE_7__["default"])("py-3", {
                  "border-b border-primary": currentKey === tabItem.key
                }),
                children: tabItem.title
              })
            });
          })
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
          className: "p-3 flex flex-col gap-3",
          children: currentTab === null || currentTab === void 0 ? void 0 : currentTab.list.map(function (item) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Card_Coupon__WEBPACK_IMPORTED_MODULE_3__["default"], {
              coupon: item,
              onUse: function onUse() {
                var extra = currentKey == 1 ? {
                  couponId: item.id
                } : {
                  userCouponId: item.id
                };
                _onUse && _onUse(extra);
              },
              renderRemark: function renderRemark() {
                if (currentKey == 1) {
                  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_8__.View, {
                    className: "text-primary font-semibold text-base whitespace-nowrap",
                    children: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.toPrice)(item.price, 2)
                  });
                }
                return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {});
              },
              renderRight: function renderRight() {
                return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
                  className: "border-backgrounds75 border rounded-sm px-2 p-1 font-semibold text-xs",
                  children: currentTab.actionName
                });
              }
            });
          })
        })]
      })
    })]
  });
});

/***/ }),

/***/ "./src/components/bussiness/DurationTime/index.tsx":
/*!*********************************************************!*\
  !*** ./src/components/bussiness/DurationTime/index.tsx ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! clsx */ "./node_modules/clsx/dist/clsx.mjs");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");








var supportAppointment = false;
var DurationTime = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(function (props, ref) {
  var plans = props.plans,
    onChange = props.onChange,
    scene = props.scene;
  var scanCode = scene === _constants__WEBPACK_IMPORTED_MODULE_2__.SCANCETYPES.SCANCODE;
  var normalizePlans = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    if (Array.isArray(plans)) {
      var plansData = Array.from(plans);
      plansData.sort(function (a, b) {
        return b.type - a.type;
      });
      return plansData;
    }
    return [];
  }, [plans]);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__["default"])(_useState, 2),
    current = _useState2[0],
    setCurrent = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__["default"])(_useState3, 2),
    type = _useState4[0],
    setType = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(2),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__["default"])(_useState5, 2),
    durationTime = _useState6[0],
    setDurationTime = _useState6[1];
  // const [visible, setVisible] = useState(false);
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(function () {
      return dayjs__WEBPACK_IMPORTED_MODULE_0___default()();
    }),
    _useState8 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__["default"])(_useState7, 2),
    currentTime = _useState8[0],
    setCurrentTime = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false),
    _useState0 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_4__["default"])(_useState9, 2),
    customizationVisible = _useState0[0],
    setCustomizationVisible = _useState0[1];

  // const changeCurrent = useCallback(
  //   (index) => {
  //     setCurrent(index);
  //     const day = dayjs();
  //     setCurrentTime(day.add(index, "day"));
  //   },
  //   [setCurrent, setCurrentTime, setDurationTime]
  // );

  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    var currentPlan = normalizePlans[current];
    props.onChange(currentPlan.value);
  }, [normalizePlans]);
  var maxValue = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    var maxValue = Math.max.apply(Math, (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_5__["default"])(plans.map(function (planItem) {
      return planItem.type === _constants__WEBPACK_IMPORTED_MODULE_2__.TIMETYPES.customization ? 0 : planItem.value;
    })));
    return maxValue;
  }, [plans]);

  // const options = useMemo(() => {
  //   const hour = 24;
  //   const defaultMinues = [
  //     { text: "0分钟", value: 0 },
  //     { text: "30分钟", value: 0.5 },
  //   ];
  //   return new Array(hour - maxValue).fill(0).map((__item, index) => ({
  //     text: `${index + maxValue + 1}小时`,
  //     value: index + maxValue + 1,
  //     children:
  //       index == hour - maxValue - 1 ? [defaultMinues[0]] : defaultMinues,
  //   }));
  // }, [maxValue]);

  var onChangeType = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (index) {
    var currentPlan = normalizePlans[index];
    if (currentPlan.type == _constants__WEBPACK_IMPORTED_MODULE_2__.TIMETYPES.customization) {
      setDurationTime(maxValue + 1);
      setCustomizationVisible(true);
    } else {
      onChange(currentPlan.value);
    }
    setType(index);
  }, [setType, setDurationTime, setCustomizationVisible, normalizePlans, onChange, maxValue]);

  // const onConfirmPicker = useCallback(
  //   (__options: PickerOption[], values: [number, number]) => {
  //     const total = values.reduce((total, item) => total + item, 0);
  //     console.log("onConfirmPicker", total, values);
  //     onChange(total);
  //     setDurationTime(total);
  //   },
  //   [setDurationTime, onChange]
  // );

  /**
   * 用于获取当前时间的方法
   * 支持预约模式下 使用currentTime 即用户选择时间
   * 非支持预约模式下 区分场景 (扫码场景->当前时间、其他场景->当前时间 + 5分钟)
   */
  var getCurrentTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {
    return supportAppointment ? currentTime : !scanCode ? dayjs__WEBPACK_IMPORTED_MODULE_0___default()().add(5, "m") : dayjs__WEBPACK_IMPORTED_MODULE_0___default()();
  }, [currentTime]);
  var getRange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {
    var currentTime = getCurrentTime();
    var currentPlan = normalizePlans[type];
    if (currentPlan && currentPlan.type == _constants__WEBPACK_IMPORTED_MODULE_2__.TIMETYPES.customization) {
      // 自定义逻辑
      return [currentTime, currentTime.add(durationTime, "h")];
    }
    return [currentTime, currentTime.add(currentPlan.value, "h")];
  }, [type, normalizePlans, durationTime, getCurrentTime]);
  var range = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(function () {
    return getRange();
  }, [getRange]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    props.onCalculationPayment();
  }, [range]);

  // const getWeekDay = useCallback((day: number, index: number) => {
  //   const weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
  //   return index ? weekdays[day] : "今天";
  // }, []);

  // const changeTime = useCallback(() => {
  //   setVisible(true);
  // }, [setVisible]);

  // const onConfirm = useCallback(
  //   (__options: PickerOption[], values: [string, string]) => {
  //     const [hour, minute] = values;
  //     const now = dayjs();
  //     const current = currentTime.set("hour", +hour).set("minute", +minute);
  //     if (current.isAfter(now)) {
  //       setCurrentTime(current);
  //     } else {
  //       Taro.showToast({ title: "不能设置历史时间", icon: "none" });
  //     }
  //   },
  //   [currentTime, setCurrentTime]
  // );

  var getData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function () {
    return getRange().map(function (item) {
      return item.format("YYYY-MM-DD HH:mm:ss");
    });
  }, [getRange]);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, function () {
    return {
      getData: getData
    };
  }, [range]);

  // const times = useMemo(() => {
  //   const nums = 5;
  //   const today = dayjs();
  //   return new Array(nums)
  //     .fill("")
  //     .map((__item, index) => today.add(index, "day"));
  // }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
    className: "flex items-center space-x-3",
    children: normalizePlans.map(function (item, index) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
        className: (0,clsx__WEBPACK_IMPORTED_MODULE_7__["default"])("text-center rounded-md h-8 flex justify-center items-center p-1 text-xs flex-1", {
          "bg-primary text-background": index === type,
          "bg-bgt text-foreground": index !== type
        }),
        onClick: function onClick() {
          return onChangeType(index);
        },
        children: item.name
      });
    })
  });
  //   return (
  //     <>
  //       <View className="bg-bgt p-3 border-1 rounded-lg flex gap-4 flex-col">
  //         {supportAppointment && !scanCode && (
  //           <View className="grid grid-cols-5 gap-2 w-full">
  //             {times.map((item, index) => (
  //               <View
  //                 className={clsx(
  //                   "text-center rounded-lg bg-bgt border border-color-muted-foreground p-1",
  //                   {
  //                     "text-primary border-primary": index === current,
  //                   }
  //                 )}
  //                 onClick={() => changeCurrent(index)}
  //               >
  //                 <View className="text-sm">{item.format("MM/DD")}</View>
  //                 <View className="text-sm">
  //                   {getWeekDay(item.day(), index)}
  //                 </View>
  //               </View>
  //             ))}
  //           </View>
  //         )}
  //         <View className="flex items-center">
  //           <Text className="text-muted-foreground mr-2 text-xs">
  //             时长选择:
  //           </Text>
  //           <View className="flex items-center gap-1 flex-1">
  //             {normalizePlans.map((item, index) => (
  //               <View
  //                 className={clsx(
  //                   "text-center rounded-lg bg-bgt border border-color-muted-foreground p-1 text-xs flex-1",
  //                   {
  //                     "bg-primary border-primary text-background": index === type,
  //                   }
  //                 )}
  //                 onClick={() => onChangeType(index)}
  //               >
  //                 {item.name}
  //               </View>
  //             ))}
  //           </View>
  //         </View>
  //         {supportAppointment && !scanCode && (
  //           <View className="flex items-center">
  //             <Text className="text-muted-foreground mr-2 text-xs">
  //               时间选择:
  //             </Text>
  //             <View className="flex gap-2 flex-1">
  //               <View
  //                 className="border text-primary border-primary rounded-lg p-1 text-xs flex-1 flex items-center justify-center"
  //                 onClick={changeTime}
  //               >
  //                 {range[0].format("HH:mm")}
  //                 <Edit className="ml-2" />
  //               </View>
  //               ~
  //               <View className="border border-color-muted-foreground rounded-lg p-1 text-xs flex-1 text-center">
  //                 {range[1].format("HH:mm")}
  //               </View>
  //             </View>
  //           </View>
  //         )}
  //       </View>
  //       <DatePicker
  //         type="hour-minutes"
  //         title="日期选择"
  //         visible={visible}
  //         defaultValue={currentTime.toDate()}
  //         showChinese
  //         onClose={() => setVisible(false)}
  //         threeDimensional={false}
  //         onConfirm={onConfirm}
  //       />
  //       <Picker
  //         visible={customizationVisible}
  //         options={options}
  //         title="自定义时间"
  //         onConfirm={onConfirmPicker}
  //         onClose={() => setCustomizationVisible(false)}
  //       ></Picker>
  //     </>
  //   );
});
/* harmony default export */ __webpack_exports__["default"] = (DurationTime);

/***/ }),

/***/ "./src/components/bussiness/Order/AverageButton.tsx":
/*!**********************************************************!*\
  !*** ./src/components/bussiness/Order/AverageButton.tsx ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");









var AverageButton = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function (props, ref) {
  var code = props.code,
    status = props.status,
    isOwner = props.isOwner,
    getPayType = props.getPayType,
    validatePhone = props.validatePhone,
    updateStatus = props.updateStatus;
  var configuration = {
    buttonText: "AA支付",
    mode: _Button__WEBPACK_IMPORTED_MODULE_1__.MODES.Light
  };
  var handleResult = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().mark(function _callee(response) {
      var result, orderNo, extra;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            result = response.result, orderNo = response.orderNo;
            if (!(result === _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.PENDING)) {
              _context.next = 7;
              break;
            }
            extra = response.extra;
            _context.next = 5;
            return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().requestPayment((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__["default"])({}, extra), {}, {
              package: extra.packageStr,
              success: function success() {
                return updateStatus && updateStatus();
              },
              fail: function fail() {
                return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].order.cancelOrder(orderNo).then(function () {
                  return updateStatus && updateStatus();
                });
              }
            }));
          case 5:
            _context.next = 8;
            break;
          case 7:
            updateStatus && updateStatus();
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [updateStatus]);
  var pay = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().mark(function _callee2() {
    var hasPhone, payType, _yield$api$order$othe, data;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return validatePhone();
        case 2:
          hasPhone = _context2.sent;
          if (hasPhone) {
            _context2.next = 5;
            break;
          }
          return _context2.abrupt("return");
        case 5:
          payType = getPayType();
          _context2.next = 8;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].order.otherFinishOrder(code, {
            type: _constants__WEBPACK_IMPORTED_MODULE_4__.OTHERFINISHTYPES.HALF,
            payType: payType
          });
        case 8:
          _yield$api$order$othe = _context2.sent;
          data = _yield$api$order$othe.data;
          _context2.next = 12;
          return handleResult(data);
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [getPayType, handleResult]);
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function () {
    return {
      finish: pay
    };
  }, [code]);
  if (!isOwner && status === _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.USING) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_1__["default"], {
      mode: configuration.mode,
      buttonText: configuration.buttonText,
      onClick: function onClick() {
        return pay();
      }
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment, {});
});
/* harmony default export */ __webpack_exports__["default"] = (AverageButton);

/***/ }),

/***/ "./src/components/bussiness/Order/CancelButton.tsx":
/*!*********************************************************!*\
  !*** ./src/components/bussiness/Order/CancelButton.tsx ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");








var CancelButton = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function (props, ref) {
  var code = props.code,
    status = props.status,
    isOwner = props.isOwner;
  var configuration = {
    buttonText: "取消订单",
    mode: _Button__WEBPACK_IMPORTED_MODULE_1__.MODES.DANGER
  };
  var cancel = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().mark(function _callee() {
    var _yield$Taro$showModal, confirm;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showModal({
            title: "提示",
            content: "是否确认当前操作",
            confirmText: "确定"
          });
        case 2:
          _yield$Taro$showModal = _context.sent;
          confirm = _yield$Taro$showModal.confirm;
          if (!confirm) {
            _context.next = 8;
            break;
          }
          _context.next = 7;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].order.cancelOrder(code);
        case 7:
          props.updateStatus && props.updateStatus();
        case 8:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [code, props.updateStatus]);
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function () {
    return {
      cancel: cancel
    };
  }, [code]);
  if (status === _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.PAID && isOwner) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_1__["default"], {
      mode: configuration.mode,
      buttonText: configuration.buttonText,
      onClick: cancel
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment, {});
});
/* harmony default export */ __webpack_exports__["default"] = (CancelButton);

/***/ }),

/***/ "./src/components/bussiness/Order/FinishButton.tsx":
/*!*********************************************************!*\
  !*** ./src/components/bussiness/Order/FinishButton.tsx ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");








var FinishButton = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function (props, ref) {
  var code = props.code,
    status = props.status,
    isOwner = props.isOwner;
  var configuration = {
    buttonText: "结束订单",
    mode: _Button__WEBPACK_IMPORTED_MODULE_1__.MODES.DANGER
  };
  var finish = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().mark(function _callee() {
    var _yield$Taro$showModal, confirm;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showModal({
            title: "提示",
            content: "您当前的操作即将关闭球桌，是否确认",
            confirmText: "确定"
          });
        case 2:
          _yield$Taro$showModal = _context.sent;
          confirm = _yield$Taro$showModal.confirm;
          if (!confirm) {
            _context.next = 8;
            break;
          }
          _context.next = 7;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].order.finishOrder(code);
        case 7:
          props.updateStatus && props.updateStatus();
        case 8:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [code, props.updateStatus]);
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function () {
    return {
      finish: finish
    };
  }, [code]);
  if (status === _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.USING && isOwner) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_1__["default"], {
      mode: configuration.mode,
      buttonText: configuration.buttonText,
      onClick: finish
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment, {});
});
/* harmony default export */ __webpack_exports__["default"] = (FinishButton);

/***/ }),

/***/ "./src/components/bussiness/Order/ForceButton.tsx":
/*!********************************************************!*\
  !*** ./src/components/bussiness/Order/ForceButton.tsx ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");









var ForceButton = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function (props, ref) {
  var code = props.code,
    status = props.status,
    isOwner = props.isOwner,
    getPayType = props.getPayType,
    validatePhone = props.validatePhone,
    updateStatus = props.updateStatus;
  var configuration = {
    buttonText: "支付全部",
    mode: _Button__WEBPACK_IMPORTED_MODULE_1__.MODES.Dark
  };
  var handleResult = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().mark(function _callee(response) {
      var result, orderNo, extra;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            result = response.result, orderNo = response.orderNo;
            if (!(result === _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.PENDING)) {
              _context.next = 7;
              break;
            }
            extra = response.extra;
            _context.next = 5;
            return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().requestPayment((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_8__["default"])({}, extra), {}, {
              package: extra.packageStr,
              success: function success() {
                return updateStatus && updateStatus();
              },
              fail: function fail() {
                return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].order.cancelOrder(orderNo).then(function () {
                  return updateStatus && updateStatus();
                });
              }
            }));
          case 5:
            _context.next = 8;
            break;
          case 7:
            updateStatus && updateStatus();
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [updateStatus]);
  var pay = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().mark(function _callee2() {
    var hasPhone, payType, _yield$api$order$othe, data;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return validatePhone();
        case 2:
          hasPhone = _context2.sent;
          if (hasPhone) {
            _context2.next = 5;
            break;
          }
          return _context2.abrupt("return");
        case 5:
          payType = getPayType();
          _context2.next = 8;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].order.otherFinishOrder(code, {
            type: _constants__WEBPACK_IMPORTED_MODULE_4__.OTHERFINISHTYPES.ALL,
            payType: payType
          });
        case 8:
          _yield$api$order$othe = _context2.sent;
          data = _yield$api$order$othe.data;
          _context2.next = 12;
          return handleResult(data);
        case 12:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [getPayType, validatePhone, handleResult]);
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function () {
    return {
      finish: pay
    };
  }, [code]);
  if (!isOwner && status === _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.USING) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_1__["default"], {
      mode: configuration.mode,
      buttonText: configuration.buttonText,
      onClick: function onClick() {
        return pay();
      }
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment, {});
});
/* harmony default export */ __webpack_exports__["default"] = (ForceButton);

/***/ }),

/***/ "./src/components/bussiness/Order/Panel.tsx":
/*!**************************************************!*\
  !*** ./src/components/bussiness/Order/Panel.tsx ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_CellGroup_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/CellGroup/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/CellGroup/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_CellGroup__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/CellGroup */ "./node_modules/@nutui/nutui-react-taro/dist/esm/cellgroup.taro-DTLGMR_c.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Cell/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell */ "./node_modules/@nutui/nutui-react-taro/dist/esm/cell.taro-DWLhb5m6.js");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");










var Panel = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.forwardRef)(function (props, ref) {
  var order = props.order;
  var code = order.orderNo,
    status = order.status,
    startTime = order.startTime,
    endTime = order.endTime,
    realAmount = order.realAmount;
  // 复制订单编号

  var realPayment = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    return (0,_utils__WEBPACK_IMPORTED_MODULE_3__.toPrice)(realAmount, 2);
  }, [realAmount]);
  console.log(realPayment);
  var statusName = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    var maps = [{
      code: _constants__WEBPACK_IMPORTED_MODULE_2__.ORDERTYPES.PENDING,
      name: "待支付"
    }, {
      code: _constants__WEBPACK_IMPORTED_MODULE_2__.ORDERTYPES.PAID,
      name: "已支付"
    }, {
      code: _constants__WEBPACK_IMPORTED_MODULE_2__.ORDERTYPES.USING,
      name: "使用中"
    }, {
      code: _constants__WEBPACK_IMPORTED_MODULE_2__.ORDERTYPES.CANCEL,
      name: "已取消"
    }, {
      code: _constants__WEBPACK_IMPORTED_MODULE_2__.ORDERTYPES.FINISH,
      name: "已完成"
    }];
    var current = maps.find(function (item) {
      return item.code === status;
    });
    if (current) return current.name;
    return false;
  }, [status]);
  var clipboardData = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(function () {
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().setClipboardData({
      data: code,
      success: function success() {
        _tarojs_taro__WEBPACK_IMPORTED_MODULE_4___default().showToast({
          title: "复制成功",
          icon: "none"
        });
      }
    });
  }, [code]);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useImperativeHandle)(ref, function () {
    return {
      statusName: statusName,
      clipboardData: clipboardData
    };
  }, [statusName]);

  // 订单状态的判断
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsxs)(_nutui_nutui_react_taro_dist_esm_CellGroup__WEBPACK_IMPORTED_MODULE_7__.C, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_8__.C, {
      title: "\u8BA2\u5355\u7F16\u53F7",
      extra: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_9__.View, {
        className: "flex items-center text-sm",
        onClick: function onClick() {
          clipboardData();
        },
        children: code
      })
    }), statusName && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_8__.C, {
      title: "\u8BA2\u5355\u72B6\u6001",
      extra: statusName
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_8__.C, {
      title: "\u5F00\u59CB\u65F6\u95F4",
      extra: startTime
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_8__.C, {
      title: "\u7ED3\u675F\u65F6\u95F4",
      extra: endTime
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_8__.C, {
      title: "\u5F53\u524D\u6D88\u8D39",
      extra: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_6__.jsx)("div", {
        className: "text-primary",
        children: realPayment
      })
    })]
  });
});
/* harmony default export */ __webpack_exports__["default"] = (Panel);

/***/ }),

/***/ "./src/components/bussiness/Order/QuickStartButton.tsx":
/*!*************************************************************!*\
  !*** ./src/components/bussiness/Order/QuickStartButton.tsx ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");








var QuickStartButton = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function (props, ref) {
  var code = props.code,
    status = props.status,
    isOwner = props.isOwner;
  var configuration = {
    buttonText: "立即开台",
    mode: _Button__WEBPACK_IMPORTED_MODULE_1__.MODES.Dark
  };
  var quickStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_6__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().mark(function _callee() {
    var _yield$Taro$showModal, confirm;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_7__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showModal({
            title: "提示",
            content: "是否确认当前操作",
            confirmText: "确定"
          });
        case 2:
          _yield$Taro$showModal = _context.sent;
          confirm = _yield$Taro$showModal.confirm;
          if (!confirm) {
            _context.next = 8;
            break;
          }
          _context.next = 7;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].order.quickStart(code);
        case 7:
          props.updateStatus && props.updateStatus();
        case 8:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [code, props.updateStatus]);
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function () {
    return {
      quickStart: quickStart
    };
  }, [code]);
  if (status === _constants__WEBPACK_IMPORTED_MODULE_4__.ORDERTYPES.PAID && isOwner) {
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(_Button__WEBPACK_IMPORTED_MODULE_1__["default"], {
      mode: configuration.mode,
      buttonText: configuration.buttonText,
      onClick: quickStart
    });
  }
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_5__.Fragment, {});
});
/* harmony default export */ __webpack_exports__["default"] = (QuickStartButton);

/***/ }),

/***/ "./src/components/bussiness/Order/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/bussiness/Order/index.tsx ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   OrderPanel: function() { return /* reexport safe */ _Panel__WEBPACK_IMPORTED_MODULE_0__["default"]; }
/* harmony export */ });
/* harmony import */ var _Panel__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Panel */ "./src/components/bussiness/Order/Panel.tsx");



/***/ }),

/***/ "./src/components/bussiness/PaymentMethod/index.tsx":
/*!**********************************************************!*\
  !*** ./src/components/bussiness/PaymentMethod/index.tsx ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_CellGroup_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/CellGroup/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/CellGroup/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_CellGroup__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/CellGroup */ "./node_modules/@nutui/nutui-react-taro/dist/esm/cellgroup.taro-DTLGMR_c.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Cell/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell */ "./node_modules/@nutui/nutui-react-taro/dist/esm/cell.taro-DWLhb5m6.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _components_bussiness_Coupon__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/bussiness/Coupon */ "./src/components/bussiness/Coupon/index.tsx");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! clsx */ "./node_modules/clsx/dist/clsx.mjs");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _VerificationCode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../VerificationCode */ "./src/components/bussiness/VerificationCode/index.tsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
















var FROM_TYPES = /*#__PURE__*/function (FROM_TYPES) {
  FROM_TYPES[FROM_TYPES["NONE"] = 0] = "NONE";
  FROM_TYPES[FROM_TYPES["WEAPP"] = 1] = "WEAPP";
  FROM_TYPES[FROM_TYPES["QRCODE"] = 2] = "QRCODE";
  return FROM_TYPES;
}(FROM_TYPES || {}); // TODO: 支付模式
// 他人支付 只允许 钱包支付/微信支付
// 自己支付 允许所有支付方式
/* harmony default export */ __webpack_exports__["default"] = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_6__.forwardRef)(function (props, ref) {
  var _wallet$coupons;
  var scene = props.scene,
    tableId = props.tableId,
    onPay = props.onPay,
    wallet = props.wallet,
    coupons = props.coupons,
    _props$isOwner = props.isOwner,
    isOwner = _props$isOwner === void 0 ? true : _props$isOwner;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({}),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_9__["default"])(_useState, 2),
    extra = _useState2[0],
    setExtra = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.NUMBER),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_9__["default"])(_useState3, 2),
    currentType = _useState4[0],
    setCurrentType = _useState4[1];
  var verificationRef = (0,react__WEBPACK_IMPORTED_MODULE_6__.useRef)(null);
  var status = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function (type) {
    return type === currentType ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_5__.Checked, {
      color: "hsl(var(--primary))",
      size: 20
    }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_5__.CheckNormal, {
      size: 20
    });
  }, [currentType]);
  var onVerificationCode = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().mark(function _callee() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.MEITUAN);
        case 1:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [setCurrentType]);
  var onCoupon = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_10__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().mark(function _callee2(extra) {
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_11__["default"])().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            setExtra(extra);
            setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.COUPON);
          case 2:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function (_x) {
      return _ref2.apply(this, arguments);
    };
  }(), []);
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if ([_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.MEITUAN, _constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.COUPON].includes(currentType)) {
      onPay();
      setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.NUMBER);
    }
  }, [currentType]);
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    if (!isOwner) {
      if ((wallet === null || wallet === void 0 ? void 0 : wallet.memberBalance) > 0) setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.SINGLE);else setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.WECHAT);
    } else if (currentType == _constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.NUMBER && (wallet === null || wallet === void 0 ? void 0 : wallet.memberBalance) <= 0) {
      if (wallet.clubBalance <= 0) {
        if (scene === FROM_TYPES.WEAPP) setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.WECHAT);else setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.DEPOSIT);
      } else {
        setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.SINGLE);
      }
    } else if (currentType == _constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.SINGLE && wallet.clubBalance <= 0) {
      if (scene === FROM_TYPES.WEAPP) setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.WECHAT);else setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.DEPOSIT);
    }
  }, [wallet]);
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useImperativeHandle)(ref, function () {
    var getCurrent = currentType == _constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.MEITUAN ? verificationRef.current.getCurrent : function () {
      return extra;
    };
    return {
      type: currentType,
      getCurrent: getCurrent
    };
  }, [currentType, verificationRef]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(_nutui_nutui_react_taro_dist_esm_CellGroup__WEBPACK_IMPORTED_MODULE_12__.C, {
      children: [tableId && isOwner && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_13__.C, {
        title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "flex items-center gap-1",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "i-ph-ticket-fill w-5 h-5 text-yellow-500"
          }), "\u7F8E\u56E2\u5238/\u6296\u97F3\u5238"]
        }),
        extra: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_VerificationCode__WEBPACK_IMPORTED_MODULE_7__.VerificationCodeButton, {
          tableId: tableId,
          ref: verificationRef,
          onUse: onVerificationCode,
          title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
            className: "text-foreground",
            children: "\u53EF\u7528\u56E2\u8D2D\u5238"
          }),
          render: function render() {
            var len = ((wallet === null || wallet === void 0 ? void 0 : wallet.tickets) || []).length;
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
              onClick: function onClick() {
                var _verificationRef$curr;
                return (_verificationRef$curr = verificationRef.current) === null || _verificationRef$curr === void 0 ? void 0 : _verificationRef$curr.setVisible(true);
              },
              className: (0,clsx__WEBPACK_IMPORTED_MODULE_15__["default"])({
                "text-primary": len > 0,
                "text-foregrounds95": len === 0
              }),
              children: len ? "".concat(len, "\u5F20\u53EF\u7528") : "点击兑换"
            });
          }
        })
      }), (coupons === null || coupons === void 0 ? void 0 : coupons.find(function (v) {
        return v.list.length > 0;
      })) && isOwner && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_13__.C, {
        title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "flex items-center gap-1",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "i-ph-ticket-fill w-5 h-5 text-pink-500"
          }), "\u4F18\u60E0\u5238"]
        }),
        extra: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_components_bussiness_Coupon__WEBPACK_IMPORTED_MODULE_2__["default"], {
          tabs: coupons,
          onUse: onCoupon,
          render: function render() {
            var couponLen = ((wallet === null || wallet === void 0 ? void 0 : wallet.coupons) || []).length;
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
              className: (0,clsx__WEBPACK_IMPORTED_MODULE_15__["default"])({
                "text-primary": couponLen > 0,
                "text-foregrounds95": couponLen === 0
              }),
              children: couponLen ? "".concat(couponLen, "\u5F20\u53EF\u7528") : "点击购买"
            });
          },
          ckey: (wallet === null || wallet === void 0 || (_wallet$coupons = wallet.coupons) === null || _wallet$coupons === void 0 ? void 0 : _wallet$coupons.length) > 0 ? 2 : 1
        })
      }), isOwner && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_13__.C, {
        title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "flex items-center gap-1",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "i-ph-crown-simple-fill w-5 h-5 text-primary"
          }), "\u4F1A\u5458\u652F\u4ED8 (\u4F59\u989D:", " ", /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "text-primary",
            children: (0,_utils__WEBPACK_IMPORTED_MODULE_4__.toPrice)(wallet === null || wallet === void 0 ? void 0 : wallet.memberBalance, 2)
          }), ")"]
        }),
        extra: status(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.NUMBER),
        onClick: function onClick() {
          return setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.NUMBER);
        }
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_13__.C, {
        title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "flex items-center gap-1",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "i-iconoir-credit-card-solid w-5 h-5 text-red-500"
          }), "\u94B1\u5305\u652F\u4ED8 (\u4F59\u989D:", " ", /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "text-primary",
            children: (0,_utils__WEBPACK_IMPORTED_MODULE_4__.toPrice)(wallet === null || wallet === void 0 ? void 0 : wallet.clubBalance, 2)
          }), ")"]
        }),
        extra: status(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.SINGLE),
        onClick: function onClick() {
          return setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.SINGLE);
        }
      }), (!isOwner || scene === FROM_TYPES.WEAPP) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_13__.C, {
        title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "flex items-center gap-1",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "i-simple-icons-wechat w-5 h-5 text-green-500"
          }), "\u5FAE\u4FE1\u652F\u4ED8"]
        }),
        extra: status(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.WECHAT),
        onClick: function onClick() {
          return setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.WECHAT);
        }
      }), isOwner && scene === FROM_TYPES.QRCODE && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_13__.C, {
        title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "flex items-center gap-1",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "i-ant-design-pay-circle-filled w-5 h-5 text-green-500"
          }), "\u5FAE\u4FE1\u652F\u4ED8 (\u9884\u4ED8:", " ", /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "text-primary",
            children: (0,_utils__WEBPACK_IMPORTED_MODULE_4__.toPrice)(props.deposit || 0, 2)
          }), "\u6309\u5206\u949F\u8BA1\u7B97)"]
        }),
        extra: status(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.DEPOSIT),
        onClick: function onClick() {
          return setCurrentType(_constants__WEBPACK_IMPORTED_MODULE_3__.PAYTYPES.DEPOSIT);
        }
      })]
    }), isOwner && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsxs)("div", {
      className: "text-xs text-muted-foreground gap-1 flex flex-col",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
        children: " \u6E29\u99A8\u63D0\u793A:"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
        children: "1\u3001\u62A2\u684C\u6210\u529F\u540E\uFF0C\u8BF7\u60A8\u57285\u5206\u949F\u5185\u5230\u5E97\u5F00\u53F0\uFF0C\u8D85\u65F6\u5C06\u81EA\u52A8\u8BA1\u8D39\u3002\u5F00\u53F0\u540E\u4E94\u5206\u949F\u5185\u65E0\u6CD5\u7ED3\u675F\u8BA2\u5355;"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
        children: "2\u3001\u53F0\u7403\u8D39\u6309\u5206\u949F\u8BA1\u7B97\uFF0C\u63D0\u524D\u7ED3\u7B97\u94B1\u539F\u8DEF\u8FD4\u8FD8;"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
        children: "3\u3001\u7F8E\u56E2\u5238/\u6296\u97F3\u5238:\u8BF7\u60A8\u5C06\u56E2\u8D2D\u5238\u622A\u56FE\uFF0C\u70B9\u51FB\u672C\u9875\u9762\"\u7F8E\u56E2\u5238/\u6296\u97F3\u5238\"-\"\u70B9\u51FB\u9A8C\u5238\"\u626B\u7801\u9A8C\u5238\u6210\u529F\uFF0C\u5373\u53EF\u5F00\u53F0;"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
        children: "4\u3001\u7CFB\u7EDF\u4F1A\u81EA\u52A8\u8BC6\u522B\u7F8E\u56E2\u56E2\u8D2D\u5238\uFF0C\u82E5\u65E0\u6CD5\u8BC6\u522B\uFF0C\u8BF7\u5C1D\u8BD5\u91CD\u65B0\u8FDB\u5165\"\u70B9\u51FB\u5151\u6362\";"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
        children: "5\u3001\u82E5\u65E0\u6CD5\u81EA\u52A8\u8BC6\u522B\u56E2\u8D2D\u5238\uFF0C\u8BF7\u60A8\u70B9\u51FB\u5C0F\u7A0B\u5E8F\u56E2\u8D2D\u5238\u529F\u80FD-\u624B\u52A8\u9A8C\u5238\u3002"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.jsx)("div", {
        children: "6\u3001\u4F18\u60E0\u5238\u4F7F\u7528\u4E4B\u540E\u4E0D\u4F1A\u9000\u8FD8\u3002"
      })]
    })]
  });
}));

/***/ }),

/***/ "./src/components/bussiness/VerificationCode/ScanCode.tsx":
/*!****************************************************************!*\
  !*** ./src/components/bussiness/VerificationCode/ScanCode.tsx ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");







var configuration = {
  size: 48,
  codes: ["qrCode"]
};
/* harmony default export */ __webpack_exports__["default"] = (/*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(function (props, ref) {
  var _Object$assign = Object.assign({}, configuration, props),
    size = _Object$assign.size,
    codes = _Object$assign.codes;
  var getCode = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_4__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_5__["default"])().mark(function _callee() {
    var scanResult;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_5__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_1___default().scanCode({
            scanType: codes
          });
        case 2:
          scanResult = _context.sent;
          props.onChange && props.onChange(scanResult.result);
          return _context.abrupt("return", scanResult.result);
        case 5:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [codes, props.onChange]);
  (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function () {
    return {
      getCode: getCode
    };
  });
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_6__.View, {
    className: "p-2 flex justify-center items-center text-foreground",
    onClick: getCode,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_0__.Scan, {
      size: size
    })
  });
}));

/***/ }),

/***/ "./src/components/bussiness/VerificationCode/index.tsx":
/*!*************************************************************!*\
  !*** ./src/components/bussiness/VerificationCode/index.tsx ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   VerificationCodeButton: function() { return /* binding */ VerificationCodeButton; }
/* harmony export */ });
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Popup/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup */ "./node_modules/@nutui/nutui-react-taro/dist/esm/popup.taro-JynjcLCn.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Input_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Input/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Input/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Input */ "./node_modules/@nutui/nutui-react-taro/dist/esm/input.taro-s1Zw62KR.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _constants_ticket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/constants/ticket */ "./src/constants/ticket.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);
/* harmony import */ var _Card_Ticket__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../Card/Ticket */ "./src/components/bussiness/Card/Ticket.tsx");
/* harmony import */ var _ScanCode__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ScanCode */ "./src/components/bussiness/VerificationCode/ScanCode.tsx");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");

















var VerificationCode = function VerificationCode(props) {
  var onSuccess = props.onSuccess,
    tableId = props.tableId;
  // 输入验证 扫码验证
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_10__["default"])(_useState, 2),
    value = _useState2[0],
    setValue = _useState2[1];
  var verificationCode = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_11__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_12__["default"])().mark(function _callee(code, tableId) {
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_12__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            setValue(code);
            _context.next = 3;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].ticket.check({
              ticketNo: code,
              tableId: tableId
            });
          case 3:
            onSuccess && onSuccess();
          case 4:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x, _x2) {
      return _ref.apply(this, arguments);
    };
  }(), [setValue, onSuccess]);

  //

  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_13__.View, {
    className: "flex bg-bgt items-center gap-2 rounded-md overflow-hidden",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_14__.C, {
      className: "flex-1",
      theme: {
        "--nutui-input-background-color": "hsl(var(--background-third))",
        "--nutui-input-color": "#f5f5f5",
        "--nutui-input-border-bottom": "hsl(var(--primary)"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_nutui_nutui_react_taro_dist_esm_Input__WEBPACK_IMPORTED_MODULE_15__.I, {
        placeholder: "\u8F93\u5165\u5361\u5238\u7801\u6216\u626B\u63CF\u4E8C\u7EF4\u7801\u9A8C\u5238",
        type: "text",
        confirmType: "done",
        onConfirm: function onConfirm(e) {
          return verificationCode(e.target.value, tableId);
        },
        onChange: function onChange(value) {
          return setValue(value);
        },
        value: value
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_ScanCode__WEBPACK_IMPORTED_MODULE_7__["default"], {
      onChange: function onChange(value) {
        return verificationCode(value, tableId);
      },
      size: 28
    })]
  });
};
var VerificationCodeButton = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_5__.forwardRef)(function (props, ref) {
  var tableId = props.tableId,
    render = props.render,
    title = props.title,
    onUse = props.onUse;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_10__["default"])(_useState3, 2),
    visible = _useState4[0],
    setVisible = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_10__["default"])(_useState5, 2),
    list = _useState6[0],
    setList = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(),
    _useState8 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_10__["default"])(_useState7, 2),
    item = _useState8[0],
    setItem = _useState8[1];
  var getList = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_11__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_12__["default"])().mark(function _callee2() {
    var _yield$api$ticket$get, data, statusCode;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_12__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().showLoading({
            title: "加载中"
          });
          _context2.prev = 1;
          _context2.next = 4;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_3__["default"].ticket.getList({
            tableId: tableId
          });
        case 4:
          _yield$api$ticket$get = _context2.sent;
          data = _yield$api$ticket$get.data;
          statusCode = _yield$api$ticket$get.statusCode;
          if (!(statusCode !== 200)) {
            _context2.next = 9;
            break;
          }
          return _context2.abrupt("return");
        case 9:
          setList(data);
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().hideLoading();
          _context2.next = 15;
          break;
        case 13:
          _context2.prev = 13;
          _context2.t0 = _context2["catch"](1);
        case 15:
        case "end":
          return _context2.stop();
      }
    }, _callee2, null, [[1, 13]]);
  })), [tableId, setList]);
  var toUse = (0,react__WEBPACK_IMPORTED_MODULE_5__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_11__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_12__["default"])().mark(function _callee3(item) {
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_12__["default"])().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            // const { confirm } = await Taro.showModal({
            //   title: "提示",
            //   content: "确认使用该团购券支付订单么",
            //   confirmText: "确定",
            // });
            // if (confirm) {
            setItem(item);
            return _context3.abrupt("return", onUse && onUse(item).then(function () {
              return setVisible(false);
            }));
          case 2:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function (_x3) {
      return _ref3.apply(this, arguments);
    };
  }(), [onUse, setItem, setVisible]);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)(function () {
    visible && getList();
  }, [visible]);
  var canUseList = (0,react__WEBPACK_IMPORTED_MODULE_5__.useMemo)(function () {
    return list.filter(function (item) {
      return [_constants_ticket__WEBPACK_IMPORTED_MODULE_4__.TICKETSTATUS.TRUE].includes(item.status);
    });
  }, [list]);
  (0,react__WEBPACK_IMPORTED_MODULE_5__.useImperativeHandle)(ref, function () {
    return {
      setVisible: setVisible,
      getCurrent: function getCurrent() {
        return item;
      }
    };
  }, [setVisible, visible, item]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.Fragment, {
    children: [render ? render(canUseList, item) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_13__.View, {
      children: "\u4F18\u60E0\u5238"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_16__.P, {
      title: title,
      position: "bottom",
      closeable: true,
      visible: visible,
      onClose: function onClose() {
        return setVisible(false);
      },
      style: {
        width: "100%",
        height: "100%",
        backgroundColor: "#000",
        overflow: "hidden",
        display: visible ? "flex" : "none",
        flexDirection: "column"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_13__.View, {
        className: "p-3 text-white",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(VerificationCode, {
          onSuccess: function onSuccess() {
            return getList();
          },
          tableId: tableId
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_13__.View, {
          className: "py-10 flex-col flex gap-4",
          children: canUseList.map(function (item) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_Card_Ticket__WEBPACK_IMPORTED_MODULE_6__["default"], {
              item: item,
              onUse: function onUse() {
                return toUse(item);
              }
            });
          })
        })]
      })
    })]
  });
});
/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (VerificationCode);

/***/ }),

/***/ "./src/constants/ticket.ts":
/*!*********************************!*\
  !*** ./src/constants/ticket.ts ***!
  \*********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   TICKETSTATUS: function() { return /* binding */ TICKETSTATUS; }
/* harmony export */ });
/* unused harmony export CHANNELTYPES */
var CHANNELTYPES = /*#__PURE__*/function (CHANNELTYPES) {
  CHANNELTYPES[CHANNELTYPES["NONE"] = 0] = "NONE";
  CHANNELTYPES[CHANNELTYPES["MEITUAN"] = 1] = "MEITUAN";
  CHANNELTYPES[CHANNELTYPES["DOUYIN"] = 2] = "DOUYIN";
  return CHANNELTYPES;
}({});
var TICKETSTATUS = /*#__PURE__*/function (TICKETSTATUS) {
  TICKETSTATUS[TICKETSTATUS["FALSE"] = 0] = "FALSE";
  TICKETSTATUS[TICKETSTATUS["TRUE"] = 1] = "TRUE";
  return TICKETSTATUS;
}({});

/***/ }),

/***/ "./src/pages/confirmation/index.tsx":
/*!******************************************!*\
  !*** ./src/pages/confirmation/index.tsx ***!
  \******************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/confirmation/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/confirmation/index!./src/pages/confirmation/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/confirmation/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/confirmation/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map