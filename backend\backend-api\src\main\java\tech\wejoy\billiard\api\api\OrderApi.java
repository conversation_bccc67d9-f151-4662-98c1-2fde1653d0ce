package tech.wejoy.billiard.api.api;

import com.congeer.core.bean.Page;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.api.queue.ClubDeviceSend;
import tech.wejoy.billiard.common.bo.OrderBo;
import tech.wejoy.billiard.common.bo.OrderFinishResultBo;
import tech.wejoy.billiard.common.bo.OrderOtherFinishResultBo;
import tech.wejoy.billiard.common.bo.OrderStartResultBo;
import tech.wejoy.billiard.common.dto.OrderOtherFinishDto;
import tech.wejoy.billiard.common.dto.OrderQueryDto;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.service.OrderService;

@Path("/order")
@Tag(name = "订单相关")
@Produces(MediaType.APPLICATION_JSON)
@Authorized
@RequiredArgsConstructor
public class OrderApi {

    private final OrderService orderService;

    private final ClubDeviceSend clubDeviceSend;

    @GET
    @Path("/list")
    @Operation(summary = "订单列表")
    public Page<OrderBo> page(@BeanParam OrderQueryDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return orderService.page(dto);
    }

    @POST
    @Path("/{no}/finish")
    @Operation(summary = "完成订单")
    public OrderFinishResultBo finish(@PathParam("no") String orderNo) {
        OrderFinishResultBo finish = orderService.finish(orderNo);
        // 关闭设备
        clubDeviceSend.send(finish.getOrderNo(), DeviceOperationTypeEnum.STOP);
        return finish;
    }

    @POST
    @Path("/{no}/start")
    @Operation(summary = "开始订单")
    public OrderStartResultBo start(@PathParam("no") String orderNo) {
        OrderStartResultBo start = orderService.start(orderNo);
        clubDeviceSend.send(start.getOrderNo(), DeviceOperationTypeEnum.START);
        return start;
    }


    @POST
    @Path("/{no}/cancel")
    @Operation(summary = "取消订单")
    public void cancel(@PathParam("no") String orderNo) {
        orderService.cancel(orderNo);
    }

    @POST
    @Path("/{no}/finish/other")
    @Operation(summary = "完成订单")
    public OrderOtherFinishResultBo finishOther(@PathParam("no") String orderNo, OrderOtherFinishDto dto) {
        dto.setOrderNo(orderNo);
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        OrderOtherFinishResultBo finish = orderService.finishOther(dto);
        // 关闭设备
        clubDeviceSend.send(finish.getOrderNo(), DeviceOperationTypeEnum.STOP);
        return finish;
    }

}
