package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class DouyinTicket extends BaseEntity {

    private String code;

    private String originCode;

    @Column(columnDefinition = "TEXT")
    private String encryptedCode;

    @Column(columnDefinition = "TEXT")
    private String encryptedData;

    private Long dealId;

    private String skuId;

    private IsEnum used;

    private String certificateId;

    private String verifyId;

    private LocalDateTime endDate;

}
