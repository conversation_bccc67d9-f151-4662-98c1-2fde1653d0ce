package tech.wejoy.billiard.common.third.meituan.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReceiptConsumeRequest extends MeituanRequest {

    private String requestId;

    private String receiptCode;

    private String openShopUuid;

    private Integer count;

    private String appShopAccount;

    private String appShopAccountName;

}
