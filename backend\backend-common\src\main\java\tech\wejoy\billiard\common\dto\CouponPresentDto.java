package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Data
public class CouponPresentDto {

    @Schema(description = "门店ID")
    private Long clubId;

    @Schema(description = "优惠券ID")
    private Long couponId;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "数量")
    private Integer count;

    @Schema(description = "备注")
    private String remark;

}
