package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.ClubStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClubInfo extends BaseEntity {

    private Long tenantId;

    private String name;

    @Column(columnDefinition = "text")
    private String address;

    @Column(columnDefinition = "varchar(9)")
    private String district;

    private ClubStatusEnum status;

    @Column(columnDefinition = "varchar(16)")
    private String phone;

    @Column(columnDefinition = "text")
    private String description;

    @Column(columnDefinition = "numeric(10,6)")
    private Double latitude;

    @Column(columnDefinition = "numeric(10,6)")
    private Double longitude;

    @Column(columnDefinition = "text")
    private String headImage;

    @Column(columnDefinition = "text")
    private String businessHours;

    private LocalDateTime openAt;

    private Integer tableCount;

    private BigDecimal price;

    private IsEnum flashLight;

    private IsEnum memberTips;

    private Integer memberMaxHours;

    private Integer serviceFeeRatio;

    private IsEnum enableTableGrabbing;

}
