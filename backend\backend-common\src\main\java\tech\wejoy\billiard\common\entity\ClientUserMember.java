package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClientUserMember extends BaseEntity {

    private Long userId;

    private BigDecimal balance;

    private BigDecimal totalAmount;

    private Long planId;

    private Integer memberLevel;

    private LocalDateTime expireTime;

    private Integer coins;

    private Integer points;

}
