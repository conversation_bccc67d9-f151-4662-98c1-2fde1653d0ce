package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.enums.BillTypeEnum;
import tech.wejoy.billiard.common.enums.ThirdPayTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class BillInfo extends BaseEntity {

    private Long tenantId;

    private Long clubId;

    private Long userId;

    private String billNo;

    private String orderNo;

    private BillTypeEnum type;

    private ThirdPayTypeEnum thirdPayType;

    private String thirdPayNo;

    @Column(columnDefinition = "text")
    private String payInfo;

    private BigDecimal amount;

    private BigDecimal totalAmount;

    private BigDecimal payAmount;

    private BigDecimal refundAmount;

    private BillStatusEnum status;

    private LocalDateTime payTime;

    private LocalDateTime pushTime;

    private LocalDateTime refundTime;

    @Column(columnDefinition = "text")
    private String resultInfo;

    @Column(columnDefinition = "text")
    private String refundInfo;

}
