package tech.wejoy.billiard.admin.queue;

import com.congeer.core.exception.BaseException;
import com.congeer.core.utils.TraceHelper;
import com.congeer.utils.JsonUtils;
import io.smallrye.reactive.messaging.annotations.Blocking;
import io.smallrye.reactive.messaging.rabbitmq.IncomingRabbitMQMetadata;
import io.vertx.core.json.JsonObject;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.reactive.messaging.Incoming;
import org.eclipse.microprofile.reactive.messaging.Message;
import tech.wejoy.billiard.admin.service.DeviceService;
import tech.wejoy.billiard.common.dto.DeviceOperationDto;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Optional;
import java.util.concurrent.CompletionStage;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class ClubDeviceReceive {

    private final DeviceService deviceService;

    @Incoming("club-device")
    @Blocking
    public CompletionStage<Void> receive(Message<JsonObject> incoming) {
        Optional<IncomingRabbitMQMetadata> metadata = incoming.getMetadata(IncomingRabbitMQMetadata.class);
        metadata.ifPresent(meta -> {
            Optional<ZonedDateTime> timestamp = meta.getTimestamp(ZoneId.systemDefault());
            final Optional<String> stringHeader = meta.getHeader("Trace-Id", String.class);
            stringHeader.ifPresent(TraceHelper::set);
        });
        JsonObject payload = incoming.getPayload();
        try {
            DeviceOperationDto dto = JsonUtils.toObject(payload.toString(), DeviceOperationDto.class);
            log.info("receive device operation: {}", JsonUtils.toJson(dto));
            deviceService.handleOrderDevice(dto);
        } catch (BaseException e) {
            log.warn("handle order device warn", e);
        } catch (Exception e) {
            log.error("handle order device error", e);
        }
        return incoming.ack();
    }

}
