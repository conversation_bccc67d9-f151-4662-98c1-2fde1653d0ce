package tech.wejoy.billiard.api.service;

import com.congeer.utils.BeanUtils;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.api.bo.CustomerBo;
import tech.wejoy.billiard.common.bo.BannerBo;
import tech.wejoy.billiard.common.bo.FeedbackBo;
import tech.wejoy.billiard.common.entity.BaseConfig;
import tech.wejoy.billiard.common.entity.Feedback;
import tech.wejoy.billiard.common.entity.FileInfo;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;
import tech.wejoy.billiard.common.enums.BaseConfigEnum;
import tech.wejoy.billiard.common.enums.FileLinkEnum;
import tech.wejoy.billiard.common.manager.ConfigManager;
import tech.wejoy.billiard.common.manager.FileManager;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class ConfigService {

    private final ConfigManager configManager;

    private final FileManager fileManager;

    public List<BannerBo> getBannerList(BannerTypeEnum type) {
        return configManager.getBannerListByType(type).stream().map(BannerBo::from).toList();
    }

    public void feedback(FeedbackBo dto) {
        Feedback entity = BeanUtils.copy(dto, Feedback.class);
        configManager.saveFeedback(entity);
        List<String> images = dto.getImages();
        for (int i = 0; i < images.size(); i++) {
            String image = images.get(i);
            FileInfo file = fileManager.findByUrl(image);
            if (file != null) {
                file.setSeq(i + 1);
                file.setLink(FileLinkEnum.FEEDBACK);
                file.setOuterId(entity.getId());
                fileManager.save(file);
            }
        }
    }

    public CustomerBo getCustomer() {
        List<BaseConfig> baseConfigs = configManager.fetchBaseConfig();
        Map<String, String> configMap = baseConfigs.stream().collect(Collectors.toMap(BaseConfig::getKey, BaseConfig::getValue));
        CustomerBo customerBo = new CustomerBo();
        customerBo.setCustomerName(configMap.get(BaseConfigEnum.CUSTOMER_NAME.getKey()));
        customerBo.setCustomerPhone(configMap.get(BaseConfigEnum.CUSTOMER_PHONE.getKey()));
        customerBo.setCustomerWechat(configMap.get(BaseConfigEnum.CUSTOMER_WECHAT.getKey()));
        return customerBo;
    }

}
