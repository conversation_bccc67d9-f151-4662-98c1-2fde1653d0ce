package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.admin.bo.AdminUserBo;
import tech.wejoy.billiard.common.bo.AdminCompetitionApplyBo;
import tech.wejoy.billiard.common.bo.CompetitionBo;
import tech.wejoy.billiard.common.bo.CompetitionBusinessApplyBo;
import tech.wejoy.billiard.common.dto.AdminCompetitionQueryDto;
import tech.wejoy.billiard.common.dto.CompetitionApplyDto;
import tech.wejoy.billiard.common.dto.CompetitionCreateDto;
import tech.wejoy.billiard.common.service.CompetitionService;

import java.util.List;

@Path("/competition")
@Tag(name = "比赛接口")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class CompetitionApi {

    private final CompetitionService competitionService;

    @GET
    @Path("/list")
    public Page<CompetitionBo> list(@BeanParam AdminCompetitionQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        return competitionService.adminList(dto);
    }

    @GET
    @Path("/{id}/apply/list")
    public List<AdminCompetitionApplyBo> applyList(@PathParam("id") Long id) {
        return competitionService.competitionApplyList(id);
    }


    @GET
    @Path("/{id}")
    @Operation(summary = "比赛详情")
    public CompetitionBo detail(@PathParam("id") Long id) {
        return competitionService.detail(id, null);
    }

    @GET
    @Path("/apply/list")
    public Page<CompetitionBusinessApplyBo> applyList(@BeanParam AdminCompetitionQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        return competitionService.applyList(dto);
    }

    @POST
    @Path("/apply")
    public void apply(CompetitionApplyDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        dto.setUserId(user.getId());
        dto.setTenantId(user.getTenantId());
        competitionService.apply(dto);
    }

    @POST
    @Path("/create")
    public void create(CompetitionCreateDto dto) {
        competitionService.create(dto);
    }

    @POST
    @Path("/{id}/start")
    public void start(@PathParam("id") Long id) {
        competitionService.start(id);
    }

    @POST
    @Path("/{id}/end")
    public void end(@PathParam("id") Long id) {
        competitionService.end(id);
    }

    @POST
    @Path("/{id}/prepare")
    public void prepare(@PathParam("id") Long id) {
        competitionService.prepare(id);
    }

    @POST
    @Path("/schedule/{id}/start")
    public void scheduleStart(@PathParam("id") Long id) {
        competitionService.scheduleStart(id);
    }

    @POST
    @Path("/schedule/{id}/end")
    public void scheduleEnd(@PathParam("id") Long id) {
        competitionService.scheduleEnd(id);
    }

    @POST
    @Path("/match/{id}/score/{userId}")
    public void matchScoreAdd(@PathParam("id") Long id, @PathParam("userId") Long userId) {
        competitionService.matchScoreAdd(id, userId);
    }

    @POST
    @Path("/match/{id}/score/reset")
    public void matchScoreReset(@PathParam("id") Long id) {
        competitionService.matchScoreReset(id);
    }

    @POST
    @Path("/match/{id}/win/{userId}")
    public void matchWin(@PathParam("id") Long id, @PathParam("userId") Long userId) {
        competitionService.matchWin(id, userId);
    }

    @POST
    @Path("/match/{id}/table/{tableId}")
    public void matchTable(@PathParam("id") Long id, @PathParam("tableId") Long tableId) {
        competitionService.matchTable(id, tableId);
    }

}
