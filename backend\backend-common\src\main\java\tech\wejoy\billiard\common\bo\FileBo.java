package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.FileLinkEnum;
import tech.wejoy.billiard.common.enums.FileTypeEnum;

@Data
public class FileBo {

    @Schema(description = "文件ID")
    private Long id;

    @Schema(description = "文件名称")
    private String name;

    @Schema(description = "文件URL")
    private String url;

    @Schema(description = "顺序")
    private Integer seq;

    @Schema(description = "文件类型")
    private FileTypeEnum type;

    @Schema(description = "文件关联类型")
    private FileLinkEnum link;

}
