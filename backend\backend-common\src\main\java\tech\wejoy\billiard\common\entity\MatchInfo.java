package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.MatchGameTypeEnum;
import tech.wejoy.billiard.common.enums.MatchStatusEnum;
import tech.wejoy.billiard.common.enums.MatchTypeEnum;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class MatchInfo extends BaseEntity {

    private Long clubId;

    private Long userId;

    private Long orderId;

    private Long tableId;

    private Long winUserId;

    private Long acceptUserId;

    private MatchGameTypeEnum gameType;

    private MatchTypeEnum type;

    private LocalDateTime startTime;

    private Integer minutes;

    private Integer level;

    private MatchStatusEnum status;

    private IsEnum deleted;

}
