package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class MeituanShop extends BaseEntity {

    private Long accountId;

    private String uuid;

    private String name;

    private String branchName;

    private String address;

    private String city;

    private Long clubId;

}
