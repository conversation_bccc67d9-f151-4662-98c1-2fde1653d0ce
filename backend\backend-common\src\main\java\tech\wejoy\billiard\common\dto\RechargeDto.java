package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.ChannelEnum;

import java.math.BigDecimal;

@Data
public class RechargeDto {

    @Schema(description = "套餐ID")
    private Long planId;

    @Schema(description = "门店ID")
    private Long clubId;

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "支付渠道")
    private ChannelEnum channel;

    @Schema(hidden = true)
    private Long userId;

}
