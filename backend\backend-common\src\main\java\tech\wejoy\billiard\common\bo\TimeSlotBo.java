package tech.wejoy.billiard.common.bo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.TimeSlotTypeEnum;

import java.math.BigDecimal;
import java.time.LocalTime;

@Data
@Accessors(chain = true)
@Schema(description = "时间段")
public class TimeSlotBo {

    @Schema(description = "时间段类型")
    private TimeSlotTypeEnum type;

    @Schema(description = "开始时间")
    private LocalTime startTime;

    @Schema(description = "结束时间")
    private LocalTime endTime;

    private boolean overnight;

    @Schema(description = "单价价格")
    private BigDecimal perPrice;

}
