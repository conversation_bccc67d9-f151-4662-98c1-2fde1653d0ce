{"version": 3, "file": "pages/competition/rank/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;ACnGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/competition/rank/index.tsx?98d1", "webpack://frontend-client/._src_pages_competition_rank_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport competition from \"@/api/bussiness/competition\";\nimport Title from \"@/components/bussiness/Title\";\nimport Back from \"@/components/bussiness/back\";\nimport { menuRect } from \"@/utils\";\nimport { useRouter } from \"@tarojs/taro\";\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport { Image } from '@tarojs/components';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar RankItem = function RankItem(props) {\n  var rankNum = props.rankNum,\n    user = props.user;\n  var getValue = function getValue(user) {\n    if (!user) return /*#__PURE__*/_jsx(\"div\", {\n      className: \"text-sm\",\n      children: \"--\"\n    });\n    var avatar = user.avatar || \"https://congeer-public.oss-cn-shanghai.aliyuncs.com/dev/icon-y.png\";\n    return /*#__PURE__*/_jsxs(\"div\", {\n      className: \"flex gap-2 text-sm items-center\",\n      children: [/*#__PURE__*/_jsx(Image, {\n        src: avatar,\n        className: \"w-10 h-10 rounded-full\"\n      }), /*#__PURE__*/_jsx(\"div\", {\n        children: user.username\n      })]\n    });\n  };\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: \"bg-bgt rounded-md p-3 py-2 flex gap-2 items-center justify-between\",\n    children: [getValue(user), /*#__PURE__*/_jsxs(\"div\", {\n      children: [\"No.\", rankNum]\n    })]\n  });\n};\nexport default (function () {\n  var rect = menuRect();\n  var router = useRouter();\n  var id = router.params.id;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    data = _useState2[0],\n    setData = _useState2[1];\n  var getData = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n    var _yield$competition$ge, _data;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          if (!id) {\n            _context.next = 6;\n            break;\n          }\n          _context.next = 3;\n          return competition.getDetail(id);\n        case 3:\n          _yield$competition$ge = _context.sent;\n          _data = _yield$competition$ge.data;\n          setData(_data);\n        case 6:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  })), [setData, id]);\n  var list = useMemo(function () {\n    if (!data) return [];\n    var tempRankList = data.ranks.concat(new Array(data.signUpCount).fill(undefined));\n    return tempRankList.slice(0, data.signUpCount);\n  }, [data]);\n  useEffect(function () {\n    getData();\n  }, [id]);\n  console.log(list);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: \"flex flex-col h-[100vh]\",\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u6BD4\\u8D5B\\u699C\\u5355\"\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"flex flex-1 flex-col gap-3 px-3 overflow-y-auto\",\n      children: list.map(function (item, index) {\n        return /*#__PURE__*/_jsx(\"div\", {\n          className: \"flex flex-col gap-2\",\n          children: /*#__PURE__*/_jsx(RankItem, {\n            rankNum: index + 1,\n            user: item\n          })\n        }, index);\n      })\n    }), /*#__PURE__*/_jsx(_SafeArea, {\n      position: \"bottom\"\n    })]\n  });\n});", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/rank/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/competition/rank/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}