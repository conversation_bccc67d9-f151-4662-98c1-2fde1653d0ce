package tech.wejoy.billiard.common.third.smyoo.response;

import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class GetIrDeviceResponse {

    @JsonProperty("devicedatas")
    private String deviceDatas;

    public List<DeviceData> getDeviceDataList() {
        return JsonUtils.toList(deviceDatas, DeviceData.class);
    }

    public DeviceData getDeviceData() {
        return getDeviceDataList().getFirst();
    }

    public Map<String, Object> getDatapoint() {
        return JsonUtils.toObject(getDeviceData().getDataPoint(), new TypeReference<>() {
        });
    }

    @Data
    public static class DeviceData {
        @JsonProperty("datatype")
        private Integer dataType;
        @JsonProperty("datapoint")
        private String dataPoint;
    }

}
