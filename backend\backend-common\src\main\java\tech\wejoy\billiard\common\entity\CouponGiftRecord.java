package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class CouponGiftRecord extends BaseEntity {

    private Long clubId;

    private Long tenantId;

    private Long operatorId;

    private Long userId;

    private String phone;

    private Long couponId;

    private String remark;

    private Integer count;

}
