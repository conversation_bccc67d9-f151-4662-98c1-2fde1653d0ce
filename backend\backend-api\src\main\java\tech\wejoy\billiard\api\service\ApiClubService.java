package tech.wejoy.billiard.api.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.api.dto.StartPrepareDto;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.ApiClubQueryDto;
import tech.wejoy.billiard.common.dto.ApiClubRechargeQueryDto;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.dto.StartTableTimeDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.*;
import tech.wejoy.billiard.common.manager.*;
import tech.wejoy.billiard.common.service.CouponService;
import tech.wejoy.billiard.common.service.DistrictService;
import tech.wejoy.billiard.common.service.TicketService;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.utils.PriceUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class ApiClubService {

    private final ClubManager clubManager;

    private final OrderManager orderManager;

    private final DistrictService districtService;

    private final MemberManager memberManager;

    private final FileManager fileManager;

    private final ConfigManager configManager;

    private final CouponManager couponManager;

    private final ClientUserManager clientUserManager;

    private final MatchManager matchManager;

    private final AssistantManager assistantManager;

    @Inject
    CouponService couponService;

    @Inject
    TicketService ticketService;

    public Page<ApiClubBo> listWithDistance(ApiClubQueryDto dto) {
        return clubManager.listWithDistance(dto);
    }

    public ApiClubBo detail(Long id) {
        ClubInfo clubInfo = clubManager.fetchClubById(id);
        if (clubInfo == null || clubInfo.getStatus().equals(ClubStatusEnum.STOP)) {
            throw new BaseException("未找到门店");
        }
        if (clubInfo.getStatus().equals(ClubStatusEnum.CLOSED)) {
            throw new BaseException("门店已关闭");
        }
        ApiClubBo bo = ApiClubBo.from(clubInfo);
        List<ClubTable> clubTables = clubManager.fetchTableByClub(id);
        bo.setTables(clubTables.stream().filter(v -> v.getStatus() != TableStatusEnum.UNAVAILABLE).map(TableBo::from).toList());
        List<FileInfo> fileInfos = fileManager.getImageInfoByTypeAndId(FileTypeEnum.IMAGE, FileLinkEnum.CLUB, id);
        List<String> images = fileInfos.stream().map(FileInfo::getUrl).toList();
        bo.setImages(images);
        bo.setCoupons(getCouponsByClub(id));
        List<AssistantInfo> assistantInfos = assistantManager.fetchAssistantByClubId(id);
        bo.setAssistants(BeanUtils.copyList(assistantInfos, AssistantBo.class));
        List<MatchInfo> matches = matchManager.fetchMatchByClubId(id);
        List<Long> userIds = matches.stream().map(MatchInfo::getUserId).toList();
        List<ClientUser> users = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        bo.setMatches(BeanUtils.copyList(matches, MatchBo.class));
        bo.getMatches().forEach(v -> {
            ClientUser user = userMap.get(v.getUserId());
            if (user != null) {
                v.setNickname(user.getNickname());
                v.setAvatar(user.getAvatar());
            }
        });
        return bo;
    }

    public List<TableStatusBo> tablesStatus(Long id) {
        List<ClubTable> clubTables = clubManager.fetchTableByClub(id);
        return BeanUtils.copyList(clubTables, TableStatusBo.class);
    }

    public StartPrepareBo startPrepare(StartPrepareDto dto) {
        StartPrepareBo ret = new StartPrepareBo();
        ClubTable table = clubManager.fetchClubTableById(dto.getTableId());
        if (table == null) {
            throw new BaseException("未找到桌台");
        }
        ret.setTable(TableBo.from(table));
        Long clubId = table.getClubId();
        ClubInfo clubInfo = clubManager.fetchClubById(clubId);
        if (clubInfo == null || clubInfo.getStatus().equals(ClubStatusEnum.STOP)) {
            throw new BaseException("未找到门店");
        }
        if (clubInfo.getStatus().equals(ClubStatusEnum.CLOSED)) {
            throw new BaseException("门店已关闭");
        }
        if (ret.getTable().getDeposit() == null || ret.getTable().getDeposit().compareTo(BigDecimal.ZERO) == 0) {
            ret.getTable().setDeposit(BigDecimal.ZERO);
            if (dto.getFrom() == StartFromEnum.QRCODE) {
                BaseConfig baseConfig = configManager.fetchBaseConfig(BaseConfigEnum.CLUB_DEPOSIT.getKey());
                if (baseConfig != null) {
                    ret.getTable().setDeposit(new BigDecimal(baseConfig.getValue()));
                } else {
                    ret.getTable().setDeposit(new BigDecimal(100));
                }
            }
        }
        ret.setClub(BeanUtils.copy(clubInfo, ApiClubBo.class));
        ret.getClub().setCoupons(getCouponsByClub(clubId));
        long userId = SecurityHolder.<Long, WxUserBo>session().getUserId();
        OrderInfo order = orderManager.fetchUsingOrderByTableId(dto.getTableId());
        List<ClientUserTenantMember> clubMembers = memberManager.getClubMembersByClubIdAndUserId(clubInfo.getId(), userId);
        if (order != null && (order.getUserId() == userId || CollectionUtils.isNotEmpty(clubMembers))) {
            ret.setOrder(BeanUtils.copy(order, OrderBo.class));
        }
        if (table.getStatus() == TableStatusEnum.IDLE || (ret.getOrder() != null && ret.getOrder().getUserId() != userId)) {
            ClientUserMember member = memberManager.getMemberByUserId(userId);
            WalletDetailBo wallet = new WalletDetailBo();
            wallet.setMemberBalance(member.getBalance());
            if (clubMembers.isEmpty()) {
                wallet.setClubBalance(BigDecimal.ZERO);
            } else {
                BigDecimal balance = clubMembers.stream().map(ClientUserTenantMember::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
                balance = balance.add(clubMembers.stream().map(ClientUserTenantMember::getBonus).reduce(BigDecimal.ZERO, BigDecimal::add));
                wallet.setClubBalance(balance);
            }
            List<CouponBo> list = getUserAvailableCoupons(userId, clubId);
            wallet.setCoupons(list);
            wallet.setTickets(ticketService.listLocal(table.getId(), userId));
            wallet.setCoins(member.getCoins());
            wallet.setPoints(member.getPoints());
            ret.setWallet(wallet);
        }
        ret.setTimePlans(PriceUtils.getClubTimePlan());
        return ret;
    }

    private List<CouponBo> getCouponsByClub(Long clubId) {
        List<TenantCoupon> coupons = couponManager.fetchShowCouponByClub(clubId);
        List<Long> couponIds = coupons.stream().map(BaseEntity::getId).toList();
        List<TenantClubCouponRel> clubRelList = couponManager.fetchCouponRelByCouponIds(couponIds);
        List<Long> clubIds = clubRelList.stream().map(TenantClubCouponRel::getClubId).distinct().toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        Map<Long, List<TenantClubCouponRel>> clubGroup = clubRelList.stream().collect(Collectors.groupingBy(TenantClubCouponRel::getCouponId));
        return coupons.stream().map(c -> {
            CouponBo coupon = BeanUtils.copy(c, CouponBo.class);
            List<TenantClubCouponRel> relList = clubGroup.get(c.getId());
            if (relList != null) {
                List<ClubInfo> clubList = relList.stream().map(TenantClubCouponRel::getClubId).map(clubMap::get).toList();
                coupon.setClubs(clubList.stream().map(ApiClubBo::optionFrom).toList());
            }
            return coupon;
        }).toList();
    }

    private List<CouponBo> getUserAvailableCoupons(long userId, long clubId) {
        List<ClientUserCoupon> userCoupons = clientUserManager.fetchCouponByUserIdAndStatus(userId, IsEnum.FALSE, IsEnum.FALSE, IsEnum.FALSE);
        List<Long> couponIds = userCoupons.stream().map(ClientUserCoupon::getCouponId).toList();
        List<TenantClubCouponRel> clubRelList = couponManager.fetchCouponRelByCouponIds(couponIds);
        Set<Long> canUseCouponIds = clubRelList.stream().filter(v -> v.getClubId().equals(clubId)).map(TenantClubCouponRel::getCouponId).collect(Collectors.toSet());
        if (canUseCouponIds.isEmpty()) {
            return List.of();
        }
        clubRelList = clubRelList.stream().filter(v -> canUseCouponIds.contains(v.getCouponId())).toList();
        List<Long> clubIds = clubRelList.stream().map(TenantClubCouponRel::getClubId).distinct().toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        Map<Long, List<TenantClubCouponRel>> clubGroup = clubRelList.stream().collect(Collectors.groupingBy(TenantClubCouponRel::getCouponId));
        Map<Long, TenantCoupon> couponMap = couponManager.fetchCouponsByIds(canUseCouponIds.stream().toList()).stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        return userCoupons.stream().map(c -> {
            if (!couponMap.containsKey(c.getCouponId())) {
                return null;
            }
            CouponBo coupon = BeanUtils.copy(couponMap.get(c.getCouponId()), CouponBo.class);
            coupon.setId(c.getId());
            coupon.setExpireTime(c.getExpireTime());
            List<TenantClubCouponRel> relList = clubGroup.get(c.getCouponId());
            if (relList != null) {
                List<ClubInfo> clubList = relList.stream().map(TenantClubCouponRel::getClubId).map(clubMap::get).toList();
                coupon.setClubs(clubList.stream().map(ApiClubBo::optionFrom).toList());
            }
            return coupon;
        }).filter(Objects::nonNull).toList();
    }

    public List<DistrictBo> openDistricts() {
        List<String> codes = clubManager.fetchOpenDistricts();
        if (codes.isEmpty()) {
            return List.of();
        }
        Set<String> set = codes.stream().filter(StringUtils::isNotBlank).collect(Collectors.toSet());
        return districtService.get().stream().filter(v -> set.contains(v.getCode())).map(DistrictBo::from).toList();
    }

    @Transactional(rollbackOn = Exception.class)
    public StartResultBo start(StartTableDto dto) {
        ClubTable table = clubManager.fetchClubTableById(dto.getTableId());
        if (table == null) {
            throw new BaseException("未找到桌台");
        }
        Long clubId = table.getClubId();
        ClubInfo clubInfo = clubManager.fetchClubById(clubId);
        if (clubInfo == null || clubInfo.getStatus().equals(ClubStatusEnum.STOP)) {
            throw new BaseException("未找到门店");
        }
        if (clubInfo.getStatus().equals(ClubStatusEnum.CLOSED)) {
            throw new BaseException("门店已关闭");
        }
        if (table.getDeposit() == null || table.getDeposit().compareTo(BigDecimal.ZERO) == 0) {
            table.setDeposit(BigDecimal.ZERO);
            if (dto.getFrom() == StartFromEnum.QRCODE) {
                BaseConfig baseConfig = configManager.fetchBaseConfig(BaseConfigEnum.CLUB_DEPOSIT.getKey());
                if (baseConfig != null) {
                    table.setDeposit(new BigDecimal(baseConfig.getValue()));
                } else {
                    table.setDeposit(new BigDecimal(100));
                }
            }
        }
        StartResultBo ret = new StartResultBo();
        ret.setTableId(dto.getTableId());
        Payment payment = Payment.getPayment(dto.getPayType());
        if (table.getStatus() != TableStatusEnum.IDLE) {
            ret.setResult(StartResultEnum.TABLE_OCCUPY);
            ret.setMessage("桌台不在空闲状态");
            return ret;
        }
        StartTableTimeDto checkTime = new StartTableTimeDto();
        PrepayBo prepay = payment.prepay(dto, table);
        if (!prepay.isSuccess()) {
            ret.setResult(StartResultEnum.NOT_ENOUGH_BALANCE);
            ret.setMessage(prepay.getMessage());
            return ret;
        }
        checkTime.setTableId(table.getId());
        checkTime.setStartTime(dto.getStartTime());
        checkTime.setEndTime(dto.getEndTime());
        if (!checkTime(checkTime)) {
            ret.setResult(StartResultEnum.TABLE_OCCUPY);
            ret.setMessage("桌台不在空闲状态");
            return ret;
        }
        checkTime.setTableId(dto.getTableId());
        BigDecimal totalPrice = PriceUtils.calculatePrice(table, dto.getStartTime(), dto.getEndTime());
        if (dto.getPayType() == OrderPayTypeEnum.DEPOSIT) {
            totalPrice = table.getDeposit();
        }
        long userId = SecurityHolder.<Long, WxUserBo>session().getUserId();
        OrderInfo order = createOrderBase(dto, userId, table, totalPrice);
        String orderNo = orderManager.generateOrderNo(order);
        order.setOrderNo(orderNo);
        PayResultBo pay = payment.pay(order, dto.getExtra());
        ret.setOrderNo(orderNo);
        if (pay.isSuccess()) {
            if (LocalDateTime.now().plusMinutes(10).isAfter(dto.getStartTime())) {
                ret.setResult(StartResultEnum.STARTING);
            } else {
                ret.setResult(StartResultEnum.SUCCESS);
            }
            order.setStatus(OrderStatusEnum.PAID);
            clubManager.updateTableUsing(table.getId(), userId, dto.getEndTime());
            ret.setStartTime(dto.getStartTime());
            order.setPayTime(LocalDateTime.now());
            orderManager.save(order);
        } else if (pay.isNeedPay()) {
            order.setStatus(OrderStatusEnum.PENDING);
            orderManager.save(order);
            ret.setResult(StartResultEnum.PENDING_PAYMENT);
            ret.setExtra(pay.getExtra());
        } else {
            orderManager.delete(order);
            ret.setResult(StartResultEnum.NOT_ENOUGH_BALANCE);
            ret.setMessage(pay.getMessage());
            return ret;
        }
        ret.setStatus(order.getStatus());
        return ret;
    }

    private OrderInfo createOrderBase(StartTableDto dto, long userId, ClubTable table, BigDecimal totalPrice) {
        OrderInfo order = new OrderInfo();
        order.setUserId(userId);
        order.setStartFrom(dto.getFrom());
        order.setTenantId(table.getTenantId());
        order.setTableId(dto.getTableId());
        order.setClubId(table.getClubId());
        order.setPayType(dto.getPayType());
        order.setStartTime(dto.getStartTime());
        order.setEndTime(dto.getEndTime());
        order.setTotalAmount(totalPrice);
        order.setRefundAmount(BigDecimal.ZERO);
        orderManager.save(order);
        return order;
    }

    public StartResultBo startStatus(String orderNo) {
        OrderInfo order = orderManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("未找到订单");
        }
        StartResultBo ret = new StartResultBo();
        ret.setTableId(order.getTableId());
        ret.setStatus(order.getStatus());
        if (order.getStatus() == OrderStatusEnum.PENDING) {
            ret.setResult(StartResultEnum.PENDING_PAYMENT);
        } else if (order.getStatus() == OrderStatusEnum.PAID) {
            ret.setResult(StartResultEnum.STARTING);
        } else if (order.getStatus() == OrderStatusEnum.USING) {
            ret.setResult(StartResultEnum.SUCCESS);
        }
        return ret;
    }

    public List<ClubRechargePlanBo> rechargePlans(Long id) {
        List<TenantPlan> clubPlans = memberManager.fetchShowTenantPlanByClub(id);
        if (clubPlans.isEmpty()) {
            return List.of();
        }
        List<Long> planIds = clubPlans.stream().map(BaseEntity::getId).toList();
        List<TenantClubPlanRel> clubPlanRelList = memberManager.getClubsByPlanIds(planIds);
        if (clubPlanRelList.isEmpty()) {
            return List.of();
        }
        List<Long> clubIds = clubPlanRelList.stream().map(TenantClubPlanRel::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, List<TenantClubPlanRel>> plans = clubPlanRelList.stream().collect(Collectors.groupingBy(TenantClubPlanRel::getPlanId));
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        return clubPlans.stream().map(plan -> {
            ClubRechargePlanBo bo = new ClubRechargePlanBo();
            bo.setId(plan.getId());
            bo.setName(plan.getName());
            bo.setClubNames(plans.get(plan.getId()).stream().map(v -> clubMap.get(v.getClubId()).getName()).toList());
            bo.setTotalAmount(plan.getTotalAmount());
            bo.setPayAmount(plan.getPayAmount());
            bo.setRemark(plan.getRemark());
            return bo;
        }).toList();
    }

    public List<ApiClubBo> rechargeClubList(ApiClubRechargeQueryDto dto) {
        List<TenantPlan> tenantPlans = memberManager.getTenantPlans();
        if (tenantPlans.isEmpty()) {
            return List.of();
        }
        List<Long> planIds = tenantPlans.stream().map(BaseEntity::getId).toList();
        List<TenantClubPlanRel> clubPlanRelList = memberManager.getClubsByPlanIds(planIds);
        if (clubPlanRelList.isEmpty()) {
            return List.of();
        }
        List<Long> clubIds = clubPlanRelList.stream().map(TenantClubPlanRel::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIdsAndKey(clubIds, dto.getKeyword(), dto.getLng(), dto.getLat());
        return clubs.stream().map(ApiClubBo::optionFrom).toList();
    }

    // public void checkTime(StartTableTimeDto dto) {
    //     List<OrderInfo> orders = orderManager.getOrderByTableId(dto.getTableId());
    //     for (OrderInfo order : orders) {
    //         if (dto.getStartTime().isBefore(order.getEndTime()) && dto.getEndTime().isAfter(order.getStartTime())) {
    //             throw new BaseException("时间 " + DateUtils.getDateTimeString(order.getStartTime(), "MM-dd HH:mm")
    //                     + " 至 " + DateUtils.getDateTimeString(order.getEndTime(), "MM-dd HH:mm")
    //                     + " 已被占用");
    //         }
    //     }
    // }

    public boolean checkTime(StartTableTimeDto dto) {
        List<OrderInfo> orders = orderManager.getOrderByTableId(dto.getTableId());
        for (OrderInfo order : orders) {
            if (dto.getStartTime().isBefore(order.getEndTime()) && dto.getEndTime().isAfter(order.getStartTime())) {
                return false;
            }
        }
        return true;
    }

    public PriceCalBo calPrice(StartTableTimeDto dto) {
        ClubTable table = clubManager.fetchClubTableById(dto.getTableId());
        if (table == null) {
            throw new BaseException("未找到桌台");
        }
        BigDecimal price = PriceUtils.calculatePrice(table, dto.getStartTime(), dto.getEndTime());
        PriceCalBo bo = new PriceCalBo();
        bo.setPrice(price);
        return bo;
    }

    public List<ApiClubBo> options(ApiClubRechargeQueryDto dto) {
        List<ClubInfo> clubs = clubManager.fetchClubByKey(dto.getKeyword(), dto.getLng(), dto.getLat());
        return clubs.stream().map(ApiClubBo::optionFrom).toList();
    }

}
