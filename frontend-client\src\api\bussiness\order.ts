import { http } from "../index";

export default {
  async getList(params: IPagination) {
    return await http("/order/list", {
      data: params,
      method: "GET",
    });
  },
  async finishOrder(id: string) {
    return await http(`/order/${id}/finish`, {
      method: "POST",
    });
  },
  async cancelOrder(id: string) {
    return await http(`/order/${id}/cancel`, {
      method: "POST",
    });
  },
  async quickStart(code: string) {
    return await http(`/order/${code}/start`, {
      method: "POST",
    });
  },
  async otherFinishOrder(orderNo: string, params: { type: number; payType: number }) {
    return await http(`/order/${orderNo}/finish/other`, {
      method: "POST",
      data: params,
    });
  },
};
