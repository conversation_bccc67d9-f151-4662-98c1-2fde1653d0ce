package tech.wejoy.billiard.common.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.IsEnum;

@Data
public class AdminCompetitionApplyBo {

    private Long id;

    private Long competitionId;

    private IsEnum enable;

    private Integer number;

    private Integer rank;

    private Long userId;

    private String username;

    private String idCard;

    private String avatar;

    private String phone;

}
