"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/assistant/request/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/request/index!./src/pages/assistant/request/index.tsx":
/*!****************************************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/request/index!./src/pages/assistant/request/index.tsx ***!
  \****************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Popup/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Popup */ "./node_modules/@nutui/nutui-react-taro/dist/esm/popup.taro-JynjcLCn.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SafeArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/safearea.taro-BJpvFsMf.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _components_bussiness_Title__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/bussiness/Title */ "./src/components/bussiness/Title/index.tsx");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ "./src/components/ui/button.tsx");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./card */ "./src/pages/assistant/request/card.tsx");
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _custom_address__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/custom/address */ "./src/custom/address.tsx");
/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/hooks */ "./src/hooks/index.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");



















/* harmony default export */ __webpack_exports__["default"] = (function () {
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_5__.menuRect)();
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_12__["default"])(_useState, 2),
    list = _useState2[0],
    setList = _useState2[1];
  var refreshList = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function () {
    _api_bussiness__WEBPACK_IMPORTED_MODULE_8__["default"].assistant.getRequestList().then(function (res) {
      setList(res.data);
    });
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(function () {
    refreshList();
  }, []);
  var cancelRequest = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_13__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().mark(function _callee(id) {
      var _yield$Taro$showModal, confirm;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _tarojs_taro__WEBPACK_IMPORTED_MODULE_10___default().showModal({
              title: "提示",
              content: "确认取消该申请？",
              confirmText: "确定"
            });
          case 2:
            _yield$Taro$showModal = _context.sent;
            confirm = _yield$Taro$showModal.confirm;
            if (!confirm) {
              _context.next = 9;
              break;
            }
            _context.next = 7;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_8__["default"].assistant.cancelRequest(id);
          case 7:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_10___default().showToast({
              title: "取消成功",
              icon: "none"
            });
            refreshList();
          case 9:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }(), [refreshList]);
  var cancelClub = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/function () {
    var _ref2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_13__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().mark(function _callee2(id) {
      var _yield$Taro$showModal2, confirm;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().wrap(function _callee2$(_context2) {
        while (1) switch (_context2.prev = _context2.next) {
          case 0:
            _context2.next = 2;
            return _tarojs_taro__WEBPACK_IMPORTED_MODULE_10___default().showModal({
              title: "提示",
              content: "确认解约该门店？",
              confirmText: "确定"
            });
          case 2:
            _yield$Taro$showModal2 = _context2.sent;
            confirm = _yield$Taro$showModal2.confirm;
            if (!confirm) {
              _context2.next = 9;
              break;
            }
            _context2.next = 7;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_8__["default"].assistant.cancelClub(id);
          case 7:
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_10___default().showToast({
              title: "解约成功",
              icon: "none"
            });
            refreshList();
          case 9:
          case "end":
            return _context2.stop();
        }
      }, _callee2);
    }));
    return function (_x2) {
      return _ref2.apply(this, arguments);
    };
  }(), [refreshList]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)("div", {
    style: {
      paddingTop: rect.bottom + 10
    },
    className: "h-_100vh_ flex flex-col",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_3__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_bussiness_Title__WEBPACK_IMPORTED_MODULE_2__["default"], {
      name: "\u95E8\u5E97\u5165\u9A7B"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
      className: "flex-1 space-y-3 p-3",
      children: list === null || list === void 0 ? void 0 : list.map(function (item) {
        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_card__WEBPACK_IMPORTED_MODULE_7__.Card, {
          data: item,
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
            className: "flex gap-2",
            children: item.pass ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {
              sizev: "sm",
              className: "h-7 font-semibold",
              onClick: function onClick() {
                return cancelClub(item.clubId);
              },
              children: "\u89E3\u7EA6"
            }) : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {
              sizev: "sm",
              className: "h-7 font-semibold",
              onClick: function onClick() {
                return cancelRequest(item.clubId);
              },
              children: "\u53D6\u6D88"
            })
          })
        }, item.id);
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)("div", {
      className: "w-full px-3",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(NewClub, {
        list: list,
        onSubmit: refreshList
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_15__.S, {
      position: "bottom"
    })]
  });
});
var NewClub = function NewClub(_ref3) {
  var list = _ref3.list,
    onSubmit = _ref3.onSubmit;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_12__["default"])(_useState3, 2),
    visible = _useState4[0],
    setVisible = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)([]),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_12__["default"])(_useState5, 2),
    addressList = _useState6[0],
    setAddressList = _useState6[1];
  var _useStore = (0,_hooks__WEBPACK_IMPORTED_MODULE_16__.useStore)(function (state) {
      return state.auth;
    }),
    lat = _useStore.lat,
    lng = _useStore.lng;
  var changeAddress = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/function () {
    var _ref4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_13__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().mark(function _callee3(params) {
      var _yield$api$venues$get, data, newData;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            _context3.next = 2;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_8__["default"].venues.getRechargeList(params);
          case 2:
            _yield$api$venues$get = _context3.sent;
            data = _yield$api$venues$get.data;
            console.log(list);
            newData = data.filter(function (item) {
              return !(list !== null && list !== void 0 && list.find(function (v) {
                return v.clubId == item.id;
              }));
            });
            console.log(newData);
            setAddressList(newData);
            return _context3.abrupt("return", data);
          case 9:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function (_x3) {
      return _ref4.apply(this, arguments);
    };
  }(), [setAddressList, list]);
  var handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(/*#__PURE__*/function () {
    var _ref5 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_13__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().mark(function _callee4(id) {
      var _yield$Taro$showModal3, confirm;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_14__["default"])().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            _context4.next = 2;
            return _tarojs_taro__WEBPACK_IMPORTED_MODULE_10___default().showModal({
              title: "提示",
              content: "确认申请入驻该门店？",
              confirmText: "确定"
            });
          case 2:
            _yield$Taro$showModal3 = _context4.sent;
            confirm = _yield$Taro$showModal3.confirm;
            if (!confirm) {
              _context4.next = 10;
              break;
            }
            _context4.next = 7;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_8__["default"].assistant.postRequest({
              clubId: id
            });
          case 7:
            setVisible(false);
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_10___default().showToast({
              title: "申请成功",
              icon: "none"
            });
            onSubmit && onSubmit();
          case 10:
          case "end":
            return _context4.stop();
        }
      }, _callee4);
    }));
    return function (_x4) {
      return _ref5.apply(this, arguments);
    };
  }(), [setVisible]);
  var onShow = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(function () {
    changeAddress({
      lat: lat,
      lng: lng
    });
    setVisible(true);
  }, [setVisible, list]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsxs)("div", {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {
      className: "w-full",
      onClick: onShow,
      children: "\u5165\u9A7B\u65B0\u95E8\u5E97"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_nutui_nutui_react_taro_dist_esm_Popup__WEBPACK_IMPORTED_MODULE_17__.P, {
      title: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_18__.Text, {
        className: "text-white",
        children: "\u53EF\u7528\u95E8\u5E97"
      }),
      position: "bottom",
      closeable: true,
      visible: visible,
      onClose: function onClose() {
        return setVisible(false);
      },
      style: {
        width: "100%",
        height: "100%",
        backgroundColor: "#000"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_11__.jsx)(_custom_address__WEBPACK_IMPORTED_MODULE_9__.Address, {
        list: addressList,
        onSearch: changeAddress,
        club: 0,
        onChoose: function onChoose(id) {
          handleSubmit(id);
          setVisible(false);
        }
      })
    })]
  });
};

/***/ }),

/***/ "./src/pages/assistant/request/card.tsx":
/*!**********************************************!*\
  !*** ./src/pages/assistant/request/card.tsx ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Card: function() { return /* binding */ Card; }
/* harmony export */ });
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");

var Card = function Card(_ref) {
  var _data$clubImg;
  var data = _ref.data,
    children = _ref.children;
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
    className: "flex gap-2 p-3 bg-bgt rounded-lg",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", {
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("img", {
        className: "w-_5rem_ h-_5rem_ rounded-lg",
        src: (_data$clubImg = data.clubImg) !== null && _data$clubImg !== void 0 ? _data$clubImg : "https://oss.gorillaballclub.cn/images/icons/icon-y.png",
        alt: ""
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)("div", {
      className: "flex flex-col justify-between",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", {
        className: "font-semibold",
        children: data.clubName
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", {
        className: "text-sm",
        children: data.pass ? "已入驻" : "审核中"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)("div", {
        children: children
      })]
    })]
  });
};

/***/ }),

/***/ "./src/pages/assistant/request/index.tsx":
/*!***********************************************!*\
  !*** ./src/pages/assistant/request/index.tsx ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_request_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/request/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/request/index!./src/pages/assistant/request/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white","disableScroll":true};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_request_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/assistant/request/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_request_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_request_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_request_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_request_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/assistant/request/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map