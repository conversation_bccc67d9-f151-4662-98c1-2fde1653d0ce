package tech.wejoy.billiard.common.third.douyin.client;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.HttpUtils;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.google.common.collect.Maps;
import io.quarkus.redis.datasource.RedisDataSource;
import io.quarkus.redis.datasource.keys.KeyCommands;
import io.quarkus.redis.datasource.value.ValueCommands;
import jakarta.enterprise.inject.spi.CDI;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.third.douyin.request.*;
import tech.wejoy.billiard.common.third.douyin.response.*;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class DouyinClient {

    private final String appKey;

    private final String appSecret;

    private final String baseUrl = "https://open.douyin.com";

    private final ObjectMapper objectMapper = new ObjectMapper();

    private final ValueCommands<String, String> tokenCommands;

    private final KeyCommands<String> keyCommands;

    public DouyinClient(String appKey, String appSecret) {
        this.appKey = appKey;
        this.appSecret = appSecret;
        JsonUtils.configMapper(objectMapper);
        this.objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);
        RedisDataSource ds = CDI.current().select(RedisDataSource.class).get();
        tokenCommands = ds.value(String.class, String.class);
        keyCommands = ds.key(String.class);
    }

    String buildQueryString(Map<String, String> params) {
        if (params == null) {
            params = Maps.newHashMap();
        }
        return params.entrySet().stream().filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
                .map(entry -> entry.getKey() + "=" + URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8)).collect(Collectors.joining("&"));
    }

    private synchronized String getAccessToken() {
        String accessToken = tokenCommands.get("client:douyin:" + appKey);
        if (accessToken != null) {
            return accessToken;
        }
        ClientTokenRequest request = new ClientTokenRequest();
        request.setClientKey(appKey);
        request.setClientSecret(appSecret);
        DouyinResponse<ClientTokenResponse> resp = post("/oauth/client_token/", request, new TypeReference<>() {
        }, false);
        if (resp.getData().getErrorCode() != 0L) {
            throw new BaseException(resp.getData().getMessage());
        }
        accessToken = resp.getData().getAccessToken();
        tokenCommands.set("client:douyin:" + appKey, accessToken);
        keyCommands.expire("client:douyin:" + appKey, resp.getData().getExpiresIn() - 5);
        return accessToken;
    }

    public <T> DouyinResponse<T> get(String url, DouyinRequest request,
                                     TypeReference<DouyinResponse<T>> typeReference,
                                     boolean needToken) {
        try {
            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            headers.put("Content-Type", "application/json");
            if (needToken) {
                headers.put("access-token", getAccessToken());
            }
            Map<String, String> params = objectMapper.convertValue(request, new TypeReference<>() {
            });
            String query = buildQueryString(params);
            HttpUtils.HttpResult response = HttpUtils.get(baseUrl + url + "?" + query).header(headers).send();
            log.info(response.body());
            return objectMapper.readValue(response.body(), typeReference);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public <T> DouyinResponse<T> post(String url, DouyinRequest request,
                                      TypeReference<DouyinResponse<T>> typeReference,
                                      boolean needToken) {
        try {
            LinkedHashMap<String, String> headers = new LinkedHashMap<>();
            headers.put("Content-Type", "application/json");
            if (needToken) {
                headers.put("access-token", getAccessToken());
            }
            String body = objectMapper.writeValueAsString(request);
            HttpUtils.HttpResult response = HttpUtils.post(baseUrl + url).body(body).header(headers).send();
            String body1 = response.body();
            log.info(body1);
            return objectMapper.readValue(response.body(), typeReference);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public ShopPage getShopList(ShopRequest request) {
        DouyinResponse<ShopPage> response = get("/goodlife/v1/shop/poi/query/", request, new TypeReference<>() {
        }, true);
        return response.getData();
    }

    public ProductPage getProductList(ProductRequest request) {
        DouyinResponse<ProductPage> response = get("/goodlife/v1/goods/product/online/query/", request, new TypeReference<>() {
        }, true);
        return response.getData();
    }

    public PrepareCertificateResponse prepareCertificate(PrepareCertificateRequest request) {
        DouyinResponse<PrepareCertificateResponse> response = get("/goodlife/v1/fulfilment/certificate/prepare/", request, new TypeReference<>() {
        }, true);
        return response.getData();
    }

    public VerifyCertificateResponse verifyCertificate(VerifyCertificateRequest request) {
        DouyinResponse<VerifyCertificateResponse> response = post("/goodlife/v1/fulfilment/certificate/verify/", request, new TypeReference<>() {
        }, true);
        return response.getData();
    }

    public ResultResponse cancelCertificate(CancelCertificateRequest request) {
        DouyinResponse<ResultResponse> response = post("/goodlife/v1/fulfilment/certificate/cancel/", request, new TypeReference<>() {
        }, true);
        return response.getData();
    }


    public ResultResponse orderQuery(OrderQueryRequest request) {
        DouyinResponse<ResultResponse> response = post("/goodlife/v1/trade/order/query/", request, new TypeReference<>() {
        }, true);
        return response.getData();
    }


}
