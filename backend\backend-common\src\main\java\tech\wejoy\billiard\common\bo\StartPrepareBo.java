package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@Data
public class StartPrepareBo {

    @Schema(description = "门店信息")
    private ApiClubBo club;

    @Schema(description = "时间方案")
    private List<ClubTimePlanBo> timePlans;

    @Schema(description = "桌台信息")
    private TableBo table;

    @Schema(description = "钱包信息")
    private WalletDetailBo wallet;

    @Schema(description = "订单信息")
    private OrderBo order;


}
