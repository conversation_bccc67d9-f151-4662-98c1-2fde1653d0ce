package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.ClubStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.util.List;

@Data
public class AdminClubDto {

    @Schema(description = "门店ID")
    private Long id;

    @Schema(description = "加盟商ID")
    private Long tenantId;

    @Schema(description = "门店名称")
    private String name;

    @Schema(description = "门店头像")
    private String headImage;

    @Schema(description = "门店图片")
    private List<String> images;

    @Schema(description = "门店地址")
    private String address;

    @Schema(description = "门店电话")
    private String phone;

    @Schema(description = "门店状态")
    private ClubStatusEnum status;

    @Schema(description = "门店标签")
    private List<String> tags;

    @Schema(description = "门店经度")
    private Double latitude;

    @Schema(description = "门店纬度")
    private Double longitude;

    @Schema(description = "闪灯开关")
    private IsEnum flashLight;

    @Schema(description = "会员提示")
    private IsEnum memberTips;

    private Integer tableCount;

    private Integer memberMaxHours;

    private Integer serviceFeeRatio;

}
