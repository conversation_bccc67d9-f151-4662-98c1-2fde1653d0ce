package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.ClubTimeTypeEnum;

import java.math.BigDecimal;

@Data
public class ClubTimePlanBo {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "俱乐部ID")
    private Long clubId;

    @Schema(description = "时长类型")
    private ClubTimeTypeEnum type;

    @Schema(description = "时长名称")
    private String name;

    @Schema(description = "时长")
    private Integer value;

    @Schema(description = "价格")
    private BigDecimal price;

}
