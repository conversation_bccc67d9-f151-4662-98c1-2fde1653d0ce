package tech.wejoy.billiard.common.service;

import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.bo.AdminCouponBo;
import tech.wejoy.billiard.common.dto.AdminCouponQueryDto;
import tech.wejoy.billiard.common.entity.ClientUserCoupon;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.entity.TenantCoupon;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.manager.CouponManager;

import java.util.List;

@Slf4j
@ApplicationScoped
@RequiredArgsConstructor
public class CouponService {

    private final CouponManager couponManager;

    private final ClientUserManager clientUserManager;

    public void reverseCoupon(OrderInfo order) {
        ClientUserCoupon coupon = JsonUtils.toObject(order.getDescription(), ClientUserCoupon.class);
        if (coupon == null) {
            return;
        }
        clientUserManager.reverseCoupon(coupon.getId());
    }

    public ClientUserCoupon getUserCouponById(Long couponId) {
        return clientUserManager.fetchCouponById(couponId);
    }

    public TenantCoupon getCouponById(Long couponId) {
        return couponManager.fetchCouponById(couponId);
    }

    public void useCoupon(Long id) {
        clientUserManager.useCoupon(id);
    }

    public List<AdminCouponBo> adminList(AdminCouponQueryDto dto) {
        if (dto.getClubId() != null) {
            List<TenantCoupon> list = couponManager.fetchCouponByClub(dto.getClubId());
            return BeanUtils.copyList(list, AdminCouponBo.class);
        } else if (dto.getTenantId() != null) {
            List<TenantCoupon> list = couponManager.fetchCouponByTenant(dto.getTenantId());
            return BeanUtils.copyList(list, AdminCouponBo.class);
        } else {
            return List.of();
        }
    }

    public void expireCoupon() {
        List<ClientUserCoupon> userCoupons = clientUserManager.fetchCouponByExpire();
        for (ClientUserCoupon userCoupon : userCoupons) {
            clientUserManager.expireCoupon(userCoupon.getId());
            log.info("expire coupon: {}", userCoupon.getId());
        }
    }

}
