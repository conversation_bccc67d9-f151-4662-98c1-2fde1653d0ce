package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "订单完成结果")
public class OrderOtherFinishResultBo {

    private String orderNo;

    private BigDecimal amount;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private OrderStatusEnum result;

    private Object extra;

}
