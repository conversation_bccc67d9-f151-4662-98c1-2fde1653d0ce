package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class TenantCoupon extends BaseEntity {

    private Long tenantId;

    @Column(columnDefinition = "text")
    private String title;

    @Column(columnDefinition = "text")
    private String description;

    private BigDecimal price;

    private CouponTypeEnum type;

    private Integer minutes;

    @Column(columnDefinition = "text")
    private String period;

    private Integer expireDays;

    private Integer userTotalLimit;

    private Integer userDayLimit;

    private Integer dayLimit;

    private Integer seq;

    private IsEnum status;

    private IsEnum show;

    private IsEnum delete;

}
