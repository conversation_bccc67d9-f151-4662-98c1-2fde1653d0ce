package tech.wejoy.billiard.admin.service;

import com.congeer.core.exception.BaseException;
import com.congeer.security.core.bean.SessionInfo;
import com.congeer.security.core.utils.SecurityHolder;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import tech.wejoy.billiard.admin.bo.AdminUserBo;

@ApplicationScoped
public class AdminSecurityService {

    @Inject
    AdminUserService userService;

    public SessionInfo<Long, AdminUserBo> loginByUsername(String username, String password) {
        AdminUserBo user = userService.fetchLoginByUsername(username);
        if (user == null) {
            throw new BaseException("登录失败");
        }
        if (!SecurityHolder.context().encryptPassword(password).equals(user.getPassword())) {
            throw new BaseException("登录失败");
        }
        if (user.getTenantId() != 0L && user.getClubId() != 0L) {
            throw new BaseException("登录失败");
        }
        return SecurityHolder.login(user);
    }

}
