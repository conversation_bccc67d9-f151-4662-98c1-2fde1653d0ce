package tech.wejoy.billiard.common.third.smyoo.response;

import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class QueryDevicesResponse {

    @JsonProperty("mcuinfos")
    private String mcuInfos;

    public List<McuInfo> getMcuInfos() {
        return JsonUtils.toList(mcuInfos, McuInfo.class);
    }

    @Data
    public static class McuInfo {
        @JsonProperty("roomid")
        private String roomId;
        @JsonProperty("isshare")
        private Integer isShare;
        @JsonProperty("mcuname")
        private String mcuName;
        @JsonProperty("note")
        private String note;
        @JsonProperty("type")
        private Integer type;
        @JsonProperty("gourl")
        private String goUrl;
        @JsonProperty("namesdata")
        private String namesData;
        @JsonProperty("pictureurl")
        private String pictureUrl;
        @JsonProperty("datatype")
        private Integer dataType;
        @JsonProperty("roomname")
        private String roomName;
        @JsonProperty("channelnum")
        private Integer channelNum;
        @JsonProperty("isonline")
        private Integer isOnline;
        @JsonProperty("datapoint")
        private String dataPoint;
        @JsonProperty("mcuid")
        private String mcuId;
    }


}
