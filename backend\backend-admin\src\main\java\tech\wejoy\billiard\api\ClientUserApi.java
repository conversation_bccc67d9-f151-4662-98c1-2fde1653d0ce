package tech.wejoy.billiard.api;

import com.congeer.core.bean.Page;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.AdminUserBo;
import tech.wejoy.billiard.bo.ClientUserBo;
import tech.wejoy.billiard.dto.ClientUserQueryDto;
import tech.wejoy.billiard.service.ClientUserService;

@Path("/client/user")
@Tag(name = "用户接口")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class ClientUserApi {

    private final ClientUserService clientUserService;

    @GET
    @Path("/list")
    @Operation(summary = "用户列表")
    public Page<ClientUserBo> list(@BeanParam ClientUserQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        return clientUserService.adminList(dto);
    }

    @GET
    @Path("/detail")
    @Operation(summary = "用户详情")
    public ClientUserBo detail(Long id) {
        return clientUserService.detail(id);
    }

    @POST
    @Path("/point/check")
    @Operation(summary = "积分校验")
    public void pointCheck() {
        clientUserService.pointCheck();
    }


}
