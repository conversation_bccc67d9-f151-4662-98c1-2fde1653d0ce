package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.CompetitionScheduleStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class CompetitionSchedule extends BaseEntity {

    private Long competitionId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private String title;

    private Integer round;

    private Integer playerCount;

    private Integer winScore;

    private IsEnum loserGroup;

    private CompetitionScheduleStatusEnum status;

}
