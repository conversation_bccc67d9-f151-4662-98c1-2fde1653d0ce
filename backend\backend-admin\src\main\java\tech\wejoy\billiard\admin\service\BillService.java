package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.exception.WxPayException;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.BillBo;
import tech.wejoy.billiard.common.dto.BillQueryDto;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.entity.ClientUser;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.enums.ThirdPayTypeEnum;
import tech.wejoy.billiard.common.manager.BillManager;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.service.ClientUserService;
import tech.wejoy.billiard.common.service.PaymentService;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class BillService {

    private final BillManager billManager;

    private final ClientUserManager clientUserManager;

    private final ClubManager clubManager;

    private final PaymentService paymentService;

    @Inject
    AdminCouponService couponService;

    @Inject
    ClientUserService clientUserService;

    @Inject
    AdminOrderService orderService;

    public BillBo check(Long id) {
        BillInfo billInfo = billManager.fetchById(id);
        if (billInfo.getThirdPayType() == ThirdPayTypeEnum.WECHAT) {
            try {
                WxPayOrderQueryV3Result order = ThirdServiceHolder.wxPayService("user").queryOrderV3(null, billInfo.getBillNo());
                if (order != null && "SUCCESS".equals(order.getTradeState())) {
                    BigDecimal total = new BigDecimal(order.getAmount().getTotal()).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                    BigDecimal payerTotal = new BigDecimal(order.getAmount().getPayerTotal()).divide(new BigDecimal(100), 2, RoundingMode.HALF_EVEN);
                    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
                    LocalDateTime payTime = LocalDateTime.parse(order.getSuccessTime(), dtf);
                    successBill(order.getTransactionId(), billInfo.getBillNo(), total, payerTotal, payTime, JsonUtils.toJson(order));
                }
            } catch (WxPayException e) {
                throw new RuntimeException(e);
            }
        }
        return BeanUtils.copy(billManager.fetchById(id), BillBo.class);
    }

    @Transactional(rollbackOn = Exception.class)
    public void successBill(String outerNo, String billNo, BigDecimal total, BigDecimal payerTotal, LocalDateTime payTime, String resultInfo) {
        BillInfo bill = billManager.fetchByBillNo(billNo);
        if (bill == null) {
            throw new BaseException("账单不存在");
        }
        bill.setStatus(BillStatusEnum.PAID);
        bill.setPayTime(payTime);
        bill.setThirdPayNo(outerNo);
        bill.setTotalAmount(total);
        bill.setPushTime(LocalDateTime.now());
        bill.setPayAmount(payerTotal);
        bill.setResultInfo(resultInfo);
        billManager.save(bill);
        switch (bill.getType()) {
            case TENANT_PLAN -> clientUserService.rechargeClub(bill);
            case MEMBER_PLAN -> clientUserService.planMember(bill);
            case ORDER -> orderService.payOrder(bill);
            case MEMBER_RECHARGE -> clientUserService.rechargeMember(bill);
            case COUPON -> couponService.issueCoupon(bill);
        }
    }

    @Transactional(rollbackOn = Exception.class)
    public void refundBill(String billNo, BigDecimal total, BigDecimal payerTotal, String json) {
        BillInfo bill = billManager.fetchByBillNo(billNo);
        if (bill == null) {
            throw new BaseException("账单不存在");
        }
        bill.setStatus(BillStatusEnum.REFUND);
        bill.setRefundTime(LocalDateTime.now());
        bill.setRefundAmount(payerTotal);
        bill.setRefundInfo(json);
        billManager.save(bill);
    }

    public BillBo refund(Long id) {
        BillInfo billInfo = billManager.fetchById(id);
        if (billInfo.getThirdPayType() == ThirdPayTypeEnum.WECHAT) {
            switch (billInfo.getType()) {
                case TENANT_PLAN -> clientUserService.refundClub(billInfo);
                case MEMBER_PLAN -> clientUserService.refundMember(billInfo);
                case ORDER -> orderService.refundOrder(billInfo);
                case MEMBER_RECHARGE -> clientUserService.refundMemberRecharge(billInfo);
                case COUPON -> clientUserService.refundCoupon(billInfo);
            }
        }
        return BeanUtils.copy(billManager.fetchById(id), BillBo.class);
    }

    public Page<BillBo> list(BillQueryDto dto) {
        if (StringUtils.isNotBlank(dto.getPhone())) {
            List<ClientUser> clientUsers = clientUserManager.fetchListByPhone(dto.getPhone());
            if (clientUsers.isEmpty()) {
                return dto.empty();
            }
            dto.setUserIds(clientUsers.stream().map(ClientUser::getId).toList());
        }
        Page<BillInfo> list = billManager.list(dto);
        List<Long> userIds = list.getRecords().stream().map(BillInfo::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> phoneMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        List<Long> clubIds = list.getRecords().stream().map(BillInfo::getClubId).distinct().toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        return list.convert(v -> {
            BillBo bill = BeanUtils.copy(v, BillBo.class);
            ClientUser clientUser = phoneMap.get(v.getUserId());
            if (clientUser != null) {
                bill.setPhone(clientUser.getPhone());
            }
            ClubInfo clubInfo = clubMap.get(v.getClubId());
            if (clubInfo != null) {
                bill.setClubName(clubInfo.getName());
            }
            return bill;
        });
    }

    @Transactional(rollbackOn = Exception.class)
    public BillBo refundOnly(Long id) {
        BillInfo bill = billManager.fetchById(id);
        String refundResult;
        if (bill.getRefundAmount() == null || bill.getRefundAmount().equals(BigDecimal.ZERO)) {
            refundResult = paymentService.refund(bill.getBillNo(), bill.getPayAmount(), bill.getPayAmount(), "退款");
        } else {
            refundResult = paymentService.refund(bill.getBillNo() + "2", bill.getPayAmount().subtract(bill.getRefundAmount()), bill.getPayAmount(), "退款");
        }
        bill.setRefundInfo(refundResult);
        bill.setRefundTime(LocalDateTime.now());
        bill.setRefundAmount(bill.getPayAmount());
        bill.setStatus(BillStatusEnum.REFUND);
        billManager.save(bill);
        return BeanUtils.copy(bill, BillBo.class);
    }

}
