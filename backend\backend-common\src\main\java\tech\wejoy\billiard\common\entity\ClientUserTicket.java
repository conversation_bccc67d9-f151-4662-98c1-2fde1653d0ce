package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClientUserTicket extends BaseEntity {

    private Long userId;

    private Long dealId;

    private TicketChannelEnum channel;

    private Long channelId;

    private LocalDateTime endTime;

    private LocalDateTime startTime;

    private IsEnum used;

    private IsEnum refund;

}
