package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
@Schema(description = "钱包")
public class WalletBo {

    private Integer memberLevel;

    @Schema(description = "单店余额")
    private BigDecimal clubTotalBalance;

    @Schema(description = "会员余额")
    private BigDecimal memberBalance;

    private Integer couponCount;

    @Schema(description = "代币")
    private Integer coins;

    @Schema(description = "积分")
    private Integer points;

}
