package tech.wejoy.billiard.common.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;

import java.math.BigDecimal;

@Data
public class ChannelDealBo {

    private Long id;

    private String name;

    private BigDecimal price;

    private BigDecimal marketPrice;

    private CouponTypeEnum type;

    private Integer minutes;

    private String period;

    private IsEnum status;

    private TicketChannelEnum channel;

}
