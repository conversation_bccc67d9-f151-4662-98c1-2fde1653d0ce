package tech.wejoy.billiard.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.OtherFinishOrderEnum;

@Data
@Accessors(chain = true)
public class OrderOtherFinishDto {

    @Schema(title = "抢单类型")
    private OtherFinishOrderEnum type;

    @Schema(hidden = true)
    private String orderNo;

    @Schema(title = "支付类型")
    private OrderPayTypeEnum payType;

    @Schema(hidden = true)
    private Long userId;

}
