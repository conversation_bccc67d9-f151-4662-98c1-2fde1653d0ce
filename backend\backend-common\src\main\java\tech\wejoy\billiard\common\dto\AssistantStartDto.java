package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;

import java.time.LocalDateTime;

@Data
public class AssistantStartDto {

    @Schema(hidden = true)
    private Long userId;

    private Long assistantId;

    private Long clubId;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private OrderPayTypeEnum payType;

    private Object extra;

}
