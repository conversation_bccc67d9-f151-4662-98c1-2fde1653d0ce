package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.admin.service.BillService;
import tech.wejoy.billiard.common.bo.BillBo;
import tech.wejoy.billiard.common.dto.BillQueryDto;

@Path("/bill")
@Tag(name = "账单接口")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class BillApi {

    private final BillService billService;

    @GET
    @Path("/list")
    @Operation(summary = "账单列表")
    public Page<BillBo> list(@BeanParam BillQueryDto dto) {
        return billService.list(dto);
    }

    @POST
    @Path("/{id}/check")
    @Operation(summary = "确认订单")
    public BillBo check(@PathParam("id") Long id) {
        return billService.check(id);
    }

    @POST
    @Path("/{id}/refund")
    @Operation(summary = "退款")
    public BillBo refund(@PathParam("id") Long id) {
        return billService.refund(id);
    }

    @POST
    @Path("/{id}/refund/only")
    @Operation(summary = "仅退款")
    public BillBo refundOnly(@PathParam("id") Long id) {
        return billService.refundOnly(id);
    }

}
