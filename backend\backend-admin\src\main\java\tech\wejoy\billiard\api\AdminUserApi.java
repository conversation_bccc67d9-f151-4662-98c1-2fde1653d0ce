package tech.wejoy.billiard.api;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.*;
import tech.wejoy.billiard.dto.*;
import tech.wejoy.billiard.enums.AdminPermissionEnum;
import tech.wejoy.billiard.enums.BusinessPermissionEnum;
import tech.wejoy.billiard.service.AdminUserService;
import tech.wejoy.billiard.service.BusinessService;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Path("/admin")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "管理员用户接口")
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class AdminUserApi {

    private final AdminUserService adminUserService;

    private final BusinessService businessService;

    @PUT
    @Path("/password")
    @Operation(summary = "修改密码")
    public void updatePassword(ChangePasswordDto dto) {
        if (StringUtils.isBlank(dto.getNewPassword())) {
            throw new BaseException("新密码不能为空");
        }
        if (StringUtils.isBlank(dto.getOldPassword())) {
            throw new BaseException("旧密码不能为空");
        }
        if (dto.getNewPassword().equals(dto.getOldPassword())) {
            throw new BaseException("新密码不能与旧密码相同");
        }
        Long userId = SecurityHolder.<Long, AdminUserBo>session().getUserId();
        String oldPassword = SecurityHolder.context().encryptPassword(dto.getOldPassword());
        String newPassword = SecurityHolder.context().encryptPassword(dto.getNewPassword());
        adminUserService.changePassword(userId, oldPassword, newPassword);
    }

    @GET
    @Path("/stats")
    @Operation(summary = "管理员统计")
    @Permission({"business_total_stats:read", "business_total_stats:write"})
    public UserStatsBo stats() {
        return businessService.userStats();
    }

    @POST
    @Path("/user")
    @Operation(summary = "添加管理员")
    @Permission({"business_employee:write", "admin_employee_op"})
    public AdminUserBo addAdmin(AdminUserDto adminUserBo) {
        return adminUserService.addAdmin(adminUserBo);
    }

    @DELETE
    @Path("/user/{id}")
    @Operation(summary = "删除管理员")
    @Permission({"business_employee:write", "admin_employee_op"})
    public void deleteAdmin(@PathParam("id") Long id) {
        adminUserService.deleteAdmin(id);
    }

    @PUT
    @Path("/user/{id}")
    @Operation(summary = "更新管理员")
    @Permission({"business_employee:write", "admin_employee_op"})
    public AdminUserBo updateAdmin(@PathParam("id") Long id, AdminUserDto adminUserBo) {
        adminUserBo.setId(id);
        return adminUserService.saveAdmin(adminUserBo);
    }

    @GET
    @Path("/user/{id}")
    @Operation(summary = "管理员详情")
    @Permission({"business_employee:read", "business_employee:write", "admin_employee"})
    public AdminUserBo detail(@PathParam("id") Long id) {
        return adminUserService.detail(id);
    }

    @GET
    @Path("/user/list")
    @Operation(summary = "管理员列表")
    @Permission({"business_employee:read", "business_employee:write", "admin_employee"})
    public Page<AdminUserBo> list(AdminUserQueryDto dto) {
        return adminUserService.list(dto);
    }

    @GET
    @Path("/role/list")
    @Operation(summary = "角色列表")
    @Permission({"business_employee:read", "business_employee:write", "admin_role"})
    public Page<AdminRoleBo> roleList(AdminRoleQueryDto dto) {
        return adminUserService.roleList(dto);
    }

    @GET
    @Path("/role/options")
    @Operation(summary = "角色选项")
    public List<AdminRoleBo> roleOptions() {
        return adminUserService.roleOptions();
    }

    @POST
    @Path("/role")
    @Operation(summary = "添加角色")
    @Permission({"business_employee:write", "admin_role_op"})
    public AdminRoleBo addRole(AdminRoleDto adminRoleBo) {
        return adminUserService.addRole(adminRoleBo);
    }

    @DELETE
    @Path("/role/{id}")
    @Operation(summary = "删除角色")
    @Permission({"business_employee:write", "admin_role_op"})
    public void deleteRole(@PathParam("id") Long id) {
        adminUserService.deleteRole(id);
    }

    @PUT
    @Path("/role/{id}")
    @Operation(summary = "更新角色")
    @Permission({"business_employee:write", "admin_role_op"})
    public AdminRoleBo updateRole(@PathParam("id") Long id, AdminRoleDto adminRoleBo) {
        adminRoleBo.setId(id);
        return adminUserService.saveRole(adminRoleBo);
    }

    @GET
    @Path("/role/{id}")
    @Operation(summary = "角色详情")
    @Permission({"business_employee:write", "admin_role"})
    public AdminRoleBo roleDetail(@PathParam("id") Long id) {
        return adminUserService.roleDetail(id);
    }

    @GET
    @Path("/permission/business")
    @Operation(summary = "商户端权限列表")
    public List<BusinessPermissionBo> businessPermissionList() {
        BusinessPermissionEnum[] values = BusinessPermissionEnum.values();
        return Arrays.stream(values).map(v -> {
            BusinessPermissionBo bo = new BusinessPermissionBo();
            bo.setCode(v.getCode());
            bo.setName(v.getName());
            bo.setGroup(v.getGroup());
            return bo;
        }).toList();
    }

    @GET
    @Path("/permission/admin")
    @Operation(summary = "管理员端权限列表")
    public List<AdminPermissionBo> adminPermissionList() {
        AdminPermissionEnum[] values = AdminPermissionEnum.values();
        return Arrays.stream(values).map(v -> {
            AdminPermissionBo bo = new AdminPermissionBo();
            bo.setCode(v.getCode());
            bo.setName(v.getName());
            return bo;
        }).toList();
    }

}
