package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@EqualsAndHashCode(callSuper = true)
@Data
public class MatchQueryDto extends PageRequest {

    @Schema(description = "门店名称")
    @QueryParam("name")
    private String name;

    @Schema(description = "经度")
    @QueryParam("lng")
    private Double lng;

    @Schema(description = "纬度")
    @QueryParam("lat")
    private Double lat;

    private Long userId;

}
