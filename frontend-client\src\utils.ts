import Taro from "@tarojs/taro";
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function menuRect() {
  if (process.env.TARO_ENV === "weapp") {
    return Taro.getMenuButtonBoundingClientRect();
  } else {
    return {
      bottom: 0,
      height: 32,
      left: 0,
      right: 0,
      top: 0,
      width: 32,
    };
  }
}

export const toPrice = (price: number, decimal: number = 2) => {
  return `¥${Number((price || 0)).toFixed(decimal).toString()}`;
};

export function chunk<T>(arr: T[], size: number) {
  const chunks: T[][] = []
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
}


export function numberFixed(num: any, fixed: number = 2) {
  return Number(Number(num).toFixed(fixed).toString());
}
