"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/assistant/confirmation/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/confirmation/index!./src/pages/assistant/confirmation/index.tsx":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/confirmation/index!./src/pages/assistant/confirmation/index.tsx ***!
  \**************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SafeArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/safearea.taro-BJpvFsMf.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Cell/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Cell */ "./node_modules/@nutui/nutui-react-taro/dist/esm/cell.taro-DWLhb5m6.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Dialog/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog */ "./node_modules/@nutui/nutui-react-taro/dist/esm/dialog.taro-1Vukbvap.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Picker_style_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Picker/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Picker/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Picker__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Picker */ "./node_modules/@nutui/nutui-react-taro/dist/esm/picker.taro-Ctc0Wt4S.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _components_bussiness_Title__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/bussiness/Title */ "./src/components/bussiness/Title/index.tsx");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/api/bussiness/assistant */ "./src/api/bussiness/assistant.ts");
/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! formik */ "./node_modules/formik/dist/formik.esm.js");
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _components_bussiness_DurationTime_index2__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/bussiness/DurationTime/index2 */ "./src/components/bussiness/DurationTime/index2.tsx");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/bussiness/Order/Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! yup */ "./node_modules/yup/index.esm.js");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/hooks */ "./src/hooks/index.ts");
/* harmony import */ var _stores_auth__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/stores/auth */ "./src/stores/auth.ts");
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _components_bussiness_assistant_StartButton__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/bussiness/assistant/StartButton */ "./src/components/bussiness/assistant/StartButton.tsx");
/* harmony import */ var _components_bussiness_assistant_FinishButton__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/bussiness/assistant/FinishButton */ "./src/components/bussiness/assistant/FinishButton.tsx");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");


































var defaultURI = "https://oss.gorillaballclub.cn/images/big-logo-y.png";
var StoreChoose = function StoreChoose(props) {
  var value = props.value,
    onChange = props.onChange,
    confirmation = props.confirmation,
    disabled = props.disabled;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_21__["default"])(_useState, 2),
    visible = _useState2[0],
    setVisible = _useState2[1];
  var options = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    var _confirmation$assista;
    return ((_confirmation$assista = confirmation.assistant) === null || _confirmation$assista === void 0 || (_confirmation$assista = _confirmation$assista.clubs) === null || _confirmation$assista === void 0 ? void 0 : _confirmation$assista.map(function (item) {
      return {
        text: item.name,
        value: item.id
      };
    })) || [];
  }, [confirmation]);
  var onConfirm = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(function (__options, _ref) {
    var _ref2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_21__["default"])(_ref, 1),
      value = _ref2[0];
    onChange(value);
    setVisible(false);
  }, [onChange, setVisible]);
  var currentValue = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(function () {
    var _options$find;
    return ((_options$find = options.find(function (item) {
      return item.value == value;
    })) === null || _options$find === void 0 ? void 0 : _options$find.text) || '';
  }, [options, value]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
      className: "flex gap-2 items-center",
      onClick: function onClick() {
        return !disabled && setVisible(true);
      },
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("span", {
        children: currentValue
      }), !disabled && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_11__.ArrowRight, {})]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Picker__WEBPACK_IMPORTED_MODULE_22__.P, {
      options: options,
      visible: visible,
      onConfirm: onConfirm,
      onClose: function onClose() {
        return setVisible(false);
      }
    })]
  });
};
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var _formik$values, _formik$values2, _formik$values3, _formik$values4;
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_7__.menuRect)();
  var router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_8__.useRouter)();
  var id = router.params.id;
  var orderId = router.params.orderId;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)('预约下单'),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_21__["default"])(_useState3, 2),
    title = _useState4[0],
    setTitle = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_21__["default"])(_useState5, 2),
    confirmation = _useState6[0],
    setConfirmation = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(0),
    _useState8 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_21__["default"])(_useState7, 2),
    price = _useState8[0],
    setPrice = _useState8[1];
  var durationTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_9__.useRef)(null);
  var dispatch = (0,_hooks__WEBPACK_IMPORTED_MODULE_23__.useDo)();
  var _useStore = (0,_hooks__WEBPACK_IMPORTED_MODULE_23__.useStore)(function (state) {
      return state.auth;
    }),
    user = _useStore.user;
  var formik = (0,formik__WEBPACK_IMPORTED_MODULE_24__.useFormik)({
    initialValues: {
      clubId: "",
      startTime: "",
      endTime: "",
      payType: 1,
      extar: {},
      status: undefined,
      code: undefined,
      userNickname: '',
      userPhone: ''
    },
    validationSchema: yup__WEBPACK_IMPORTED_MODULE_14__.object({
      clubId: yup__WEBPACK_IMPORTED_MODULE_14__.number().required('请选择所在门店')
    }),
    validateOnChange: false,
    onSubmit: function onSubmit() {}
  });
  var cancelOrder = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/function () {
    var _ref3 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().mark(function _callee(orderNo) {
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            _context.next = 2;
            return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__["default"].cancelOrder(orderNo);
          case 2:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref3.apply(this, arguments);
    };
  }(), []);
  var getConfirmation = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().mark(function _callee2() {
    var _data$assistant, _yield$assistant$conf, data, _yield$assistant$getO, _data;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          if (!id) {
            _context2.next = 10;
            break;
          }
          _context2.next = 3;
          return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__["default"].confirmation({
            assistantId: id
          });
        case 3:
          _yield$assistant$conf = _context2.sent;
          data = _yield$assistant$conf.data;
          setConfirmation(data);
          formik.setFieldValue('clubId', ((_data$assistant = data.assistant) === null || _data$assistant === void 0 || (_data$assistant = _data$assistant.clubs) === null || _data$assistant === void 0 ? void 0 : _data$assistant[0].id) || '');
          setTitle('预约下单');
          _context2.next = 18;
          break;
        case 10:
          _context2.next = 12;
          return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__["default"].getOrder(orderId);
        case 12:
          _yield$assistant$getO = _context2.sent;
          _data = _yield$assistant$getO.data;
          setConfirmation(_data);
          formik.resetForm({
            values: _data.order
          });
          setPrice(_data.order.realAmount);
          setTitle('订单详情');
        case 18:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [id, setConfirmation, setPrice, formik, setTitle]);
  var onCalculationPayment = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/function () {
    var _ref5 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().mark(function _callee3(range) {
      var _range, startTime, endTime, _yield$assistant$calc, data;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().wrap(function _callee3$(_context3) {
        while (1) switch (_context3.prev = _context3.next) {
          case 0:
            if (!Array.isArray(range)) {
              _context3.next = 9;
              break;
            }
            _range = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_21__["default"])(range, 2), startTime = _range[0], endTime = _range[1];
            _context3.next = 4;
            return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__["default"].calculationPayment({
              assistantId: id,
              startTime: startTime,
              endTime: endTime
            });
          case 4:
            _yield$assistant$calc = _context3.sent;
            data = _yield$assistant$calc.data;
            setPrice(data.price);
            formik.setFieldValue('startTime', startTime);
            formik.setFieldValue('endTime', endTime);
          case 9:
          case "end":
            return _context3.stop();
        }
      }, _callee3);
    }));
    return function (_x2) {
      return _ref5.apply(this, arguments);
    };
  }(), [id, durationTimeRef, setPrice]);
  var onGetPhoneNumber = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/function () {
    var _ref6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().mark(function _callee4(e) {
      var code, resp, payload;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().wrap(function _callee4$(_context4) {
        while (1) switch (_context4.prev = _context4.next) {
          case 0:
            code = e.detail.code;
            if (code) {
              _context4.next = 3;
              break;
            }
            return _context4.abrupt("return");
          case 3:
            _context4.next = 5;
            return _api_bussiness__WEBPACK_IMPORTED_MODULE_17__["default"].user.bindPhone(code);
          case 5:
            resp = _context4.sent;
            if (!(resp.statusCode !== 204 && resp.statusCode !== 200)) {
              _context4.next = 8;
              break;
            }
            return _context4.abrupt("return", _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().showToast({
              title: resp.data.message,
              icon: "none"
            }));
          case 8:
            payload = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_27__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_27__["default"])({}, user), {}, {
              hasPhone: true
            });
            dispatch((0,_stores_auth__WEBPACK_IMPORTED_MODULE_16__.login)(payload));
            _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_28__.B.close("authorizePhone");
          case 11:
          case "end":
            return _context4.stop();
        }
      }, _callee4);
    }));
    return function (_x3) {
      return _ref6.apply(this, arguments);
    };
  }(), [user]);
  var onPayment = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().mark(function _callee6() {
    var errorObjects, params, _yield$assistant$star, data, extra, result;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().wrap(function _callee6$(_context6) {
      while (1) switch (_context6.prev = _context6.next) {
        case 0:
          if (user !== null && user !== void 0 && user.hasPhone) {
            _context6.next = 2;
            break;
          }
          return _context6.abrupt("return", _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_28__.B.open("authorizePhone", {
            title: "授权手机号",
            content: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("div", {
                className: "mb-4",
                children: "\u4E3A\u4E86\u63D0\u4F9B\u66F4\u597D\u7684\u552E\u540E\u670D\u52A1\u6211\u4EEC\u5E0C\u671B\u83B7\u53D6\u5230\u4F60\u7684\u624B\u673A\u53F7"
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_29__.Button, {
                className: "bg-primary rounded-sm text-bgt h-10 py-1 px-3 flex items-center justify-center",
                openType: "getPhoneNumber",
                onGetPhoneNumber: onGetPhoneNumber,
                children: "\u6388\u6743\u7ED1\u5B9A"
              })]
            }),
            hideCancelButton: true,
            hideConfirmButton: true
          }));
        case 2:
          _context6.next = 4;
          return formik.validateForm();
        case 4:
          errorObjects = _context6.sent;
          if (!(errorObjects && Object.keys(errorObjects).length)) {
            _context6.next = 7;
            break;
          }
          return _context6.abrupt("return", _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().showToast({
            title: Object.values(errorObjects)[0],
            icon: 'none'
          }));
        case 7:
          params = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_27__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_27__["default"])({}, formik.values), {}, {
            assistantId: id
          });
          _context6.next = 10;
          return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__["default"].start(params);
        case 10:
          _yield$assistant$star = _context6.sent;
          data = _yield$assistant$star.data;
          extra = data.extra, result = data.result;
          if (!(result == 2)) {
            _context6.next = 15;
            break;
          }
          return _context6.abrupt("return", _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().showToast({
            title: data.message,
            icon: 'none'
          }));
        case 15:
          _context6.next = 17;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().requestPayment((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_27__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_27__["default"])({}, extra), {}, {
            package: extra.packageStr,
            success: function success() {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().redirectTo({
                url: '/pages/order/index?tab=1'
              });
            },
            fail: function () {
              var _fail = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().mark(function _callee5() {
                return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().wrap(function _callee5$(_context5) {
                  while (1) switch (_context5.prev = _context5.next) {
                    case 0:
                      _context5.next = 2;
                      return _api_bussiness_assistant__WEBPACK_IMPORTED_MODULE_10__["default"].cancelOrder(data.orderNo);
                    case 2:
                      _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().redirectTo({
                        url: '/pages/order/index?tab=1'
                      });
                    case 3:
                    case "end":
                      return _context5.stop();
                  }
                }, _callee5);
              }));
              function fail() {
                return _fail.apply(this, arguments);
              }
              return fail;
            }()
          }));
        case 17:
        case "end":
          return _context6.stop();
      }
    }, _callee6);
  })), [formik, id]);
  (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(function () {
    getConfirmation();
  }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_30__.C, {
      theme: {
        "--nutui-cell-group-title-color": "hsl(var(--primary))",
        "--nutui-cell-title-color": "#fff",
        "--nutui-cell-background-color": "hsl(var(--background-third))",
        "--nutui-cell-group-background-color": "hsl(var(--background-third))",
        "--nutui-cell-group-wrap-margin": '0',
        "--nutui-picker-title-ok-color": "hsl(var(--primary))"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
        className: "flex flex-col h-_100vh_",
        style: {
          paddingTop: rect.bottom + 10
        },
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_5__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_components_bussiness_Title__WEBPACK_IMPORTED_MODULE_6__["default"], {
          name: title
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
          className: "flex-1 flex flex-col px-3 gap-3",
          children: [confirmation && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
            className: "p-3 bg-bgt flex justify-between rounded-md items-center",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
              className: "flex gap-3 items-center",
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_29__.Image, {
                src: confirmation.assistant.avatar || defaultURI,
                className: "rounded-full w-12 h-12"
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("div", {
                className: "text-lg font-bold text-white",
                children: confirmation.assistant.name
              })]
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
              className: "flex text-lg items-center",
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("span", {
                className: "text-primary font-bold",
                children: (0,_utils__WEBPACK_IMPORTED_MODULE_7__.toPrice)(confirmation.assistant.price, 2)
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("span", {
                className: "text-base text-muted-foreground",
                children: "/\u5C0F\u65F6"
              })]
            })]
          }), confirmation && !orderId && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_components_bussiness_DurationTime_index2__WEBPACK_IMPORTED_MODULE_12__["default"], {
            plans: confirmation === null || confirmation === void 0 ? void 0 : confirmation.timePlans,
            onChange: function onChange() {},
            ref: durationTimeRef,
            onCalculationPayment: onCalculationPayment,
            scene: 0
          }), confirmation && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C.Group, {
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
              title: "\u6240\u5728\u95E8\u5E97",
              extra: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(StoreChoose, {
                disabled: !!orderId,
                confirmation: confirmation,
                value: formik.values.clubId,
                onChange: function onChange(value) {
                  return formik.setFieldValue('clubId', value);
                }
              })
            }), (user === null || user === void 0 ? void 0 : user.assistantId) != (confirmation === null || confirmation === void 0 ? void 0 : confirmation.assistant.id) && [_constants__WEBPACK_IMPORTED_MODULE_15__.ORDERTYPES.PAID].includes((_formik$values = formik.values) === null || _formik$values === void 0 ? void 0 : _formik$values.status) && formik.values.code && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
              title: "\u9A8C\u8BC1\u7801",
              extra: formik.values.code
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
              title: "\u5F00\u59CB\u65F6\u95F4",
              extra: formik.values.startTime
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
              title: "\u7ED3\u675F\u65F6\u95F4",
              extra: formik.values.endTime
            }), (user === null || user === void 0 ? void 0 : user.assistantId) && (user === null || user === void 0 ? void 0 : user.assistantId) == (confirmation === null || confirmation === void 0 ? void 0 : confirmation.assistant.id) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.Fragment, {
              children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
                title: "\u5B66\u5458\u6635\u79F0",
                extra: formik.values.userNickname
              }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
                title: "\u5B66\u5458\u8054\u7CFB\u65B9\u5F0F",
                extra: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("div", {
                  onClick: function onClick() {
                    return formik.values.userPhone && _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().makePhoneCall({
                      phoneNumber: formik.values.userPhone
                    });
                  },
                  children: formik.values.userPhone
                })
              })]
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
              title: "\u4EF7\u683C",
              extra: (0,_utils__WEBPACK_IMPORTED_MODULE_7__.toPrice)(price, 2)
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Cell__WEBPACK_IMPORTED_MODULE_31__.C, {
              title: "\u652F\u4ED8\u65B9\u5F0F",
              extra: "微信支付"
            })]
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
          className: "px-4 py-3 bg-bgt",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
            className: "flex justify-between items-center",
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("div", {
              className: "text-base font-semibold",
              children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)("div", {
                className: "flex space-x-1 items-center",
                children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("div", {
                  className: "text-sm",
                  children: "\u5B9E\u4ED8\u6B3E:"
                }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)("div", {
                  className: "text-primary text-xl first-letterctext-sm",
                  children: (0,_utils__WEBPACK_IMPORTED_MODULE_7__.toPrice)(price, 2)
                })]
              })
            }), !orderId && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_13__["default"], {
              buttonText: "\u786E\u5B9A\u9884\u7EA6",
              onClick: onPayment
            }), (user === null || user === void 0 ? void 0 : user.assistantId) != (confirmation === null || confirmation === void 0 ? void 0 : confirmation.assistant.id) && [_constants__WEBPACK_IMPORTED_MODULE_15__.ORDERTYPES.PENDING, _constants__WEBPACK_IMPORTED_MODULE_15__.ORDERTYPES.PAID].includes((_formik$values2 = formik.values) === null || _formik$values2 === void 0 ? void 0 : _formik$values2.status) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_13__["default"], {
              buttonText: "\u53D6\u6D88",
              mode: _components_bussiness_Order_Button__WEBPACK_IMPORTED_MODULE_13__.MODES.DANGER,
              onClick: /*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_25__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().mark(function _callee7() {
                var _yield$Taro$showModal, confirm;
                return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_26__["default"])().wrap(function _callee7$(_context7) {
                  while (1) switch (_context7.prev = _context7.next) {
                    case 0:
                      _context7.next = 2;
                      return _tarojs_taro__WEBPACK_IMPORTED_MODULE_8___default().showModal({
                        title: "提示",
                        content: "是否确认当前操作",
                        confirmText: "确定"
                      });
                    case 2:
                      _yield$Taro$showModal = _context7.sent;
                      confirm = _yield$Taro$showModal.confirm;
                      if (confirm) {
                        cancelOrder(orderId).then(function () {
                          return getConfirmation();
                        });
                      }
                    case 5:
                    case "end":
                      return _context7.stop();
                  }
                }, _callee7);
              }))
            }), (user === null || user === void 0 ? void 0 : user.assistantId) && (user === null || user === void 0 ? void 0 : user.assistantId) == (confirmation === null || confirmation === void 0 ? void 0 : confirmation.assistant.id) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.Fragment, {
              children: [[_constants__WEBPACK_IMPORTED_MODULE_15__.ORDERTYPES.PAID].includes((_formik$values3 = formik.values) === null || _formik$values3 === void 0 ? void 0 : _formik$values3.status) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_components_bussiness_assistant_StartButton__WEBPACK_IMPORTED_MODULE_18__["default"], {
                orderNo: orderId,
                onLoad: getConfirmation
              }), [_constants__WEBPACK_IMPORTED_MODULE_15__.ORDERTYPES.USING].includes((_formik$values4 = formik.values) === null || _formik$values4 === void 0 ? void 0 : _formik$values4.status) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_components_bussiness_assistant_FinishButton__WEBPACK_IMPORTED_MODULE_19__["default"], {
                orderNo: orderId,
                onLoad: getConfirmation
              })]
            })]
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_32__.S, {
            position: "bottom"
          })]
        })]
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_30__.C, {
      theme: {
        "--nutui-gray-7": "#fff",
        "--nutui-gray-6": "#fff",
        "--nutui-dialog-header-font-weight": "600",
        "--nutui-dialog-header-font-size": "1.25rem",
        "--nutui-dialog-padding": "40rpx"
      },
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_20__.jsx)(_nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_28__.B, {
        id: "authorizePhone"
      })
    })]
  });
});

/***/ }),

/***/ "./src/components/bussiness/DurationTime/index2.tsx":
/*!**********************************************************!*\
  !*** ./src/components/bussiness/DurationTime/index2.tsx ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Picker_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Picker/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Picker/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Picker__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Picker */ "./node_modules/@nutui/nutui-react-taro/dist/esm/picker.taro-Ctc0Wt4S.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_DatePicker_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/DatePicker/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/DatePicker/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_DatePicker__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/DatePicker */ "./node_modules/@nutui/nutui-react-taro/dist/esm/datepicker.taro-CPSTcVSz.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! clsx */ "./node_modules/clsx/dist/clsx.mjs");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/constants */ "./src/constants/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");














var supportAppointment = false;
var DurationTime = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_4__.forwardRef)(function (props, ref) {
  var plans = props.plans,
    onChange = props.onChange,
    scene = props.scene;
  var scanCode = scene === _constants__WEBPACK_IMPORTED_MODULE_6__.SCANCETYPES.SCANCODE;
  var normalizePlans = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    if (Array.isArray(plans)) {
      var plansData = Array.from(plans);
      plansData.sort(function (a, b) {
        return b.type - a.type;
      });
      return plansData;
    }
    return [];
  }, [plans]);
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__["default"])(_useState, 2),
    current = _useState2[0],
    setCurrent = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(0),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__["default"])(_useState3, 2),
    type = _useState4[0],
    setType = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(2),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__["default"])(_useState5, 2),
    durationTime = _useState6[0],
    setDurationTime = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState8 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__["default"])(_useState7, 2),
    visible = _useState8[0],
    setVisible = _useState8[1];
  var _useState9 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(function () {
      return dayjs__WEBPACK_IMPORTED_MODULE_3___default()();
    }),
    _useState0 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__["default"])(_useState9, 2),
    currentTime = _useState0[0],
    setCurrentTime = _useState0[1];
  var _useState1 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false),
    _useState10 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__["default"])(_useState1, 2),
    customizationVisible = _useState10[0],
    setCustomizationVisible = _useState10[1];
  var changeCurrent = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (index) {
    setCurrent(index);
    var day = dayjs__WEBPACK_IMPORTED_MODULE_3___default()();
    setCurrentTime(day.add(index, "day"));
  }, [setCurrent, setCurrentTime, setDurationTime]);
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    var currentPlan = normalizePlans[current];
    props.onChange(currentPlan.value);
  }, [normalizePlans]);
  var maxValue = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var maxValue = Math.max.apply(Math, (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_9__["default"])(plans.map(function (planItem) {
      return planItem.type === _constants__WEBPACK_IMPORTED_MODULE_6__.TIMETYPES.customization ? 0 : planItem.value;
    })));
    return maxValue;
  }, [plans]);
  var options = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var hour = 24;
    var defaultMinues = [{
      text: "0分钟",
      value: 0
    }, {
      text: "30分钟",
      value: 0.5
    }];
    return new Array(hour - maxValue).fill(0).map(function (__item, index) {
      return {
        text: "".concat(index + maxValue + 1, "\u5C0F\u65F6"),
        value: index + maxValue + 1,
        children: index == hour - maxValue - 1 ? [defaultMinues[0]] : defaultMinues
      };
    });
  }, [maxValue]);
  var onChangeType = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (index) {
    var currentPlan = normalizePlans[index];
    if (currentPlan.type == _constants__WEBPACK_IMPORTED_MODULE_6__.TIMETYPES.customization) {
      setDurationTime(maxValue + 1);
      setCustomizationVisible(true);
    } else {
      onChange(currentPlan.value);
    }
    setType(index);
  }, [setType, setDurationTime, setCustomizationVisible, normalizePlans, onChange, maxValue]);
  var onConfirmPicker = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (__options, values) {
    var total = values.reduce(function (total, item) {
      return total + item;
    }, 0);
    console.log("onConfirmPicker", total, values);
    onChange(total);
    setDurationTime(total);
  }, [setDurationTime, onChange]);
  var range = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var currentPlan = normalizePlans[type];
    if (currentPlan && currentPlan.type == _constants__WEBPACK_IMPORTED_MODULE_6__.TIMETYPES.customization) {
      // 自定义逻辑
      return [currentTime, currentTime.add(durationTime, "h")];
    }
    return [currentTime, currentTime.add(currentPlan.value, "h")];
  }, [type, normalizePlans, durationTime, currentTime]);
  var getWeekDay = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (day, index) {
    var weekdays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return index ? weekdays[day] : "今天";
  }, []);
  var changeTime = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    setVisible(true);
  }, [setVisible]);
  var onConfirm = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function (__options, values) {
    var _values = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_8__["default"])(values, 2),
      hour = _values[0],
      minute = _values[1];
    var now = dayjs__WEBPACK_IMPORTED_MODULE_3___default()();
    var current = currentTime.set("hour", +hour).set("minute", +minute);
    if (current.isAfter(now)) {
      setCurrentTime(current);
    } else {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().showToast({
        title: "不能设置历史时间",
        icon: "none"
      });
    }
  }, [currentTime, setCurrentTime]);
  var getData = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(function () {
    return range.map(function (item) {
      return item.format("YYYY-MM-DD HH:mm:ss");
    });
  }, [range]);
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(function () {
    console.log(getData());
    props.onCalculationPayment(getData());
  }, [range]);
  (0,react__WEBPACK_IMPORTED_MODULE_4__.useImperativeHandle)(ref, function () {
    return {
      getData: getData
    };
  }, [range]);
  var times = (0,react__WEBPACK_IMPORTED_MODULE_4__.useMemo)(function () {
    var nums = 5;
    var today = dayjs__WEBPACK_IMPORTED_MODULE_3___default()();
    return new Array(nums).fill("").map(function (__item, index) {
      return today.add(index, "day");
    });
  }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)("div", {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
      className: "border-1 rounded-lg flex gap-3 flex-col",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
        className: "grid grid-cols-5 gap-2 w-full",
        children: times.map(function (item, index) {
          return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
            className: (0,clsx__WEBPACK_IMPORTED_MODULE_11__["default"])("text-center rounded-md p-1 text-xs flex-1", {
              "bg-primary text-background": index === current,
              "bg-bgt text-foreground": index !== current
            }),
            onClick: function onClick() {
              return changeCurrent(index);
            },
            children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
              className: "text-sm",
              children: item.format("MM/DD")
            }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
              className: "text-sm",
              children: getWeekDay(item.day(), index)
            })]
          });
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
        className: "flex items-center",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
          className: "flex items-center gap-2 flex-1",
          children: normalizePlans.map(function (item, index) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
              className: (0,clsx__WEBPACK_IMPORTED_MODULE_11__["default"])("text-center rounded-md h-8 flex justify-center items-center p-1 text-xs flex-1", {
                "bg-primary text-background": index === type,
                "bg-bgt text-foreground": index !== type
              }),
              onClick: function onClick() {
                return onChangeType(index);
              },
              children: item.name
            });
          })
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
        className: "flex items-center",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
          className: "flex gap-2 flex-1",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
            className: " h-8 border bg-primary text-background rounded-lg p-1 text-xs flex-1 flex items-center justify-center",
            onClick: changeTime,
            children: [range[0].format("HH:mm"), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_5__.Edit, {
              className: "ml-2"
            })]
          }), "~", /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
            className: " h-8 bg-bgt text-foreground rounded-lg p-1 text-xs flex-1 flex items-center justify-center",
            children: range[1].format("HH:mm")
          })]
        })
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_nutui_nutui_react_taro_dist_esm_DatePicker__WEBPACK_IMPORTED_MODULE_12__.D, {
      type: "hour-minutes",
      title: "\u65E5\u671F\u9009\u62E9",
      visible: visible,
      defaultValue: currentTime.toDate(),
      showChinese: true,
      onClose: function onClose() {
        return setVisible(false);
      },
      threeDimensional: false,
      onConfirm: onConfirm
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.jsx)(_nutui_nutui_react_taro_dist_esm_Picker__WEBPACK_IMPORTED_MODULE_13__.P, {
      visible: customizationVisible,
      options: options,
      title: "\u81EA\u5B9A\u4E49\u65F6\u95F4",
      onConfirm: onConfirmPicker,
      onClose: function onClose() {
        return setCustomizationVisible(false);
      }
    })]
  });
});
/* harmony default export */ __webpack_exports__["default"] = (DurationTime);

/***/ }),

/***/ "./src/pages/assistant/confirmation/index.tsx":
/*!****************************************************!*\
  !*** ./src/pages/assistant/confirmation/index.tsx ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/confirmation/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/confirmation/index!./src/pages/assistant/confirmation/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white","disableScroll":true};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/assistant/confirmation/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_assistant_confirmation_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/assistant/confirmation/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map