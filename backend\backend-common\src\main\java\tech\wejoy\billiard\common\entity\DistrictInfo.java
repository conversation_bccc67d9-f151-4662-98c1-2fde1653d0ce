package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Transient;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.DistrictLevelEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class DistrictInfo extends BaseEntity {

    @Column(columnDefinition = "varchar(9)")
    private String code;

    private String name;

    private Double latitude;

    private Double longitude;

    private DistrictLevelEnum level;

    private Long parentId;

    @Transient
    private List<DistrictInfo> children;

}
