package tech.wejoy.billiard.api.service;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Synchronization;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.api.queue.ClubDeviceSend;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.entity.ClientUserCoupon;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.entity.TenantCoupon;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.OrderManager;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApplicationScoped
@Slf4j
@RequiredArgsConstructor
public class CouponApiService {

    private final ClubDeviceSend clubDeviceSend;

    private final TransactionManager transactionManager;

    private final ClientUserManager clientUserManager;

    private final OrderManager orderManager;

    private final ClubManager clubManager;

    public void issueCoupon(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        log.info("issueCoupon userId: {}, payerTotal: {}, payInfo: {}", userId, payerTotal, payInfo);
        TenantCoupon coupon = JsonUtils.toObject(payInfo, TenantCoupon.class);
        ClientUserCoupon userCoupon = clientUserManager.saveUserCoupon(coupon, userId, bill.getBillNo(), false);
        if (StringUtils.isNotBlank(bill.getOrderNo())) {
            clientUserManager.useCoupon(userCoupon.getId());
            OrderInfo order = orderManager.fetchOrderByNo(bill.getOrderNo());
            order.setStatus(OrderStatusEnum.PAID);
            order.setPayAmount(payerTotal);
            order.setPayTime(LocalDateTime.now());
            order.setDescription(JsonUtils.toJson(userCoupon));
            orderManager.save(order);
            clubManager.updateTableUsing(order.getTableId(), userId, order.getEndTime());
            if (LocalDateTime.now().plusSeconds(30).isAfter(order.getStartTime())) {
                try {
                    Transaction transaction = transactionManager.getTransaction();
                    transaction.registerSynchronization(new Synchronization() {
                        @Override
                        public void beforeCompletion() {
                        }

                        @Override
                        public void afterCompletion(int status) {
                            clubDeviceSend.send(order.getOrderNo(), DeviceOperationTypeEnum.START);
                        }
                    });
                } catch (Exception e) {
                    log.error("send start device error", e);
                }
            }
        }
    }


}
