{"projectname": "billiard-user", "setting": {"compileHotReLoad": false, "urlCheck": false, "autoAudits": false}, "description": "项目私有配置文件。此文件中的内容将覆盖 project.config.json 中的相同字段。项目的改动优先同步到此文件中。详见文档：https://developers.weixin.qq.com/miniprogram/dev/devtools/projectconfig.html", "condition": {"miniprogram": {"list": [{"name": "pages/confirmation/index", "pathName": "pages/confirmation/index", "query": "scene=1011&id=1", "launchMode": "default", "scene": null}, {"name": "pages/confirmation/index", "pathName": "pages/confirmation/index", "query": "scene=1011&id=2", "launchMode": "default", "scene": null}, {"name": "pages/competition/score/index", "pathName": "pages/competition/score/index", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "pages/confirmation/index", "pathName": "pages/confirmation/index", "query": "id=1&scene=1011", "launchMode": "default", "scene": null}, {"name": "pages/coupons/index", "pathName": "pages/coupons/index", "query": "", "launchMode": "default", "scene": null}, {"name": "pages/order/index", "pathName": "pages/order/index", "query": "", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pages/confirmation/index", "query": "scene=1011&id=1", "launchMode": "default", "scene": null}, {"name": "", "pathName": "pages/confirmation/index", "query": "id=1", "launchMode": "default", "scene": null}]}}, "libVersion": "3.8.1"}