package tech.wejoy.billiard.common.third.meituan.client;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.HttpUtils;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import jakarta.enterprise.inject.spi.CDI;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.entity.MeituanAccount;
import tech.wejoy.billiard.common.manager.MeituanTicketManager;
import tech.wejoy.billiard.common.third.meituan.request.*;
import tech.wejoy.billiard.common.third.meituan.response.*;
import tech.wejoy.billiard.common.third.meituan.utils.SignUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
public class MeituanClient {

    private final String appKey;

    private final String appSecret;

    private final String baseUrl = "https://api-open-cater.meituan.com";

    private final ObjectMapper objectMapper = new ObjectMapper();

    public MeituanClient(String appKey, String appSecret) {
        this.appKey = appKey;
        this.appSecret = appSecret;
        JsonUtils.configMapper(objectMapper);
    }

    String buildQueryString(Map<String, String> params) {
        if (params == null) {
            params = Maps.newHashMap();
        }
        return params.entrySet().stream().filter(entry -> entry.getValue() != null && !entry.getValue().isEmpty())
                .map(entry -> entry.getKey() + "=" + URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8)).collect(Collectors.joining("&"));
    }

    public String post(String url, MeituanRequest request, MeituanAccount account) {
        Map<String, String> params = buildParams(request, account);
        String body = buildQueryString(params);
        HttpUtils.HttpResult response = HttpUtils.post(baseUrl + url).body(body).header("Content-Type", "application/x-www-form-urlencoded").send();
        return response.body();
    }

    private synchronized String getAccessToken(MeituanAccount account) {
        if (account.getExpireAt() == null || account.getExpireAt().isBefore(LocalDateTime.now())) {
            TokenResponse token = refreshToken(account.getRefreshToken());
            account.setAccessToken(token.getAccessToken());
            account.setRefreshToken(token.getRefreshToken());
            account.setRemainRefreshCount(account.getRemainRefreshCount() - 1);
            account.setExpiresIn(token.getExpiresIn() + "");
            account.setExpireAt(LocalDateTime.now().plusSeconds(token.getExpiresIn() - 60));
            MeituanTicketManager ticketManager = CDI.current().select(MeituanTicketManager.class).get();
            ticketManager.saveMeituanAccount(account);
        }
        return account.getAccessToken();
    }

    private TokenResponse refreshToken(String refreshToken) {
        Map<String, String> params = Maps.newLinkedHashMap();
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("scope", "all");
        params.put("refreshToken", refreshToken);
        params.put("grantType", "refresh_token");
        params.put("developerId", appKey);
        params.put("charset", "UTF-8");
        params.put("businessId", "58");

        // 计算签名
        String sign = SignUtils.getSign(appSecret, params);
        params.put("sign", sign);

        String body = buildQueryString(params);
        HttpUtils.HttpResult response = HttpUtils.post(baseUrl + "/oauth/refresh").body(body).header("Content-Type", "application/x-www-form-urlencoded").send();
        try {
            TokenResponse result = objectMapper.readValue(response.body(), TokenResponse.class);
            if (result.getCode() == 0) { // 根据文档，成功的code是0
                return result;
            } else {
                log.error("refreshToken error:{}", response.body());
                throw new BaseException(result.getMsg());
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("refreshToken error: {}", response.body(), e);
        }
        throw new BaseException("刷新token失败");
    }

    /**
     * 使用authorization_code换取access_token
     *
     * @param code       授权码
     * @param businessId
     * @param state
     * @return TokenResponse 包含accessToken、refreshToken等信息
     */
    public TokenResponse getOAuthToken(String code, String businessId, String state) {
        Map<String, String> params = Maps.newLinkedHashMap();
        params.put("businessId", businessId); // 根据业务ID，需要根据实际情况设置
        params.put("charset", "UTF-8");
        params.put("code", code);
        params.put("state", state);
        params.put("developerId", appKey);
        params.put("grantType", "authorization_code");
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));

        // 计算签名
        String sign = SignUtils.getSign(appSecret, params);
        params.put("sign", sign);

        String body = buildQueryString(params);
        HttpUtils.HttpResult response = HttpUtils.post(baseUrl + "/oauth/token").body(body).header("Content-Type", "application/x-www-form-urlencoded").send();
        try {
            TokenResponse result = objectMapper.readValue(response.body(), TokenResponse.class);
            if (result.getCode() == 0) { // 根据文档，成功的code是0
                return result;
            } else {
                log.error("getOAuthToken error:{}", response.body());
                throw new BaseException(result.getMsg());
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("getOAuthToken error: {}", response.body(), e);
        }
        throw new BaseException("获取access_token失败");
    }

    private Map<String, String> buildParams(MeituanRequest request, MeituanAccount account) {
        Map<String, String> params = new HashMap<>();
        params.put("developerId", appKey);
        params.put("version", "2");
        params.put("businessId", account.getTokenType());
        params.put("charset", "UTF-8");
        params.put("appAuthToken", getAccessToken(account));
        params.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
        params.put("biz", JsonUtils.toJson(request));
        params.put("sign", SignUtils.getSign(appSecret, params));
        return params;
    }

    public List<String> queryTicketByPhone(MeituanAccount account, QueryByPhoneRequest request) {
        String response = post("/ddzh/tuangou/receipt/querybymobile", request, account);
        try {
            DataResponse<TicketListResponse> result = objectMapper.readValue(response, new TypeReference<>() {
            });
            if (Objects.equals(result.getCode(), "OP_SUCCESS")) {
                return result.getData().getResult().stream().map(Ticket::getSerialNumber).collect(Collectors.toList());
            } else if (Objects.equals(result.getCode(), "1051")) {
                return Lists.newArrayList();
            } else if (Objects.equals(result.getCode(), "1050")) {
                return Lists.newArrayList();
            } else {
                log.error("queryShop error:{}", response);
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("queryTicketByPhone error", e);
        }
        throw new BaseException("查询美团券失败");
    }

    public List<Deal> queryShopDeal(MeituanAccount account, QueryShopTicketRequest request) {
        String response = post("/ddzh/tuangou/deal/queryshopdeal", request, account);
        try {
            DataResponse<DealListResponse> result = objectMapper.readValue(response, new TypeReference<>() {
            });
            if (Objects.equals(result.getCode(), "OP_SUCCESS")) {
                return result.getData().getResult();
            } else {
                log.error("queryShopDeal error:{}", response);
                throw new BaseException(result.getMsg());
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("queryShopDeal error: {}", response, e);
        }
        throw new BaseException("查询美团券失败");
    }

    public ReceiptValidateResponse receiptPrepare(MeituanAccount account, ReceiptPrepareRequest request) {
        String response = post("/ddzh/tuangou/receipt/prepare", request, account);
        try {
            DataResponse<ReceiptValidateResponse> result = objectMapper.readValue(response, new TypeReference<>() {
            });
            if (Objects.equals(result.getCode(), "OP_SUCCESS")) {
                return result.getData();
            } else if (Objects.equals(result.getCode(), "1006")) {
                throw new BaseException("NotExist", result.getMsg());
            } else if (Objects.equals(result.getCode(), "1011")) {
                throw new BaseException("Expired", result.getMsg());
            } else if (Objects.equals(result.getCode(), "1008")) {
                throw new BaseException("Used", result.getMsg());
            } else {
                log.error("receiptPrepare error:{}", response);
                throw new BaseException(result.getMsg());
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("receiptPrepare error: {}", response, e);
        }
        throw new BaseException("查询美团券失败");
    }

    public ReceiptConsumeResponse receiptConsume(MeituanAccount account, ReceiptConsumeRequest request) {
        String response = post("/ddzh/tuangou/receipt/consume", request, account);
        try {
            DataResponse<ReceiptListResponse> result = objectMapper.readValue(response, new TypeReference<>() {
            });
            if (Objects.equals(result.getCode(), "OP_SUCCESS")) {
                List<ReceiptConsumeResponse> data = result.getData().getResult();
                data.getFirst().setSourceInfo(response);
                return data.getFirst();
            } else {
                log.error("receiptPrepare error:{}", response);
                throw new BaseException(result.getMsg());
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("receiptPrepare error: {}", response, e);
        }
        throw new BaseException("查询美团券失败");
    }

    public Object reverseConsume(MeituanAccount account, ReverseConsumeRequest request) {
        String response = post("/ddzh/tuangou/receipt/reverseconsume", request, account);
        try {
            DataResponse<Object> result = objectMapper.readValue(response, new TypeReference<>() {
            });
            if (Objects.equals(result.getCode(), "OP_SUCCESS")) {
                return result.getData();
            } else {
                log.error("reverseConsume error:{}", response);
                throw new BaseException(result.getMsg());
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("receiptPrepare error: {}", response, e);
        }
        throw new BaseException("查询美团券失败");
    }

    /**
     * 查询店铺列表 - 使用新版API
     * 文档：https://developer.meituan.com/docs/api/ddzhkh-auth-token-pageQueryPoiList
     */
    public List<Shop> queryShopScope(MeituanAccount account) {
        QueryShopRequest request = new QueryShopRequest();
        // 使用新的API endpoint
        String response = post("/ddzhkh/auth/token/pageQueryPoiList", request, account);
        try {
            DataResponse<ShopPageResponse> result = objectMapper.readValue(response, new TypeReference<>() {
            });

            if (Objects.equals(result.getCode(), "OP_SUCCESS") && result.getData() != null && result.getData().getPoiInfoList() != null) {
                // 转换为Shop列表返回
                return result.getData().getPoiInfoList().stream()
                        .map(poi -> {
                            Shop shop = new Shop();
                            shop.setOpenShopUuid(poi.getOpPoiId());
                            shop.setShopname(poi.getName());
                            shop.setShopaddress(poi.getAddress());
                            return shop;
                        })
                        .collect(Collectors.toList());
            } else {
                log.error("queryShop error:{}", response);
                throw new BaseException(result.getMsg());
            }
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("queryShop error: {}", response, e);
        }
        throw new BaseException("查询美团店铺失败");
    }

}
