package tech.wejoy.billiard.common.third;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.congeer.core.exception.BaseException;
import com.congeer.utils.JsonUtils;
import com.github.binarywang.wxpay.bean.request.WxPayPartnerUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayUnifiedOrderV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.google.common.collect.Maps;
import io.quarkus.runtime.Startup;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.PayParamBo;
import tech.wejoy.billiard.common.bo.WechatPayParamBo;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.manager.ConfigManager;
import tech.wejoy.billiard.common.third.aqara.client.AqaraClient;
import tech.wejoy.billiard.common.third.douyin.client.DouyinClient;
import tech.wejoy.billiard.common.third.meituan.client.MeituanClient;
import tech.wejoy.billiard.common.third.smyoo.client.SmyooClient;
import tech.wejoy.billiard.common.third.wejoy.client.WejoyClient;

import java.util.Map;

@Slf4j
@ApplicationScoped
public class ThirdServiceHolder {

    private static final Map<String, WxMaService> wxMaServiceCache = Maps.newConcurrentMap();

    private static final Map<String, AqaraClient> aqaraClientCache = Maps.newConcurrentMap();

    private static final Map<String, SmyooClient> smyooClientCache = Maps.newConcurrentMap();

    private static final Map<String, WxPayService> wxPayServiceCache = Maps.newConcurrentMap();

    private static final Map<String, OSSClient> ossClientCache = Maps.newConcurrentMap();

    private static final Map<String, AliyunOss> ossConfigCache = Maps.newConcurrentMap();

    private static final Map<String, MeituanClient> meituanClientCache = Maps.newConcurrentMap();

    private static final Map<String, DouyinClient> douyinClientCache = Maps.newConcurrentMap();

    private static final Map<String, WejoyClient> wejoyClientCache = Maps.newConcurrentMap();

    private static ConfigManager configManager;

    public static synchronized WejoyClient wejoyClient(String env) {
        if (wejoyClientCache.get(env) == null) {
            WejoyClient wejoyClient = new WejoyClient();
            wejoyClientCache.put(env, wejoyClient);
        }
        return wejoyClientCache.get(env);
    }

    public static SmyooClient smyooClient(String env) {
        if (smyooClientCache.get(env) == null) {
            SmyooConfig config = configManager.getSmyoo(env);
            SmyooClient client = new SmyooClient(config.getClientId(), config.getClientSecret(), config.getDeviceId());
            smyooClientCache.put(env, client);
        }
        return smyooClientCache.get(env);
    }

    @Startup
    void init() {
        log.info("ThirdServiceHolder init");
    }

    public ThirdServiceHolder(ConfigManager configManager) {
        ThirdServiceHolder.configManager = configManager;
    }

    public static synchronized DouyinClient douyinClient(String env) {
        if (douyinClientCache.get(env) == null) {
            DouyinConfig douyin = configManager.getDouyin(env);
            DouyinClient douyinClient = new DouyinClient(douyin.getAppKey(), douyin.getAppSecret());
            douyinClientCache.put(env, douyinClient);
        }
        return douyinClientCache.get(env);
    }

    public static synchronized MeituanClient meituanClient(String env) {
        if (meituanClientCache.get(env) == null) {
            MeituanConfig meituan = configManager.getMeituan(env);
            MeituanClient meituanClient = new MeituanClient(meituan.getAppKey(), meituan.getAppSecret());
            meituanClientCache.put(env, meituanClient);
        }
        return meituanClientCache.get(env);
    }

    public static synchronized WxMaService wxMaService(String env) {
        if (wxMaServiceCache.get(env) == null) {
            WxMiniapp wxMiniapp = configManager.getWxMiniapp(env);
            if (wxMiniapp == null) {
                throw new BaseException("未找到相关环境小程序配置");
            }
            WxMaService wxMaService = new WxMaServiceImpl();
            WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
            config.setAppid(wxMiniapp.getAppId());
            config.setSecret(wxMiniapp.getAppSecret());
            wxMaService.setWxMaConfig(config);
            wxMaServiceCache.put(env, wxMaService);
        }
        return wxMaServiceCache.get(env);
    }

    public static synchronized AqaraClient aqaraClient(String env) {
        if (aqaraClientCache.get(env) == null) {
            AqaraConfig aqara = configManager.getAqara(env);
            AqaraClient aqaraClient = new AqaraClient(aqara.getAppId(), aqara.getKeyId(), aqara.getAppKey());
            aqaraClientCache.put(env, aqaraClient);
        }
        return aqaraClientCache.get(env);
    }

    public static synchronized WxPayService wxPayService(String env) {
        if (wxPayServiceCache.get(env) == null) {
            WxMiniapp wxMiniapp = configManager.getWxMiniapp(env);
            if (wxMiniapp == null) {
                throw new BaseException("未找到相关环境小程序配置");
            }
            WxPayConfig wxPayConfig = new WxPayConfig();
            if (wxMiniapp.getPartnerMchId() != null) {
                wxPayConfig.setMchId(wxMiniapp.getPartnerMchId());
                wxPayConfig.setAppId(wxMiniapp.getPartnerAppId());
                wxPayConfig.setSubMchId(wxMiniapp.getMchId());
                wxPayConfig.setSubAppId(wxMiniapp.getAppId());
            } else {
                wxPayConfig.setMchId(wxMiniapp.getMchId());
                wxPayConfig.setAppId(wxMiniapp.getAppId());
            }
            wxPayConfig.setApiV3Key(wxMiniapp.getMchKey());
            wxPayConfig.setPrivateKeyContent(wxMiniapp.getKeyData());
            wxPayConfig.setPrivateCertContent(wxMiniapp.getCertData());
            wxPayConfig.setNotifyUrl(wxMiniapp.getNotifyUrl());
            WxPayService wxPayService = new WxPayServiceImpl();
            wxPayService.setConfig(wxPayConfig);
            wxPayServiceCache.put(env, wxPayService);
        }
        return wxPayServiceCache.get(env);
    }

    public static OSSClient ossClient() {
        cacheAliyunOss();
        return ossClientCache.get("aliyun");
    }

    public static AliyunOss ossConfig() {
        cacheAliyunOss();
        return ossConfigCache.get("aliyun");
    }

    private static synchronized void cacheAliyunOss() {
        if (ossConfigCache.get("aliyun") == null || ossClientCache.get("aliyun") == null) {
            AliyunOss aliyunOss = configManager.getAliyunOss();
            if (aliyunOss == null) {
                throw new BaseException("未找到阿里云OSS配置");
            }
            OSSClient ossClient = new OSSClient(aliyunOss.getEndpoint(), new DefaultCredentialProvider(aliyunOss.getAccessKey(), aliyunOss.getSecret()), null);
            ossClientCache.put("aliyun", ossClient);
            ossConfigCache.put("aliyun", aliyunOss);
        }
    }

    /**
     * @deprecated Use PaymentService.createPayment instead
     */
    @Deprecated
    public static PayParamBo wechatPay(WxPayUnifiedOrderV3Request request) {
        try {
            WxPayService payService = wxPayService("user");
            WxPayUnifiedOrderV3Result.JsapiResult order;
            if (StringUtils.isNotBlank(payService.getConfig().getSubMchId())) {
                WxPayPartnerUnifiedOrderV3Request partnerRequest = new WxPayPartnerUnifiedOrderV3Request();
                partnerRequest.setAmount(new WxPayPartnerUnifiedOrderV3Request.Amount().setTotal(request.getAmount().getTotal()));
                partnerRequest.setDescription(request.getDescription());
                partnerRequest.setPayer(new WxPayPartnerUnifiedOrderV3Request.Payer().setSubOpenid(request.getPayer().getOpenid()));
                partnerRequest.setOutTradeNo(request.getOutTradeNo());
                partnerRequest.setTimeExpire(request.getTimeExpire());
                order = payService.createPartnerOrderV3(TradeTypeEnum.JSAPI, partnerRequest);
            } else {
                order = payService.createOrderV3(TradeTypeEnum.JSAPI, request);
            }
            log.info("微信支付下单成功:{}", JsonUtils.toJson(order));
            WechatPayParamBo payParam = new WechatPayParamBo();
            payParam.setTimeStamp(order.getTimeStamp());
            payParam.setPackageStr(order.getPackageValue());
            payParam.setNonceStr(order.getNonceStr());
            payParam.setSignType(order.getSignType());
            payParam.setPaySign(order.getPaySign());
            return payParam;
        } catch (WxPayException e) {
            log.error("微信支付下单失败", e);
            throw new BaseException("微信支付下单失败");
        }
    }

    /**
     * @deprecated Use PaymentService.refund instead
     */
    @Deprecated
    public static WxPayRefundV3Result wechatRefund(WxPayRefundV3Request request) {
        try {
            WxPayService payService = wxPayService("user");
            WxPayRefundV3Result wxPayRefundV3Result = payService.refundV3(request);
            log.info("微信退款成功:{}", JsonUtils.toJson(wxPayRefundV3Result));
            return wxPayRefundV3Result;
        } catch (WxPayException e) {
            log.error("微信退款失败", e);
            throw new BaseException("微信退款失败");
        }
    }

}
