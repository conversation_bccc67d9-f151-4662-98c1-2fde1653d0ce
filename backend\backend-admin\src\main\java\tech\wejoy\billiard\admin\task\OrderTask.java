package tech.wejoy.billiard.admin.task;

import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.admin.task.order.OrderDelayQueue;
import tech.wejoy.billiard.admin.task.order.bo.OrderDelayMessage;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.manager.OrderManager;
import tech.wejoy.billiard.common.service.OrderService;

import java.time.LocalDateTime;
import java.util.List;

@ApplicationScoped
@RequiredArgsConstructor
public class OrderTask {

    private final OrderService orderService;

    private final OrderManager orderManager;

    private final OrderDelayQueue orderDelayQueue;

    @Scheduled(every = "3m")
    void startOrder() {
        List<OrderInfo> orders = orderManager.fetchOrderByStatus(OrderStatusEnum.PAID);
        for (OrderInfo order : orders) {
            if (LocalDateTime.now().plusMinutes(3).isAfter(order.getStartTime())) {
                orderDelayQueue.offer(new OrderDelayMessage(order.getOrderNo(), DeviceOperationTypeEnum.START, order.getStartTime()));
            }
        }
    }

    @Scheduled(every = "3m")
    void finishOrder() {
        List<OrderInfo> orders = orderManager.fetchOrderByStatus(OrderStatusEnum.USING);
        for (OrderInfo order : orders) {
            if (LocalDateTime.now().plusMinutes(3).isAfter(order.getEndTime())) {
                orderDelayQueue.offer(new OrderDelayMessage(order.getOrderNo(), DeviceOperationTypeEnum.STOP, order.getEndTime()));
            }
        }
    }

    @Scheduled(every = "5m")
    void warnOrder() {
        List<OrderInfo> orders = orderManager.fetchOrderByStatus(OrderStatusEnum.USING);
        for (OrderInfo order : orders) {
            if (LocalDateTime.now().plusMinutes(10).isAfter(order.getEndTime()) && LocalDateTime.now().plusMinutes(5).isBefore(order.getEndTime())) {
                orderDelayQueue.offer(new OrderDelayMessage(order.getOrderNo(), DeviceOperationTypeEnum.WARN, order.getEndTime().minusMinutes(5)));
            }
        }
    }


    @Scheduled(every = "1m")
    void cancelOrder() {
        List<OrderInfo> orders = orderManager.fetchOrderByStatus(OrderStatusEnum.PENDING);
        for (OrderInfo order : orders) {
            if (order.getCreateAt().plusMinutes(5).isBefore(LocalDateTime.now())) {
                orderService.cancel(order.getOrderNo());
            }
        }
    }


}
