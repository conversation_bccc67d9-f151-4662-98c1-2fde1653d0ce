package tech.wejoy.billiard.api.api;

import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.common.bo.DistrictBo;
import tech.wejoy.billiard.common.service.DistrictService;

@Path("/district")
@Tag(name = "地区")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class DistrictApi {

    private final DistrictService districtService;

    @GET
    @Path("/tree")
    @Operation(summary = "全部地区")
    public DistrictBo list() {
        return districtService.getTree();
    }


    @GET
    @Path("/regeo")
    @Operation(summary = "逆地理编码")
    public DistrictBo regeo(@QueryParam("lat") Float latitude, @QueryParam("lng") Float longitude) {
        try {
            return districtService.regeo(new float[]{longitude, latitude});
        } catch (Exception e) {
            return null;
        }
    }

}
