package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

@Data
public class SignUpDto {

    @Schema(description = "比赛id")
    private Long competitionId;

    @Schema(description = "真实姓名")
    private String realName;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "城市编码")
    private String city;

    private Long userId;

}
