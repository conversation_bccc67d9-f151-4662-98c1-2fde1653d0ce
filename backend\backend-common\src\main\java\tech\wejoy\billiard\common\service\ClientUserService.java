package tech.wejoy.billiard.common.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import com.google.common.collect.Lists;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.ClientUserQueryDto;
import tech.wejoy.billiard.common.dto.CouponBuyDto;
import tech.wejoy.billiard.common.dto.RechargeDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.enums.BillTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.ThirdPayTypeEnum;
import tech.wejoy.billiard.common.manager.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class ClientUserService {

    private final ClientUserManager clientUserManager;

    private final MemberManager memberManager;

    private final BillManager billManager;

    private final OrderManager orderManager;

    private final CouponManager couponManager;

    private final ClubManager clubManager;

    private final PaymentService paymentService;

    public ClientUser createUser(ClientUser user) {
        user.setStatus(IsEnum.TRUE);
        clientUserManager.save(user);
        ClientUserMember userMember = new ClientUserMember();
        userMember.setUserId(user.getId());
        userMember.setBalance(BigDecimal.ZERO);
        userMember.setPlanId(0L);
        userMember.setMemberLevel(0);
        userMember.setCoins(0);
        userMember.setPoints(0);
        memberManager.createMember(userMember);
        return user;
    }

    public ClientUser getUserByOpenId(String openId) {
        return clientUserManager.fetchUserByOpenId(openId);
    }

    public ClientUser save(ClientUser user) {
        return clientUserManager.save(user);
    }

    public ClientUserMember getMemberByUserId(Long userId) {
        return memberManager.getMemberByUserId(userId);
    }

    public WalletBo getWalletByUserId(Long userId) {
        ClientUserMember member = getMemberByUserId(userId);
        WalletBo walletBo = new WalletBo();
        walletBo.setMemberBalance(member.getBalance());
        List<ClientUserTenantMember> clubMembers = memberManager.getClubMembersByUserId(userId);
        BigDecimal clubTotalBalance = clubMembers.stream().map(ClientUserTenantMember::getBalance).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal clubBonus = clubMembers.stream().map(ClientUserTenantMember::getBonus).reduce(BigDecimal.ZERO, BigDecimal::add);
        walletBo.setClubTotalBalance(clubTotalBalance.add(clubBonus));
        walletBo.setMemberLevel(member.getMemberLevel());
        walletBo.setCouponCount(clientUserManager.fetchCouponCountByUserId(userId));
        return walletBo;
    }

    public List<ClubWalletBo> getClubMembersByUserId(Long userId) {
        List<ClientUserTenantMember> clubMembers = memberManager.getClubMembersByUserId(userId);
        List<Long> planIds = clubMembers.stream().map(ClientUserTenantMember::getPlanId).toList();
        List<TenantClubPlanRel> clubs = memberManager.getClubsByPlanIds(planIds);
        Map<Long, ClubWalletBo> clubWalletBoMap = new HashMap<>();
        clubs.stream().map(TenantClubPlanRel::getClubId).distinct().forEach(clubId -> {
            ClubWalletBo clubWalletBo = new ClubWalletBo();
            clubWalletBo.setClubId(clubId);
            clubWalletBo.setBalance(BigDecimal.ZERO);
            clubWalletBoMap.put(clubId, clubWalletBo);
        });
        Map<Long, List<TenantClubPlanRel>> planGroup = clubs.stream().collect(Collectors.groupingBy(TenantClubPlanRel::getPlanId));

        for (ClientUserTenantMember clubMember : clubMembers) {
            List<TenantClubPlanRel> key = planGroup.get(clubMember.getPlanId());
            for (TenantClubPlanRel tenantClubPlanRel : key) {
                ClubWalletBo clubWalletBo = clubWalletBoMap.get(tenantClubPlanRel.getClubId());
                clubWalletBo.setBalance(clubWalletBo.getBalance().add(clubMember.getBalance()).add(clubMember.getBonus()));
            }
        }

        return Lists.newArrayList(clubWalletBoMap.values());
    }

    public List<ClientUserTenantMember> getClubMembersByClubIdAndUserId(Long clubId, Long userId) {
        return memberManager.getClubMembersByClubIdAndUserId(clubId, userId);
    }

    public void updateClubMember(ClientUserTenantMember clubMember) {
        memberManager.saveClubMember(clubMember);
    }

    public void updateClubMemberList(List<ClientUserTenantMember> clubMembers) {
        for (ClientUserTenantMember clubMember : clubMembers) {
            memberManager.saveClubMember(clubMember);
        }
    }

    public void updateMember(ClientUserMember member) {
        memberManager.saveMember(member);
    }

    @Transactional(rollbackOn = Exception.class)
    public PayParamBo prePlanMember(RechargeDto dto) {
        MemberPlan plan = memberManager.getMemberPlanById(dto.getPlanId());
        if (plan == null) {
            throw new RuntimeException("会员套餐不存在");
        }
        Long userId = dto.getUserId();
        ClientUser user = clientUserManager.fetchUserById(userId);
        ClientUserMember member = getMemberByUserId(userId);
        BillInfo bill = new BillInfo();
        bill.setUserId(userId);
        bill.setTenantId(0L);
        bill.setClubId(0L);
        bill.setType(BillTypeEnum.MEMBER_PLAN);
        if (member.getMemberLevel() == 0) {
            bill.setAmount(plan.getInitialPrice());
            plan.setInit(true);
        } else if (member.getMemberLevel().equals(plan.getLevel())) {
            bill.setAmount(plan.getRenewalPrice());
            plan.setInit(false);
        }
        bill.setPayInfo(JsonUtils.toJson(plan));
        bill.setThirdPayType(ThirdPayTypeEnum.WECHAT);
        billManager.createBill(bill);

        return paymentService.createPayment(bill.getBillNo(), bill.getAmount(), userId, "会员套餐");
    }

    @Transactional(rollbackOn = Exception.class)
    public PayParamBo preRechargeMember(RechargeDto dto) {
        Long userId = dto.getUserId();
        ClientUser user = clientUserManager.fetchUserById(userId);
        ClientUserMember member = getMemberByUserId(userId);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        if (member.getMemberLevel() == 0) {
            throw new RuntimeException("请先选择会员套餐");
        }
        BillInfo bill = new BillInfo();
        bill.setUserId(userId);
        bill.setTenantId(0L);
        bill.setClubId(0L);
        bill.setType(BillTypeEnum.MEMBER_RECHARGE);
        bill.setAmount(dto.getAmount());
        bill.setPayInfo(dto.getAmount().toString());
        bill.setThirdPayType(ThirdPayTypeEnum.WECHAT);
        billManager.createBill(bill);

        return paymentService.createPayment(bill.getBillNo(), bill.getAmount(), userId, "会员充值");
    }

    @Transactional(rollbackOn = Exception.class)
    public PayParamBo preRechargeClub(RechargeDto dto, Long userId) {
        TenantPlan plan = memberManager.getTenantPlanById(dto.getPlanId());
        if (plan == null) {
            throw new RuntimeException("门店套餐不存在");
        }
        BillInfo bill = new BillInfo();
        bill.setUserId(userId);
        bill.setClubId(dto.getClubId());
        bill.setTenantId(plan.getTenantId());
        bill.setType(BillTypeEnum.TENANT_PLAN);
        bill.setAmount(plan.getPayAmount());
        bill.setPayInfo(JsonUtils.toJson(plan));
        bill.setThirdPayType(ThirdPayTypeEnum.WECHAT);
        billManager.createBill(bill);

        return paymentService.createPayment(bill.getBillNo(), bill.getAmount(), userId, "门店充值");
    }

    public void rechargeClub(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        log.info("rechargeClub userId: {}, payerTotal: {}, payInfo: {}", userId, payerTotal, payInfo);
        TenantPlan plan = JsonUtils.toObject(payInfo, TenantPlan.class);

        ClientUserTenantMember clubMember = memberManager.getClubMemberByUserIdAndPlanId(userId, plan.getId());
        if (clubMember == null) {
            clubMember = new ClientUserTenantMember();
            clubMember.setUserId(userId);
            clubMember.setTenantId(plan.getTenantId());
            clubMember.setPlanId(plan.getId());
            clubMember.setBalance(BigDecimal.ZERO);
            clubMember.setBonus(BigDecimal.ZERO);
            clubMember.setTotalAmount(BigDecimal.ZERO);
            clubMember.setSeq(plan.getPaySeq());
        }
        clubMember.setBalance(clubMember.getBalance().add(plan.getPayAmount()));
        clubMember.setBonus(clubMember.getBonus().add(plan.getTotalAmount().subtract(plan.getPayAmount())));
        clubMember.setTotalAmount(clubMember.getTotalAmount().add(plan.getPayAmount()));
        memberManager.saveClubMember(clubMember);
    }

    public void planMember(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        log.info("planMember userId: {}, payerTotal: {}, payInfo: {}", userId, payerTotal, payInfo);
        MemberPlan plan = JsonUtils.toObject(payInfo, MemberPlan.class);
        ClientUserMember member = memberManager.getMemberByUserId(userId);
        member.setPlanId(plan.getId());
        if (member.getMemberLevel().equals(plan.getLevel())) {
            if (member.getExpireTime() == null || member.getExpireTime().isBefore(LocalDateTime.now())) {
                member.setExpireTime(LocalDateTime.now().plusDays(plan.getDuration()));
            } else {
                member.setExpireTime(member.getExpireTime().plusDays(plan.getDuration()));
            }
        } else {
            member.setMemberLevel(plan.getLevel());
            member.setExpireTime(LocalDateTime.now().plusDays(plan.getDuration()));
        }
        member.setBalance(member.getBalance().add(payerTotal));
        member.setTotalAmount(member.getTotalAmount().add(payerTotal));
        memberManager.saveMember(member);
    }

    public void rechargeMember(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        log.info("rechargeMember userId: {}, payerTotal: {}, payInfo: {}", userId, payerTotal, payInfo);
        ClientUserMember member = memberManager.getMemberByUserId(userId);
        member.setBalance(member.getBalance().add(payerTotal));
        member.setTotalAmount(member.getTotalAmount().add(payerTotal));
        memberManager.saveMember(member);
    }

    public String getUserPhone(Long userId) {
        ClientUser user = clientUserManager.fetchUserById(userId);
        return user.getPhone();
    }

    public void updateBirth(Long id, LocalDate birth) {
        ClientUser user = clientUserManager.fetchUserById(id);
        user.setBirth(birth);
        clientUserManager.save(user);
    }

    public ClientUserTenantMember getClubMemberById(Long id) {
        return memberManager.getClubMemberById(id);
    }

    public Page<ClientUserBo> adminList(ClientUserQueryDto dto) {
        if ((dto.getTenantId() != null && dto.getTenantId() != 0) || (dto.getClubId() != null && dto.getClubId() != 0)) {
            List<Long> userIds = orderManager.fetchUserIdsByTenantIdAndClubId(dto.getTenantId(), dto.getClubId());
            List<Long> userIds2 = billManager.fetchUserIdsByTenantIdAndClubId(dto.getTenantId(), dto.getClubId());
            dto.setUserIds(Lists.newArrayList(userIds, userIds2).stream().flatMap(List::stream).distinct().toList());
        }
        Page<ClientUserBo> ret = clientUserManager.adminPage(dto);
        if (dto.getClubId() != null && dto.getClubId() != 0) {
            List<Long> userIds = ret.getRecords().stream().map(ClientUserBo::getId).toList();
            List<ClubUserOrderCountBo> clubUserOrderCountBos = orderManager.fetchClubOrderCountGroupByUserIds(dto.getTenantId(), dto.getClubId(), userIds);
            Map<Long, Long> countMap = clubUserOrderCountBos.stream().collect(Collectors.toMap(ClubUserOrderCountBo::getUserId, ClubUserOrderCountBo::getCount, Long::sum));
            ret.getRecords().forEach(v -> v.setOrderCount(countMap.getOrDefault(v.getId(), 0L)));
        }
        return ret;
    }

    public PayParamBo buyCoupon(CouponBuyDto dto) {
        Long userId = dto.getUserId();
        TenantCoupon coupon = couponManager.fetchCouponById(dto.getCouponId());
        Integer userTotalLimit = coupon.getUserTotalLimit();
        Integer userDayLimit = coupon.getUserDayLimit();
        if (userTotalLimit != null && userTotalLimit > 0) {
            List<ClientUserCoupon> userCoupons = clientUserManager.fetchCouponByIdAndUserId(dto.getCouponId(), userId);
            if (userCoupons.size() >= userTotalLimit) {
                throw new BaseException("已经超过总购买次数");
            }
        } else if (userDayLimit != null && userDayLimit > 0) {
            List<ClientUserCoupon> userCoupons = clientUserManager.fetchCouponByIdAndUserId(dto.getCouponId(), userId);
            long count = userCoupons.stream().filter(c -> c.getCreateAt().toLocalDate().isEqual(LocalDateTime.now().toLocalDate())).count();
            if (count >= userDayLimit) {
                throw new BaseException("已经超过每日购买次数，请明日再来");
            }
        }
        BillInfo bill = new BillInfo();
        bill.setUserId(userId);
        bill.setTenantId(coupon.getTenantId());
        bill.setClubId(dto.getClubId());
        bill.setType(BillTypeEnum.COUPON);
        bill.setAmount(coupon.getPrice());
        bill.setPayInfo(JsonUtils.toJson(coupon));
        bill.setThirdPayType(ThirdPayTypeEnum.WECHAT);
        billManager.createBill(bill);

        return paymentService.createPayment(bill.getBillNo(), bill.getAmount(), userId, "购买" + coupon.getTitle());
    }

    public ClientUser getUserByPhone(String phoneNumber) {
        return clientUserManager.fetchUserByPhone(phoneNumber);
    }

    @Transactional(rollbackOn = Exception.class)
    public void refundClub(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        log.info("refundClub userId: {}, payerTotal: {}, payInfo: {}", userId, payerTotal, payInfo);
        TenantPlan plan = JsonUtils.toObject(payInfo, TenantPlan.class);
        ClientUserTenantMember clubMember = memberManager.getClubMemberByUserIdAndPlanId(userId, plan.getId());
        BigDecimal bonus = plan.getTotalAmount().subtract(plan.getPayAmount());
        if (clubMember.getBalance().compareTo(plan.getPayAmount()) < 0) {
            throw new RuntimeException("余额已使用");
        }
        clubMember.setBalance(clubMember.getBalance().subtract(plan.getPayAmount()));
        if (clubMember.getBonus().compareTo(bonus) < 0) {
            throw new RuntimeException("赠送金额已使用");
        }
        clubMember.setBonus(clubMember.getBonus().subtract(bonus));
        clubMember.setTotalAmount(clubMember.getTotalAmount().subtract(plan.getPayAmount()));
        memberManager.saveClubMember(clubMember);
        String refundResult = paymentService.refund(bill.getBillNo(), bill.getPayAmount(), bill.getPayAmount(), "退款");
        bill.setRefundInfo(refundResult);
        bill.setRefundTime(LocalDateTime.now());
        bill.setRefundAmount(plan.getPayAmount());
        bill.setStatus(BillStatusEnum.REFUND);
        billManager.save(bill);
    }

    public void refundMember(BillInfo bill) {

    }

    public void refundMemberRecharge(BillInfo bill) {

    }

    public void refundCoupon(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        log.info("refundCoupon userId: {}, payerTotal: {}, payInfo: {}", userId, payerTotal, payInfo);
        ClientUserCoupon userCoupon = clientUserManager.fetchCouponByBillNo(bill.getBillNo());
        if (userCoupon == null) {
            throw new RuntimeException("优惠券不存在");
        }
        if (userCoupon.getUsed().isTrue()) {
            throw new RuntimeException("优惠券已使用");
        }
        if (userCoupon.getExpired().isTrue()) {
            throw new RuntimeException("优惠券已过期");
        }
        if (userCoupon.getRefund().isTrue()) {
            throw new RuntimeException("优惠券已退款");
        }
        clientUserManager.refundCoupon(userCoupon.getId());
        String refundResult = paymentService.refund(bill.getBillNo(), bill.getPayAmount(), bill.getPayAmount(), "退款");
        bill.setRefundInfo(refundResult);
        bill.setRefundTime(LocalDateTime.now());
        bill.setRefundAmount(bill.getPayAmount());
        bill.setStatus(BillStatusEnum.REFUND);
        billManager.save(bill);
    }

    public List<CouponBo> getCouponsByUserId(Long userId, boolean used, boolean expired) {
        List<ClientUserCoupon> userCoupons = clientUserManager.fetchCouponByUserIdAndStatus(userId, IsEnum.of(used), IsEnum.of(expired), IsEnum.FALSE);
        List<Long> couponIds = userCoupons.stream().map(ClientUserCoupon::getCouponId).toList();
        List<TenantCoupon> coupons = couponManager.fetchCouponsByIds(couponIds);
        List<TenantClubCouponRel> clubRelList = couponManager.fetchCouponRelByCouponIds(couponIds);
        List<Long> clubIds = clubRelList.stream().map(TenantClubCouponRel::getClubId).distinct().toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        Map<Long, List<TenantClubCouponRel>> clubGroup = clubRelList.stream().collect(Collectors.groupingBy(TenantClubCouponRel::getCouponId));
        Map<Long, TenantCoupon> couponMap = coupons.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        return userCoupons.stream().map(v -> {
            CouponBo couponBo = new CouponBo();
            couponBo.setId(v.getId());
            TenantCoupon coupon = couponMap.get(v.getCouponId());
            List<TenantClubCouponRel> relList = clubGroup.get(v.getCouponId());
            if (relList != null) {
                List<ClubInfo> clubList = relList.stream().map(TenantClubCouponRel::getClubId).map(clubMap::get).toList();
                couponBo.setClubs(clubList.stream().map(ApiClubBo::optionFrom).toList());
            }
            couponBo.setTitle(coupon.getTitle());
            couponBo.setExpireTime(v.getExpireTime());
            couponBo.setDescription(coupon.getDescription());
            couponBo.setPrice(coupon.getPrice());
            couponBo.setType(coupon.getType());
            couponBo.setMinutes(coupon.getMinutes());
            couponBo.setPeriod(coupon.getPeriod());
            return couponBo;
        }).toList();
    }

    public ClientUserBo detail(Long id) {
        ClientUser user = clientUserManager.fetchUserById(id);
        return BeanUtils.copy(user, ClientUserBo.class);
    }

    public void pointCheck() {

    }

}
