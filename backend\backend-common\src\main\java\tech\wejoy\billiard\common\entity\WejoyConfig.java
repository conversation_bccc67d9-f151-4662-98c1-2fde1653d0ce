package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class WejoyConfig extends BaseEntity {

    private String env;

    @Column(columnDefinition = "TEXT")
    private String privateKey;

    @Column(columnDefinition = "TEXT")
    private String publicKey;

    private String baseUrl;

    private String merchantId;

    private String appId;

}
