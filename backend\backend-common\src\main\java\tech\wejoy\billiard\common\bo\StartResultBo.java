package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.enums.StartResultEnum;

import java.time.LocalDateTime;

@Data
@Schema(description = "开桌结果")
public class StartResultBo {

    private Long tableId;

    private StartResultEnum result;

    private String message;

    private String orderNo;

    private OrderStatusEnum status;

    private Object extra;

    private LocalDateTime startTime;

}
