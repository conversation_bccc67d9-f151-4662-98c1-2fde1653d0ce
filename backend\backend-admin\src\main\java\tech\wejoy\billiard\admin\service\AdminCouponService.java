package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.admin.bo.AdminUserBo;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.AdminCouponQueryDto;
import tech.wejoy.billiard.common.dto.AdminCouponSaveDto;
import tech.wejoy.billiard.common.dto.CouponPresentDto;
import tech.wejoy.billiard.common.dto.DeviceOperationDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.BillTypeEnum;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.manager.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class AdminCouponService {

    private final CouponManager couponManager;

    private final ClientUserManager clientUserManager;

    private final BillManager billManager;

    private final AdminUserManager adminUserManager;

    private final OrderManager orderManager;

    private final ClubManager clubManager;

    @Inject
    DeviceService deviceService;

    @Transactional(rollbackOn = Exception.class)
    public AdminCouponBo create(AdminCouponSaveDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        TenantCoupon coupon = BeanUtils.copy(dto, TenantCoupon.class);
        coupon.setTenantId(user.getTenantId());
        validate(dto);
        coupon.setDelete(IsEnum.FALSE);
        couponManager.saveCoupon(coupon);
        if (CollectionUtils.isNotEmpty(dto.getClubIds())) {
            if (user.getTenantId() == 0) {
                ClubInfo club = clubManager.fetchClubById(dto.getClubIds().getFirst());
                dto.setTenantId(club.getTenantId());
                coupon.setTenantId(club.getTenantId());
                couponManager.saveCoupon(coupon);
            }
            couponManager.saveCouponClub(coupon.getId(), coupon.getTenantId(), dto.getClubIds());
        }
        if (coupon.getTenantId() == 0) {
            throw new BaseException("请选择门店");
        }
        return BeanUtils.copy(dto, AdminCouponBo.class);
    }

    public AdminCouponBo update(AdminCouponSaveDto dto) {
        TenantCoupon db = couponManager.fetchCouponById(dto.getId());
        if (db == null) {
            throw new BaseException("优惠券不存在");
        }
        validate(dto);
        db.setTitle(dto.getTitle());
        db.setDescription(dto.getDescription());
        db.setPeriod(dto.getPeriod());
        db.setMinutes(dto.getMinutes());
        db.setSeq(dto.getSeq());
        db.setType(dto.getType());
        db.setPrice(dto.getPrice());
        db.setUserTotalLimit(dto.getUserTotalLimit());
        db.setUserDayLimit(dto.getUserDayLimit());
        db.setDayLimit(dto.getDayLimit());
        db.setExpireDays(dto.getExpireDays());
        db.setShow(dto.getShow());
        db.setStatus(dto.getStatus());
        couponManager.saveCoupon(db);
        if (CollectionUtils.isNotEmpty(dto.getClubIds())) {
            couponManager.saveCouponClub(db.getId(), db.getTenantId(), dto.getClubIds());
        }
        return BeanUtils.copy(dto, AdminCouponBo.class);
    }

    private static void validate(AdminCouponSaveDto dto) {
        if (dto.getPeriod() != null) {
            try {
                TimeBo period = JsonUtils.toObject(dto.getPeriod(), TimeBo.class);
                if (period == null) {
                    throw new BaseException("有效期格式错误");
                }
                if (period.getStartTime() == null || period.getEndTime() == null) {
                    throw new BaseException("有效期格式错误");
                }
                if (!period.getEndTime().isAfter(period.getStartTime())) {
                    period.setOvernight(true);
                    dto.setPeriod(JsonUtils.toJson(period));
                }
            } catch (BaseException e) {
                throw e;
            } catch (Exception e) {
                throw new BaseException("有效期格式错误");
            }
        }
    }

    public void delete(Long id) {
        couponManager.deleteCoupon(id);
    }


    @Transactional(rollbackOn = Exception.class)
    public void presentCoupon(CouponPresentDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        ClientUser clientUser = clientUserManager.fetchUserByPhone(dto.getPhone());
        if (clientUser == null) {
            throw new BaseException("用户不存在");
        }
        TenantCoupon coupon = couponManager.fetchCouponById(dto.getCouponId());
        if (coupon == null) {
            throw new BaseException("优惠券不存在");
        }
        CouponGiftRecord record = new CouponGiftRecord();
        record.setCouponId(coupon.getId());
        record.setUserId(clientUser.getId());
        record.setRemark(dto.getRemark());
        record.setCount(dto.getCount());
        record.setPhone(dto.getPhone());
        record.setClubId(dto.getClubId());
        record.setTenantId(user.getTenantId());
        record.setOperatorId(user.getId());
        couponManager.saveGiftRecord(record);
        for (int i = 0; i < dto.getCount(); i++) {
            clientUserManager.saveUserCoupon(coupon, clientUser.getId(), record.getId() + "", true);
        }
    }

    public Page<CouponGiftRecordBo> giftList(AdminCouponQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        Page<CouponGiftRecord> list = couponManager.fetchGiftRecordByTenantIdAndClubId(dto);
        List<Long> couponIds = list.getRecords().stream().map(CouponGiftRecord::getCouponId).toList();
        List<TenantCoupon> coupons = couponManager.fetchCouponByIds(couponIds);
        List<Long> operatorIds = list.getRecords().stream().map(CouponGiftRecord::getOperatorId).toList();
        List<AdminUser> adminUsers = adminUserManager.fetchByIds(operatorIds);
        Map<Long, String> operatorMap = adminUsers.stream().collect(Collectors.toMap(BaseEntity::getId, AdminUser::getUsername));
        Map<Long, String> titleMap = coupons.stream().collect(Collectors.toMap(BaseEntity::getId, TenantCoupon::getTitle));
        return list.convert(v -> {
            CouponGiftRecordBo bo = BeanUtils.copy(v, CouponGiftRecordBo.class);
            bo.setTitle(titleMap.get(v.getCouponId()));
            bo.setGiftTime(v.getCreateAt());
            bo.setUserPhone(v.getPhone());
            bo.setOperator(operatorMap.get(v.getOperatorId()));
            return bo;
        });
    }

    public Page<CouponBillBo> billList(AdminCouponQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        Page<BillInfo> page = billManager.fetchPageByByTenantIdAndClubIdAndType(dto, dto.getStartDate(), dto.getEndDate(), dto.getTenantId(), dto.getClubId(), BillTypeEnum.COUPON);
        List<Long> userIds = page.getRecords().stream().map(BillInfo::getUserId).toList();
        List<String> billNos = page.getRecords().stream().map(BillInfo::getBillNo).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        List<ClientUserCoupon> coupons = clientUserManager.fetchCouponByBillNos(billNos);
        Map<String, ClientUserCoupon> userCouponMap = coupons.stream().collect(Collectors.toMap(ClientUserCoupon::getBillNo, v -> v));
        List<Long> couponIds = coupons.stream().map(ClientUserCoupon::getCouponId).toList();
        List<TenantCoupon> tenantCoupons = couponManager.fetchCouponsByIds(couponIds);
        Map<Long, TenantCoupon> couponMap = tenantCoupons.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        return page.convert(v -> {
            CouponBillBo bo = new CouponBillBo();
            ClientUser clientUser = userMap.get(v.getUserId());
            ClientUserCoupon clientUserCoupon = userCouponMap.get(v.getBillNo());
            TenantCoupon tenantCoupon = couponMap.get(clientUserCoupon.getCouponId());
            bo.setTitle(tenantCoupon.getTitle());
            bo.setUsed(clientUserCoupon.getUsed());
            bo.setCreateTime(v.getCreateAt());
            bo.setBillId(v.getId());
            bo.setDescription(tenantCoupon.getDescription());
            bo.setPayAmount(v.getPayAmount());
            bo.setStatus(v.getStatus());
            bo.setId(clientUserCoupon.getId());
            bo.setNickname(clientUser.getNickname());
            bo.setUserPhone(clientUser.getPhone());
            return bo;
        });

    }

    public void issueCoupon(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        log.info("issueCoupon userId: {}, payerTotal: {}, payInfo: {}", userId, payerTotal, payInfo);
        TenantCoupon coupon = JsonUtils.toObject(payInfo, TenantCoupon.class);
        ClientUserCoupon userCoupon = clientUserManager.saveUserCoupon(coupon, userId, bill.getBillNo(), false);
        if (StringUtils.isNotBlank(bill.getOrderNo())) {
            clientUserManager.useCoupon(userCoupon.getId());
            OrderInfo order = orderManager.fetchOrderByNo(bill.getOrderNo());
            order.setStatus(OrderStatusEnum.PAID);
            order.setPayAmount(payerTotal);
            order.setPayTime(LocalDateTime.now());
            orderManager.save(order);
            clubManager.updateTableUsing(order.getTableId(), userId, order.getEndTime());
            if (LocalDateTime.now().plusSeconds(30).isAfter(order.getStartTime())) {
                DeviceOperationDto dto = new DeviceOperationDto();
                dto.setOrderNo(order.getOrderNo());
                dto.setType(DeviceOperationTypeEnum.START);
                deviceService.handleOrderDevice(dto);
            }
        }
    }

    public void giftCouponExpired(Long id) {

    }

    public void userCouponExpired(Long id) {
        clientUserManager.expireCoupon(id);
    }

    public Page<AdminUserCouponBo> clubUserList(AdminCouponQueryDto dto) {
        AdminUserBo user = SecurityHolder.<Long, AdminUserBo>session().getUser();
        if (user.getTenantId() != 0) {
            dto.setTenantId(user.getTenantId());
        }
        List<Long> queryUserIds = null;
        if (StringUtils.isNotBlank(dto.getPhone())) {
            List<ClientUser> users = clientUserManager.fetchUsersByPhone(dto.getPhone());
            if (CollectionUtils.isEmpty(users)) {
                return dto.empty();
            }
            queryUserIds = users.stream().map(BaseEntity::getId).toList();
        }
        List<TenantCoupon> couponList = couponManager.fetchCouponByClub(dto.getClubId());
        List<Long> couponIds = couponList.stream().map(BaseEntity::getId).toList();
        Page<ClientUserCoupon> page = clientUserManager.fetchCouponByCouponIds(dto, couponIds, queryUserIds);
        List<Long> userIds = page.getRecords().stream().map(ClientUserCoupon::getUserId).toList();
        List<ClientUser> clientUsers = clientUserManager.fetchUserByIds(userIds);
        Map<Long, TenantCoupon> couponMap = couponList.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        Map<Long, ClientUser> userMap = clientUsers.stream().collect(Collectors.toMap(BaseEntity::getId, v -> v));
        return page.convert(v -> {
            AdminUserCouponBo bo = BeanUtils.copy(v, AdminUserCouponBo.class);
            TenantCoupon tenantCoupon = couponMap.get(v.getCouponId());
            ClientUser clientUser = userMap.get(v.getUserId());
            bo.setUserId(v.getUserId());
            bo.setCreateTime(v.getCreateAt());
            bo.setExpireTime(v.getExpireTime());
            bo.setUsed(v.getUsed());
            bo.setNickname(clientUser.getNickname());
            bo.setUserPhone(clientUser.getPhone());
            bo.setTitle(tenantCoupon.getTitle());
            bo.setDescription(tenantCoupon.getDescription());
            bo.setPrice(tenantCoupon.getPrice());
            bo.setType(tenantCoupon.getType());
            bo.setMinutes(tenantCoupon.getMinutes());
            bo.setPeriod(tenantCoupon.getPeriod());
            return bo;
        });
    }

}
