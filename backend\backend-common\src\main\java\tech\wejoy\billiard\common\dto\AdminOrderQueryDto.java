package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AdminOrderQueryDto extends PageRequest {

    @Schema(description = "订单状态")
    @QueryParam("status")
    private OrderStatusEnum status;

    @Schema(description = "店铺")
    @QueryParam("clubId")
    private Long clubId;

    @Schema(description = "加盟商")
    @QueryParam("tenantId")
    private Long tenantId;

    @Schema(description = "订单号")
    @QueryParam("orderNo")
    private String orderNo;

    @Schema(description = "手机号")
    @QueryParam("phone")
    private String phone;

    @Schema(hidden = true)
    private List<Long> userIds;

    @Schema(description = "开始时间")
    @QueryParam("startDate")
    private LocalDate startDate;

    @Schema(description = "结束时间")
    @QueryParam("endDate")
    private LocalDate endDate;

}
