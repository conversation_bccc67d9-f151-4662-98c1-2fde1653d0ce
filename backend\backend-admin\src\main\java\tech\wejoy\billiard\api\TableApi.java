package tech.wejoy.billiard.api;


import com.congeer.core.exception.BaseException;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.AdminStartPrepareBo;
import tech.wejoy.billiard.bo.AdminTableBo;
import tech.wejoy.billiard.bo.StartResultBo;
import tech.wejoy.billiard.dto.AdminStartTableDto;
import tech.wejoy.billiard.dto.DeviceOperationDto;
import tech.wejoy.billiard.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.enums.StartResultEnum;
import tech.wejoy.billiard.service.AdminClubService;
import tech.wejoy.billiard.service.DeviceService;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;

@Path("/table")
@Tag(name = "台桌")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class TableApi {

    private final AdminClubService tableService;

    private final DeviceService deviceService;

    @Path("/list")
    @GET
    @Operation(summary = "获取台桌列表")
    public List<AdminTableBo> page(@QueryParam("clubId") Long clubId) {
        return tableService.listTableByClub(clubId);
    }

    @GET
    @Path("/{id}/code")
    @Operation(summary = "获取桌台码")
    @Produces("image/jpeg")
    public Response getBindQrCode(@PathParam("id") Long id) {
        File entity = tableService.getTableCode(id);
        return Response.ok(entity)
                .header("Content-Disposition", "attachment; filename=" + entity.getName())
                .build();
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "获取桌台详情")
    public AdminTableBo detail(@PathParam("id") Long id) {
        return tableService.tableDetail(id);
    }

    @POST
    @Path("/")
    @Operation(summary = "创建桌台")
    public AdminTableBo create(AdminTableBo tableBo) {
        return tableService.create(tableBo);
    }

    @POST
    @Path("/{id}")
    @Operation(summary = "更新桌台")
    public AdminTableBo update(@PathParam("id") Long id, AdminTableBo tableBo) {
        tableBo.setId(id);
        return tableService.update(tableBo);
    }

    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除桌台")
    public void delete(@PathParam("id") Long id) {
        tableService.delete(id);
    }

    @POST
    @Path("/{id}/bind/{deviceId}")
    @Operation(summary = "绑定设备")
    public void bindDevice(@PathParam("id") Long id, @PathParam("deviceId") Long deviceId) {
        tableService.bindDevice(id, deviceId);
    }

    @POST
    @Path("/{id}/unbind/{deviceId}")
    @Operation(summary = "解绑设备")
    public void unbindDevice(@PathParam("id") Long id, @PathParam("deviceId") Long deviceId) {
        tableService.unbindDevice(id, deviceId);
    }

    @GET
    @Path("/options")
    @Operation(summary = "获取桌台选项")
    public List<AdminTableBo> tableOptions(@QueryParam("clubId") Long clubId) {
        return tableService.tableOptions(clubId);
    }

    @POST
    @Path("/{id}/prepare")
    @Operation(summary = "开桌准备信息")
    public AdminStartPrepareBo startPrepare(@PathParam("id") Long id) {
        return tableService.startPrepare(id);
    }

    @POST
    @Path("/{id}/start")
    @Operation(summary = "开始使用桌台")
    @Permission({"business_free:write"})
    public StartResultBo start(@PathParam("id") Long id, AdminStartTableDto dto) {
        dto.setTableId(id);
        if (dto.getTableId() == null) {
            throw new BaseException("请选择桌台");
        }
        StartResultBo start = tableService.start(dto);
        if (start.getResult() == StartResultEnum.STARTING && LocalDateTime.now().plusSeconds(30).isAfter(start.getStartTime())) {
            DeviceOperationDto enter = new DeviceOperationDto();
            enter.setOrderNo(start.getOrderNo());
            enter.setType(DeviceOperationTypeEnum.START);
            deviceService.handleOrderDevice(enter);
        }
        return start;
    }

}
