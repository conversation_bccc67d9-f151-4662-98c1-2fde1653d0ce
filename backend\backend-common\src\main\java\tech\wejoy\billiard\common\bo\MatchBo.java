package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.MatchGameTypeEnum;
import tech.wejoy.billiard.common.enums.MatchStatusEnum;
import tech.wejoy.billiard.common.enums.MatchTypeEnum;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class MatchBo {

    @Schema(description = "ID")
    private Long id;

    @Schema(description = "俱乐部ID")
    private Long clubId;

    private String clubName;

    private String clubHeadImg;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "昵称")
    private String nickname;

    private String userPhone;

    private Long acceptUserId;

    private String acceptNickname;

    private String acceptUserPhone;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "比赛类型")
    private MatchGameTypeEnum gameType;

    @Schema(description = "类型")
    private MatchTypeEnum type;

    @Schema(description = "时间")
    private Integer minutes;

    @Schema(description = "段位")
    private Integer level;

    @Schema(description = "状态")
    private MatchStatusEnum status;

    private boolean request;

    @Schema(description = "约球请求列表")
    private List<MatchRequestBo> requests;

}
