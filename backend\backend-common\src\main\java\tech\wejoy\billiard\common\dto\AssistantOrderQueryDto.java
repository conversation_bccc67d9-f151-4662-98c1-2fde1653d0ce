package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class AssistantOrderQueryDto extends PageRequest {

    @Schema(description = "订单状态")
    @QueryParam("status")
    @Separator(",")
    private List<OrderStatusEnum> status;

    @Schema(description = "店铺")
    @QueryParam("clubId")
    private Long clubId;

    @Schema(hidden = true)
    private Long assistantId;

    @Schema(hidden = true)
    private Long userId;

}
