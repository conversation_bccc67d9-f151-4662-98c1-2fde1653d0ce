package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.MatchStatusEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class MatchAdminQueryDto extends PageRequest {

    @QueryParam("clubId")
    private Long clubId;

    @QueryParam("status")
    @Separator(",")
    private List<MatchStatusEnum> status;

    private Long userId;

}
