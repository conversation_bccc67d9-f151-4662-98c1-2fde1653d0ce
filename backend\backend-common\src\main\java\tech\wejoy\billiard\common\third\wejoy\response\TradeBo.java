package tech.wejoy.billiard.common.third.wejoy.response;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TradeBo {
    private Long id;
    private Long tenantId;
    private Long userId;
    private String orderNo;
    private String thirdPayNo;
    private String payInfo;
    private BigDecimal amount;
    private BigDecimal totalAmount;
    private BigDecimal payAmount;
    private LocalDateTime payAt;
    private LocalDateTime pushAt;
    private String resultInfo;
    private String notifyUrl;
    private String returnUrl;
}
