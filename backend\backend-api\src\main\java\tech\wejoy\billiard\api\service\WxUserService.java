package tech.wejoy.billiard.api.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.congeer.core.exception.BaseException;
import com.congeer.security.core.bean.SessionInfo;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.JsonUtils;
import com.congeer.web.util.WebUtils;
import io.vertx.core.http.HttpServerRequest;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Context;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.api.bo.WxLoginBo;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.common.bo.AssistantBo;
import tech.wejoy.billiard.common.entity.ClientUser;
import tech.wejoy.billiard.common.enums.GenderEnum;
import tech.wejoy.billiard.common.service.AssistantService;
import tech.wejoy.billiard.common.service.ClientUserService;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;

import java.io.File;
import java.time.LocalDateTime;

@ApplicationScoped
@Slf4j
public class WxUserService {

    @Inject
    ClientUserService clientUserService;

    @Inject
    AssistantService assistantService;

    @Context
    HttpServerRequest request;

    @Transactional(rollbackOn = Exception.class)
    public WxLoginBo login(String code) {
        String sessionKey = null;
        String openId = null;
        try {
            WxMaJscode2SessionResult result = ThirdServiceHolder.wxMaService("user").getUserService().getSessionInfo(code);
            sessionKey = result.getSessionKey();
            openId = result.getOpenid();
        } catch (Exception e) {
            log.error("获取sessionKey失败:{}", e.getMessage(), e);
        }

        if (sessionKey == null || openId == null) {
            throw new BaseException("获取sessionKey失败");
        }
        ClientUser user = clientUserService.getUserByOpenId(openId);

        if (user != null) {
            // 更新用户信息
            user.setLastLoginIp(WebUtils.getIpAddr(request));
            user.setLastLoginAt(LocalDateTime.now());
            clientUserService.save(user);
        } else {
            // 创建用户
            user = new ClientUser();
            user.setOpenId(openId);
            user.setUsername("wx_" + openId);
            user.setLastLoginIp(WebUtils.getIpAddr(request));
            user.setLastLoginAt(LocalDateTime.now());
            String[] nicks = new String[]{"高杆左塞", "低杆右塞", "长台吸库", "大力轻推", "爆杆响袋"};
            int index = (int) (Math.random() * nicks.length);
            user.setNickname(nicks[index] + openId.substring(openId.length() - 4).toUpperCase());
            user.setAvatar("");
            user.setGender(GenderEnum.NONE);
            clientUserService.createUser(user);
        }
        SessionInfo<Long, WxUserBo> session = SecurityHolder.login(WxUserBo.from(user));
        log.info("用户登录成功:{}", JsonUtils.toJson(session));
        WxLoginBo dto = new WxLoginBo();
        dto.setId(user.getId());
        dto.setToken(session.getToken());
        dto.setNickname(user.getNickname());
        dto.setAvatar(user.getAvatar());
        dto.setHasPhone(StringUtils.isNotBlank(user.getPhone()));
        if (user.getPhone() != null) {
            dto.setPhone(user.getPhone().replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2"));
        }
        AssistantBo assistant = assistantService.getAssistantByUserId(user.getId());
        if (assistant != null) {
            dto.setAssistantId(assistant.getId());
        }
        dto.setBirth(user.getBirth());
        return dto;
    }

    @Transactional(rollbackOn = Exception.class)
    public void updatePhone(String code) {
        SessionInfo<Long, WxUserBo> sessionInfo = SecurityHolder.session();
        WxUserBo user = sessionInfo.getUser();
        String phoneNumber = null;
        try {
            WxMaPhoneNumberInfo phoneNoInfo = ThirdServiceHolder.wxMaService("user").getUserService().getPhoneNoInfo(code);
            phoneNumber = phoneNoInfo.getPhoneNumber();
        } catch (Exception e) {
            log.error("获取手机号失败:{}", e.getMessage(), e);
        }
        if (phoneNumber == null) {
            throw new BaseException("获取手机号失败");
        }
        ClientUser userByPhone = clientUserService.getUserByPhone(phoneNumber);
        if (userByPhone != null) {
            throw new BaseException("手机号已被绑定");
        }
        ClientUser userByOpenId = clientUserService.getUserByOpenId(user.getOpenId());
        userByOpenId.setPhone(phoneNumber);
        clientUserService.save(userByOpenId);
    }

    public File getAssistantQrcode(Long id) {
        try {
            return ThirdServiceHolder.wxMaService("user").getQrcodeService().createQrcode("pages/assistant/detail/index?id=" + id);
        } catch (WxErrorException e) {
            log.error("获取二维码失败:{}", e.getMessage(), e);
            throw new BaseException("获取二维码失败");
        }
    }

    public File getCompetitionQrcode(Long id) {
        try {
            return ThirdServiceHolder.wxMaService("user").getQrcodeService().createQrcode("pages/competition/detail/index?id=" + id);
        } catch (WxErrorException e) {
            log.error("获取二维码失败:{}", e.getMessage(), e);
            throw new BaseException("获取二维码失败");
        }
    }

}
