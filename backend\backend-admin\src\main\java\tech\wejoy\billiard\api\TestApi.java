package tech.wejoy.billiard.api;

import com.congeer.utils.DateUtils;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import tech.wejoy.billiard.entity.TenantSmyooAccount;
import tech.wejoy.billiard.third.smyoo.client.SmyooClient;
import tech.wejoy.billiard.third.smyoo.request.DeviceRequest;
import tech.wejoy.billiard.third.smyoo.response.*;

import java.time.LocalDateTime;
import java.util.List;

@Path("/test")
@Produces(MediaType.APPLICATION_JSON)
public class TestApi {


    @GET
    @Path("/")
    public String stats() {
        SmyooClient client = new SmyooClient("********", "7C723781D2894B41A0C8650A55D3273C9", "6C810AFD0C044715A74B0036E1D715B4");
        TenantSmyooAccount account = new TenantSmyooAccount();
        account.setPhone("***********");
        account.setPassword("xxqs6703");
        LoginOpenResponse login = client.loginOpen(account);
        LoginTicketResponse loginTicketResponse = client.loginTicket(login.getTicket());
        account.setSessionId(loginTicketResponse.getSessionId());
        StatusChangeResponse statusChangeResponse = client.statusChanged(account);
        LocalDateTime localDateTime = DateUtils.getLocalDateTime(statusChangeResponse.getDeviceUpdateTime(), "yyyy-MM-dd HH:mm:ss.SSS");
        if (!localDateTime.equals(account.getDeviceUpdateAt())) {
            account.setDeviceUpdateAt(localDateTime);
            QueryDevicesResponse resp = client.queryDevices(account);
            List<QueryDevicesResponse.McuInfo> mcuInfos = resp.getMcuInfos();
            for (QueryDevicesResponse.McuInfo mcuInfo : mcuInfos) {
//                System.out.println(JsonUtils.toJson(mcuInfo));
                if (mcuInfo.getType() == 29) {
                    DeviceRequest req = new DeviceRequest();
                    req.setMcuId(mcuInfo.getMcuId());
                    GetIrDeviceResponse getIrDeviceResponse = client.irDeviceGetData(account, req);
                    System.out.println(getIrDeviceResponse.getDatapoint());
                }
            }
        }
        return null;
    }


}
