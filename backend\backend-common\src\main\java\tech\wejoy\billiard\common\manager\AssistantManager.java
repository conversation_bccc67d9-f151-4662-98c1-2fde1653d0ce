package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.web.bean.request.PageRequest;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.dto.AdminAssistantApplyQueryDto;
import tech.wejoy.billiard.common.dto.AdminAssistantOrderQueryDto;
import tech.wejoy.billiard.common.dto.AdminAssistantQueryDto;
import tech.wejoy.billiard.common.dto.AssistantOrderQueryDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.AssistantStatusEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.repo.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class AssistantManager {

    private final AssistantClubRelRepo assistantClubRelRepo;

    private final AssistantClubRequestRepo assistantClubRequestRepo;

    private final AssistantOrderRepo assistantOrderRepo;

    private final AssistantInfoRepo assistantInfoRepo;

    private final AssistantApplyRepo assistantApplyRepo;

    public Page<AssistantInfo> list(PageRequest dto) {
        return assistantInfoRepo.page(dto.toPage(), "enable = 1", Map.of());
    }

    public AssistantInfo fetchById(Long id) {
        return assistantInfoRepo.findById(id);
    }

    public List<AssistantOrder> fetchAssistantOrderByAssistantId(Long assistantId) {
        return assistantOrderRepo.find("assistantId", assistantId).list();
    }

    public List<AssistantClubRel> fetchClubRelByAssistantId(Long assistantId) {
        return assistantClubRelRepo.find("assistantId", assistantId).list();
    }

    public List<AssistantOrder> getUsingOrderByAssistantId(Long assistantId) {
        return assistantOrderRepo.find("assistantId = ?1 and status in ?2", Sort.by("startTime"), assistantId, OrderStatusEnum.usingStatus).list();
    }

    public AssistantInfo fetchAssistantById(Long id) {
        return assistantInfoRepo.findById(id);
    }

    public void saveApply(AssistantApply apply) {
        assistantApplyRepo.save(apply);
    }

    public AssistantApply fetchAssistantApplyByUserId(Long userId) {
        return assistantApplyRepo.find("userId = ?1 and status <> ?2", userId, AssistantStatusEnum.REJECT).firstResult();
    }

    public Page<AssistantOrder> pageOrder(AssistantOrderQueryDto dto) {
        Map<String, Object> map = new HashMap<>();
        String query = "1=1";
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            query += " and status in :status";
            map.put("status", dto.getStatus());
        }
        if (dto.getClubId() != null) {
            query += " and clubId = :clubId";
            map.put("clubId", dto.getClubId());
        }
        if (dto.getUserId() != null) {
            query += " and userId = :userId";
            map.put("userId", dto.getUserId());
        }
        if (dto.getAssistantId() != null) {
            query += " and assistantId = :assistantId";
            map.put("assistantId", dto.getAssistantId());
        }
        return assistantOrderRepo.page(dto.toPage(), query, Sort.descending("createAt"), map);
    }

    public AssistantOrder fetchOrderByNo(String orderNo) {
        return assistantOrderRepo.find("orderNo", orderNo).firstResult();
    }

    public void saveOrder(AssistantOrder order) {
        assistantOrderRepo.save(order);
    }

    public List<AssistantInfo> fetchAssistantByClubId(Long id) {
        List<AssistantClubRel> clubRelList = assistantClubRelRepo.find("clubId", id).list();
        List<Long> assistantIds = clubRelList.stream().map(AssistantClubRel::getAssistantId).toList();
        return assistantInfoRepo.find("id in ?1 and enable = 1", Sort.by("id"), assistantIds).list();
    }

    public AssistantInfo fetchAssistantByUserId(Long id) {
        return assistantInfoRepo.find("userId", id).firstResult();
    }

    public String generateOrderNo(AssistantOrder order) {
        String clubId = (order.getClubId() + 30105) + "";
        String assistantId = (order.getAssistantId() + 102040) + "";
        String orderId = (order.getId() + 13510000) + "";
        return "A" + clubId + assistantId + orderId;
    }

    public void deleteOrder(AssistantOrder order) {
        assistantOrderRepo.delete(order);
    }

    public Page<AssistantInfo> adminList(AdminAssistantQueryDto dto) {
        Map<String, Object> map = new HashMap<>();
        String query = "1=1";
        if (dto.getName() != null) {
            query += " and name like :name";
            map.put("name", "%" + dto.getName() + "%");
        }
        if (dto.getPhone() != null) {
            query += " and phone like :phone";
            map.put("phone", "%" + dto.getPhone() + "%");
        }
        return assistantInfoRepo.page(dto.toPage(), query, Sort.descending("createAt"), map);
    }

    public void saveAssistant(AssistantInfo assistant) {
        assistantInfoRepo.save(assistant);
    }

    public Page<AssistantApply> adminApplyList(AdminAssistantApplyQueryDto dto) {
        Map<String, Object> map = new HashMap<>();
        String query = "1=1";
        if (dto.getName() != null) {
            query += " and name like :name";
            map.put("name", "%" + dto.getName() + "%");
        }
        if (dto.getPhone() != null) {
            query += " and phone like :phone";
            map.put("phone", "%" + dto.getPhone() + "%");
        }
        if (CollectionUtils.isNotEmpty(dto.getStatus())) {
            query += " and status in :status";
            map.put("status", dto.getStatus());
        }
        return assistantApplyRepo.page(dto.toPage(), query, map);
    }

    public AssistantApply fetchAssistantApplyById(Long id) {
        return assistantApplyRepo.findById(id);
    }

    public void deleteClubIdsByAssistantId(Long id) {
        assistantClubRelRepo.delete("assistantId", id);
    }

    public void saveClubRel(AssistantClubRel rel) {
        assistantClubRelRepo.save(rel);
    }

    public List<AssistantInfo> fetchAssistantByIds(List<Long> assistantIds) {
        return assistantInfoRepo.find("id in ?1", Sort.by("id"), assistantIds).list();
    }

    public Page<AssistantOrder> adminOrderList(AdminAssistantOrderQueryDto dto) {
        Map<String, Object> map = new HashMap<>();
        String query = "1=1";
        if (CollectionUtils.isNotEmpty(dto.getClubId())) {
            query += " and clubId in :clubId";
            map.put("clubId", dto.getClubId());
        }
        if (dto.getStatus() != null) {
            query += " and status = :status";
            map.put("status", dto.getStatus());
        }
        if (dto.getOrderNo() != null) {
            query += " and orderNo like :orderNo";
            map.put("orderNo", "%" + dto.getOrderNo() + "%");
        }
        if (dto.getPhone() != null) {
            query += " and phone like :phone";
            map.put("phone", "%" + dto.getPhone() + "%");
        }
        return assistantOrderRepo.page(dto.toPage(), query, Sort.descending("id"), map);
    }

    public List<AssistantOrder> fetchOrderByStatus(OrderStatusEnum status) {
        return assistantOrderRepo.find("status", status).list();
    }

    public List<AssistantInfo> fetchAssistantByClub(Long id) {
        List<AssistantClubRel> clubRelList = assistantClubRelRepo.find("clubId", id).list();
        return fetchAssistantByIds(clubRelList.stream().map(AssistantClubRel::getAssistantId).toList());
    }

    public AssistantOrder fetchOrderById(Long id) {
        return assistantOrderRepo.findById(id);
    }

    public List<AssistantClubRequest> getAssistantClubRequestByAssistantId(Long id) {
        return assistantClubRequestRepo.find("assistantId", id).list();
    }

    public List<AssistantClubRel> fetchAssistantClubByAssistantId(Long id) {
        return assistantClubRelRepo.find("assistantId", id).list();
    }

    public List<AssistantClubRel> fetchAssistantClubByClubId(Long clubId) {
        return assistantClubRelRepo.find("clubId", clubId).list();
    }

    public List<AssistantClubRequest> getAssistantClubRequestByClubId(Long clubId) {
        return assistantClubRequestRepo.find("clubId", clubId).list();
    }

    public void saveAssistantClubRequest(AssistantClubRequest request) {
        assistantClubRequestRepo.save(request);
    }

    public AssistantClubRequest fetchAssistantRequestByAssistantIdAndClubId(Long assistantId, Long clubId) {
        return assistantClubRequestRepo.find("assistantId = ?1 and clubId = ?2", assistantId, clubId).firstResult();
    }

    public AssistantClubRequest fetchAssistantRequestById(Long id) {
        return assistantClubRequestRepo.findById(id);
    }

    public void deleteAssistantClubRequest(Long id) {
        assistantClubRequestRepo.deleteById(id);
    }

    public void deleteClubRel(Long id) {
        assistantClubRelRepo.deleteById(id);
    }

    public AssistantClubRel fetchAssistantClubRelById(Long id) {
        return assistantClubRelRepo.findById(id);
    }

    public AssistantClubRequest fetchAssistantRequestByClubIdAndAssistantId(Long clubId, Long assistantId) {
        return assistantClubRequestRepo.find("clubId = ?1 and assistantId = ?2", clubId, assistantId).firstResult();
    }

    public AssistantClubRel fetchAssistantClubRelByClubIdAndAssistantId(Long clubId, Long assistantId) {
        return assistantClubRelRepo.find("clubId = ?1 and assistantId = ?2", clubId, assistantId).firstResult();
    }
}
