package tech.wejoy.billiard.api;

import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.OperationsStatsBo;
import tech.wejoy.billiard.bo.TableHoursBo;
import tech.wejoy.billiard.bo.TableOperationsBo;
import tech.wejoy.billiard.dto.ClubDatePeriodDto;
import tech.wejoy.billiard.service.BusinessService;

import java.util.List;

@Slf4j
@Path("/operations")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "营业接口")
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class OperationsApi {

    private final BusinessService businessService;

    @GET
    @Path("/stats")
    @Operation(summary = "数据统计")
    @Permission({"business_data_stats:read", "business_data_stats:write"})
    public OperationsStatsBo stats(@BeanParam ClubDatePeriodDto dto) {
        return businessService.statsOperations(dto.getTenantId(), dto.getClubId(), dto.getStartDate(), dto.getEndDate());
    }

    @GET
    @Path("/table/stats/hours")
    @Operation(summary = "开台时长统计")
    @Permission({"business_table_stats:read", "business_table_stats:write"})
    public List<TableHoursBo> statsTableHours(@BeanParam ClubDatePeriodDto dto) {
        return businessService.statsTableHours(dto.getTenantId(), dto.getClubId(), dto.getStartDate(), dto.getEndDate());
    }

    @GET
    @Path("/table/stats")
    @Operation(summary = "当前球桌营业状态")
    @Permission({"business_table_current:read", "business_table_current:write"})
    public List<TableOperationsBo> statsTable(@BeanParam ClubDatePeriodDto dto) {
        return businessService.statsTable(dto.getTenantId(), dto.getClubId());
    }

}
