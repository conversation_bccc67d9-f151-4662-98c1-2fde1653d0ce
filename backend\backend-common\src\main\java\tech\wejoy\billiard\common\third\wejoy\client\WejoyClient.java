package tech.wejoy.billiard.common.third.wejoy.client;

import com.congeer.core.exception.BaseException;
import com.congeer.utils.HttpUtils;
import com.congeer.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.enterprise.inject.spi.CDI;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.dto.RefundDto;
import tech.wejoy.billiard.common.entity.WejoyConfig;
import tech.wejoy.billiard.common.manager.ConfigManager;
import tech.wejoy.billiard.common.third.wejoy.request.CreateTradeDto;
import tech.wejoy.billiard.common.third.wejoy.response.TradeCreateBo;
import tech.wejoy.billiard.common.third.wejoy.response.WejoyTradeBo;
import tech.wejoy.billiard.common.third.wejoy.utils.RSAUtils;

import java.util.HashMap;
import java.util.Map;

@Slf4j
public class WejoyClient {

    private final String privateKey;
    private final String publicKey;
    private final String baseUrl;
    private final String merchantId;
    @Getter
    private final String appId;


    public WejoyClient() {
        this("default");
    }

    public WejoyClient(String env) {
        WejoyConfig config = CDI.current().select(ConfigManager.class).get().getWejoy(env);
        if (config == null) {
            throw new BaseException("WeJoy config not found for env: " + env);
        }
        this.privateKey = config.getPrivateKey();
        this.publicKey = config.getPublicKey();
        this.baseUrl = config.getBaseUrl();
        this.merchantId = config.getMerchantId();
        this.appId = config.getAppId();
    }

    public String get(String url, String query) {
        String method = "POST";
        Map<String, String> headers = buildHeader(url, method, query);
        HttpUtils.HttpResult response = HttpUtils.get(baseUrl + url + "?" + query).header(headers).send();
        if (response.statusCode() != 200) {
            throw new BaseException(response.body());
        }
        return response.body();
    }

    public String post(String url, String body) {
        String method = "POST";
        Map<String, String> headers = buildHeader(url, method, body);
        HttpUtils.HttpResult response = HttpUtils.post(baseUrl + url).header(headers).body(body).send();
        if (response.statusCode() != 200) {
            throw new BaseException(response.body());
        }
        return response.body();
    }

    private Map<String, String> buildHeader(String url, String method, String body) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("Content-Type", "application/json");
            params.put("X-Merchant-Id", merchantId);
            String timestamp = System.currentTimeMillis() + "";
            params.put("X-Timestamp", timestamp);
            params.put("X-Signature", RSAUtils.sign(url + method + timestamp + body, privateKey));
            return params;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public TradeCreateBo createTrade(CreateTradeDto request) {
        String url = "/trade/prepay";
        String json = JsonUtils.toJson(request);
        String response = post(url, json);
        try {
            TradeCreateBo result = JsonUtils.toObject(response, new TypeReference<>() {
            });
            log.info("createTrade result:{}", result);
            return result;
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("createTrade error", e);
        }
        throw new BaseException("创建交易失败");
    }

    /**
     * Refund a trade by trade number
     *
     * @param tradeNo The trade number to refund
     * @param request The refund request details
     * @return The trade information after refund
     */
    public WejoyTradeBo refundTradeByNo(String tradeNo, RefundDto request) {
        String url = "/trade/refund/no/" + tradeNo;
        String json = JsonUtils.toJson(request);
        String response = post(url, json);
        try {
            WejoyTradeBo result = JsonUtils.toObject(response, new TypeReference<>() {
            });
            log.info("refundTradeByNo result:{}", result);
            return result;
        } catch (Exception e) {
            if (e instanceof BaseException) {
                throw (BaseException) e;
            }
            log.error("refundTradeByNo error", e);
        }
        throw new BaseException("退款失败");
    }

}
