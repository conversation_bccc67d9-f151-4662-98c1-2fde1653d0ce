package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.admin.bo.TenantBo;
import tech.wejoy.billiard.admin.dto.TenantQueryDto;
import tech.wejoy.billiard.admin.dto.TenantUpdateDto;
import tech.wejoy.billiard.admin.service.TenantService;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.io.File;
import java.util.List;

@Path("/tenant")
@Tag(name = "加盟商")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
@Authorized
public class TenantApi {

    private final TenantService tenantService;

    @GET
    @Path("/list")
    @Operation(summary = "加盟商列表")
    public Page<TenantBo> page(@BeanParam TenantQueryDto dto) {
        return tenantService.page(dto);
    }

    @POST
    @Path("/")
    @Operation(summary = "创建加盟商")
    public TenantBo create(TenantBo bo) {
        return tenantService.save(bo);
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "更新加盟商")
    public TenantBo update(@PathParam("id") Long id, TenantBo bo) {
        bo.setId(id);
        return tenantService.save(bo);
    }

    @PUT
    @Path("/{id}/status/{status}")
    @Operation(summary = "修改加盟商状态")
    public void changeStatus(@PathParam("id") Long id, @PathParam("status") IsEnum status) {
        tenantService.changeStatus(id, status);
    }

    @GET
    @Path("/options")
    @Operation(summary = "获取加盟商选项")
    public List<TenantBo> options(@QueryParam("name") String name) {
        return tenantService.options(name);
    }

    @GET
    @Path("/{id}/bind/code")
    @Operation(summary = "获取绑定码")
    @Produces("image/jpeg")
    public Response getBindQrCode(@PathParam("id") Long id) {
        File entity = tenantService.bindCode(id);
        return Response.ok(entity)
                .header("Content-Disposition", "attachment; filename=" + entity.getName())
                .build();
    }

    @PATCH
    @Path("/{id}/info")
    @Operation(summary = "编辑加盟商信息")
    @Consumes(MediaType.APPLICATION_JSON)
    public TenantBo updateTenantInfo(@PathParam("id") Long id, TenantUpdateDto dto) {
        return tenantService.updateTenantInfo(id, dto);
    }

}
