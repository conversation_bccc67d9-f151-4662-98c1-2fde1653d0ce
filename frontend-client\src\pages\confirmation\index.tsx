import api from "@/api/bussiness";
import DurationTime, {
  IDurationTimeRef,
} from "@/components/bussiness/DurationTime";
import { OrderPanel } from "@/components/bussiness/Order";
import AverageButton from "@/components/bussiness/Order/AverageButton";
import CancelButton from "@/components/bussiness/Order/CancelButton";
import FinishButton from "@/components/bussiness/Order/FinishButton";
import ForceButton from "@/components/bussiness/Order/ForceButton";
import QuickStartButton from "@/components/bussiness/Order/QuickStartButton";
import PaymentMethod, {
  IPaymentRef,
} from "@/components/bussiness/PaymentMethod";
import RechargeTab from "@/components/bussiness/Recharge/Tab";
import Back from "@/components/bussiness/back";
import { PAYTYPES } from "@/constants";
import { TableCard } from "@/custom/table-card";
import { useDo, useStore } from "@/hooks";
import { login } from "@/stores/auth";
import { menuRect, toPrice } from "@/utils";
import {
  Cell,
  CellGroup,
  ConfigProvider,
  Dialog,
  Popup,
  SafeArea,
} from "@nutui/nutui-react-taro";
import { Button, Text, View } from "@tarojs/components";
import Taro from "@tarojs/taro";
import dayjs from "dayjs";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
const SceneTypes = {
  SCANCODE: "1011",
  OTHER: "1000",
};

enum FROM_TYPES {
  NONE,
  WEAPP,
  QRCODE,
}

enum RESULT_TYPES {
  SUCCESS = 0,
  PENDING_PAYMENT = 1,
  STARTING = 2,
  TABLE_OCCUPY = 3,
  DEVICE_OFFLINE = 4,
  NOT_ENOUGH_BALANCE = 5,
}

enum PAY_TYPE {
  NONE,
  WECHAT,
  SINGLE,
  NUMBER,
  COUPON,
  MEITUAN,
  DOUYIN,
  DEPOSIT,
}

interface IParams {
  from: FROM_TYPES;
  tableId: number;
}

interface IConfirmationData {
  table: ITable;
  timePlans: TimePlan[];
  club: { id: string; deposit: number; coupons: ICoupon[]; phone: string };
  order?: IOrder;
  wallet: {
    tickets?: ITicket[];
    coupons: ICoupon[];
  };
}

const OVERTIME = 60 * 5;

const INTERVAL = 5;


export default () => {
  const [instance] = useState(() => {
    const instance = Taro.getCurrentInstance();
    return instance;
  });

  const route = Taro.useRouter();
  const rect = menuRect();
  const ref = useRef(true);
  const durationTimeRef = useRef<IDurationTimeRef>(null);

  const [from, setFrom] = useState(FROM_TYPES.QRCODE);

  const paymentRef = useRef<IPaymentRef>(null);

  const [price, setPrice] = useState(0);
  const [durationTime, setDurationTime] = useState(0);
  const [visible, setVisible] = useState(false);

  const [confirmationData, setConfirmationData] = useState<IConfirmationData>();

  const dispatch = useDo();
  const { user } = useStore((state) => state.auth);
  const sleep = useCallback(
    (time: number) =>
      new Promise((reslove) =>
        setTimeout(() => {
          reslove("success");
        }, time * 1000)
      ),
    []
  );

  const getCheckOrder = useCallback(async (orderNo) => {
    const { data } = await api.venues.getTableStatusByOrder({ orderNo });
    return data.result === 0 && data.status === 2;
  }, []);

  const recursiveCheck = useCallback(
    async (orderNo) => {
      const res = await getCheckOrder(orderNo);
      if (!res && ref.current) {
        await sleep(INTERVAL);
        return await recursiveCheck(orderNo);
      }
      return res;
    },
    [getCheckOrder, sleep, ref]
  );

  const limitRecursiveCheck = (orderNo) => {
    ref.current = true;
    sleep(OVERTIME).then(() => {
      ref.current = false;
    });
    return recursiveCheck(orderNo);
  };

  const tableId = route.params.id;
  const scene = route.params.scene;

  useEffect(() => {
    if (scene === SceneTypes.SCANCODE) {
      setFrom(FROM_TYPES.QRCODE);
    } else {
      setFrom(FROM_TYPES.WEAPP);
    }
  }, [scene]);

  const onChangeDurationTime = useCallback(
    (value) => {
      setDurationTime(value);
    },
    [setDurationTime]
  );

  const calculationPayment = useCallback(async () => {
    if (durationTimeRef.current) {
      const [startTime, endTime] = (durationTimeRef.current as any)?.getData();
      const tableId = instance.router?.params.id;
      const {
        data: { price },
      } = await api.venues.calculationPayment({
        tableId,
        startTime,
        endTime,
      });
      setPrice(price);
    }
  }, [durationTimeRef, instance, setPrice]);

  const getConfirmationData = useCallback(
    async (params: IParams) => {
      const { data } = await api.venues.createTablePrepare(params);
      setConfirmationData(data);
      if (data?.order) {
        setFrom(data.order.startFrom);
      }
      if ((data as IConfirmationData)?.order?.status == 1) {
        limitRecursiveCheck(data?.order.orderNo).then(() => {
          ref.current &&
            getConfirmationData({
              from,
              tableId: Number(tableId),
            });
        });
      }
    },
    [setConfirmationData, limitRecursiveCheck, ref]
  );

  const onGetPhoneNumber = useCallback(
    async (e) => {
      const { code } = e.detail;
      if (!code) return;
      const resp = await api.user.bindPhone(code);
      if (resp.statusCode !== 204 && resp.statusCode !== 200) {
        return Taro.showToast({
          title: resp.data.message,
          icon: "none",
        });
      }
      const payload = { ...user, hasPhone: true };
      dispatch(login(payload));
      Dialog.close("authorizePhone");
    },
    [user]
  );

  const validatePhone = useCallback(async () => {
    if (!user?.hasPhone) {
      Dialog.open("authorizePhone", {
        title: "授权手机号",
        content: (
          <div>
            <div className="mb-4">
              为了提供更好的售后服务我们希望获取到你的手机号
            </div>
            <Button
              className="bg-primary rounded-sm text-bgt h-10 py-1 px-3 flex items-center justify-center"
              openType="getPhoneNumber"
              onGetPhoneNumber={onGetPhoneNumber}
            >
              授权绑定
            </Button>
          </div>
        ),
        hideCancelButton: true,
        hideConfirmButton: true,
      });
    }
    return !!user?.hasPhone;
  }, [user?.hasPhone, onGetPhoneNumber]);

  const pay = useCallback(async () => {
    if (await validatePhone()) {
      const params: any = {
        from,
        tableId: +(tableId as string),
      };
      const payType = (paymentRef.current as any)?.type;

      if (payType === PAY_TYPE.MEITUAN) {
        const ticket = paymentRef.current?.getCurrent();
        params.extra = { ticketId: ticket?.id };
      } else if (payType === PAY_TYPE.COUPON) {
        const extra = paymentRef.current?.getCurrent();
        params.extra = extra;
      } else {
        const [startTime, endTime] =
          (durationTimeRef?.current as any)?.getData() ?? [];
        params.startTime = startTime;
        params.endTime = endTime;
      }
      const { confirm } = await Taro.showModal({
        title: "提示",
        content: "是否确认支付订单？",
        confirmText: "确定",
      });
      if (!confirm) {
        return;
      }
      params.payType = payType;
      if (tableId) {
        Taro.showLoading({ title: "", mask: true });
        const { data } = await api.venues.startTable(params);
        Taro.hideLoading();
        const { result, extra, message, orderNo } = data;
        if (
          result === RESULT_TYPES.STARTING ||
          result === RESULT_TYPES.SUCCESS
        ) {
          // Taro.redirectTo({ url: `/pages/result/index?code=${orderNo}` });
          getConfirmationData({
            from,
            tableId: Number(tableId),
          });
        } else if (result === RESULT_TYPES.PENDING_PAYMENT) {
          // todo 微信支付接口
          await Taro.requestPayment({
            ...extra,
            package: extra.packageStr,
            success: () => {
              getConfirmationData({
                from,
                tableId: Number(tableId),
              });
            },
            fail: () => {
              api.order.cancelOrder(orderNo).then(() => {
                getConfirmationData({
                  from,
                  tableId: Number(tableId),
                });
              });
            },
          });
        } else {
          Taro.showToast({
            title: message,
            icon: "none",
          });
        }
        if (result === RESULT_TYPES.NOT_ENOUGH_BALANCE) {
          if (payType === PAY_TYPE.NUMBER || payType === PAY_TYPE.SINGLE) {
            // 引导当前用户
            Taro.showToast({
              title: message,
              icon: "none",
              complete: () => {
                setVisible(true);
              },
            });
          }
        }
      }
    }
  }, [durationTimeRef, paymentRef, from, instance, setVisible, validatePhone]);

  useEffect(() => {
    // 获取当前确认信息
    const params = {
      from,
      tableId: Number(tableId),
    };
    getConfirmationData(params);
    return () => {
      ref.current = false;
    };
  }, [instance.router?.params.id]);

  const getIncludes = useCallback(() => {
    if (paymentRef.current) {
      if ((paymentRef.current as any).type === PAYTYPES.NUMBER) return [0];
      if ((paymentRef.current as any).type === PAYTYPES.SINGLE) return [1];
    }

    return [];
  }, [paymentRef.current]);

  const coupons = useMemo(() => {
    if (confirmationData) {
      const { club, wallet } = confirmationData;
      return [
        {
          title: "购买优惠券",
          list: club?.coupons ?? [],
          key: 1,
          actionName: "购买并使用",
        },
        {
          title: "我的优惠券",
          list: wallet?.coupons ?? [],
          key: 2,
          actionName: "使用",
        },
      ];
    }
    return [];
  }, [confirmationData]);

  const makePhoneCall = useCallback(async () => {
    if (confirmationData?.club?.phone) {
      return await Taro.makePhoneCall({
        phoneNumber: confirmationData?.club?.phone,
      });
    }
    throw Error("当前没有可用的手机号码");
  }, [confirmationData?.club?.phone]);

  return (
    <View
      className="flex flex-col h-full p-3"
      style={{ paddingTop: rect.bottom + 5 }}
    >
      <Back />
      <ConfigProvider
        theme={{
          "--nutui-cell-group-title-color": "hsl(var(--primary))",
          "--nutui-cell-title-color": "#fff",
          "--nutui-cell-background-color": "hsl(var(--background-third))",
          "--nutui-cell-group-background-color": "hsl(var(--background-third))",
        }}
      >
        {/* 用户信息 */}
        {confirmationData && (
          <TableCard table={confirmationData.table} className="mb-3">
            <View className="h-full flex flex-col justify-end w-full items-end">
              <View
                onClick={makePhoneCall}
                className="text-muted-foreground text-sm flex flex-col justify-center items-center space-y-1"
              >
                <View className="i-ph-phone-fill w-4 h-4"></View>
                <View className="text-sm">电话</View>
              </View>
            </View>
          </TableCard>
        )}
        {/* 时间选择信息 */}
        {confirmationData &&
          from !== FROM_TYPES.QRCODE &&
          !confirmationData.order && (
            <DurationTime
              ref={durationTimeRef}
              scene={from}
              plans={confirmationData.timePlans}
              onChange={onChangeDurationTime}
              onCalculationPayment={calculationPayment}
            />
          )}
        {/* 订单信息 */}
        {/* {confirmationData && !confirmationData.order && (
          <ConfirmationInfo durationTime={durationTime} price={price} />
        )} */}
        {confirmationData?.order && (
          <OrderPanel order={confirmationData.order} />
        )}
        {/* 支付方式 */}
        {confirmationData &&
          // confirmationData.wallet &&
          (!confirmationData.order ||
            confirmationData.order.userId !== user?.id) && (
            <PaymentMethod
              ref={paymentRef}
              scene={from}
              tableId={confirmationData.table.id}
              deposit={confirmationData.table.deposit}
              wallet={confirmationData.wallet}
              coupons={coupons}
              isOwner={!confirmationData.order}
              onPay={pay}
            />
          )}
        {confirmationData?.table?.status === 2 && !confirmationData?.order && (
          <CellGroup>
            <Cell title="桌台状态" extra="使用中" />
            <Cell title="结束时间" extra={confirmationData?.table?.endTime} />
          </CellGroup>
        )}
        {/* 支付按钮 */}
      </ConfigProvider>

      <View className="fixed left-0 bottom-0 w-full px-4 py-3 bg-bgt">
        <View className="flex justify-between items-center ">
          <View className="text-xl font-semibold">
            {!confirmationData?.order && from !== FROM_TYPES.QRCODE && (
              <View>
                实付款:
                <View className="inline-block text-primary first-letter:text-xs">
                  {toPrice(price, 2)}
                </View>
                <View className="text-muted-foreground text-[24rpx]">
                  若您提前到店,可提前开门计费
                </View>
              </View>
            )}
          </View>
          <View className="flex gap-2">
            {confirmationData?.table?.status === 1 && (
              <Button
                className="bg-primary text-bgt px-4 py-2 font-semibold"
                onClick={() => pay()}
              >
                确认订单
              </Button>
            )}

            {confirmationData?.order && (
              <>
                {dayjs(confirmationData?.order?.startTime).diff(
                  dayjs(),
                  "minute"
                ) > 1 && (
                  <>
                    <QuickStartButton
                      code={confirmationData?.order.orderNo}
                      status={confirmationData.order.status}
                      isOwner={confirmationData.order.userId === user?.id}
                      updateStatus={() => {
                        return getConfirmationData({
                          from,
                          tableId: Number(tableId),
                        });
                      }}
                    />
                    <CancelButton
                      code={confirmationData?.order.orderNo}
                      status={confirmationData.order.status}
                      isOwner={confirmationData.order.userId === user?.id}
                      updateStatus={() => {
                        ref.current = false;
                        return getConfirmationData({
                          from,
                          tableId: Number(tableId),
                        });
                      }}
                    />
                  </>
                )}
                <FinishButton
                  code={confirmationData?.order.orderNo}
                  status={confirmationData.order.status}
                  isOwner={confirmationData.order.userId === user?.id}
                  updateStatus={() => {
                    ref.current = false;
                    return getConfirmationData({
                      from,
                      tableId: Number(tableId),
                    });
                  }}
                />
                <ForceButton
                  isOwner={confirmationData.order.userId === user?.id}
                  status={confirmationData.order.status}
                  getPayType={() => paymentRef.current?.type}
                  code={confirmationData?.order.orderNo}
                  validatePhone={validatePhone}
                  updateStatus={() => {
                    ref.current = false;
                    return getConfirmationData({
                      from,
                      tableId: Number(tableId),
                    });
                  }}
                />
                <AverageButton
                  isOwner={confirmationData.order.userId === user?.id}
                  status={confirmationData.order.status}
                  getPayType={() => paymentRef.current?.type}
                  code={confirmationData?.order.orderNo}
                  validatePhone={validatePhone}
                  updateStatus={() => {
                    ref.current = false;
                    return getConfirmationData({
                      from,
                      tableId: Number(tableId),
                    });
                  }}
                />
              </>
            )}
          </View>
        </View>
        <SafeArea position={"bottom"}></SafeArea>
      </View>

      <Popup
        position="bottom"
        closeable
        visible={visible}
        onClose={() => setVisible(false)}
        style={{
          width: "100%",
          height: "100%",
          backgroundColor: "#000",
        }}
      >
        {confirmationData && visible && (
          <RechargeTab
            id={Number(confirmationData.club.id)}
            includes={getIncludes()}
            onSuccess={() => setVisible(false)}
          />
        )}
      </Popup>
      <ConfigProvider
        theme={{
          "--nutui-gray-7": "#fff",
          "--nutui-gray-6": "#fff",
          "--nutui-dialog-header-font-weight": "600",
          "--nutui-dialog-header-font-size": "1.25rem",
          "--nutui-dialog-padding": "40rpx",
        }}
      >
        <Dialog id="authorizePhone" />
      </ConfigProvider>
    </View>
  );
};
