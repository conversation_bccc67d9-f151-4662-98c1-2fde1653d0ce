package tech.wejoy.billiard.common.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.utils.BeanUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.*;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.CompetitionMatchStatusEnum;
import tech.wejoy.billiard.common.enums.CompetitionScheduleStatusEnum;
import tech.wejoy.billiard.common.enums.CompetitionStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.CompetitionManager;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class CompetitionService {

    private final CompetitionManager competitionManager;

    private final ClubManager clubManager;

    private final ClientUserManager clientUserManager;

    public Page<CompetitionBo> list(CompetitionQueryDto dto) {
        Page<CompetitionInfo> list = competitionManager.list(dto);
        Page<CompetitionBo> page = list.convert(v -> BeanUtils.copy(v, CompetitionBo.class));
        List<Long> clubIds = page.getRecords().stream().map(CompetitionBo::getClubId).toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(ClubInfo::getId, v -> v));
        page.getRecords().forEach(v -> {
            ClubInfo clubInfo = clubMap.get(v.getClubId());
            if (clubInfo != null) {
                v.setClub(ApiClubBo.from(clubInfo));
            }
        });
        return page;
    }

    public void sign(SignUpDto dto) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(dto.getCompetitionId());
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        List<CompetitionApply> applyList = competitionManager.findApplyByCompetitionId(dto.getCompetitionId());
        if (applyList.stream().anyMatch(v -> v.getUserId().equals(dto.getUserId()))) {
            throw new RuntimeException("已报名");
        }
        if (applyList.stream().filter(v -> v.getEnable().equals(IsEnum.TRUE)).count() >= competitionInfo.getMaxPlayer()) {
            throw new RuntimeException("报名人数已满");
        }
        CompetitionApply apply = BeanUtils.copy(dto, CompetitionApply.class);
        apply.setUsername(dto.getRealName());
        apply.setEnable(IsEnum.TRUE);
        if (competitionInfo.getEntryFee().equals(BigDecimal.ZERO)) {
            apply.setPay(IsEnum.TRUE);
        } else {
            apply.setPay(IsEnum.FALSE);
        }
        competitionManager.saveApply(apply);
    }

    public CompetitionBo detail(Long id, Long userId) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(id);
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        CompetitionBo bo = BeanUtils.copy(competitionInfo, CompetitionBo.class);
        ClubInfo clubInfo = clubManager.fetchClubById(bo.getClubId());
        if (clubInfo != null) {
            bo.setClub(ApiClubBo.from(clubInfo));
        }
        List<CompetitionAward> awards = competitionManager.fetchAwardsByCompetitionId(id);
        List<CompetitionApply> applyList = competitionManager.findApplyByCompetitionId(id);
        List<Long> userIds = applyList.stream().map(CompetitionApply::getUserId).toList();
        List<ClientUser> users = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        Map<Long, CompetitionApply> collect = applyList.stream().collect(Collectors.toMap(CompetitionApply::getUserId, v -> v));
        bo.setAwards(awards.stream().map(v -> BeanUtils.copy(v, CompetitionAwardBo.class)).toList());
        List<CompetitionMatch> matches = competitionManager.fetchMatchesByCompetitionId(id);
        bo.setSignUpCount(applyList.stream().filter(v -> v.getEnable().equals(IsEnum.TRUE)).count());
        bo.setRanks(applyList.stream().filter(v -> v.getEnable().equals(IsEnum.TRUE)).filter(v -> v.getRank() != null && v.getRank() != 0).map(v -> {
            CompetitionApplyBo copy = BeanUtils.copy(v, CompetitionApplyBo.class);
            ClientUser user = userMap.get(v.getUserId());
            if (user != null) {
                copy.setAvatar(user.getAvatar());
            }
            return copy;
        }).toList());

        bo.setMatches(matches.stream().map(v -> {
            CompetitionMatchBo copy = BeanUtils.copy(v, CompetitionMatchBo.class);
            ClientUser user1 = userMap.get(v.getPlayer1UserId());
            if (user1 != null) {
                copy.setPlayer1Avatar(user1.getAvatar());
            }
            ClientUser user2 = userMap.get(v.getPlayer2UserId());
            if (user2 != null) {
                copy.setPlayer2Avatar(user2.getAvatar());
            }
            CompetitionApply apply1 = collect.get(v.getPlayer1UserId());
            if (apply1 != null) {
                copy.setPlayer1Name(apply1.getUsername());
                copy.setPlayer1Number(apply1.getNumber());
            }
            CompetitionApply apply2 = collect.get(v.getPlayer2UserId());
            if (apply2 != null) {
                copy.setPlayer2Name(apply2.getUsername());
                copy.setPlayer2Number(apply2.getNumber());
            }
            return copy;
        }).toList());
        List<CompetitionSchedule> schedules = competitionManager.fetchSchedulesByCompetitionId(id);
        bo.setSchedules(schedules.stream().map(v -> BeanUtils.copy(v, CompetitionScheduleBo.class)).toList());
        if (userId != null) {
            CompetitionApply apply = competitionManager.findApplyByCompetitionIdAndUserId(id, userId);
            if (apply != null) {
                bo.setSignUp(true);
                bo.setApply(BeanUtils.copy(apply, CompetitionApplyBo.class));
            }
        }
        return bo;
    }

    public Page<CompetitionBo> adminList(AdminCompetitionQueryDto dto) {
        Page<CompetitionInfo> list = competitionManager.adminList(dto);
        return list.convert(v -> BeanUtils.copy(v, CompetitionBo.class));
    }

    public void apply(CompetitionApplyDto dto) {
        CompetitionBusinessApply competitionInfo = BeanUtils.copy(dto, CompetitionBusinessApply.class);
        competitionManager.saveBusinessApply(competitionInfo);
    }

    public Page<CompetitionBusinessApplyBo> applyList(AdminCompetitionQueryDto dto) {
        Page<CompetitionBusinessApply> list = competitionManager.applyList(dto);
        return list.convert(v -> BeanUtils.copy(v, CompetitionBusinessApplyBo.class));
    }

    public void draw(Long id, Long userId) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(id);
        List<CompetitionApply> applyList = competitionManager.findApplyByCompetitionId(id);
        Set<Integer> numbers = applyList.stream().map(CompetitionApply::getNumber).filter(Objects::nonNull).collect(Collectors.toSet());
        CompetitionApply apply = applyList.stream().filter(v -> v.getUserId().equals(userId)).findFirst().orElse(null);
        if (apply == null) {
            throw new RuntimeException("您未报名比赛");
        }
        int size = competitionInfo.getMaxPlayer();
        while (true) {
            int number = (int) (Math.random() * size) + 1;
            if (!numbers.contains(number)) {
                apply.setNumber(number);
                competitionManager.saveApply(apply);
                break;
            }
        }
    }

    public Page<CompetitionBo> myList(CompetitionQueryDto dto) {
        List<CompetitionApply> competitionApplies = competitionManager.fetchApplyByUserId(dto.getUserId());
        if (competitionApplies.isEmpty()) {
            return dto.empty();
        }
        List<Long> competitionIds = competitionApplies.stream().map(CompetitionApply::getCompetitionId).toList();
        dto.setIds(competitionIds);
        Page<CompetitionInfo> list = competitionManager.myList(dto);
        Page<CompetitionBo> page = list.convert(v -> BeanUtils.copy(v, CompetitionBo.class));
        List<Long> clubIds = page.getRecords().stream().map(CompetitionBo::getClubId).toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(ClubInfo::getId, v -> v));
        page.getRecords().forEach(v -> {
            ClubInfo clubInfo = clubMap.get(v.getClubId());
            if (clubInfo != null) {
                v.setClub(ApiClubBo.from(clubInfo));
            }
        });
        return page;
    }

    @Transactional(rollbackOn = Exception.class)
    public void start(Long id) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(id);
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        if (!competitionInfo.getStatus().equals(CompetitionStatusEnum.PREPARE) && !competitionInfo.getStatus().equals(CompetitionStatusEnum.SIGN_UP)) {
            throw new RuntimeException("比赛状态不正确");
        }
        List<CompetitionApply> applyList = competitionManager.findApplyByCompetitionId(id);
        // if (applyList.stream().filter(v -> v.getEnable().equals(IsEnum.TRUE)).count() < competitionInfo.getMaxPlayer()) {
        //     throw new RuntimeException("报名人数未满");
        // }
        competitionInfo.setStatus(CompetitionStatusEnum.START);
        Set<Integer> numbers = applyList.stream().map(CompetitionApply::getNumber).filter(Objects::nonNull).collect(Collectors.toSet());
        int size = competitionInfo.getMaxPlayer();
        int numberSize = size;
        while (applyList.size() <= numberSize / 2) {
            numberSize = numberSize / 2;
        }
        for (CompetitionApply competitionApply : applyList) {
            if (competitionApply.getNumber() == null) {
                while (true) {
                    int number = (int) (Math.random() * numberSize) + 1;
                    if (!numbers.contains(number)) {
                        competitionApply.setNumber(number);
                        competitionManager.saveApply(competitionApply);
                        numbers.add(number);
                        break;
                    }
                }
            }
        }
        List<CompetitionSchedule> schedules = competitionManager.fetchSchedulesByCompetitionId(id);
        List<CompetitionMatch> matches = new ArrayList<>();
        for (CompetitionSchedule schedule : schedules) {
            for (int i = 1; i <= schedule.getPlayerCount() / 2; i++) {
                CompetitionMatch match = new CompetitionMatch();
                match.setCompetitionId(id);
                match.setRound(schedule.getRound());
                match.setScheduleId(schedule.getId());
                match.setStatus(CompetitionMatchStatusEnum.PREPARE);
                match.setPlayer1Score(0);
                match.setPlayer2Score(0);
                int number = i;
                match.setNumber(number);
                if (schedule.getRound() == 1) {
                    applyList.stream().filter(v -> v.getNumber().equals(number)).findFirst().ifPresent(player1 -> match.setPlayer1UserId(player1.getUserId()));
                    applyList.stream().filter(v -> v.getNumber().equals(size - number + 1)).findFirst().ifPresent(player2 -> match.setPlayer2UserId(player2.getUserId()));
                }
                matches.add(match);
            }
        }
        competitionManager.saveMatches(matches);
        // for (CompetitionSchedule schedule : schedules) {
        //     List<CompetitionMatch> list = competitionManager.fetchMatchesByScheduleId(schedule.getId());
        //
        //     for (CompetitionMatch match : list) {
        //         if (match.getPlayer1UserId() == null && match.getPlayer2UserId() != null) {
        //             handleMatchWin(schedule, match, match.getPlayer2UserId());
        //         } else if (match.getPlayer1UserId() != null && match.getPlayer2UserId() == null) {
        //             handleMatchWin(schedule, match, match.getPlayer1UserId());
        //         }
        //     }
        // }
    }


    public void matchScoreAdd(Long id, Long userId) {
        CompetitionMatch competitionMatch = competitionManager.fetchMatchById(id);
        if (competitionMatch == null) {
            throw new RuntimeException("比赛不存在");
        }
        CompetitionSchedule schedule = competitionManager.fetchScheduleById(competitionMatch.getScheduleId());
        if (schedule == null) {
            throw new RuntimeException("比赛日程不存在");
        }
        if (!schedule.getStatus().equals(CompetitionScheduleStatusEnum.ONGOING)) {
            throw new RuntimeException("比赛日程不在进行中");
        }
        competitionMatch.setStatus(CompetitionMatchStatusEnum.START);
        if (Objects.equals(userId, competitionMatch.getPlayer1UserId())) {
            competitionMatch.setPlayer1Score(competitionMatch.getPlayer1Score() + 1);
        } else if (Objects.equals(userId, competitionMatch.getPlayer2UserId())) {
            competitionMatch.setPlayer2Score(competitionMatch.getPlayer2Score() + 1);
        } else {
            throw new RuntimeException("选手不匹配");
        }
        if (competitionMatch.getPlayer1Score() >= schedule.getWinScore()) {
            competitionMatch.setStatus(CompetitionMatchStatusEnum.END);
            competitionMatch.setWinner(competitionMatch.getPlayer1UserId());
            competitionMatch.setLoser(competitionMatch.getPlayer2UserId());
        } else if (competitionMatch.getPlayer2Score() >= schedule.getWinScore()) {
            competitionMatch.setStatus(CompetitionMatchStatusEnum.END);
            competitionMatch.setWinner(competitionMatch.getPlayer2UserId());
            competitionMatch.setLoser(competitionMatch.getPlayer1UserId());
        }
        competitionManager.saveMatch(competitionMatch);
        setNextMatch(competitionMatch);
    }

    @Transactional(rollbackOn = Exception.class)
    public void end(Long id) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(id);
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        competitionInfo.setStatus(CompetitionStatusEnum.END);
        competitionManager.saveCompetition(competitionInfo);
    }

    @Transactional(rollbackOn = Exception.class)
    public void awards(Long id) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(id);
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        competitionInfo.setStatus(CompetitionStatusEnum.AWARDS);
        competitionManager.saveCompetition(competitionInfo);
    }

    @Transactional(rollbackOn = Exception.class)
    public void create(CompetitionCreateDto dto) {
        CompetitionBusinessApply apply = competitionManager.findBusinessApplyById(dto.getApplyId());
        if (apply == null) {
            throw new RuntimeException("申请不存在");
        }
        ClubInfo clubInfo = clubManager.fetchClubById(apply.getClubId());
        CompetitionInfo competitionInfo = BeanUtils.copy(dto, CompetitionInfo.class);
        competitionInfo.setClubId(apply.getClubId());
        competitionInfo.setCity(clubInfo.getDistrict());
        competitionInfo.setTenantId(clubInfo.getTenantId());
        competitionInfo.setLevel(apply.getLevel());
        competitionInfo.setStatus(CompetitionStatusEnum.CREATE);
        dto.getAwards().stream().map(v -> v.getBonus().add(v.getPlanBonus())).reduce(BigDecimal::add).ifPresent(competitionInfo::setBonus);
        competitionManager.saveCompetition(competitionInfo);
        AtomicInteger seq = new AtomicInteger(1);
        List<CompetitionAward> awards = dto.getAwards().stream().map(v -> {
            CompetitionAward copy = BeanUtils.copy(v, CompetitionAward.class);
            copy.setSeq(seq.getAndIncrement());
            return copy;
        }).toList();
        awards.forEach(v -> v.setCompetitionId(competitionInfo.getId()));
        competitionManager.saveAwards(awards);
        List<CompetitionSchedule> schedules = dto.getSchedules().stream().map(v -> {
            CompetitionSchedule copy = BeanUtils.copy(v, CompetitionSchedule.class);
            copy.setStatus(CompetitionScheduleStatusEnum.WAITING);
            return copy;
        }).toList();
        schedules.forEach(v -> {
            v.setCompetitionId(competitionInfo.getId());
            if (v.getLoserGroup() == null) {
                v.setLoserGroup(IsEnum.FALSE);
            }
        });
        competitionManager.saveSchedules(schedules);
        competitionManager.deleteBusinessApply(apply);
    }

    public CompetitionApplyBo drawResult(Long competitionId, Long userId) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(competitionId);
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        CompetitionApply apply = competitionManager.findApplyByCompetitionIdAndUserId(competitionId, userId);
        if (apply == null) {
            return new CompetitionApplyBo();
        }
        return BeanUtils.copy(apply, CompetitionApplyBo.class);
    }

    @Transactional(rollbackOn = Exception.class)
    public void prepare(Long id) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(id);
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        competitionInfo.setStatus(CompetitionStatusEnum.PREPARE);
        competitionManager.saveCompetition(competitionInfo);
    }

    @Transactional(rollbackOn = Exception.class)
    public void scheduleStart(Long id) {
        CompetitionSchedule schedule = competitionManager.fetchScheduleById(id);
        if (schedule == null) {
            throw new RuntimeException("比赛日程不存在");
        }
        schedule.setStatus(CompetitionScheduleStatusEnum.ONGOING);
        competitionManager.saveSchedule(schedule);
    }

    @Transactional(rollbackOn = Exception.class)
    public void scheduleEnd(Long id) {
        CompetitionSchedule schedule = competitionManager.fetchScheduleById(id);
        if (schedule == null) {
            throw new RuntimeException("比赛日程不存在");
        }
        schedule.setStatus(CompetitionScheduleStatusEnum.FINISHED);
        competitionManager.saveSchedule(schedule);
    }

    @Transactional(rollbackOn = Exception.class)
    public void matchScoreReset(Long id) {
        CompetitionMatch competitionMatch = competitionManager.fetchMatchById(id);
        if (competitionMatch == null) {
            throw new RuntimeException("比赛不存在");
        }
        if (competitionMatch.getStatus().equals(CompetitionMatchStatusEnum.END)) {
            throw new RuntimeException("比赛已结束");
        }
        CompetitionSchedule schedule = competitionManager.fetchScheduleById(competitionMatch.getScheduleId());
        if (schedule == null) {
            throw new RuntimeException("比赛日程不存在");
        }
        if (!schedule.getStatus().equals(CompetitionScheduleStatusEnum.ONGOING)) {
            throw new RuntimeException("比赛日程不在进行中");
        }
        competitionMatch.setPlayer1Score(0);
        competitionMatch.setPlayer2Score(0);
        competitionMatch.setStatus(CompetitionMatchStatusEnum.PREPARE);
        competitionManager.saveMatch(competitionMatch);
    }

    @Transactional(rollbackOn = Exception.class)
    public void matchWin(Long id, Long userId) {
        CompetitionMatch competitionMatch = competitionManager.fetchMatchById(id);
        if (competitionMatch == null) {
            throw new RuntimeException("比赛不存在");
        }
        if (competitionMatch.getStatus().equals(CompetitionMatchStatusEnum.END)) {
            throw new RuntimeException("比赛已结束");
        }
        CompetitionSchedule schedule = competitionManager.fetchScheduleById(competitionMatch.getScheduleId());
        if (schedule == null) {
            throw new RuntimeException("比赛日程不存在");
        }
        if (!schedule.getStatus().equals(CompetitionScheduleStatusEnum.ONGOING)) {
            throw new RuntimeException("比赛日程不在进行中");
        }
        handleMatchWin(schedule, competitionMatch, userId);
    }


    private void handleMatchWin(CompetitionSchedule schedule, CompetitionMatch competitionMatch, Long userId) {
        if (userId.equals(competitionMatch.getPlayer1UserId())) {
            competitionMatch.setWinner(competitionMatch.getPlayer1UserId());
            competitionMatch.setLoser(competitionMatch.getPlayer2UserId());
        } else if (userId.equals(competitionMatch.getPlayer2UserId())) {
            competitionMatch.setWinner(competitionMatch.getPlayer2UserId());
            competitionMatch.setLoser(competitionMatch.getPlayer1UserId());
        } else {
            throw new RuntimeException("选手不匹配");
        }
        competitionMatch.setStatus(CompetitionMatchStatusEnum.END);
        competitionManager.saveMatch(competitionMatch);
        if (schedule.getPlayerCount() == 2) {
            if (schedule.getLoserGroup().isTrue()) {
                if (competitionMatch.getWinner() != null) {
                    CompetitionApply apply = competitionManager.findApplyByCompetitionIdAndUserId(competitionMatch.getCompetitionId(), competitionMatch.getWinner());
                    apply.setRank(3);
                    competitionManager.saveApply(apply);
                }
                if (competitionMatch.getLoser() != null) {
                    CompetitionApply apply = competitionManager.findApplyByCompetitionIdAndUserId(competitionMatch.getCompetitionId(), competitionMatch.getLoser());
                    apply.setRank(4);
                    competitionManager.saveApply(apply);
                }
            } else {
                if (competitionMatch.getWinner() != null) {
                    CompetitionApply apply = competitionManager.findApplyByCompetitionIdAndUserId(competitionMatch.getCompetitionId(), competitionMatch.getWinner());
                    apply.setRank(1);
                    competitionManager.saveApply(apply);
                }
                if (competitionMatch.getLoser() != null) {
                    CompetitionApply apply = competitionManager.findApplyByCompetitionIdAndUserId(competitionMatch.getCompetitionId(), competitionMatch.getLoser());
                    apply.setRank(2);
                    competitionManager.saveApply(apply);
                }
            }
        } else {
            Long loser = competitionMatch.getLoser();
            if (loser != null) {
                CompetitionApply apply = competitionManager.findApplyByCompetitionIdAndUserId(competitionMatch.getCompetitionId(), loser);
                List<CompetitionApply> applys = competitionManager.findApplyByCompetitionId(competitionMatch.getCompetitionId());
                Set<Integer> collect = applys.stream().map(CompetitionApply::getRank).collect(Collectors.toSet());
                int rank = schedule.getPlayerCount();
                if (rank > applys.size()) {
                    rank = applys.size();
                }
                while (collect.contains(rank)) {
                    rank--;
                }
                if (apply != null) {
                    apply.setRank(rank);
                    competitionManager.saveApply(apply);
                }
            }
        }
        setNextMatch(competitionMatch);
    }

    private void setNextMatch(CompetitionMatch competitionMatch) {
        CompetitionSchedule schedule = competitionManager.fetchScheduleById(competitionMatch.getScheduleId());
        List<CompetitionMatch> matches = competitionManager.fetchMatchesByScheduleId(competitionMatch.getScheduleId());
        if (matches.stream().allMatch(v -> v.getStatus().equals(CompetitionMatchStatusEnum.END))) {
            schedule.setStatus(CompetitionScheduleStatusEnum.FINISHED);
            competitionManager.saveSchedule(schedule);
        }
        if (schedule.getLoserGroup().isTrue()) {
            return;
        }
        int nextRound = competitionMatch.getRound() + 1;
        CompetitionSchedule nextSchedule = competitionManager.fetchScheduleByCompetitionIdAndRound(competitionMatch.getCompetitionId(), nextRound);
        if (nextSchedule == null) {
            // 比赛结束
            awards(competitionMatch.getCompetitionId());
            return;
        }
        if (nextSchedule.getLoserGroup() == IsEnum.TRUE) {
            CompetitionSchedule finalSchedule = competitionManager.fetchScheduleByCompetitionIdAndRound(competitionMatch.getCompetitionId(), nextSchedule.getRound() + 1);
            int currentNumber = competitionMatch.getNumber();
            int nextNumber;
            if (currentNumber % 2 == 0) {
                nextNumber = currentNumber / 2;
            } else {
                nextNumber = currentNumber / 2 + 1;
            }
            CompetitionMatch nextMatch = competitionManager.fetchMatchByScheduleIdAndNumber(nextSchedule.getId(), nextNumber);
            if (nextMatch == null) {
                throw new BaseException("未找到比赛");
            }
            CompetitionMatch finalMatch = competitionManager.fetchMatchByScheduleIdAndNumber(finalSchedule.getId(), nextNumber);
            if (finalMatch == null) {
                throw new BaseException("未找到比赛");
            }
            if (currentNumber % 2 == 0) {
                finalMatch.setPlayer2UserId(competitionMatch.getWinner());
                nextMatch.setPlayer2UserId(competitionMatch.getLoser());
            } else {
                finalMatch.setPlayer1UserId(competitionMatch.getWinner());
                nextMatch.setPlayer1UserId(competitionMatch.getLoser());
            }
            competitionManager.saveMatch(nextMatch);
            competitionManager.saveMatch(finalMatch);
        } else {
            int currentNumber = competitionMatch.getNumber();
            int nextNumber;
            if (currentNumber % 2 == 0) {
                nextNumber = currentNumber / 2;
            } else {
                nextNumber = currentNumber / 2 + 1;
            }
            CompetitionMatch nextMatch = competitionManager.fetchMatchByScheduleIdAndNumber(nextSchedule.getId(), nextNumber);
            if (nextMatch == null) {
                throw new BaseException("未找到比赛");
            }
            if (currentNumber % 2 == 0) {
                nextMatch.setPlayer2UserId(competitionMatch.getWinner());
            } else {
                nextMatch.setPlayer1UserId(competitionMatch.getWinner());
            }
            competitionManager.saveMatch(nextMatch);
        }
    }

    public List<AdminCompetitionApplyBo> competitionApplyList(Long id) {
        List<CompetitionApply> applyList = competitionManager.findApplyByCompetitionId(id);
        List<Long> userIds = applyList.stream().map(CompetitionApply::getUserId).toList();
        List<ClientUser> users = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(ClientUser::getId, v -> v));
        return applyList.stream().map(v -> {
            AdminCompetitionApplyBo copy = BeanUtils.copy(v, AdminCompetitionApplyBo.class);
            ClientUser user = userMap.get(v.getUserId());
            if (user != null) {
                copy.setAvatar(user.getAvatar());
            }
            return copy;
        }).toList();
    }

    public void matchTable(Long id, Long tableId) {
        CompetitionMatch competitionMatch = competitionManager.fetchMatchById(id);
        if (competitionMatch == null) {
            throw new RuntimeException("比赛不存在");
        }
        competitionMatch.setTableId(tableId);
        competitionManager.saveMatch(competitionMatch);
    }

    public void changeCompetitionStatus(Long id, CompetitionStatusEnum status) {
        CompetitionInfo competitionInfo = competitionManager.findCompetitionById(id);
        if (competitionInfo == null) {
            throw new RuntimeException("比赛不存在");
        }
        competitionInfo.setStatus(status);
        competitionManager.saveCompetition(competitionInfo);
    }
}
