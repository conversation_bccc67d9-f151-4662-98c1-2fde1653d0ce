package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class AssistantOrder extends BaseEntity {

    private Long clubId;

    private Long assistantId;

    private Long tenantId;

    private Long userId;

    private String orderNo;

    @Column(columnDefinition = "text")
    private String description;

    @Schema(description = "订单状态")
    private OrderStatusEnum status;

    @Schema(description = "支付类型")
    private OrderPayTypeEnum payType;

    @Schema(description = "预计开始时间")
    private LocalDateTime startTime;

    @Schema(description = "预计结束时间")
    private LocalDateTime endTime;

    @Schema(description = "实际开始时间")
    private LocalDateTime realStartTime;

    @Schema(description = "实际结束时间")
    private LocalDateTime realEndTime;

    @Schema(description = "支付时间")
    private LocalDateTime payTime;

    @Schema(description = "退款时间")
    private LocalDateTime refundTime;

    @Schema(description = "预计金额")
    private BigDecimal totalAmount;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "退款信息")
    @Column(columnDefinition = "text")
    private String refundInfo;

    @Schema(description = "实际金额")
    private BigDecimal realAmount;

    private String code;

    @Column(columnDefinition = "text")
    private String assistantInfo;

}
