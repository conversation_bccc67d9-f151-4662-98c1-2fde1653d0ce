package tech.wejoy.billiard.common.third.wejoy.request;

import com.congeer.core.bean.BaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum PayTypeEnum implements BaseEnum<PayTypeEnum> {
    WX_JSAPI(PayChannelEnum.WECHAT, "JSAPI"),
    WX_APP(PayChannelEnum.WECHAT, "APP"),
    WX_H5(PayChannelEnum.WECHAT, "H5"),
    WX_NATIVE(PayChannelEnum.WECHAT, "NATIVE"),
    WX_MINI_PROGRAM(PayChannelEnum.WECHAT, "MINI_PROGRAM"),
    ;

    private final PayChannelEnum channel;
    private final String tradeType;

}
