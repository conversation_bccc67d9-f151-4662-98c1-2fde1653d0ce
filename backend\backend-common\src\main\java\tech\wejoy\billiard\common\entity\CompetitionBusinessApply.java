package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.CompetitionLevelEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class CompetitionBusinessApply extends BaseEntity {

    private Long userId;

    private Long clubId;

    private Long tenantId;

    private String phone;

    private CompetitionLevelEnum level;

}
