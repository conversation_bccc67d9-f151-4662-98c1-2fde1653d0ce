package tech.wejoy.billiard.common.service;

import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.bo.MemberRechargePlanBo;
import tech.wejoy.billiard.common.entity.MemberPlan;
import tech.wejoy.billiard.common.manager.MemberManager;

import java.util.List;

@ApplicationScoped
@RequiredArgsConstructor
public class MemberService {

    private final MemberManager memberManager;

    public List<MemberRechargePlanBo> listMemberPlans(int minLevel) {
        List<MemberPlan> memberPlans = memberManager.getMemberPlans(minLevel);
        return memberPlans.stream().map(MemberRechargePlanBo::from).toList();
    }

}
