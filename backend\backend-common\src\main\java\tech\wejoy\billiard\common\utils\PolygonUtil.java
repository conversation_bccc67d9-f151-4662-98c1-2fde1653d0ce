package tech.wejoy.billiard.common.utils;

import java.util.List;

public class PolygonUtil {


    private static final int POLYGON_MIN_SIZE = 3;

    /**
     * 判断点是否在多边形内
     *
     * @param point   假设point点为人的坐标 A 点
     * @param polygon
     * @return
     */
    public static boolean isPointInPolygon(float[] point, List<float[]> polygon) {
        assertParams(point, polygon);
        int count = 0;
        double x1, x2, y1, y2;
        // 人的坐标点水平向右射线与边的交点的x坐标
        double dLon;
        int size = polygon.size();
        for (int index = 0; index < size; index++) {
            if (index == size - 1) {
                x1 = polygon.get(index)[0];
                y1 = polygon.get(index)[1];
                x2 = polygon.get(0)[0];
                y2 = polygon.get(0)[1];
            } else {
                x1 = polygon.get(index)[0];
                y1 = polygon.get(index)[1];
                x2 = polygon.get(index + 1)[0];
                y2 = polygon.get(index + 1)[1];
            }
            /**
             * 以下语句判断人的坐标 A 点 是否在边的两端点的水平平行线之间，在则可能有交点
             * 注意: 如果与直线的顶点相交，则与纵坐标y值大的那个顶点相交，才认为是点与直线相交
             */
            if (((point[1] > y1) && (point[1] <= y2)) || ((point[1] > y2) && (point[1] <= y1))) {
                // line (x1, y1) -> (x2, y2) 不是水平线，即 y1 != y2
                if (Math.abs(y1 - y2) > 0) {
                    /**
                     * 计算以 (point.x, point.y) 点向右的射线与 line (x1, y1) -> (x2, y2) 交点的x坐标，（dLon，point.y）
                     *
                     * point.y - y1       y2 - y1                     (point.y - y1) * (x2 - x1)
                     * ------------  =  -----------  =>  dLon = x1 + -----------------------------
                     * dLon - x1          x2 - x1                              y2 - y1
                     */
                    dLon = x1 + ((point[1] - y1) * (x2 - x1)) / (y2 - y1);
                    // 如果交点在人的坐标 A 点 右侧（说明是做射线与 边的交点），则射线与边的全部交点数加一：
                    if (dLon > point[0]) {
                        count++;
                    }
                }
            }
        }
        // 交点为奇数个，则人在城市内
        return (count % 2) != 0;
    }

    private static void assertParams(float[] point, List<float[]> polygon) {
        if (null == point || null == polygon || polygon.size() < POLYGON_MIN_SIZE) {
            throw new IllegalArgumentException("参数不能为空，且多边形点数大于3");
        }
    }

}
