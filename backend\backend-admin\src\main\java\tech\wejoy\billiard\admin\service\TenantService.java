package tech.wejoy.billiard.admin.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.utils.BeanUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.admin.bo.TenantBo;
import tech.wejoy.billiard.admin.dto.TenantQueryDto;
import tech.wejoy.billiard.admin.dto.TenantUpdateDto;
import tech.wejoy.billiard.admin.manager.TenantManager;
import tech.wejoy.billiard.common.entity.AdminUser;
import tech.wejoy.billiard.common.entity.AdminUserRoleRel;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.entity.TenantInfo;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.manager.ClubManager;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class TenantService {

    private final TenantManager tenantManager;

    private final ClubManager clubManager;

    @Inject
    AdminUserService adminUserService;

    @Inject
    WxBusinessService wxBusinessService;

    Map<String, Long> codeMap = new ConcurrentHashMap<>();

    @Transactional(rollbackOn = Exception.class)
    public TenantBo save(TenantBo tenantBo) {
        TenantInfo info = BeanUtils.copy(tenantBo, TenantInfo.class);
        info.setStatus(IsEnum.TRUE);
        tenantManager.save(info);
        return BeanUtils.copy(info, TenantBo.class);
    }

    public Page<TenantBo> page(TenantQueryDto dto) {
        Page<TenantBo> page = tenantManager.page(dto);
        List<Long> tenantIds = page.getRecords().stream().map(TenantBo::getId).toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByTenantIds(tenantIds);
        Map<Long, List<ClubInfo>> clubGroups = clubInfos.stream().collect(Collectors.groupingBy(ClubInfo::getTenantId));
        for (TenantBo record : page.getRecords()) {
            List<ClubInfo> clubs = clubGroups.get(record.getId());
            if (clubs != null) {
                record.setClubCount(clubs.size());
            }
        }
        return page;
    }

    public void changeStatus(Long id, IsEnum status) {
        tenantManager.changeStatus(id, status);
    }

    public List<TenantBo> options(String name) {
        List<TenantInfo> tenants = tenantManager.findByName(name);
        return BeanUtils.copyList(tenants, TenantBo.class);
    }

    public File bindCode(long id) {
        String code = UUID.randomUUID().toString().replace("-", "");
        TenantInfo tenant = tenantManager.fetchById(id);
        if (tenant == null) {
            throw new BaseException("加盟商不存在");
        }
        codeMap.put(code, tenant.getId());
        return wxBusinessService.getBindQRCode(code);
    }

    public AdminUser bindAccount(String code, String openId) {
        Long id = codeMap.get(code);
        if (id == null) {
            throw new BaseException("绑定码无效");
        }
        AdminUser user = adminUserService.createTenantAdmin(id);
        user.setOpenId(openId);
        codeMap.remove(code);
        AdminUserRoleRel roleRel = new AdminUserRoleRel();
        roleRel.setUserId(user.getId());
        roleRel.setRole("business");
        adminUserService.saveUserRole(roleRel);
        return user;
    }

    public AdminUser bindAccount(String username, String password, String openId) {
        AdminUser user = adminUserService.fetchByUsername(username);
        if (user == null) {
            throw new BaseException("用户不存在");
        }
        AdminUser userByOpenId = adminUserService.getUserByOpenId(openId);
        if (userByOpenId != null) {
            throw new BaseException("用户已绑定其他账号");
        }
        String pass = SecurityHolder.context("business").encryptPassword(password);
        if (!user.getPassword().equals(pass)) {
            throw new BaseException("密码错误");
        }
        if (StringUtils.isNotBlank(user.getOpenId())) {
            throw new BaseException("用户已绑定");
        }
        user.setOpenId(openId);
        return user;
    }

    public TenantInfo getTenantById(Long tenantId) {
        return tenantManager.fetchById(tenantId);
    }

    /**
     * 更新加盟商信息（名称、联系人、联系方式）
     *
     * @param id  加盟商ID
     * @param dto 更新信息
     * @return 更新后的加盟商信息
     */
    @Transactional(rollbackOn = Exception.class)
    public TenantBo updateTenantInfo(Long id, TenantUpdateDto dto) {
        TenantInfo tenant = tenantManager.fetchById(id);
        if (tenant == null) {
            throw new BaseException("加盟商不存在");
        }

        if (dto.getName() != null) {
            tenant.setName(dto.getName());
        }
        if (dto.getContact() != null) {
            tenant.setContact(dto.getContact());
        }
        if (dto.getPhone() != null) {
            tenant.setPhone(dto.getPhone());
        }

        tenantManager.save(tenant);
        return BeanUtils.copy(tenant, TenantBo.class);
    }

}
