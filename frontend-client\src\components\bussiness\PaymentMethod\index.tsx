import CouponChoose from "@/components/bussiness/Coupon";
import { PAYTYPES } from "@/constants";
import { toPrice } from "@/utils";
import { CheckNormal, Checked } from "@nutui/icons-react-taro";
import { Cell, CellGroup } from "@nutui/nutui-react-taro";
import { Text, View } from "@tarojs/components";
import clsx from "clsx";
import {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import {
  VerificationCodeButton,
  VerificationCodeButtonRef,
} from "../VerificationCode";

export interface IPaymentRef {
  type: number;
  getCurrent: VerificationCodeButtonRef["getCurrent"];
}

interface IPaymentProps {
  scene: FROM_TYPES;
  wallet: any;
  deposit?: number;
  tableId?: number;
  coupons: any[];
  onPay: () => void;
  isOwner?: boolean;
}

enum FROM_TYPES {
  NONE,
  WEAPP,
  QRCODE,
}

// TODO: 支付模式
// 他人支付 只允许 钱包支付/微信支付
// 自己支付 允许所有支付方式

export default forwardRef<any, IPaymentProps>((props, ref) => {
  const { scene, tableId, onPay, wallet, coupons, isOwner = true } = props;
  const [extra, setExtra] = useState({});
  const [currentType, setCurrentType] = useState<PAYTYPES>(PAYTYPES.NUMBER);

  const verificationRef = useRef<VerificationCodeButtonRef>(null);

  const status = useCallback(
    (type: PAYTYPES) => {
      return type === currentType ? (
        <Checked color="hsl(var(--primary))" size={20} />
      ) : (
        <CheckNormal size={20} />
      );
    },
    [currentType]
  );

  const onVerificationCode = useCallback(async () => {
    setCurrentType(PAYTYPES.MEITUAN);
  }, [setCurrentType]);

  const onCoupon = useCallback(async (extra) => {
    setExtra(extra);
    setCurrentType(PAYTYPES.COUPON);
  }, []);

  useEffect(() => {
    if ([PAYTYPES.MEITUAN, PAYTYPES.COUPON].includes(currentType)) {
      onPay();
      setCurrentType(PAYTYPES.NUMBER);
    }
  }, [currentType]);

  useEffect(() => {
    if (!isOwner) {
      if (wallet?.memberBalance > 0) setCurrentType(PAYTYPES.SINGLE);
      else setCurrentType(PAYTYPES.WECHAT);
    } else if (currentType == PAYTYPES.NUMBER && wallet?.memberBalance <= 0) {
      if (wallet.clubBalance <= 0) {
        if (scene === FROM_TYPES.WEAPP) setCurrentType(PAYTYPES.WECHAT);
        else setCurrentType(PAYTYPES.DEPOSIT);
      } else {
        setCurrentType(PAYTYPES.SINGLE);
      }
    } else if (currentType == PAYTYPES.SINGLE && wallet.clubBalance <= 0) {
      if (scene === FROM_TYPES.WEAPP) setCurrentType(PAYTYPES.WECHAT);
      else setCurrentType(PAYTYPES.DEPOSIT);
    }
  }, [wallet]);

  useImperativeHandle(
    ref,
    () => {
      const getCurrent =
        currentType == PAYTYPES.MEITUAN
          ? (verificationRef.current as VerificationCodeButtonRef).getCurrent
          : () => {
              return extra;
            };
      return {
        type: currentType,
        getCurrent,
      };
    },
    [currentType, verificationRef]
  );

  return (
    <>
      <CellGroup>
        {tableId && isOwner && (
          <Cell
            title={
              <View className="flex items-center gap-1">
                <Text className="i-ph-ticket-fill w-5 h-5 text-yellow-500" />
                美团券/抖音券
              </View>
            }
            extra={
              <VerificationCodeButton
                tableId={tableId}
                ref={verificationRef}
                onUse={onVerificationCode}
                title={<View className="text-foreground">可用团购券</View>}
                render={() => {
                  const len = (wallet?.tickets || []).length;
                  return (
                    <View
                      onClick={() => verificationRef.current?.setVisible(true)}
                      className={clsx({
                        "text-primary": len > 0,
                        "text-foreground/95": len === 0,
                      })}
                    >
                      {len ? `${len}张可用` : "点击兑换"}
                    </View>
                  );
                }}
              ></VerificationCodeButton>
            }
          />
        )}
        {coupons?.find((v) => v.list.length > 0) && isOwner && (
          <Cell
            title={
              <View className="flex items-center gap-1">
                <Text className="i-ph-ticket-fill w-5 h-5 text-pink-500" />
                优惠券
              </View>
            }
            extra={
              <CouponChoose
                tabs={coupons}
                onUse={onCoupon}
                render={() => {
                  const couponLen = (wallet?.coupons || []).length;
                  return (
                    <View
                      className={clsx({
                        "text-primary": couponLen > 0,
                        "text-foreground/95": couponLen === 0,
                      })}
                    >
                      {couponLen ? `${couponLen}张可用` : "点击购买"}
                    </View>
                  );
                }}
                ckey={wallet?.coupons?.length > 0 ? 2 : 1}
              />
            }
          />
        )}

        {isOwner && (
          <Cell
            title={
              <View className="flex items-center gap-1">
                <Text className="i-ph-crown-simple-fill w-5 h-5 text-primary" />
                会员支付 (余额:{" "}
                <Text className="text-primary">
                  {toPrice(wallet?.memberBalance, 2)}
                </Text>
                )
              </View>
            }
            extra={status(PAYTYPES.NUMBER)}
            onClick={() => setCurrentType(PAYTYPES.NUMBER)}
          ></Cell>
        )}
        <Cell
          title={
            <View className="flex items-center gap-1">
              <Text className="i-iconoir-credit-card-solid w-5 h-5 text-red-500" />
              钱包支付 (余额:{" "}
              <Text className="text-primary">
                {toPrice(wallet?.clubBalance, 2)}
              </Text>
              )
            </View>
          }
          extra={status(PAYTYPES.SINGLE)}
          onClick={() => setCurrentType(PAYTYPES.SINGLE)}
        ></Cell>

        {(!isOwner || scene === FROM_TYPES.WEAPP) && (
          <Cell
            title={
              <View className="flex items-center gap-1">
                <Text className="i-simple-icons-wechat w-5 h-5 text-green-500" />
                微信支付
              </View>
            }
            extra={status(PAYTYPES.WECHAT)}
            onClick={() => setCurrentType(PAYTYPES.WECHAT)}
          ></Cell>
        )}
        {isOwner && scene === FROM_TYPES.QRCODE && (
          <Cell
            title={
              <View className="flex items-center gap-1">
                <Text className="i-ant-design-pay-circle-filled w-5 h-5 text-green-500" />
                微信支付 (预付:{" "}
                <Text className="text-primary">
                  {toPrice(props.deposit || 0, 2)}
                </Text>
                按分钟计算)
              </View>
            }
            extra={status(PAYTYPES.DEPOSIT)}
            onClick={() => setCurrentType(PAYTYPES.DEPOSIT)}
          />
        )}
      </CellGroup>
      {isOwner && (
        <div className="text-xs text-muted-foreground gap-1 flex flex-col">
          <div> 温馨提示:</div>
          <div>
            1、抢桌成功后，请您在5分钟内到店开台，超时将自动计费。开台后五分钟内无法结束订单;
          </div>
          <div>2、台球费按分钟计算，提前结算钱原路返还;</div>
          <div>
            3、美团券/抖音券:请您将团购券截图，点击本页面"美团券/抖音券"-"点击验券"扫码验券成功，即可开台;
          </div>
          <div>
            4、系统会自动识别美团团购券，若无法识别，请尝试重新进入"点击兑换";
          </div>
          <div>
            5、若无法自动识别团购券，请您点击小程序团购券功能-手动验券。
          </div>
          <div>6、优惠券使用之后不会退还。</div>
        </div>
      )}
    </>
  );
});
