package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.GenderEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class AssistantInfo extends BaseEntity {

    private Long userId;

    private String name;

    private String avatar;

    private String phone;

    private LocalDate birth;

    private GenderEnum gender;

    private LocalDate startWork;

    private Integer level;

    private BigDecimal price;

    private String tags;

    private Integer minMinutes;

    private String realName;

    private String idCard;

    private String bankAccount;

    private String bankName;

    private String bankBranch;

    private IsEnum enable;

}
