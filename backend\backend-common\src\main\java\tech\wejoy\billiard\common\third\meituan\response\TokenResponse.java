package tech.wejoy.billiard.common.third.meituan.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * 美团授权令牌响应
 * <p>
 * 响应示例：
 * {
 * "code": 0,
 * "data": {
 * "accessToken": "V2-303759161e825d9c8d782b31e1c5c3b29a9496e2819992baff7bdada7eefbe8aa294e72dfdb2e0151c8bed3cd3ae47c9ab8c8fbf89e1af5ff775a3f2698b978c",
 * "expireIn": 2582471,
 * "refreshToken": "684669e69c5ccf13e451f57b88c2bfe66c5d57ab96b0b65e8a3729107f48175216f701d2868e9b6438e02cafa85416bcd16930c6197db043f62ac393973c6b27",
 * "scope": "all",
 * "opBizCode": "iQq1c-qUQ66Gt4HDR-oFxQ",
 * "opBizName": "授权实体名称"
 * }
 * }
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TokenResponse {
    private Integer code;
    private String msg;
    private DataResponse data;

    // 直接映射内部data字段的数据，方便使用
    private String accessToken;
    private Long expireIn;
    private String refreshToken;
    private String scope;
    private String opBizCode;
    private String opBizName;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class DataResponse {
        private String accessToken;
        private Long expireIn;
        private String refreshToken;
        private String scope;
        private String opBizCode;
        private String opBizName;
    }

    /**
     * 获取accessToken，优先从data字段获取
     */
    public String getAccessToken() {
        if (data != null && data.accessToken != null) {
            return data.accessToken;
        }
        return accessToken;
    }

    /**
     * 获取refreshToken，优先从data字段获取
     */
    public String getRefreshToken() {
        if (data != null && data.refreshToken != null) {
            return data.refreshToken;
        }
        return refreshToken;
    }

    /**
     * 获取expireIn，优先从data字段获取
     */
    public Long getExpiresIn() {
        if (data != null && data.expireIn != null) {
            return data.expireIn;
        }
        return expireIn;
    }
}
