package tech.wejoy.billiard.common.bo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.TableStatusEnum;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@Schema(description = "桌台状态")
public class TableStatusBo {

    @Schema(description = "桌台ID")
    private Long id;

    @Schema(description = "桌台状态")
    private TableStatusEnum status;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    private Long userId;

}
