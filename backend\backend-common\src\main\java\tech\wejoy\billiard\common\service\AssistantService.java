package tech.wejoy.billiard.common.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import com.congeer.web.bean.request.PageRequest;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.*;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.*;
import tech.wejoy.billiard.common.manager.AssistantManager;
import tech.wejoy.billiard.common.manager.ClientUserManager;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.MemberManager;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.utils.PriceUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class AssistantService {

    private final AssistantManager assistantManager;

    private final ClubManager clubManager;

    private final MemberManager memberManager;

    private final ClientUserManager clientUserManager;

    public Page<AssistantBo> list(PageRequest dto) {
        return assistantManager.list(dto).convert(v -> BeanUtils.copy(v, AssistantBo.class));
    }

    public AssistantBo detail(Long id) {
        AssistantInfo detail = assistantManager.fetchById(id);
        if (detail == null) {
            return null;
        }
        AssistantBo ret = BeanUtils.copy(detail, AssistantBo.class);
        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(detail.getId());
        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        ret.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));
        return ret;
    }

    public boolean checkTime(StartAssistantTimeDto dto) {
        List<AssistantOrder> orders = assistantManager.getUsingOrderByAssistantId(dto.getAssistantId());
        for (AssistantOrder order : orders) {
            if (dto.getStartTime().isBefore(order.getEndTime()) && dto.getEndTime().isAfter(order.getStartTime())) {
                return false;
            }
        }
        return true;
    }

    public PriceCalBo calPrice(StartAssistantTimeDto dto) {
        AssistantInfo assistant = assistantManager.fetchAssistantById(dto.getAssistantId());
        if (assistant == null) {
            throw new BaseException("未找到助教");
        }
        BigDecimal price = PriceUtils.calculatePrice(assistant.getPrice(), dto.getStartTime(), dto.getEndTime());
        PriceCalBo bo = new PriceCalBo();
        bo.setPrice(price);
        return bo;
    }

    public AssistantPrepareBo startPrepare(AssistantPrepareDto dto) {
        AssistantInfo detail = assistantManager.fetchById(dto.getAssistantId());
        if (detail == null) {
            throw new BaseException("未找到助教");
        }
        AssistantPrepareBo ret = new AssistantPrepareBo();
        AssistantBo bo = BeanUtils.copy(detail, AssistantBo.class);
        ret.setAssistant(bo);
        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(detail.getId());
        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        bo.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));
        ret.setTimePlans(PriceUtils.getAssistantTimePlan());
        Long userId = dto.getUserId();
        ClientUserMember member = memberManager.getMemberByUserId(userId);
        WalletDetailBo wallet = new WalletDetailBo();
        wallet.setMemberBalance(member.getBalance());
        ret.setWallet(wallet);
        return ret;
    }

    public AssistantResultBo startStatus(String orderNo) {
        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("未找到订单");
        }
        AssistantResultBo ret = new AssistantResultBo();
        ret.setStatus(order.getStatus());
        ret.setAssistantId(order.getAssistantId());
        if (order.getStatus() == OrderStatusEnum.PENDING) {
            ret.setResult(AssistantResultEnum.PENDING_PAYMENT);
        } else if (order.getStatus() == OrderStatusEnum.PAID) {
            ret.setResult(AssistantResultEnum.SUCCESS);
        } else if (order.getStatus() == OrderStatusEnum.USING) {
            ret.setResult(AssistantResultEnum.SUCCESS);
        }
        return ret;
    }

    @Transactional(rollbackOn = Exception.class)
    public AssistantResultBo start(AssistantStartDto dto) {
        AssistantInfo assistant = assistantManager.fetchAssistantById(dto.getAssistantId());
        if (assistant == null) {
            throw new BaseException("未找到助教");
        }
        if (dto.getUserId().equals(assistant.getUserId())) {
            throw new BaseException("不能预约自己");
        }
        ClubInfo club = clubManager.fetchClubById(dto.getClubId());
        if (club == null) {
            throw new BaseException("未找到门店");
        }
        AssistantResultBo ret = new AssistantResultBo();
        ret.setAssistantId(assistant.getId());
        Payment payment = Payment.getPayment(dto.getPayType());
        StartAssistantTimeDto checkTime = new StartAssistantTimeDto();
        checkTime.setAssistantId(dto.getAssistantId());
        checkTime.setStartTime(dto.getStartTime());
        checkTime.setEndTime(dto.getEndTime());
        if (!checkTime(checkTime)) {
            ret.setResult(AssistantResultEnum.OCCUPY);
            ret.setMessage("助教当前时间已被预约");
            return ret;
        }
        BigDecimal totalPrice = PriceUtils.calculatePrice(assistant.getPrice(), dto.getStartTime(), dto.getEndTime());
        AssistantOrder order = createOrderBase(dto, totalPrice);
        order.setAssistantInfo(JsonUtils.toJson(assistant));
        String orderNo = assistantManager.generateOrderNo(order);
        order.setOrderNo(orderNo);
        PayResultBo pay = payment.pay(order, dto.getExtra());
        ret.setOrderNo(orderNo);
        if (pay.isSuccess()) {
            ret.setResult(AssistantResultEnum.SUCCESS);
            order.setStatus(OrderStatusEnum.PAID);
            ret.setStartTime(dto.getStartTime());
            order.setPayTime(LocalDateTime.now());
            assistantManager.saveOrder(order);
        } else if (pay.isNeedPay()) {
            order.setStatus(OrderStatusEnum.PENDING);
            assistantManager.saveOrder(order);
            ret.setResult(AssistantResultEnum.PENDING_PAYMENT);
            ret.setExtra(pay.getExtra());
        } else {
            assistantManager.deleteOrder(order);
            ret.setResult(AssistantResultEnum.NOT_ENOUGH_BALANCE);
            ret.setMessage(pay.getMessage());
            return ret;
        }
        ret.setStatus(order.getStatus());
        return ret;
    }

    private AssistantOrder createOrderBase(AssistantStartDto dto, BigDecimal totalPrice) {
        AssistantOrder order = new AssistantOrder();
        order.setUserId(dto.getUserId());
        order.setAssistantId(dto.getAssistantId());
        order.setClubId(dto.getClubId());
        order.setPayType(dto.getPayType());
        order.setStartTime(dto.getStartTime());
        order.setEndTime(dto.getEndTime());
        order.setTotalAmount(totalPrice);
        order.setRefundAmount(BigDecimal.ZERO);
        order.setCode(RandomStringUtils.randomNumeric(6));
        assistantManager.saveOrder(order);
        return order;
    }

    public void apply(ApplyAssistantDto dto) {
        AssistantApply assistantApply = assistantManager.fetchAssistantApplyByUserId(dto.getUserId());
        if (assistantApply != null && assistantApply.getStatus() == AssistantStatusEnum.APPLY) {
            throw new BaseException("您已经申请过，请等待审核");
        } else if (assistantApply != null && assistantApply.getStatus() == AssistantStatusEnum.PASS) {
            throw new BaseException("您已经是助教了");
        }
        AssistantApply apply = BeanUtils.copy(dto, AssistantApply.class);
        apply.setTags(JsonUtils.toJson(dto.getTags()));
        apply.setStatus(AssistantStatusEnum.APPLY);
        assistantManager.saveApply(apply);
    }

    public Page<AssistantOrderBo> pageOrder(AssistantOrderQueryDto dto) {
        AssistantBo self = getAssistantByUserId(dto.getUserId());
        Page<AssistantOrder> sourcePage = assistantManager.pageOrder(dto);
        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        if (page.getTotal() == 0L) {
            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        }

        List<Long> assistantIds = page.getRecords().stream().map(AssistantOrderBo::getAssistantId).toList();
        List<AssistantInfo> assistants = assistantManager.fetchAssistantByIds(assistantIds);
        Map<Long, AssistantInfo> assistantMap = assistants.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));

        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        page.getRecords().forEach(v -> {
            v.setClubName(clubMap.get(v.getClubId()).getName());
            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());
            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {
                v.setTotalAmount(BigDecimal.ZERO);
            } else {
                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));
            }
            if (assistantMap.get(v.getAssistantId()) != null) {
                v.setAssistantName(assistantMap.get(v.getAssistantId()).getName());
            }
            if (v.getStatus() == OrderStatusEnum.FINISH) {
                v.setEndTime(v.getRealEndTime());
            }
            if (self != null && self.getId().equals(v.getAssistantId())) {
                v.setCode(null);
            }
        });
        return page;
    }

    @Transactional(rollbackOn = Exception.class)
    public OrderFinishResultBo finishOrder(String orderNo) {
        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        AssistantInfo assistantInfo = assistantManager.fetchAssistantById(order.getAssistantId());
        OrderFinishResultBo result = new OrderFinishResultBo();
        if (order.getStatus() == OrderStatusEnum.FINISH) {
            result.setOrderNo(order.getOrderNo());
            result.setStartTime(order.getRealStartTime());
            result.setEndTime(order.getRealEndTime());
            return result;
        }
        if (order.getStatus() != OrderStatusEnum.USING) {
            throw new BaseException("订单无法完成");
        }
        if (order.getRefundTime() != null) {
            throw new BaseException("订单完成中");
        }
        LocalDateTime endTime = LocalDateTime.now();
        if (endTime.isBefore(order.getRealStartTime().plusMinutes(5))) {
            throw new BaseException("订单时间不足5分钟");
        }
        BigDecimal realPrice = PriceUtils.calculatePrice(assistantInfo.getPrice(), order.getRealStartTime(), endTime);
        BigDecimal refundAmount = order.getTotalAmount().subtract(realPrice);
        if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
            RefundResultBo refund = Payment.getPayment(order.getPayType()).refund(order, refundAmount);
            if (refund.isSuccess()) {
                order.setRefundTime(endTime);
                order.setRefundAmount(refund.getAmount());
            }
        }
        order.setStatus(OrderStatusEnum.FINISH);
        order.setRealEndTime(endTime);
        assistantManager.saveOrder(order);
        result.setOrderNo(orderNo);
        result.setStartTime(order.getRealStartTime());
        result.setEndTime(endTime);
        result.setAmount(refundAmount);
        return result;
    }

    @Transactional(rollbackOn = Exception.class)
    public OrderStartResultBo startOrder(String orderNo, String code) {
        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (!order.getCode().equals(code)) {
            throw new BaseException("验证码错误");
        }
        if (order.getStatus() == OrderStatusEnum.USING) {
            throw new BaseException("订单已开始");
        }
        if (order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("订单无法开始");
        }
        order.setRealStartTime(LocalDateTime.now());
        order.setStatus(OrderStatusEnum.USING);
        assistantManager.saveOrder(order);
        OrderStartResultBo result = new OrderStartResultBo();
        result.setOrderNo(orderNo);
        result.setStartTime(order.getRealStartTime());
        result.setEndTime(order.getEndTime());
        return result;
    }

    @Transactional(rollbackOn = Exception.class)
    public void cancelOrder(String orderNo) {
        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PENDING
                && order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("订单无法取消");
        }
        if (order.getStatus() == OrderStatusEnum.PAID) {
            RefundResultBo refund = Payment.getPayment(order.getPayType()).cancel(order, order.getRealAmount());
            if (refund.isSuccess()) {
                order.setRefundTime(LocalDateTime.now());
                order.setRefundAmount(refund.getAmount());
            }
        }
        order.setStatus(OrderStatusEnum.CANCEL);
        assistantManager.saveOrder(order);
    }

    public AssistantBo getAssistantByUserId(Long id) {
        AssistantInfo assistant = assistantManager.fetchAssistantByUserId(id);
        if (assistant == null) {
            return null;
        }
        return BeanUtils.copy(assistant, AssistantBo.class);

    }

    public void payOrder(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        BigDecimal payerTotal = bill.getPayAmount();
        AssistantOrder payInfoOrder = JsonUtils.toObject(payInfo, AssistantOrder.class);
        AssistantOrder order = assistantManager.fetchOrderByNo(payInfoOrder.getOrderNo());
        order.setStatus(OrderStatusEnum.PAID);
        order.setPayAmount(payerTotal);
        order.setPayTime(LocalDateTime.now());
        assistantManager.saveOrder(order);
    }

    public Page<AdminAssistantBo> adminList(AdminAssistantQueryDto dto) {
        Page<AssistantInfo> list = assistantManager.adminList(dto);
        return list.convert(v -> BeanUtils.copy(v, AdminAssistantBo.class));
    }

    @Transactional(rollbackOn = Exception.class)
    public void pass(Long id) {
        AssistantApply apply = assistantManager.fetchAssistantApplyById(id);
        if (apply == null) {
            throw new BaseException("申请不存在");
        }
        if (apply.getStatus() != AssistantStatusEnum.APPLY) {
            throw new BaseException("申请状态错误");
        }
        apply.setStatus(AssistantStatusEnum.PASS);
        assistantManager.saveApply(apply);
        AssistantInfo assistant = BeanUtils.copy(apply, AssistantInfo.class);
        assistant.setId(null);
        assistant.setPrice(BigDecimal.ZERO);
        assistant.setEnable(IsEnum.FALSE);
        assistantManager.saveAssistant(assistant);
    }

    public void reject(Long id) {
        AssistantApply apply = assistantManager.fetchAssistantApplyById(id);
        if (apply == null) {
            throw new BaseException("申请不存在");
        }
        if (apply.getStatus() != AssistantStatusEnum.APPLY) {
            throw new BaseException("申请状态错误");
        }
        apply.setStatus(AssistantStatusEnum.REJECT);
        assistantManager.saveApply(apply);
    }

    public Page<AssistantApplyBo> adminApplyList(AdminAssistantApplyQueryDto dto) {
        Page<AssistantApply> list = assistantManager.adminApplyList(dto);
        return list.convert(v -> BeanUtils.copy(v, AssistantApplyBo.class));
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(AssistantUpdateDto dto) {
        AssistantInfo assistant = assistantManager.fetchAssistantById(dto.getId());
        if (assistant == null) {
            throw new BaseException("未找到助教");
        }
        if (StringUtils.isNotBlank(dto.getAvatar())) {
            assistant.setAvatar(dto.getAvatar());
        }
        if (StringUtils.isNotBlank(dto.getName())) {
            assistant.setName(dto.getName());
        }
        if (dto.getGender() != null) {
            assistant.setGender(dto.getGender());
        }
        if (StringUtils.isNotBlank(dto.getPhone())) {
            assistant.setPhone(dto.getPhone());
        }
        if (dto.getBirth() != null) {
            assistant.setBirth(dto.getBirth());
        }
        if (dto.getPrice() != null) {
            assistant.setPrice(dto.getPrice());
        }
        if (dto.getStartWork() != null) {
            assistant.setStartWork(dto.getStartWork());
        }
        if (dto.getEnable() != null) {
            assistant.setEnable(dto.getEnable());
        }
        if (dto.getTags() != null) {
            assistant.setTags(JsonUtils.toJson(dto.getTags()));
        }
        assistantManager.saveAssistant(assistant);
        if (dto.getClubIds() != null) {
            assistantManager.deleteClubIdsByAssistantId(assistant.getId());
            List<ClubInfo> clubInfos = clubManager.fetchClubByIds(dto.getClubIds());
            Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
            for (Long addId : dto.getClubIds()) {
                AssistantClubRel rel = new AssistantClubRel();
                rel.setAssistantId(assistant.getId());
                rel.setClubId(addId);
                rel.setTenantId(clubMap.get(addId).getTenantId());
                assistantManager.saveClubRel(rel);
            }
        }
    }

    public AssistantOrderDetailBo orderDetail(String orderNo, Long userId) {
        AssistantBo self = getAssistantByUserId(userId);
        AssistantOrder order = assistantManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        AssistantOrderDetailBo ret = new AssistantOrderDetailBo();
        AssistantInfo assistant = assistantManager.fetchAssistantById(order.getAssistantId());
        AssistantBo bo = BeanUtils.copy(assistant, AssistantBo.class);
        ret.setAssistant(bo);
        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(assistant.getId());
        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        bo.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));
        ret.setOrder(BeanUtils.copy(order, AssistantOrderBo.class));
        if (self != null && self.getId().equals(order.getAssistantId())) {
            ret.getOrder().setCode(null);
        }
        ClientUser user = clientUserManager.fetchUserById(order.getUserId());
        if (user != null) {
            ret.getOrder().setUserNickname(user.getNickname());
            ret.getOrder().setUserPhone(user.getPhone());
        }
        return ret;
    }

    public AdminAssistantBo adminDetail(Long id) {
        AssistantInfo detail = assistantManager.fetchAssistantById(id);
        if (detail == null) {
            return null;
        }
        AdminAssistantBo ret = BeanUtils.copy(detail, AdminAssistantBo.class);
        List<AssistantClubRel> clubRelList = assistantManager.fetchClubRelByAssistantId(detail.getId());
        List<Long> clubIds = clubRelList.stream().map(AssistantClubRel::getClubId).toList();
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(clubIds);
        ret.setClubs(BeanUtils.copyList(clubInfos, ApiClubBo.class));
        return ret;
    }

    public Page<AssistantOrderBo> adminOrderList(AdminAssistantOrderQueryDto dto) {
        Page<AssistantOrder> sourcePage = assistantManager.adminOrderList(dto);
        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        if (page.getTotal() == 0L) {
            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        }

        List<Long> assistantIds = page.getRecords().stream().map(AssistantOrderBo::getAssistantId).toList();
        List<AssistantInfo> assistants = assistantManager.fetchAssistantByIds(assistantIds);
        Map<Long, AssistantInfo> assistantMap = assistants.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));

        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        page.getRecords().forEach(v -> {
            v.setClubName(clubMap.get(v.getClubId()).getName());
            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());
            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {
                v.setTotalAmount(BigDecimal.ZERO);
            } else {
                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));
            }
            if (assistantMap.get(v.getAssistantId()) != null) {
                v.setAssistantName(assistantMap.get(v.getAssistantId()).getName());
            }
            if (v.getStatus() == OrderStatusEnum.FINISH) {
                v.setEndTime(v.getRealEndTime());
            }
        });
        return page;
    }

    public Page<AssistantOrderBo> pageTodo(AssistantOrderQueryDto dto) {
        AssistantBo self = getAssistantByUserId(dto.getUserId());
        if (self == null) {
            throw new BaseException("未找到助教");
        }
        dto.setAssistantId(self.getId());
        dto.setUserId(null);
        dto.setStatus(List.of(OrderStatusEnum.PAID, OrderStatusEnum.USING));
        Page<AssistantOrder> sourcePage = assistantManager.pageOrder(dto);
        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        if (page.getTotal() == 0L) {
            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        }

        List<Long> userIds = page.getRecords().stream().map(AssistantOrderBo::getUserId).toList();
        List<ClientUser> users = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));

        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        page.getRecords().forEach(v -> {
            v.setClubName(clubMap.get(v.getClubId()).getName());
            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());
            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {
                v.setTotalAmount(BigDecimal.ZERO);
            } else {
                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));
            }
            ClientUser user = userMap.get(v.getUserId());
            if (user != null) {
                v.setUserNickname(user.getNickname());
                v.setUserPhone(user.getPhone());
            }
            if (v.getStatus() == OrderStatusEnum.FINISH) {
                v.setEndTime(v.getRealEndTime());
            }
            v.setCode(null);
        });
        return page;
    }

    public Page<AssistantOrderBo> pageDone(AssistantOrderQueryDto dto) {
        AssistantBo self = getAssistantByUserId(dto.getUserId());
        if (self == null) {
            throw new BaseException("未找到助教");
        }
        dto.setAssistantId(self.getId());
        dto.setUserId(null);
        dto.setStatus(List.of(OrderStatusEnum.FINISH));
        Page<AssistantOrder> sourcePage = assistantManager.pageOrder(dto);
        Page<AssistantOrderBo> page = sourcePage.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        if (page.getTotal() == 0L) {
            return page.convert(v -> BeanUtils.copy(v, AssistantOrderBo.class));
        }

        List<Long> userIds = page.getRecords().stream().map(AssistantOrderBo::getUserId).toList();
        List<ClientUser> users = clientUserManager.fetchUserByIds(userIds);
        Map<Long, ClientUser> userMap = users.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));

        List<Long> clubIds = page.getRecords().stream().map(AssistantOrderBo::getClubId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        page.getRecords().forEach(v -> {
            v.setClubName(clubMap.get(v.getClubId()).getName());
            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());
            if (v.getPayType() == OrderPayTypeEnum.MEITUAN) {
                v.setTotalAmount(BigDecimal.ZERO);
            } else {
                v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));
            }
            ClientUser user = userMap.get(v.getUserId());
            if (user != null) {
                v.setUserNickname(user.getNickname());
                v.setUserPhone(user.getPhone());
            }
            if (v.getStatus() == OrderStatusEnum.FINISH) {
                v.setEndTime(v.getRealEndTime());
            }
            v.setCode(null);
        });
        return page;
    }

    public List<AssistantBo> clubAssistant(Long clubId) {
        List<AssistantInfo> assistants = assistantManager.fetchAssistantByClub(clubId);
        return BeanUtils.copyList(assistants, AssistantBo.class);
    }

    public AssistantOrderBo adminOrderDetail(Long id) {
        AssistantOrder order = assistantManager.fetchOrderById(id);
        if (order == null) {
            return null;
        }
        AssistantOrderBo ret = BeanUtils.copy(order, AssistantOrderBo.class);
        AssistantInfo assistant = assistantManager.fetchAssistantById(order.getAssistantId());
        ret.setAssistantName(assistant.getName());
        ClubInfo club = clubManager.fetchClubById(order.getClubId());
        ret.setClubName(club.getName());
        ClientUser user = clientUserManager.fetchUserById(order.getUserId());
        ret.setUserNickname(user.getNickname());
        ret.setUserPhone(user.getPhone());
        return ret;
    }

    public List<AssistantClubRequestBo> clubRequestByUserId(Long userId) {
        AssistantBo self = getAssistantByUserId(userId);
        List<AssistantClubRequest> requests = assistantManager.getAssistantClubRequestByAssistantId(self.getId());
        List<AssistantClubRel> relList = assistantManager.fetchAssistantClubByAssistantId(self.getId());
        List<AssistantClubRequestBo> requestList = BeanUtils.copyList(requests, AssistantClubRequestBo.class);
        List<AssistantClubRequestBo> passList = BeanUtils.copyList(relList, AssistantClubRequestBo.class);
        passList.forEach(v -> v.setPass(true));
        List<AssistantClubRequestBo> ret = new ArrayList<>(requestList);
        ret.addAll(passList);
        List<ClubInfo> clubInfos = clubManager.fetchClubByIds(ret.stream().map(AssistantClubRequestBo::getClubId).toList());
        Map<Long, ClubInfo> clubMap = clubInfos.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        for (AssistantClubRequestBo v : ret) {
            ClubInfo clubInfo = clubMap.get(v.getClubId());
            if (clubInfo == null) {
                continue;
            }
            v.setClubName(clubInfo.getName());
            v.setClubImg(clubInfo.getHeadImage());
        }
        return ret;
    }

    public List<AssistantClubRequestBo> adminRequestList(ClubRequestDto dto) {
        if (dto.getClubId() == null) {
            return new ArrayList<>();
        }
        List<AssistantClubRequest> requests = assistantManager.getAssistantClubRequestByClubId(dto.getClubId());
        return updateAssistantInfo(BeanUtils.copyList(requests, AssistantClubRequestBo.class));
    }

    public List<AssistantClubRequestBo> adminPassList(ClubRequestDto dto) {
        if (dto.getClubId() == null) {
            return new ArrayList<>();
        }
        List<AssistantClubRel> relList = assistantManager.fetchAssistantClubByClubId(dto.getClubId());
        return updateAssistantInfo(BeanUtils.copyList(relList, AssistantClubRequestBo.class));
    }

    private List<AssistantClubRequestBo> updateAssistantInfo(List<AssistantClubRequestBo> list) {
        List<Long> assistantIds = list.stream().map(AssistantClubRequestBo::getAssistantId).toList();
        List<AssistantInfo> assistants = assistantManager.fetchAssistantByIds(assistantIds);
        Map<Long, AssistantInfo> assistantMap = assistants.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        list.forEach(v -> {
            AssistantInfo assistant = assistantMap.get(v.getAssistantId());
            if (assistant != null) {
                v.setAssistantName(assistant.getName());
                v.setAssistantAvatar(assistant.getAvatar());
                v.setAssistantPhone(assistant.getPhone());
            }
        });
        return list;
    }

    public void clubRequest(AssistantClubRequestDto dto) {
        AssistantBo self = getAssistantByUserId(dto.getUserId());
        AssistantClubRequest req = assistantManager.fetchAssistantRequestByAssistantIdAndClubId(self.getId(), dto.getClubId());
        if (req != null) {
            throw new BaseException("已经申请过");
        }
        AssistantClubRequest request = new AssistantClubRequest();
        request.setAssistantId(self.getId());
        request.setClubId(dto.getClubId());
        assistantManager.saveAssistantClubRequest(request);
    }

    public void cancelClubRequest(Long clubId, Long userId) {
        AssistantBo self = getAssistantByUserId(userId);
        AssistantClubRequest request = assistantManager.fetchAssistantRequestByClubIdAndAssistantId(clubId, self.getId());
        if (request == null) {
            throw new BaseException("申请不存在");
        }
        assistantManager.deleteAssistantClubRequest(request.getId());
    }

    public void passRequest(Long id) {
        AssistantClubRequest request = assistantManager.fetchAssistantRequestById(id);
        if (request == null) {
            throw new BaseException("申请不存在");
        }
        AssistantClubRel rel = new AssistantClubRel();
        rel.setAssistantId(request.getAssistantId());
        rel.setClubId(request.getClubId());
        assistantManager.saveClubRel(rel);
        assistantManager.deleteAssistantClubRequest(id);
    }

    public void rejectRequest(Long id) {
        assistantManager.deleteAssistantClubRequest(id);
    }

    public void cancelPass(Long id) {
        assistantManager.deleteClubRel(id);
    }

    public void cancelClub(Long clubId, Long userId) {
        AssistantBo self = getAssistantByUserId(userId);
        AssistantClubRel rel = assistantManager.fetchAssistantClubRelByClubIdAndAssistantId(clubId, self.getId());
        if (rel == null) {
            throw new BaseException("关系不存在");
        }
        assistantManager.deleteClubRel(rel.getId());
    }

}
