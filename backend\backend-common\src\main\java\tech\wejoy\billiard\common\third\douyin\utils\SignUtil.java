package tech.wejoy.billiard.common.third.douyin.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

@Slf4j
public class SignUtil {

    /**
     * @param data base64后的密文
     * @return 明文
     * @Description AES解密
     */
    public static String decryptAES(String data, String secret) throws Exception {
        String key = parseSecret(secret);
        String iv = key.substring(16);
        try {
            byte[] encrypted1 = decode(data);// 先用base64解密

            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());

            cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);

            byte[] original = cipher.doFinal(encrypted1);
            String originalString = new String(original);
            return originalString.trim();
        } catch (Exception e) {
            log.error("douyin sign error:{}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * base64编码
     */
    public static String encode(byte[] byteArray) {
        return new String(Base64.getEncoder().encode(byteArray));
    }

    /**
     * base64解码
     */
    public static byte[] decode(String base64EncodedString) {
        return Base64.getDecoder().decode(base64EncodedString);
    }

    private static String parseSecret(String secret) {
        secret = fillSecret(secret);
        secret = cutSecret(secret);
        return secret;
    }

    private static String cutSecret(String secret) {
        if (secret.length() <= 32) {
            return secret;
        }

        int rightCnt = (secret.length() - 32) / 2;
        int leftCnt = secret.length() - 32 - rightCnt;

        return secret.substring(leftCnt, 32 + leftCnt);
    }

    private static String fillSecret(String secret) {
        if (secret.length() >= 32) {
            return secret;
        }
        int rightCnt = (32 - secret.length()) / 2;
        int leftCnt = 32 - secret.length() - rightCnt;

        return "#".repeat(Math.max(0, leftCnt)) +
                secret +
                "#".repeat(rightCnt);
    }

}