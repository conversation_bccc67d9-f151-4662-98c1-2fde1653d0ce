package tech.wejoy.billiard.common.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;

import java.time.LocalDateTime;

@Data
public class TicketBo {

    private Long id;

    private Long userId;

    private TicketChannelEnum channel;

    private Long channelId;

    private CouponTypeEnum type;

    private String name;

    private String description;

    private LocalDateTime endTime;

    private LocalDateTime startTime;

    private Integer minutes;

    private String period;

    private IsEnum status;

}
