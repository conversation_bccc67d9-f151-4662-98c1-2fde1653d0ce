package tech.wejoy.billiard.common.strategy.payment;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.inject.spi.CDI;
import tech.wejoy.billiard.common.bo.PayParamBo;
import tech.wejoy.billiard.common.bo.PayResultBo;
import tech.wejoy.billiard.common.bo.PrepayBo;
import tech.wejoy.billiard.common.bo.TimeSlotBo;
import tech.wejoy.billiard.common.dto.StartTableDto;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.StartFromEnum;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.service.PaymentService;

import java.time.LocalDateTime;
import java.util.List;

public class WechatDepositPayment extends WechatPayment {

    ClubManager clubManager;
    PaymentService paymentService;

    public WechatDepositPayment() {
        super();
        this.clubManager = CDI.current().select(ClubManager.class).get();
        this.paymentService = CDI.current().select(PaymentService.class).get();
    }

    @Override
    public PrepayBo prepay(StartTableDto dto, ClubTable table) {
        if (dto.getFrom() == StartFromEnum.QRCODE) {
            dto.setStartTime(LocalDateTime.now());
            dto.setEndTime(calEndTime(dto, table));
        }
        return super.prepay(dto, table);
    }

    private LocalDateTime calEndTime(StartTableDto dto, ClubTable table) {
        List<TimeSlotBo> timeSlots = JsonUtils.toList(table.getTimeSlots(), TimeSlotBo.class);
        LocalDateTime start = dto.getStartTime().withSecond(0);
        ClubInfo club = clubManager.fetchClubById(table.getClubId());
        Integer memberMaxTime = club.getMemberMaxHours();
        if (memberMaxTime == null) {
            memberMaxTime = 10;
        }
        return calEndTime(timeSlots, start, table.getDeposit(), memberMaxTime).withSecond(0);
    }

    @Override
    public PayResultBo pay(OrderInfo order, Object params) {
        BillInfo bill = createWxOrder(order);
        PayParamBo payParamBo = paymentService.createPayment(bill.getBillNo(), bill.getAmount(), order.getUserId(), "开台押金");
        PayResultBo payResultBo = new PayResultBo();
        payResultBo.setSuccess(false);
        payResultBo.setAmount(order.getTotalAmount());
        payResultBo.setNeedPay(true);
        payResultBo.setExtra(payParamBo);
        order.setRealAmount(order.getTotalAmount());
        order.setPayAmount(order.getTotalAmount());
        return payResultBo;
    }

}
