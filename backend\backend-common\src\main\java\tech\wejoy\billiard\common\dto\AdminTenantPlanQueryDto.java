package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
public class AdminTenantPlanQueryDto extends PageRequest {

    @QueryParam("clubId")
    private Long clubId;

    @QueryParam("tenantId")
    private Long tenantId;

    @QueryParam("phone")
    private String phone;

    @QueryParam("startDate")
    private LocalDate startDate;

    @QueryParam("endDate")
    private LocalDate endDate;

}
