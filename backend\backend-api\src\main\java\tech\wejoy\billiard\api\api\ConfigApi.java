package tech.wejoy.billiard.api.api;

import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.CustomerBo;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.api.dto.BannerQueryDto;
import tech.wejoy.billiard.api.service.ConfigService;
import tech.wejoy.billiard.common.bo.BannerBo;
import tech.wejoy.billiard.common.bo.FeedbackBo;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;

import java.util.List;

@Path("/config")
@Tag(name = "设置")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class ConfigApi {

    private final ConfigService configService;

    @GET
    @Path("/banner")
    @Operation(summary = "获取banner列表")
    public List<BannerBo> bannerList(BannerQueryDto dto) {
        BannerTypeEnum type = dto.getType();
        if (type == null) {
            type = BannerTypeEnum.HOME;
        }
        return configService.getBannerList(type);
    }

    @POST
    @Path("/feedback")
    @Operation(summary = "意见反馈")
    @Authorized
    public void feedback(FeedbackBo dto) {
        Long userId = SecurityHolder.<Long, WxUserBo>session().getUserId();
        dto.setUserId(userId);
        configService.feedback(dto);
    }

    @GET
    @Path("/customer")
    @Operation(summary = "客服信息")
    public CustomerBo getCustomer() {
        return configService.getCustomer();
    }

}
