package tech.wejoy.billiard.api;

import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.FileBo;
import tech.wejoy.billiard.dto.MultipartDto;
import tech.wejoy.billiard.service.FileService;

@Path("/file")
@Tag(name = "文件")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
@Slf4j
public class UploadApi {

    private final FileService fileService;

    @POST
    @Path("/upload")
    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Operation(summary = "上传文件")
    public FileBo sendMultipartData(@BeanParam MultipartDto data) {
        return fileService.createFile(data);
    }

    @POST
    @Path("/delete")
    @Operation(summary = "删除文件")
    public void deleteFile(@QueryParam("id") Long id) {
        fileService.deleteById(id);
    }

}
