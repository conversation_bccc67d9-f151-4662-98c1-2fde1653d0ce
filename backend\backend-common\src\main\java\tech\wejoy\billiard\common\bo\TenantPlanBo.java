package tech.wejoy.billiard.common.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;

@Data
public class TenantPlanBo {

    private Long id;

    private Long tenantId;

    private String name;

    private BigDecimal totalAmount;

    private BigDecimal payAmount;

    private String remark;

    private IsEnum status;

    private IsEnum show;

    private Integer seq;

    private Integer paySeq;

}
