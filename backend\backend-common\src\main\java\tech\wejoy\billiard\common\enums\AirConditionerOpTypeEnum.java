package tech.wejoy.billiard.common.enums;

import com.congeer.core.bean.BaseEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum AirConditionerOpTypeEnum implements BaseEnum<AirConditionerOpTypeEnum> {
    NONE(new int[0]),
    POWER(new int[]{0, 1}),// 0:关机 1:开机
    SPEED(new int[]{0, 1, 2, 3}),// 0:自动 1:低速 2:中速 3:高速
    MODE(new int[]{0, 1, 2, 3, 4}), // 0:制冷 1:制热 2:自动 3:送风 4:除湿
    TEMP(new int[]{16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30}),
    DIRECTION(new int[]{0, 1}),// 0:关闭 1:打开
    ;

    private final int[] values;

    public boolean inValues(int value) {
        for (int v : values) {
            if (v == value) {
                return true;
            }
        }
        return false;
    }

}
