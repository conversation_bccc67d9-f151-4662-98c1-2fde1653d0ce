package tech.wejoy.billiard.api.queue;

import com.congeer.core.utils.TraceHelper;
import io.smallrye.reactive.messaging.annotations.Broadcast;
import io.smallrye.reactive.messaging.rabbitmq.OutgoingRabbitMQMetadata;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.reactive.messaging.Channel;
import org.eclipse.microprofile.reactive.messaging.Emitter;
import org.eclipse.microprofile.reactive.messaging.Message;
import org.eclipse.microprofile.reactive.messaging.Metadata;
import tech.wejoy.billiard.common.dto.DeviceOperationDto;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;

import java.time.ZonedDateTime;

@ApplicationScoped
public class ClubDeviceSend {

    @Channel("club-device")
    @Broadcast
    Emitter<DeviceOperationDto> emitter;

    /**
     * 发送启动订单的指令
     */
    public void send(String orderNo, DeviceOperationTypeEnum type) {
        DeviceOperationDto dto = new DeviceOperationDto();
        dto.setOrderNo(orderNo);
        dto.setType(type);
        OutgoingRabbitMQMetadata metadata = new OutgoingRabbitMQMetadata.Builder()
                .withHeader("Trace-Id", TraceHelper.get())
                .withTimestamp(ZonedDateTime.now())
                .build();
        emitter.send(Message.of(dto, Metadata.of(metadata)));
    }

}
