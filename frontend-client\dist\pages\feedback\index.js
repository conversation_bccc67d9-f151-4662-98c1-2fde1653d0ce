"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/feedback/index"],{

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/TextArea/style/css.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/TextArea/style/css.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./style.css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/TextArea/style/style.css");


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/TextArea/style/style.css":
/*!********************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/TextArea/style/style.css ***!
  \********************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/Uploader/style/css.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/Uploader/style/css.js ***!
  \*****************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./style.css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Uploader/style/style.css");


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/Uploader/style/style.css":
/*!********************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/Uploader/style/style.css ***!
  \********************************************************************************/
/***/ (function() {

// extracted by mini-css-extract-plugin


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/index-DeNfBlua.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/index-DeNfBlua.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   a: function() { return /* binding */ isPromise; }
/* harmony export */ });
/* unused harmony exports c, i, p */
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_typeof_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/typeof.js */ "./node_modules/@babel/runtime/helpers/esm/typeof.js");

var isObject = function isObject(val) {
  return val !== null && (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_typeof_js__WEBPACK_IMPORTED_MODULE_0__["default"])(val) === "object";
};
var isFunction = function isFunction(val) {
  return typeof val === "function";
};
var isPromise = function isPromise(val) {
  return isObject(val) && isFunction(val.then) && isFunction(val.catch);
};
var clamp = function clamp(num, min, max) {
  return Math.min(Math.max(num, min), max);
};
function preventDefault(event, isStopPropagation) {
  if (typeof event.cancelable !== "boolean" || event.cancelable) {
    event.preventDefault();
  }
  if (isStopPropagation) {
    event.stopPropagation();
  }
}


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/progress.taro-cveY9Y_I.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/progress.taro-cveY9Y_I.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   P: function() { return /* binding */ Progress; }
/* harmony export */ });
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/defineProperty.js */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var _tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tslib.es6-iWu3F_1J.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/tslib.es6-iWu3F_1J.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./typings-DV9RBfhj.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/typings-DV9RBfhj.js");
/* harmony import */ var _configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./configprovider.taro-DpK4IiCE.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _use_uuid_BvqmbYZ7_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./use-uuid-BvqmbYZ7.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/use-uuid-BvqmbYZ7.js");









var defaultProps = Object.assign(Object.assign({}, _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_3__.C), {
  percent: 0,
  showText: false,
  animated: false,
  lazy: false,
  delay: 0
});
var Progress = function Progress(props) {
  var rtl = (0,_configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_4__.a)();
  var _a = Object.assign(Object.assign({}, defaultProps), props),
    className = _a.className,
    style = _a.style,
    percent = _a.percent,
    background = _a.background,
    color = _a.color,
    strokeWidth = _a.strokeWidth,
    showText = _a.showText,
    animated = _a.animated,
    children = _a.children,
    lazy = _a.lazy,
    delay = _a.delay,
    rest = (0,_tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_5__._)(_a, ["className", "style", "percent", "background", "color", "strokeWidth", "showText", "animated", "children", "lazy", "delay"]);
  var classPrefix = "nut-progress";
  var classesInner = classnames__WEBPACK_IMPORTED_MODULE_1___default()((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_6__["default"])((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_6__["default"])({}, "".concat(classPrefix, "-inner"), true), "".concat(classPrefix, "-active"), animated));
  var stylesOuter = {
    height: "".concat(strokeWidth, "px"),
    background: background
  };
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__["default"])(_useState, 2),
    displayPercent = _useState2[0],
    setDispalyPercent = _useState2[1];
  var stylesInner = {
    width: "".concat(displayPercent, "%"),
    background: color
  };
  var handlePercent = function handlePercent() {
    var timer = null;
    if (delay) {
      setDispalyPercent(0);
      timer = setTimeout(function () {
        setDispalyPercent(percent);
      }, delay);
    }
    return function () {
      lazy && resetObserver(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getEnv());
      timer && clearTimeout(timer);
    };
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    setDispalyPercent(percent);
  }, [percent]);
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__["default"])(_useState3, 2),
    intersecting = _useState4[0],
    setIntersecting = _useState4[1];
  var progressRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  var webObserver = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  var uuid = (0,_use_uuid_BvqmbYZ7_js__WEBPACK_IMPORTED_MODULE_8__.u)();
  var selector = "".concat(classPrefix, "-lazy-").concat(uuid);
  var resetObserver = function resetObserver(env) {
    var observer = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;
    if (env === "WEB") {
      webObserver.current.disconnect && webObserver.current.disconnect();
    } else {
      observer && observer.disconnect();
    }
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (lazy) {
      setTimeout(function () {
        if (intersecting) {
          setDispalyPercent(percent);
        } else {
          setDispalyPercent(0.01);
        }
      }, delay);
    }
  }, [intersecting]);
  var handleWebObserver = function handleWebObserver() {
    if (lazy) {
      webObserver.current = new IntersectionObserver(function (entires, self) {
        entires.forEach(function (item) {
          setIntersecting(item.isIntersecting);
        });
      }, {
        threshold: [0],
        rootMargin: "0px"
      });
      webObserver.current.observe(progressRef.current);
    }
    handlePercent();
  };
  var handleOtherObserver = function handleOtherObserver() {
    var observer = null;
    if (lazy) {
      observer = _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().createIntersectionObserver(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getCurrentInstance().page, {
        thresholds: [0],
        observeAll: true
      });
      observer.relativeToViewport({
        top: 0
      }).observe("#".concat(selector), function (res) {
        setIntersecting(res.intersectionRatio > 0);
      });
    }
    handlePercent();
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (_tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getEnv() === "WEB") {
      handleWebObserver();
    } else {
      handleOtherObserver();
    }
  }, []);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", Object.assign({
    ref: progressRef,
    id: selector,
    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(classPrefix, className),
    style: style
  }, rest), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "".concat(classPrefix, "-outer"),
    style: stylesOuter
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: classesInner,
    style: stylesInner
  }, showText && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "".concat(classPrefix, "-text"),
    style: rtl ? {
      right: "".concat(displayPercent, "%")
    } : {
      left: "".concat(displayPercent, "%")
    }
  }, children || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "".concat(classPrefix, "-text-inner"),
    style: {
      background: color
    }
  }, percent, "%")))));
};
Progress.displayName = "NutProgress";


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/textarea.taro-CGMI5QNw.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/textarea.taro-CGMI5QNw.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   T: function() { return /* binding */ TextArea; }
/* harmony export */ });
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/defineProperty.js */ "./node_modules/@babel/runtime/helpers/esm/defineProperty.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tslib.es6-iWu3F_1J.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/tslib.es6-iWu3F_1J.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./configprovider.taro-DpK4IiCE.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./typings-DV9RBfhj.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/typings-DV9RBfhj.js");
/* harmony import */ var _use_props_value_SH9krhkx_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./use-props-value-SH9krhkx.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/use-props-value-SH9krhkx.js");










var defaultProps = Object.assign(Object.assign({}, _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_3__.C), {
  defaultValue: "",
  showCount: false,
  maxLength: 140,
  readOnly: false,
  disabled: false,
  autoSize: false
});
var TextArea = function TextArea(props) {
  var _useConfig = (0,_configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_4__.u)(),
    locale = _useConfig.locale;
  var _a = Object.assign(Object.assign({}, defaultProps), props),
    className = _a.className,
    value = _a.value,
    defaultValue = _a.defaultValue,
    showCount = _a.showCount,
    maxLength = _a.maxLength,
    rows = _a.rows,
    placeholder = _a.placeholder,
    readOnly = _a.readOnly,
    disabled = _a.disabled,
    autoSize = _a.autoSize,
    style = _a.style,
    onChange = _a.onChange,
    onBlur = _a.onBlur,
    onFocus = _a.onFocus,
    rest = (0,_tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_5__._)(_a, ["className", "value", "defaultValue", "showCount", "maxLength", "rows", "placeholder", "readOnly", "disabled", "autoSize", "style", "onChange", "onBlur", "onFocus"]);
  var classPrefix = "nut-textarea";
  var compositionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);
  var format = function format(value2) {
    if (maxLength !== -1 && value2.length > maxLength) {
      return value2.substring(0, maxLength);
    }
    return value2;
  };
  var _usePropsValue = (0,_use_props_value_SH9krhkx_js__WEBPACK_IMPORTED_MODULE_6__.u)({
      value: value,
      defaultValue: defaultValue,
      finalValue: format(defaultValue),
      onChange: onChange
    }),
    _usePropsValue2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_7__["default"])(_usePropsValue, 2),
    inputValue = _usePropsValue2[0],
    setInputValue = _usePropsValue2[1];
  var handleChange = function handleChange(event) {
    var _a2;
    var text = (_a2 = event === null || event === void 0 ? void 0 : event.detail) === null || _a2 === void 0 ? void 0 : _a2.value;
    if (text) {
      var value2 = compositionRef.current ? text : format(text);
      setInputValue(value2);
    } else {
      setInputValue("");
    }
  };
  var handleFocus = function handleFocus(event) {
    if (disabled) return;
    if (readOnly) return;
    onFocus && onFocus(event);
  };
  var handleBlur = function handleBlur(event) {
    if (disabled) return;
    if (readOnly) return;
    onBlur && onBlur(event);
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: classnames__WEBPACK_IMPORTED_MODULE_1___default()(classPrefix, (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_defineProperty_js__WEBPACK_IMPORTED_MODULE_8__["default"])({}, "".concat(classPrefix, "-disabled"), disabled), className)
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tarojs_components__WEBPACK_IMPORTED_MODULE_9__.Textarea, Object.assign({
    nativeProps: {
      style: style,
      readOnly: readOnly,
      rows: rows,
      onCompositionStart: function onCompositionStart() {
        compositionRef.current = true;
      },
      onCompositionEnd: function onCompositionEnd() {
        compositionRef.current = false;
      }
    },
    className: "".concat(classPrefix, "-textarea"),
    style: _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getEnv() === "WEB" ? void 0 : style,
    disabled: _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getEnv() === "WEB" ? disabled : disabled || readOnly,
    value: inputValue,
    // @ts-ignore
    onInput: function onInput(e) {
      return handleChange(e);
    },
    onBlur: function onBlur(e) {
      return handleBlur(e);
    },
    onFocus: function onFocus(e) {
      return handleFocus(e);
    },
    autoHeight: autoSize,
    maxlength: maxLength,
    placeholder: placeholder === void 0 ? locale.placeholder : placeholder
  }, rest)), showCount && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "".concat(classPrefix, "-limit")
  }, inputValue.length, "/", maxLength < 0 ? 0 : maxLength));
};
TextArea.displayName = "NutTextArea";


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/uploader.taro-DAWwySrs.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/uploader.taro-DAWwySrs.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   U: function() { return /* binding */ Uploader; }
/* harmony export */ });
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js */ "./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_callSuper_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/callSuper.js */ "./node_modules/@babel/runtime/helpers/esm/callSuper.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_inherits_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/inherits.js */ "./node_modules/@babel/runtime/helpers/esm/inherits.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/createClass.js */ "./node_modules/@babel/runtime/helpers/esm/createClass.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/classCallCheck.js */ "./node_modules/@babel/runtime/helpers/esm/classCallCheck.js");
/* harmony import */ var _tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./tslib.es6-iWu3F_1J.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/tslib.es6-iWu3F_1J.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! classnames */ "./node_modules/classnames/index.js");
/* harmony import */ var classnames__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(classnames__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutui/icons-react-taro */ "./node_modules/@nutui/icons-react-taro/dist/es/index.es.js");
/* harmony import */ var _button_taro_CixO32Hw_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./button.taro-CixO32Hw.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/button.taro-CixO32Hw.js");
/* harmony import */ var _configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./configprovider.taro-DpK4IiCE.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _index_DeNfBlua_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./index-DeNfBlua.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/index-DeNfBlua.js");
/* harmony import */ var _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./typings-DV9RBfhj.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/typings-DV9RBfhj.js");
/* harmony import */ var _use_props_value_SH9krhkx_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./use-props-value-SH9krhkx.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/use-props-value-SH9krhkx.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _progress_taro_cveY9Y_I_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./progress.taro-cveY9Y_I.js */ "./node_modules/@nutui/nutui-react-taro/dist/esm/progress.taro-cveY9Y_I.js");
/* provided dependency */ var document = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/index.js")["document"];


















var UploadOptions = /*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_4__["default"])(function UploadOptions() {
  (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, UploadOptions);
  this.url = "";
  this.name = "file";
  this.fileType = "image";
  this.method = "post";
  this.xhrState = 200;
  this.timeout = 30 * 1e3;
  this.headers = {};
  this.withCredentials = false;
});
var UPLOADING = "uploading";
var SUCCESS = "success";
var ERROR = "error";
var Upload = /*#__PURE__*/function () {
  function Upload(options) {
    (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, Upload);
    this.options = options;
  }
  return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_4__["default"])(Upload, [{
    key: "upload",
    value: function upload() {
      var _a;
      var options = this.options;
      var xhr = new XMLHttpRequest();
      xhr.timeout = options.timeout;
      if (xhr.upload) {
        xhr.upload.addEventListener("progress", function (e) {
          var _a2;
          (_a2 = options.onProgress) === null || _a2 === void 0 ? void 0 : _a2.call(options, e, options);
        }, false);
        xhr.onreadystatechange = function () {
          var _a2, _b;
          if (xhr.readyState === 4) {
            if (xhr.status === options.xhrState) {
              (_a2 = options.onSuccess) === null || _a2 === void 0 ? void 0 : _a2.call(options, xhr.responseText, options);
            } else {
              (_b = options.onFailure) === null || _b === void 0 ? void 0 : _b.call(options, xhr.responseText, options);
            }
          }
        };
        xhr.withCredentials = options.withCredentials;
        xhr.open(options.method, options.url, true);
        for (var _i = 0, _Object$entries = Object.entries(options.headers); _i < _Object$entries.length; _i++) {
          var _Object$entries$_i = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__["default"])(_Object$entries[_i], 2),
            key = _Object$entries$_i[0],
            value = _Object$entries$_i[1];
          xhr.setRequestHeader(key, value);
        }
        (_a = options.onStart) === null || _a === void 0 ? void 0 : _a.call(options, options);
        if (options.beforeXhrUpload) {
          options.beforeXhrUpload(xhr, options);
        } else {
          xhr.send(options.formData);
        }
      } else {
        console.warn("浏览器不支持 XMLHttpRequest");
      }
    }
  }]);
}();
var UploaderTaro = /*#__PURE__*/function (_Upload) {
  function UploaderTaro(options) {
    (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, UploaderTaro);
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_callSuper_js__WEBPACK_IMPORTED_MODULE_7__["default"])(this, UploaderTaro, [options]);
  }
  (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_inherits_js__WEBPACK_IMPORTED_MODULE_8__["default"])(UploaderTaro, _Upload);
  return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_4__["default"])(UploaderTaro, [{
    key: "uploadTaro",
    value: function uploadTaro(uploadFile2, env) {
      var _a;
      var options = this.options;
      if (options.beforeXhrUpload) {
        options.beforeXhrUpload(uploadFile2, options);
        return;
      }
      if (env === "WEB") {
        this.upload();
      } else {
        var uploadTask = uploadFile2({
          url: options.url,
          filePath: options.taroFilePath,
          fileType: options.fileType,
          header: Object.assign({
            "Content-Type": "multipart/form-data"
          }, options.headers),
          //
          formData: options.formData,
          name: options.name,
          success: function success(response) {
            var _a2, _b;
            if (options.xhrState === response.statusCode) {
              (_a2 = options.onSuccess) === null || _a2 === void 0 ? void 0 : _a2.call(options, response, options);
            } else {
              (_b = options.onFailure) === null || _b === void 0 ? void 0 : _b.call(options, response, options);
            }
          },
          fail: function fail(e) {
            var _a2;
            (_a2 = options.onFailure) === null || _a2 === void 0 ? void 0 : _a2.call(options, e, options);
          }
        });
        (_a = options.onStart) === null || _a === void 0 ? void 0 : _a.call(options, options);
        uploadTask.progress(function (res) {
          var _a2;
          (_a2 = options.onProgress) === null || _a2 === void 0 ? void 0 : _a2.call(options, res, options);
        });
      }
    }
  }]);
}(Upload);
var funcInterceptor = function funcInterceptor(interceptor, _ref) {
  var _ref$args = _ref.args,
    args = _ref$args === void 0 ? [] : _ref$args,
    done = _ref.done,
    canceled = _ref.canceled;
  if (interceptor) {
    var returnVal = interceptor.apply(null, args);
    if ((0,_index_DeNfBlua_js__WEBPACK_IMPORTED_MODULE_9__.a)(returnVal)) {
      returnVal.then(function (value) {
        if (value) {
          done(value);
        } else if (canceled) {
          canceled();
        }
      }).catch(function () {});
    } else if (returnVal) {
      done();
    } else if (canceled) {
      canceled();
    }
  } else {
    done();
  }
};
var FileItem = /*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_createClass_js__WEBPACK_IMPORTED_MODULE_4__["default"])(function FileItem() {
  (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_classCallCheck_js__WEBPACK_IMPORTED_MODULE_5__["default"])(this, FileItem);
  this.status = "ready";
  this.message = "";
  this.uid = (/* @__PURE__ */new Date()).getTime().toString();
  this.percentage = 0;
  this.formData = {};
});
var Preview = function Preview(_ref2) {
  var fileList = _ref2.fileList,
    previewType = _ref2.previewType,
    deletable = _ref2.deletable,
    onDeleteItem = _ref2.onDeleteItem,
    handleItemClick = _ref2.handleItemClick,
    previewUrl = _ref2.previewUrl,
    deleteIcon = _ref2.deleteIcon,
    children = _ref2.children;
  var renderIcon = function renderIcon(item) {
    if (item.status === ERROR) {
      return item.failIcon || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__.Failure, {
        color: "#fff"
      });
    }
    return item.loadingIcon || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__.Loading, {
      className: "nut-icon-loading",
      color: "#fff"
    });
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, fileList.length !== 0 && fileList.map(function (item, index) {
    var _a;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview ".concat(previewType),
      key: item.uid
    }, previewType === "picture" && !children && deletable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.View, {
      className: "close",
      onClick: function onClick() {
        return onDeleteItem(item, index);
      }
    }, deleteIcon), previewType === "picture" && !children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-img"
    }, item.status === "ready" ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-progress"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-progress-msg"
    }, item.message)) : item.status !== "success" && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-progress"
    }, renderIcon(item), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-progress-msg"
    }, item.message)), ((_a = item.type) === null || _a === void 0 ? void 0 : _a.includes("image")) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, item.url && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.Image, {
      className: "nut-uploader-preview-img-c",
      style: {
        objectFit: "fill"
      },
      mode: "aspectFit",
      src: item.url,
      onClick: function onClick() {
        return handleItemClick(item, index);
      }
    })) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, previewUrl ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_tarojs_components__WEBPACK_IMPORTED_MODULE_10__.Image, {
      className: "nut-uploader-preview-img-c",
      mode: "aspectFit",
      src: previewUrl,
      onClick: function onClick() {
        return handleItemClick(item, index);
      }
    }) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-img-file"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      onClick: function onClick() {
        return handleItemClick(item, index);
      },
      className: "nut-uploader-preview-img-file-name"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__.Link, {
      color: "#808080"
    }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, " ", item.name)))), item.status === "success" ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "tips"
    }, item.name) : null), previewType === "list" && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-list"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "nut-uploader-preview-img-file-name ".concat(item.status),
      onClick: function onClick() {
        return handleItemClick(item, index);
      }
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__.Link, null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, " ", item.name)), deletable && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__.Del, {
      color: "#808080",
      className: "nut-uploader-preview-img-file-del",
      onClick: function onClick() {
        return onDeleteItem(item, index);
      }
    }), item.status === "uploading" && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_progress_taro_cveY9Y_I_js__WEBPACK_IMPORTED_MODULE_11__.P, {
      percent: item.percentage,
      color: "linear-gradient(270deg, rgba(18,126,255,1) 0%,rgba(32,147,255,1) 32.815625%,rgba(13,242,204,1) 100%)",
      showText: false
    })));
  }));
};
var defaultProps = Object.assign(Object.assign({}, _typings_DV9RBfhj_js__WEBPACK_IMPORTED_MODULE_12__.C), {
  url: "",
  maxCount: 1,
  sizeType: ["original", "compressed"],
  sourceType: ["album", "camera"],
  mediaType: ["image", "video"],
  camera: "back",
  uploadIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__.Photograph, {
    size: "20px",
    color: "#808080"
  }),
  deleteIcon: /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_nutui_icons_react_taro__WEBPACK_IMPORTED_MODULE_3__.Failure, {
    color: "rgba(0,0,0,0.6)"
  }),
  uploadLabel: "",
  previewType: "picture",
  fit: "cover",
  name: "file",
  accept: "*",
  disabled: false,
  autoUpload: true,
  multiple: false,
  maxFileSize: Number.MAX_VALUE,
  data: {},
  headers: {},
  method: "post",
  previewUrl: "",
  xhrState: 200,
  timeout: 1e3 * 30,
  preview: true,
  deletable: true,
  maxDuration: 10,
  beforeDelete: function beforeDelete(file, files) {
    return true;
  }
});
var InternalUploader = function InternalUploader(props, ref) {
  var _useConfig = (0,_configprovider_taro_DpK4IiCE_js__WEBPACK_IMPORTED_MODULE_13__.u)(),
    locale = _useConfig.locale;
  var _a = Object.assign(Object.assign({}, defaultProps), props),
    children = _a.children,
    uploadIcon = _a.uploadIcon,
    deleteIcon = _a.deleteIcon,
    uploadLabel = _a.uploadLabel,
    accept = _a.accept,
    name = _a.name,
    camera = _a.camera,
    defaultValue = _a.defaultValue,
    value = _a.value,
    previewType = _a.previewType,
    fit = _a.fit,
    disabled = _a.disabled,
    multiple = _a.multiple,
    url = _a.url,
    previewUrl = _a.previewUrl,
    headers = _a.headers,
    timeout = _a.timeout,
    method = _a.method,
    xhrState = _a.xhrState,
    data = _a.data,
    preview = _a.preview,
    deletable = _a.deletable,
    maxCount = _a.maxCount,
    maxFileSize = _a.maxFileSize,
    mediaType = _a.mediaType,
    className = _a.className,
    autoUpload = _a.autoUpload,
    sizeType = _a.sizeType,
    sourceType = _a.sourceType,
    maxDuration = _a.maxDuration,
    onStart = _a.onStart,
    onDelete = _a.onDelete,
    _onChange = _a.onChange,
    onFileItemClick = _a.onFileItemClick,
    onProgress = _a.onProgress,
    onSuccess = _a.onSuccess,
    onUpdate = _a.onUpdate,
    onFailure = _a.onFailure,
    onOversize = _a.onOversize,
    beforeUpload = _a.beforeUpload,
    beforeXhrUpload = _a.beforeXhrUpload,
    beforeDelete = _a.beforeDelete,
    restProps = (0,_tslib_es6_iWu3F_1J_js__WEBPACK_IMPORTED_MODULE_14__._)(_a, ["children", "uploadIcon", "deleteIcon", "uploadLabel", "accept", "name", "camera", "defaultValue", "value", "previewType", "fit", "disabled", "multiple", "url", "previewUrl", "headers", "timeout", "method", "xhrState", "data", "preview", "deletable", "maxCount", "maxFileSize", "mediaType", "className", "autoUpload", "sizeType", "sourceType", "maxDuration", "onStart", "onDelete", "onChange", "onFileItemClick", "onProgress", "onSuccess", "onUpdate", "onFailure", "onOversize", "beforeUpload", "beforeXhrUpload", "beforeDelete"]);
  var _usePropsValue = (0,_use_props_value_SH9krhkx_js__WEBPACK_IMPORTED_MODULE_15__.u)({
      value: value,
      defaultValue: defaultValue,
      finalValue: [],
      onChange: function onChange(v) {
        _onChange === null || _onChange === void 0 ? void 0 : _onChange(v);
      }
    }),
    _usePropsValue2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__["default"])(_usePropsValue, 2),
    fileList = _usePropsValue2[0],
    setFileList = _usePropsValue2[1];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__["default"])(_useState, 2),
    uploadQueue = _useState2[0],
    setUploadQueue = _useState2[1];
  var classes = classnames__WEBPACK_IMPORTED_MODULE_1___default()(className, "nut-uploader");
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function () {
    return {
      submit: function submit() {
        Promise.all(uploadQueue).then(function (res) {
          res.forEach(function (i) {
            return i.uploadTaro(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.uploadFile, (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.getEnv)());
          });
        });
      },
      clear: function clear() {
        clearUploadQueue();
      }
    };
  });
  var fileListRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    fileListRef.current = fileList;
  }, [fileList]);
  var clearUploadQueue = function clearUploadQueue() {
    var index = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : -1;
    if (index > -1) {
      uploadQueue.splice(index, 1);
      setUploadQueue(uploadQueue);
    } else {
      setUploadQueue([]);
      setFileList([]);
    }
  };
  var _chooseImage = function _chooseImage() {
    if (disabled) {
      return;
    }
    if (_tarojs_taro__WEBPACK_IMPORTED_MODULE_2___default().getEnv() === "WEB") {
      var el = document.getElementById("taroChooseImage");
      if (el) {
        el === null || el === void 0 ? void 0 : el.setAttribute("accept", accept);
      } else {
        var obj = document.createElement("input");
        obj.setAttribute("type", "file");
        obj.setAttribute("id", "taroChooseImage");
        obj.setAttribute("accept", accept);
        obj.setAttribute("style", "position: fixed; top: -4000px; left: -3000px; z-index: -300;");
        document.body.appendChild(obj);
      }
    }
    if (["WEAPP", "JD", "WEB"].includes((0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.getEnv)()) && _tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.chooseMedia) {
      (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.chooseMedia)({
        count: multiple ? maxCount * 1 - fileList.length : 1,
        /** 文件类型 */
        mediaType: mediaType,
        /** 图片和视频选择的来源 */
        sourceType: sourceType,
        /** 拍摄视频最长拍摄时间，单位秒。时间范围为 3s 至 30s 之间 */
        maxDuration: maxDuration,
        /** 仅对 mediaType 为 image 时有效，是否压缩所选文件 */
        sizeType: sizeType,
        /** 仅在 sourceType 为 camera 时生效，使用前置或后置摄像头 */
        camera: camera,
        fail: function fail(res) {
          onFailure && onFailure(res);
        },
        success: onChangeMedia
      });
    } else {
      (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.chooseImage)({
        count: multiple ? maxCount * 1 - fileList.length : 1,
        // 可以指定是原图还是压缩图，默认二者都有
        sizeType: sizeType,
        sourceType: sourceType,
        success: onChangeImage,
        fail: function fail(res) {
          onFailure && onFailure(res);
        }
      });
    }
  };
  var executeUpload = function executeUpload(fileItem, index) {
    var uploadOption = new UploadOptions();
    uploadOption.name = name;
    uploadOption.url = url;
    uploadOption.fileType = fileItem.type;
    uploadOption.formData = fileItem.formData;
    uploadOption.timeout = timeout * 1;
    uploadOption.method = method;
    uploadOption.xhrState = xhrState;
    uploadOption.headers = headers;
    uploadOption.taroFilePath = fileItem.path;
    uploadOption.beforeXhrUpload = beforeXhrUpload;
    uploadOption.onStart = function (option) {
      clearUploadQueue(index);
      setFileList(fileListRef.current.map(function (item) {
        if (item.uid === fileItem.uid) {
          item.status = "ready";
          item.message = locale.uploader.readyUpload;
        }
        return item;
      }));
      onStart === null || onStart === void 0 ? void 0 : onStart(option);
    };
    uploadOption.onProgress = function (e, option) {
      setFileList(fileListRef.current.map(function (item) {
        if (item.uid === fileItem.uid) {
          item.status = UPLOADING;
          item.message = locale.uploader.uploading;
          item.percentage = e.progress;
          onProgress === null || onProgress === void 0 ? void 0 : onProgress({
            e: e,
            option: option,
            percentage: item.percentage
          });
        }
        return item;
      }));
    };
    uploadOption.onSuccess = function (responseText, option) {
      var list = fileListRef.current.map(function (item) {
        if (item.uid === fileItem.uid) {
          item.status = SUCCESS;
          item.message = locale.uploader.success;
          item.responseText = responseText;
        }
        return item;
      });
      setFileList(list);
      onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({
        responseText: responseText,
        option: option,
        files: list
      });
    };
    uploadOption.onFailure = function (responseText, option) {
      var list = fileListRef.current.map(function (item) {
        if (item.uid === fileItem.uid) {
          item.status = ERROR;
          item.message = locale.uploader.error;
          item.responseText = responseText;
        }
        return item;
      });
      setFileList(list);
      onFailure === null || onFailure === void 0 ? void 0 : onFailure({
        responseText: responseText,
        option: option,
        files: list
      });
    };
    var task = new UploaderTaro(uploadOption);
    if (autoUpload) {
      task.uploadTaro(_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.uploadFile, (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.getEnv)());
    } else {
      uploadQueue.push(new Promise(function (resolve, reject) {
        resolve(task);
      }));
      setUploadQueue(uploadQueue);
    }
  };
  var readFile = function readFile(files) {
    var results = [];
    files.forEach(function (file, index) {
      var _a2, _b;
      var fileType = file.type;
      var filepath = file.tempFilePath || file.path;
      var fileItem = new FileItem();
      if (file.fileType) {
        fileType = file.fileType;
      } else {
        var imgReg = /\.(png|jpeg|jpg|webp|gif)$/i;
        if (!fileType && (imgReg.test(filepath) || filepath.includes("data:image"))) {
          fileType = "image";
        }
      }
      fileItem.path = filepath;
      fileItem.name = filepath;
      fileItem.status = "ready";
      fileItem.type = fileType;
      fileItem.uid = "".concat(fileItem.uid, "_").concat(index);
      fileItem.message = autoUpload ? locale.uploader.readyUpload : locale.uploader.waitingUpload;
      if ((0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_2__.getEnv)() === "WEB") {
        var formData = new FormData();
        for (var _i2 = 0, _Object$entries2 = Object.entries(data); _i2 < _Object$entries2.length; _i2++) {
          var _Object$entries2$_i = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_6__["default"])(_Object$entries2[_i2], 2),
            key = _Object$entries2$_i[0],
            value2 = _Object$entries2$_i[1];
          formData.append(key, value2);
        }
        formData.append(name, file.originalFileObj);
        fileItem.name = (_a2 = file.originalFileObj) === null || _a2 === void 0 ? void 0 : _a2.name;
        fileItem.type = (_b = file.originalFileObj) === null || _b === void 0 ? void 0 : _b.type;
        fileItem.formData = formData;
      } else {
        fileItem.formData = data;
      }
      if (preview) {
        fileItem.url = fileType === "video" ? file.thumbTempFilePath : filepath;
      }
      executeUpload(fileItem, index);
      results.push(fileItem);
    });
    setFileList([].concat((0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_toConsumableArray_js__WEBPACK_IMPORTED_MODULE_16__["default"])(fileList), results));
  };
  var filterFiles = function filterFiles(files) {
    var maximum = maxCount * 1;
    var maximize = maxFileSize * 1;
    var oversizes = new Array();
    var filterFile = files.filter(function (file) {
      if (file.size > maximize) {
        oversizes.push(file);
        return false;
      }
      return true;
    });
    oversizes.length && (onOversize === null || onOversize === void 0 ? void 0 : onOversize(files));
    var currentFileLength = filterFile.length + fileList.length;
    if (currentFileLength > maximum) {
      filterFile.splice(filterFile.length - (currentFileLength - maximum));
    }
    return filterFile;
  };
  var deleted = function deleted(file, index) {
    var deletedFileList = fileList.filter(function (file2, idx) {
      return idx !== index;
    });
    onDelete === null || onDelete === void 0 ? void 0 : onDelete(file, deletedFileList);
    setFileList(deletedFileList);
  };
  var onDeleteItem = function onDeleteItem(file, index) {
    clearUploadQueue(index);
    funcInterceptor(beforeDelete, {
      args: [file, fileList],
      done: function done() {
        return deleted(file, index);
      }
    });
  };
  var onChangeMedia = function onChangeMedia(res) {
    var tempFiles = res.tempFiles;
    var _files = filterFiles(tempFiles);
    if (beforeUpload) {
      beforeUpload(new Array().slice.call(_files)).then(function (f) {
        var _files2 = filterFiles(new Array().slice.call(f));
        if (!_files2.length) res.tempFiles = [];
        readFile(_files2);
      });
    } else {
      readFile(_files);
    }
  };
  var onChangeImage = function onChangeImage(res) {
    var tempFiles = res.tempFiles;
    var _files = filterFiles(tempFiles);
    if (beforeUpload) {
      beforeUpload(new Array().slice.call(_files)).then(function (f) {
        var _files2 = filterFiles(new Array().slice.call(f));
        if (!_files2.length) res.tempFiles = [];
        readFile(_files2);
      });
    } else {
      readFile(_files);
    }
  };
  var handleItemClick = function handleItemClick(file, index) {
    onFileItemClick === null || onFileItemClick === void 0 ? void 0 : onFileItemClick(file, index);
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", Object.assign({
    className: classes
  }, restProps), (children || previewType === "list") && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "nut-uploader-slot"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, children || /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_button_taro_CixO32Hw_js__WEBPACK_IMPORTED_MODULE_17__.B, {
    nativeType: "button",
    size: "small",
    type: "primary"
  }, locale.uploader.list), Number(maxCount) > fileList.length && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_button_taro_CixO32Hw_js__WEBPACK_IMPORTED_MODULE_17__.B, {
    nativeType: "button",
    className: "nut-uploader-input",
    onClick: _chooseImage
  }))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Preview, {
    fileList: fileList,
    previewType: previewType,
    deletable: deletable,
    onDeleteItem: onDeleteItem,
    handleItemClick: handleItemClick,
    previewUrl: previewUrl,
    deleteIcon: deleteIcon,
    children: children
  }), Number(maxCount) > fileList.length && previewType === "picture" && !children && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "nut-uploader-upload ".concat(previewType, " ").concat(disabled ? "nut-uploader-upload-disabled" : "")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "nut-uploader-icon"
  }, uploadIcon, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "nut-uploader-icon-tip"
  }, uploadLabel)), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_button_taro_CixO32Hw_js__WEBPACK_IMPORTED_MODULE_17__.B, {
    nativeType: "button",
    className: "nut-uploader-input",
    onClick: _chooseImage
  })));
};
var Uploader = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(InternalUploader);
Uploader.displayName = "NutUploader";


/***/ }),

/***/ "./node_modules/@nutui/nutui-react-taro/dist/esm/use-uuid-BvqmbYZ7.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@nutui/nutui-react-taro/dist/esm/use-uuid-BvqmbYZ7.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   u: function() { return /* binding */ useUuid; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var idCounter = {};
function uniqueId() {
  var prefix = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : "$nut$";
  if (!idCounter[prefix]) {
    idCounter[prefix] = 0;
  }
  var id = ++idCounter[prefix];
  if (prefix === "$nut$") {
    return "".concat(id);
  }
  return "".concat(prefix).concat(id);
}
function useUuid() {
  var idRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(uniqueId());
  return idRef.current;
}


/***/ }),

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/feedback/index!./src/pages/feedback/index.tsx":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/feedback/index!./src/pages/feedback/index.tsx ***!
  \**********************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SafeArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/safearea.taro-BJpvFsMf.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Uploader_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Uploader/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Uploader/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Uploader__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Uploader */ "./node_modules/@nutui/nutui-react-taro/dist/esm/uploader.taro-DAWwySrs.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_TextArea_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/TextArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/TextArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_TextArea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/TextArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/textarea.taro-CGMI5QNw.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);
/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! clsx */ "./node_modules/clsx/dist/clsx.mjs");
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_6__);
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/api */ "./src/api/index.ts");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");




















var FEEDBACK_TYPES = /*#__PURE__*/function (FEEDBACK_TYPES) {
  FEEDBACK_TYPES[FEEDBACK_TYPES["APP"] = 0] = "APP";
  FEEDBACK_TYPES[FEEDBACK_TYPES["EASY_TO_USE"] = 1] = "EASY_TO_USE";
  FEEDBACK_TYPES[FEEDBACK_TYPES["PAYMENT"] = 2] = "PAYMENT";
  FEEDBACK_TYPES[FEEDBACK_TYPES["SERVICE"] = 3] = "SERVICE";
  FEEDBACK_TYPES[FEEDBACK_TYPES["OTHER"] = 4] = "OTHER";
  return FEEDBACK_TYPES;
}(FEEDBACK_TYPES || {});
var FEEDBACK_LINK = 2;
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_9__.menuRect)();
  var list = [{
    name: "支付问题",
    value: FEEDBACK_TYPES.PAYMENT
  }, {
    name: "易用性问题",
    value: FEEDBACK_TYPES.EASY_TO_USE
  }, {
    name: "App问题",
    value: FEEDBACK_TYPES.APP
  }, {
    name: "服务问题",
    value: FEEDBACK_TYPES.SERVICE
  }, {
    name: "其他问题",
    value: FEEDBACK_TYPES.OTHER
  }];
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(FEEDBACK_TYPES.PAYMENT),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_11__["default"])(_useState, 2),
    value = _useState2[0],
    setValue = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(""),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_11__["default"])(_useState3, 2),
    content = _useState4[0],
    setContent = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)([]),
    _useState6 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_11__["default"])(_useState5, 2),
    images = _useState6[0],
    setImages = _useState6[1];
  var create = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_12__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_13__["default"])().mark(function _callee() {
    var params, _yield$api$config$fee, message;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_13__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          params = {
            content: content,
            type: value,
            images: images
          };
          _context.next = 3;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_5__["default"].config.feedback(params);
        case 3:
          _yield$api$config$fee = _context.sent;
          message = _yield$api$config$fee.data.message;
          if (!message) {
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_6___default().navigateBack();
          }
        case 6:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [content, value, images]);
  var multipleChangeFile = function multipleChangeFile(taroUploadFile, options) {
    var uploadTask = taroUploadFile({
      url: _api__WEBPACK_IMPORTED_MODULE_7__.BASE_URI + "/file/upload",
      filePath: options.taroFilePath,
      fileType: options.fileType,
      name: options.name,
      formData: {
        type: 1,
        link: FEEDBACK_LINK
      },
      success: function success(response) {
        if (response.statusCode === 200) {
          var _JSON$parse = JSON.parse(response.data),
            url = _JSON$parse.url;
          options.onSuccess(url);
        }
      }
    });
    options.onStart(options);
    uploadTask.progress(function (res) {
      var _options$onProgress;
      (_options$onProgress = options.onProgress) === null || _options$onProgress === void 0 || _options$onProgress.call(options, res, options);
    });
  };
  var onDelete = function onDelete(values) {
    setImages(values.map(function (item) {
      return item.responseText;
    }));
  };
  var onChange = function onChange(values) {
    setImages(values.map(function (item) {
      return item.responseText;
    }));
  };
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
    className: "px-3 py-4 flex flex-col h-_100vh_",
    style: {
      paddingTop: rect.bottom + 10
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_8__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
      className: "flex flex-col gap-3 flex-1 overflow-y-auto",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
        className: "text-white",
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "mb-2",
          children: "\u7C7B\u578B\u9009\u62E9"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "flex gap-1d5 flex-wrap",
          children: list.map(function (item) {
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
              className: (0,clsx__WEBPACK_IMPORTED_MODULE_15__["default"])("py-1 px-1d5 bg-bgs text-xs rounded-sm border border-muted-foregrounds50 text-muted-foreground", {
                "text-primary": item.value === value,
                "border-primary": item.value === value
              }),
              onClick: function onClick() {
                return setValue(item.value);
              },
              children: item.name
            }, item.value);
          })
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "mb-2",
          children: "\u95EE\u9898\u63CF\u8FF0"
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_16__.C, {
          theme: {
            "--nutui-textarea-text-color": "hsl(var(--muted-foreground))",
            "--nutui-textarea-text-curror-color": "hsl(var(--muted-foreground))",
            "--nutui-gray-1": "hsl(var(--background-third))"
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_nutui_nutui_react_taro_dist_esm_TextArea__WEBPACK_IMPORTED_MODULE_17__.T, {
            value: content,
            onChange: function onChange(value) {
              return setContent(value);
            }
          })
        })]
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
          className: "mb-2",
          children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
            children: "\u4E0A\u4F20\u51ED\u8BC1"
          }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Text, {
            className: "text-muted-foreground text-xs",
            children: "\u53EF\u7528\u4E8E\u4E0A\u4F20\u56FE\u7247\u6216\u89C6\u9891"
          })]
        }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_16__.C, {
          theme: {
            "--nutui-uploader-background": "hsl(var(--muted-foreground))"
          },
          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_nutui_nutui_react_taro_dist_esm_Uploader__WEBPACK_IMPORTED_MODULE_18__.U, {
            multiple: true,
            maxCount: "5",
            beforeXhrUpload: multipleChangeFile,
            onChange: onChange,
            onDelete: onDelete
          })
        })]
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.Button, {
      className: "login bg-primary text-background h-10 flex items-center justify-center",
      onClick: function onClick() {
        return create();
      },
      children: "\u63D0\u4EA4"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_10__.jsx)(_nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_19__.S, {
      position: "bottom"
    })]
  });
});
// message: "准备上传"
// name: "http://tmp/EnwIYQVkPKeL1cffecc6a482d7d1eb1069d766fb0d91.svg"
// path: "http://tmp/EnwIYQVkPKeL1cffecc6a482d7d1eb1069d766fb0d91.svg"
// percentage: 0
// responseText: {errMsg: "uploadFile:fail createUploadTask:fail Error: Invalid URL passed to App.getProxyForURL()'"}
// status: "ready"
// type: "image"
// uid: "1716545504021_0"
// url: "http://tmp/EnwIYQVkPKeL1cffecc6a482d7d1eb1069d766fb0d91.svg"

/***/ }),

/***/ "./src/pages/feedback/index.tsx":
/*!**************************************!*\
  !*** ./src/pages/feedback/index.tsx ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_feedback_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/feedback/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/feedback/index!./src/pages/feedback/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_feedback_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/feedback/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_feedback_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_feedback_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_feedback_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_feedback_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/feedback/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map