/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[4].oneOf[0].use[2]!./node_modules/@nutui/nutui-react-taro/dist/esm/CellGroup/style/style.css ***!
  \***********************************************************************************************************************************************************************************************************************************************/
.nut-cell-group{display:block}.nut-cell-group-title{display:inherit;padding:var(--nutui-cell-group-title-padding, 0 10px);color:var(--nutui-cell-group-title-color, var(--nutui-gray-7, #1a1a1a));font-size:var(--nutui-cell-group-title-font-size, var(--nutui-font-size-3, 14px));line-height:var(--nutui-cell-group-title-line-height, 20px);margin-top:30px;margin-bottom:10px}.nut-cell-group-description{display:inherit;padding:var(--nutui-cell-group-description-padding, 0 10px);color:var(--nutui-cell-group-description-color, var(--nutui-gray-6, #595959));font-size:var(--nutui-cell-group-description-font-size, var(--nutui-font-size-2, 12px));line-height:var(--nutui-cell-group-description-line-height, 16px);margin-top:10px;margin-bottom:10px}.nut-cell-group-wrap{display:inherit;border-radius:var(--nutui-cell-border-radius, 6px);overflow:hidden;background-color:var(--nutui-cell-group-background-color, var(--nutui-gray-1, #ffffff));margin:var(--nutui-cell-group-wrap-margin, 10px 0)}.nut-cell-group-wrap .nut-cell{margin:0;-webkit-box-shadow:none;box-shadow:none;border-radius:0}.nut-cell-group-wrap-divider .nut-cell-divider{position:absolute;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none;right:var(--nutui-cell-divider-right, 16px);bottom:0;left:var(--nutui-cell-divider-left, 16px);-webkit-transform:scaleY(.5);-ms-transform:scaleY(.5);transform:scaleY(.5);border-top:var(--nutui-cell-divider-border-bottom, 2px solid var(--nutui-black-3, rgba(0, 0, 0, .06)))}.nut-cell-group-wrap-divider .nut-cell:last-child .nut-cell-divider{border-top:0}[dir=rtl] .nut-cell-group-wrap-divider .nut-cell-divider,.nut-rtl .nut-cell-group-wrap-divider .nut-cell-divider{left:var(--nutui-cell-divider-right, 16px);right:var(--nutui-cell-divider-left, 16px)}

