package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.CouponTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class AdminUserCouponBo {

    private Long id;

    @Schema(description = "优惠券名称")
    private String title;

    @Schema(description = "优惠券描述")
    private String description;

    @Schema(description = "优惠券价格")
    private BigDecimal price;

    @Schema(description = "优惠券类型")
    private CouponTypeEnum type;

    @Schema(description = "优惠券有效时间")
    private Integer minutes;

    @Schema(description = "优惠券有效时段")
    private String period;

    @Schema(description = "用户id")
    private Long userId;

    private String nickname;

    @Schema(description = "用户手机")
    private String userPhone;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "是否使用")
    private IsEnum used;

    @Schema(description = "是否退款")
    private IsEnum refund;

    @Schema(description = "是否赠送")
    private IsEnum gift;

    @Schema(description = "是否过期")
    private IsEnum expired;

    @Schema(description = "过期时间")
    private LocalDateTime expireTime;

}
