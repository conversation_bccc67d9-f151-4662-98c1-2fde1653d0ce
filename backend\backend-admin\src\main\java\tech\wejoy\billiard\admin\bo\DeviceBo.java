package tech.wejoy.billiard.admin.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.DeviceBrandEnum;
import tech.wejoy.billiard.common.enums.DeviceTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.util.List;

@Data
public class DeviceBo {

    private Long id;

    private String name;

    private Long tenantId;

    private Long accountId;

    private DeviceBrandEnum brand;

    private DeviceTypeEnum type;

    private IsEnum online;

    private IsEnum open;

    private Boolean linked;

    private Long parentId;

    private String parentName;

    private List<DeviceRelBo> rel;

}
