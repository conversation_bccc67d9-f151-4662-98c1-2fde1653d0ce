package tech.wejoy.billiard.admin.task.order.bo;

import com.congeer.utils.DateUtils;
import lombok.Data;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;

import java.time.LocalDateTime;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

@Data
public class OrderDelayMessage implements Delayed {

    private final String orderNo;
    private DeviceOperationTypeEnum type;
    private final long ttl;

    public OrderDelayMessage(String orderNo, DeviceOperationTypeEnum type, LocalDateTime time) {
        this.orderNo = orderNo;
        this.type = type;
        this.ttl = DateUtils.getTimestamp(time);
    }

    @Override
    public long getDelay(TimeUnit unit) {
        long remaining = ttl - System.currentTimeMillis();
        return unit.convert(remaining, TimeUnit.MILLISECONDS);
    }

    @Override
    public int compareTo(Delayed o) {
        return (int) (this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS));
    }

}
