package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.entity.MemberPlan;

import java.math.BigDecimal;

@Data
public class MemberRechargePlanBo {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "会员等级")
    private Integer memberLevel;

    private String memberLevelName;

    @Schema(description = "首次价格")
    private BigDecimal initialPrice;

    @Schema(description = "续费价格")
    private BigDecimal renewalPrice;

    @Schema(description = "消费折扣")
    private BigDecimal discount;

    @Schema(description = "备注")
    private String remark;

    public static MemberRechargePlanBo from(MemberPlan plan) {
        MemberRechargePlanBo bo = new MemberRechargePlanBo();
        bo.setId(plan.getId());
        bo.setMemberLevel(plan.getLevel());
        bo.setMemberLevelName(plan.getName());
        bo.setInitialPrice(plan.getInitialPrice());
        bo.setRenewalPrice(plan.getRenewalPrice());
        bo.setDiscount(plan.getDiscount());
        bo.setRemark(plan.getRemark());
        return bo;
    }

}
