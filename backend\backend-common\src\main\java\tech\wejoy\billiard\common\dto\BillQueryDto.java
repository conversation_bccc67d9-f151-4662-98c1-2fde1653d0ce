package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.Separator;
import tech.wejoy.billiard.common.enums.BillStatusEnum;
import tech.wejoy.billiard.common.enums.BillTypeEnum;

import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class BillQueryDto extends PageRequest {

    @QueryParam("billNo")
    private String billNo;

    @QueryParam("phone")
    private String phone;

    @QueryParam("type")
    @Separator(",")
    private List<BillTypeEnum> type;

    @QueryParam("status")
    @Separator(",")
    private List<BillStatusEnum> status;

    @Schema(hidden = true)
    private List<Long> userIds;

    @QueryParam("startDate")
    private LocalDate startDate;

    @QueryParam("endDate")
    private LocalDate endDate;

}
