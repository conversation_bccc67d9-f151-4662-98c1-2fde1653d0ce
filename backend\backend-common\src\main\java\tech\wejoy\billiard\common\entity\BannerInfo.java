package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class BannerInfo extends BaseEntity {

    @Column(columnDefinition = "text")
    private String title;

    @Column(columnDefinition = "text")
    private String content;

    @Column(columnDefinition = "text")
    private String image;

    @Column(columnDefinition = "text")
    private String link;

    private Integer seq;

    private String params;

    private IsEnum status;

    private BannerTypeEnum type;

}
