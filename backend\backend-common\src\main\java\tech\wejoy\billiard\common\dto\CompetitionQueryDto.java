package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.CompetitionLevelEnum;
import tech.wejoy.billiard.common.enums.CompetitionStatusEnum;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class CompetitionQueryDto extends PageRequest {

    @QueryParam("status")
    @Schema(description = "比赛状态")
    private CompetitionStatusEnum status;

    @QueryParam("cityCode")
    @Schema(description = "城市编码")
    private String cityCode;

    @QueryParam("level")
    @Schema(description = "比赛级别")
    private CompetitionLevelEnum level;

    @Schema(hidden = true)
    private Long userId;

    @Schema(hidden = true)
    private List<Long> ids;

}
