package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class PayResultBo {

    @Schema(description = "是否成功")
    private boolean success;

    @Schema(description = "是否需要支付")
    private boolean needPay;

    @Schema(description = "支付方式")
    private String message;

    @Schema(description = "支付参数")
    private Object extra;

    @Schema(description = "支付金额")
    private BigDecimal amount;

}
