{"miniprogramRoot": "./", "projectname": "billiard-user", "description": "", "appid": "wx328c2d73a2269b3f", "setting": {"urlCheck": false, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "preloadBackgroundData": false, "minified": true, "newFeature": true, "autoAudits": false, "coverView": true, "showShadowRootInWxmlPanel": false, "scopeDataCheck": false, "useCompilerModule": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false}, "compileType": "miniprogram", "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "libVersion": "3.4.2", "srcMiniprogramRoot": "dist/", "packOptions": {"ignore": [], "include": []}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}