{"version": 3, "file": "pages/assistant/list/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC/GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/assistant/list/index.tsx?63cb", "webpack://frontend-client/._src_pages_assistant_list_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport \"@nutui/nutui-react-taro/dist/esm/InfiniteLoading/style/css\";\nimport _InfiniteLoading from \"@nutui/nutui-react-taro/dist/esm/InfiniteLoading\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _toConsumableArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport { useCallback, useEffect, useMemo, useRef, useState } from \"react\";\nimport api from \"@/api/bussiness/assistant\";\nimport { menuRect } from \"@/utils\";\nimport Back from \"@/components/bussiness/back\";\nimport Title from \"@/components/bussiness/Title\";\nimport { Item } from \"@/custom/assistant-card\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default (function () {\n  var rect = menuRect();\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    list = _useState2[0],\n    setList = _useState2[1];\n  var pagination = useRef({\n    size: 30,\n    current: 0\n  });\n  var _useState3 = useState(-1),\n    _useState4 = _slicedToArray(_useState3, 2),\n    total = _useState4[0],\n    setTotal = _useState4[1];\n  var getList = useCallback(/*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(initialize) {\n      var _pagination$current, size, current, params, _ref2, _ref2$data, records, total;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _pagination$current = pagination.current, size = _pagination$current.size, current = _pagination$current.current;\n            params = JSON.parse(JSON.stringify({\n              size: size,\n              current: current\n            }));\n            _context.next = 4;\n            return api.getList(params);\n          case 4:\n            _ref2 = _context.sent;\n            _ref2$data = _ref2.data;\n            records = _ref2$data.records;\n            total = _ref2$data.total;\n            setList(function (items) {\n              if (initialize) return records;\n              return [].concat(_toConsumableArray(items), _toConsumableArray(records));\n            });\n            setTotal(total);\n          case 10:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }(), [setList, setTotal]);\n  var onLoad = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          pagination.current.current++;\n          _context2.next = 3;\n          return getList();\n        case 3:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  })), [getList]);\n  var hasMore = useMemo(function () {\n    if (total < 0) return true;\n    return list.length < total && total > 0;\n  }, [list, total]);\n  useEffect(function () {\n    pagination.current.current = 0;\n    getList(true);\n  }, []);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: \"flex flex-col h-[100vh] overflow-hidden\",\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u52A9\\u6559\"\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"flex-1 overflow-y-auto\",\n      children: /*#__PURE__*/_jsx(_InfiniteLoading, {\n        loadMoreText: \"\\u6CA1\\u6709\\u66F4\\u591A\\u4E86\",\n        onLoadMore: onLoad,\n        hasMore: hasMore,\n        loadingText: \"loading\",\n        target: \"target\",\n        children: /*#__PURE__*/_jsx(\"div\", {\n          className: \"px-3 flex flex-col gap-2\",\n          children: list.map(function (item) {\n            return /*#__PURE__*/_jsx(Item, {\n              item: item\n            });\n          })\n        })\n      })\n    }), /*#__PURE__*/_jsx(_SafeArea, {\n      position: \"bottom\"\n    })]\n  });\n});", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/list/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/assistant/list/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}