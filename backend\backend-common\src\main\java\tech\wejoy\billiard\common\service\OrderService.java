package tech.wejoy.billiard.common.service;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.database.bean.BaseEntity;
import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.OrderOtherFinishDto;
import tech.wejoy.billiard.common.dto.OrderQueryDto;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.enums.OtherFinishOrderEnum;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.OrderManager;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.utils.PriceUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
@RequiredArgsConstructor
public class OrderService {

    private final OrderManager orderManager;

    private final ClubManager clubManager;

    public OrderInfo getOrderById(Long id) {
        return orderManager.fetchOrderById(id);
    }

    public Page<OrderBo> page(OrderQueryDto dto) {
        Page<OrderBo> page = orderManager.page(dto);
        if (page.getTotal() == 0L) {
            return page;
        }
        List<Long> clubIds = page.getRecords().stream().map(OrderBo::getClubId).toList();
        List<Long> tableIds = page.getRecords().stream().map(OrderBo::getTableId).toList();
        List<ClubInfo> clubs = clubManager.fetchClubByIds(clubIds);
        List<ClubTable> tableByIds = clubManager.fetchTableByIds(tableIds);
        Map<Long, ClubInfo> clubMap = clubs.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        Map<Long, ClubTable> tableMap = tableByIds.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        page.getRecords().forEach(v -> {
            v.setClubName(clubMap.get(v.getClubId()).getName());
            v.setClubImage(clubMap.get(v.getClubId()).getHeadImage());
            v.setTableName(tableMap.get(v.getTableId()).getName());
            // if (v.getPayType() == OrderPayTypeEnum.MEITUAN || v.getPayType() == OrderPayTypeEnum.DOUYIN) {
            //     v.setTotalAmount(BigDecimal.ZERO);
            // } else {
            v.setTotalAmount(v.getRealAmount().subtract(v.getRefundAmount()));
            // }
            if (v.getStatus() == OrderStatusEnum.FINISH) {
                v.setStartTime(v.getRealStartTime());
                v.setEndTime(v.getRealEndTime());
            }
        });
        return page;
    }

    @Transactional(rollbackOn = Exception.class)
    public OrderFinishResultBo finish(String orderNo) {
        OrderInfo order = orderManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        OrderFinishResultBo result = new OrderFinishResultBo();
        if (order.getStatus() == OrderStatusEnum.FINISH) {
            result.setOrderNo(order.getOrderNo());
            result.setStartTime(order.getRealStartTime());
            result.setEndTime(order.getRealEndTime());
            return result;
        }
        if (order.getStatus() != OrderStatusEnum.USING) {
            throw new BaseException("订单无法完成");
        }
        if (order.getRefundTime() != null) {
            throw new BaseException("订单完成中");
        }
        LocalDateTime endTime = LocalDateTime.now();
        if (endTime.isBefore(order.getRealStartTime().plusMinutes(5))) {
            throw new BaseException("订单时间不足5分钟");
        }
        ClubTable table = clubManager.fetchClubTableById(order.getTableId());
        clubManager.updateTableIdle(table.getId());
        BigDecimal realPrice = PriceUtils.calculatePrice(table, order.getRealStartTime(), endTime);
        BigDecimal refundAmount = order.getTotalAmount().subtract(realPrice);
        if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
            RefundResultBo refund = Payment.getPayment(order.getPayType()).refund(order, refundAmount);
            if (refund.isSuccess()) {
                order.setRefundTime(endTime);
                order.setRefundAmount(refund.getAmount());
            }
        }
        order.setStatus(OrderStatusEnum.FINISH);
        orderManager.save(order);
        result.setOrderNo(orderNo);
        result.setStartTime(order.getRealStartTime());
        result.setEndTime(endTime);
        result.setAmount(refundAmount);
        return result;
    }

    @Transactional(rollbackOn = Exception.class)
    public OrderOtherFinishResultBo finishOther(OrderOtherFinishDto dto) {
        OrderInfo order = orderManager.fetchOrderByNo(dto.getOrderNo());
        if (order == null) {
            throw new BaseException("订单不存在");
        }

        // 检查门店是否开启抢台费功能
        ClubInfo club = clubManager.fetchClubById(order.getClubId());
        if (club == null) {
            throw new BaseException("门店不存在");
        }
        if (club.getEnableTableGrabbing() != IsEnum.TRUE) {
            throw new BaseException("该门店未开启抢台费功能");
        }

        // 检查是否为本人订单
        if (order.getUserId().equals(dto.getUserId())) {
            throw new BaseException("不能为自己的订单付费");
        }

        OrderOtherFinishResultBo result = new OrderOtherFinishResultBo();
        if (order.getStatus() == OrderStatusEnum.FINISH) {
            throw new BaseException("订单已完成");
        }
        if (order.getStatus() != OrderStatusEnum.USING) {
            throw new BaseException("订单无法完成");
        }
        if (order.getRefundTime() != null) {
            throw new BaseException("订单完成中");
        }
        LocalDateTime endTime = LocalDateTime.now();
        if (endTime.isBefore(order.getRealStartTime().plusMinutes(5))) {
            throw new BaseException("订单时间不足5分钟");
        }
        ClubTable table = clubManager.fetchClubTableById(order.getTableId());
        BigDecimal realPrice = PriceUtils.calculatePrice(table, order.getRealStartTime(), endTime);

        // 根据抢台类型计算新订单费用
        if (dto.getType() == OtherFinishOrderEnum.HALF) {
            // 半价抢台：新用户支付一半费用
            realPrice = realPrice.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
        } else if (dto.getType() != OtherFinishOrderEnum.ALL) {
            throw new BaseException("不支持的抢台类型");
        }
        // 全额抢台：新用户支付全部费用（realPrice保持不变）

        // 创建新订单
        OrderInfo newOrder = new OrderInfo();
        String orderNo = orderManager.generateOrderNo(order);
        newOrder.setClubId(order.getClubId());
        newOrder.setOrderNo(orderNo);
        newOrder.setTableId(order.getTableId());
        newOrder.setUserId(dto.getUserId());
        newOrder.setStartTime(order.getRealStartTime());
        newOrder.setEndTime(endTime);
        newOrder.setRealStartTime(order.getRealStartTime());
        newOrder.setRealEndTime(endTime);
        newOrder.setPayType(dto.getPayType());
        newOrder.setPayAmount(realPrice);

        // 标记这是一个抢台费订单，存储原订单信息
        String grabOrderInfo = JsonUtils.toJson(Map.of(
            "type", "TABLE_GRAB",
            "originalOrderNo", order.getOrderNo(),
            "grabType", dto.getType().name()
        ));
        newOrder.setDescription(grabOrderInfo);

        // 设置返回结果基本信息
        result.setOrderNo(newOrder.getOrderNo());
        result.setAmount(realPrice);
        result.setEndTime(endTime);
        result.setStartTime(order.getRealStartTime());

        // 先处理新订单支付
        Payment payment = Payment.getPayment(newOrder.getPayType());
        PayResultBo pay = payment.pay(newOrder, realPrice);

        if (pay.isSuccess()) {
            // 支付成功，处理原订单退款和状态更新
            newOrder.setStatus(OrderStatusEnum.FINISH);

            // 处理原订单退款
            if (dto.getType() == OtherFinishOrderEnum.ALL) {
                // 全额抢台：原订单全额退款
                BigDecimal refundAmount = order.getTotalAmount();
                if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    RefundResultBo refund = Payment.getPayment(order.getPayType()).refund(order, refundAmount);
                    if (refund.isSuccess()) {
                        order.setRefundTime(endTime);
                        order.setRefundAmount(refund.getAmount());
                    } else {
                        // 退款失败，需要回滚新订单
                        throw new BaseException("原订单退款失败，抢台费操作已取消：" + refund.getMessage());
                    }
                }
            } else if (dto.getType() == OtherFinishOrderEnum.HALF) {
                // 半价抢台：原订单部分退款
                BigDecimal originalRealPrice = PriceUtils.calculatePrice(table, order.getRealStartTime(), endTime);
                BigDecimal refundAmount = order.getTotalAmount().subtract(originalRealPrice.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP));
                if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    RefundResultBo refund = Payment.getPayment(order.getPayType()).refund(order, refundAmount);
                    if (refund.isSuccess()) {
                        order.setRefundTime(endTime);
                        order.setRefundAmount(refund.getAmount());
                    } else {
                        // 退款失败，需要回滚新订单
                        throw new BaseException("原订单退款失败，抢台费操作已取消：" + refund.getMessage());
                    }
                }
            }

            // 更新原订单状态
            order.setStatus(OrderStatusEnum.FINISH);
            order.setRealEndTime(endTime);
            order.setEndTime(endTime);
            orderManager.save(order);

            // 更新桌台状态为空闲
            clubManager.updateTableIdle(table.getId());

        } else if (pay.isNeedPay()) {
            // 需要支付，订单状态为待支付，原订单保持不变
            newOrder.setStatus(OrderStatusEnum.PENDING);
            result.setExtra(pay.getExtra());
        } else {
            // 支付失败，不做任何状态变更
            throw new BaseException("支付失败：" + (pay.getMessage() != null ? pay.getMessage() : "未知错误"));
        }

        result.setResult(newOrder.getStatus());
        orderManager.save(newOrder);
        return result;
    }

    @Transactional(rollbackOn = Exception.class)
    public void cancel(String orderNo) {
        OrderInfo order = orderManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() != OrderStatusEnum.PENDING
                && order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("订单无法取消");
        }
        if (order.getStatus() == OrderStatusEnum.PAID) {
            RefundResultBo refund = Payment.getPayment(order.getPayType()).cancel(order, order.getRealAmount());
            if (refund.isSuccess()) {
                order.setRefundTime(LocalDateTime.now());
                order.setRefundAmount(refund.getAmount());
            }
        }
        order.setStatus(OrderStatusEnum.CANCEL);
        orderManager.save(order);
        clubManager.updateTableIdle(order.getTableId());
    }

    public OrderStartResultBo start(String orderNo) {
        OrderInfo order = orderManager.fetchOrderByNo(orderNo);
        if (order == null) {
            throw new BaseException("订单不存在");
        }
        if (order.getStatus() == OrderStatusEnum.USING) {
            throw new BaseException("订单已开始");
        }
        if (order.getStatus() != OrderStatusEnum.PAID) {
            throw new BaseException("订单无法开始");
        }
        LocalDateTime startTime = order.getStartTime();
        LocalDateTime endTime = order.getEndTime();
        LocalDateTime newStart = LocalDateTime.now();
        order.setStartTime(newStart);
        long until = startTime.until(newStart, ChronoUnit.SECONDS);
        order.setEndTime(endTime.plusSeconds(until));
        orderManager.save(order);
        OrderStartResultBo result = new OrderStartResultBo();
        result.setOrderNo(orderNo);
        result.setStartTime(newStart);
        result.setEndTime(order.getEndTime());
        return result;
    }
}
