package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.WithdrawStatusEnum;

import java.math.BigDecimal;

@Data
public class WithdrawConfigBo {

    private Long id;

    private Long tenantId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "银行")
    private String bank;

    @Schema(description = "支行")
    private String branch;

    @Schema(description = "账号")
    private String account;

    private WithdrawStatusEnum status;

    private BigDecimal balance;

    private BigDecimal fee;

    private BigDecimal total;

}
