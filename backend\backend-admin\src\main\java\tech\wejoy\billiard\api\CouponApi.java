package tech.wejoy.billiard.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.AdminCouponBo;
import tech.wejoy.billiard.bo.AdminUserCouponBo;
import tech.wejoy.billiard.bo.CouponBillBo;
import tech.wejoy.billiard.bo.CouponGiftRecordBo;
import tech.wejoy.billiard.dto.AdminCouponQueryDto;
import tech.wejoy.billiard.dto.AdminCouponSaveDto;
import tech.wejoy.billiard.dto.CouponPresentDto;
import tech.wejoy.billiard.service.AdminCouponService;
import tech.wejoy.billiard.service.CouponService;

import java.util.List;

@Path("/coupon")
@Tag(name = "优惠券相关")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class CouponApi {

    private final CouponService couponService;

    private final AdminCouponService adminCouponService;

    @GET
    @Path("/list")
    @Operation(summary = "优惠券列表")
    @Permission({"business_coupon:read", "business_coupon:write"})
    public List<AdminCouponBo> list(@BeanParam AdminCouponQueryDto dto) {
        return couponService.adminList(dto);
    }

    @POST
    @Path("/")
    @Operation(summary = "创建优惠券")
    @Permission({"business_coupon:write"})
    public AdminCouponBo createCoupon(AdminCouponSaveDto dto) {
        return adminCouponService.create(dto);
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "更新优惠券")
    @Permission({"business_coupon:write"})
    public AdminCouponBo updateCoupon(@PathParam("id") Long id, AdminCouponSaveDto dto) {
        dto.setId(id);
        return adminCouponService.update(dto);
    }

    @DELETE
    @Path("/{id}")
    @Operation(summary = "删除优惠券")
    @Permission({"business_coupon:write"})
    public void deleteCoupon(@PathParam("id") Long id) {
        adminCouponService.delete(id);
    }

    @POST
    @Path("/present")
    @Operation(summary = "赠送优惠券")
    @Permission({"business_coupon:write"})
    public void presentCoupon(CouponPresentDto dto) {
        adminCouponService.presentCoupon(dto);
    }

    @GET
    @Path("/gift/list")
    @Operation(summary = "赠送记录")
    @Permission({"business_coupon:read", "business_coupon:write"})
    public Page<CouponGiftRecordBo> giftList(@BeanParam AdminCouponQueryDto dto) {
        return adminCouponService.giftList(dto);
    }

    @GET
    @Path("/bill/list")
    @Operation(summary = "购买记录")
    @Permission({"business_coupon:read", "business_coupon:write"})
    public Page<CouponBillBo> billList(@BeanParam AdminCouponQueryDto dto) {
        return adminCouponService.billList(dto);
    }

    @POST
    @Path("/gift/{id}/expired")
    @Operation(summary = "赠送优惠券过期")
    @Permission({"business_coupon:write"})
    public void giftCouponExpired(@PathParam("id") Long id) {
        adminCouponService.giftCouponExpired(id);
    }

    @POST
    @Path("/user/{id}/expired")
    @Operation(summary = "用户优惠券过期")
    @Permission({"business_coupon:write"})
    public void userCouponExpired(@PathParam("id") Long id) {
        adminCouponService.userCouponExpired(id);
    }

    @GET
    @Path("/club/user/list")
    @Operation(summary = "门店用户优惠券列表")
    @Permission({"business_coupon:read", "business_coupon:write"})
    public Page<AdminUserCouponBo> clubUserList(@BeanParam AdminCouponQueryDto dto) {
        return adminCouponService.clubUserList(dto);
    }

}
