package tech.wejoy.billiard.admin.bo;

import com.congeer.security.core.bean.IUsernameUser;
import com.congeer.utils.BeanUtils;
import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.entity.AdminUser;

import java.io.Serial;
import java.time.LocalDateTime;
import java.util.List;

@Data
@RegisterForReflection
public class AdminUserBo implements IUsernameUser<Long> {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private String openId;

    private String phone;

    private Long tenantId;

    private Long clubId;

    private String avatar;

    private String nickname;

    private String username;

    private LocalDateTime lastLoginAt;

    private String password;

    private String sessionKey;

    private boolean bind;

    private List<String> roles;

    private List<Long> roleIds;

    private List<String> roleNames;

    private List<Long> clubIds;

    private List<String> permissions;

    @Override
    public AdminUserBo clearSensitive() {
        AdminUserBo copy = new AdminUserBo();
        copy.setId(id);
        copy.setTenantId(tenantId);
        copy.setClubId(clubId);
        copy.setAvatar(avatar);
        copy.setNickname(nickname);
        copy.setUsername(username);
        copy.setPhone(phone);
        copy.setClubIds(clubIds);
        copy.setRoles(roles);
        copy.setPermissions(permissions);
        copy.setRoleIds(roleIds);
        copy.setRoleNames(roleNames);
        copy.setBind(StringUtils.isNotBlank(openId));
        copy.setLastLoginAt(lastLoginAt);
        return copy;
    }

    public static AdminUserBo from(AdminUser user) {
        return BeanUtils.copy(user, AdminUserBo.class);
    }

}
