package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Schema(description = "订单信息")
public class OrderProfitBo {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "加盟商ID")
    private Long tenantId;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户手机号")
    private String userPhone;

    @Schema(description = "门店ID")
    private Long clubId;

    @Schema(description = "门店名称")
    private String clubName;

    @Schema(description = "桌台ID")
    private Long tableId;

    @Schema(description = "桌台名称")
    private String tableName;

    @Schema(description = "支付方式")
    private OrderPayTypeEnum payType;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "预约时长")
    private BigDecimal appointmentHours;

    @Schema(description = "服务时长")
    private BigDecimal serviceHours;

    @Schema(description = "订单金额")
    private BigDecimal orderAmount;

    @Schema(description = "退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "服务费")
    private BigDecimal serviceFee;

    @Schema(description = "收益")
    private BigDecimal profitAmount;

    @Schema(description = "收入")
    private BigDecimal revenueAmount;

}
