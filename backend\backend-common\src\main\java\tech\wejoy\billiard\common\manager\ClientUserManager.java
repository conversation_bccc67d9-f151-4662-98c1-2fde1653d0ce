package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.utils.BeanUtils;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.common.bo.ClientUserBo;
import tech.wejoy.billiard.common.dto.AdminCouponQueryDto;
import tech.wejoy.billiard.common.dto.ClientUserQueryDto;
import tech.wejoy.billiard.common.entity.ClientUser;
import tech.wejoy.billiard.common.entity.ClientUserCoupon;
import tech.wejoy.billiard.common.entity.ClientUserTicket;
import tech.wejoy.billiard.common.entity.TenantCoupon;
import tech.wejoy.billiard.common.enums.CouponStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.enums.TicketChannelEnum;
import tech.wejoy.billiard.common.repo.ClientUserCouponRepo;
import tech.wejoy.billiard.common.repo.ClientUserRepo;
import tech.wejoy.billiard.common.repo.ClientUserTicketRepo;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class ClientUserManager {

    private final ClientUserRepo userRepo;

    private final ClientUserTicketRepo clientUserTicketRepo;

    private final ClientUserCouponRepo clientUserCouponRepo;

    public ClientUser fetchUserByOpenId(String openId) {
        return userRepo.find("openId", openId).firstResult();
    }

    public ClientUser fetchUserById(Long userId) {
        return userRepo.findById(userId);
    }

    public ClientUser save(ClientUser user) {
        return userRepo.save(user);
    }

    public ClientUserTicket fetchTicketById(Long ticketId) {
        return clientUserTicketRepo.findById(ticketId);
    }

    public void saveClientUserTicket(ClientUserTicket clientUserTicket) {
        clientUserTicketRepo.save(clientUserTicket);
    }

    public List<ClientUserTicket> fetchTicketByUserId(Long userId) {
        return clientUserTicketRepo.find("userId", userId).list();
    }

    public List<ClientUserTicket> fetchAvailableTicketByUserId(Long userId) {
        return clientUserTicketRepo.find("userId = ?1 and used = ?2", userId, IsEnum.FALSE).list();
    }

    public ClientUserTicket fetchTicketByChannelId(TicketChannelEnum ticketChannelEnum, Long id, Long userId) {
        return clientUserTicketRepo.find("channel = ?1 and channelId = ?2 and userId = ?3", ticketChannelEnum, id, userId).firstResult();
    }

    public List<ClientUser> fetchUserByIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        return userRepo.find("id in ?1", userIds).list();
    }

    public Page<ClientUser> page(List<Long> userIds, String phone, int current, int size) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            query += " and id in :userIds";
            params.put("userIds", userIds);
        }
        if (phone != null) {
            query += " and phone like concat('%', :phone, '%')";
            params.put("phone", phone);
        }
        long total = userRepo.count(query, params);
        if (total == 0) {
            return Page.empty();
        }
        List<ClientUser> list = userRepo.find(query, params).page(current, size).list();
        return Page.of(total, list, current, size);
    }

    public List<ClientUser> list(List<Long> pageUserIds, String phone) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(pageUserIds)) {
            query += " and id in :userIds";
            params.put("userIds", pageUserIds);
        }
        if (phone != null) {
            query += " and phone like concat('%', :phone, '%')";
            params.put("phone", phone);
        }
        return userRepo.find(query, params).list();
    }

    public List<ClientUser> fetchListByPhone(String phone) {
        return userRepo.find("phone like concat('%', ?1, '%')", phone).list();
    }

    public ClientUser fetchUserByPhone(String phone) {
        return userRepo.find("phone", phone).firstResult();
    }

    public Page<ClientUserBo> adminPage(ClientUserQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (dto.getPhone() != null) {
            query += " and phone like concat('%', :phone, '%')";
            params.put("phone", dto.getPhone());
        }
        if (CollectionUtils.isNotEmpty(dto.getUserIds())) {
            query += " and id in :userIds";
            params.put("userIds", dto.getUserIds());
        }
        long total = userRepo.count(query, params);
        if (total == 0) {
            return Page.empty();
        }
        List<ClientUser> list = userRepo.find(query, Sort.descending("lastLoginAt"), params).page(dto.getCurrent(), dto.getSize()).list();
        return Page.of(total, list.stream().map(v -> BeanUtils.copy(v, ClientUserBo.class)).toList(), dto.getCurrent(), dto.getSize());
    }

    public ClientUserCoupon fetchCouponById(Long couponId) {
        return clientUserCouponRepo.findById(couponId);
    }

    public ClientUserCoupon saveUserCoupon(TenantCoupon coupon, Long userId, String billNo, boolean gift) {
        ClientUserCoupon userCoupon = new ClientUserCoupon();
        userCoupon.setUserId(userId);
        userCoupon.setCouponId(coupon.getId());
        userCoupon.setExpireTime(LocalDateTime.now().plusDays(coupon.getExpireDays()));
        userCoupon.setUsed(IsEnum.FALSE);
        userCoupon.setExpired(IsEnum.FALSE);
        userCoupon.setRefund(IsEnum.FALSE);
        userCoupon.setBillNo(billNo);
        userCoupon.setGift(IsEnum.of(gift));
        return clientUserCouponRepo.save(userCoupon);
    }

    public void refundCoupon(Long couponId) {
        ClientUserCoupon coupon = clientUserCouponRepo.findById(couponId);
        coupon.setRefund(IsEnum.TRUE);
        clientUserCouponRepo.save(coupon);
    }

    public void useCoupon(Long couponId) {
        ClientUserCoupon coupon = clientUserCouponRepo.findById(couponId);
        coupon.setUsed(IsEnum.TRUE);
        clientUserCouponRepo.save(coupon);
    }

    public void reverseCoupon(Long couponId) {
        ClientUserCoupon coupon = clientUserCouponRepo.findById(couponId);
        coupon.setUsed(IsEnum.FALSE);
        clientUserCouponRepo.save(coupon);
    }

    public void expireCoupon(Long couponId) {
        ClientUserCoupon coupon = clientUserCouponRepo.findById(couponId);
        coupon.setExpired(IsEnum.TRUE);
        clientUserCouponRepo.save(coupon);
    }

    public List<ClientUserCoupon> fetchCouponByUserIdAndStatus(Long userId, IsEnum used, IsEnum expired, IsEnum refund) {
        String query = "userId = :userId";
        Map<String, Object> params = new HashMap<>();
        params.put("userId", userId);
        if (used != null) {
            query += " and used = :used";
            params.put("used", used);
        }
        if (expired != null) {
            query += " and expired = :expired";
            params.put("expired", expired);
        }
        if (refund != null) {
            query += " and refund = :refund";
            params.put("refund", refund);
        }
        return clientUserCouponRepo.find(query, params).list();
    }

    public ClientUserCoupon fetchCouponByBillNo(String billNo) {
        return clientUserCouponRepo.find("billNo", billNo).firstResult();
    }

    public List<ClientUserCoupon> fetchCouponByBillNos(List<String> billNos) {
        if (CollectionUtils.isEmpty(billNos)) {
            return List.of();
        }
        return clientUserCouponRepo.find("billNo in ?1", billNos).list();
    }


    public Integer fetchCouponCountByUserId(Long userId) {
        return (int) clientUserCouponRepo.find("userId = ?1 and used = ?2 and expired = ?3 and refund = ?4",
                userId, IsEnum.FALSE, IsEnum.FALSE, IsEnum.FALSE).count();
    }

    public List<ClientUserCoupon> fetchCouponByExpire() {
        return clientUserCouponRepo.find("expireTime < ?1 and expired = ?2 and used = ?3", LocalDateTime.now(), IsEnum.FALSE, IsEnum.FALSE).list();
    }

    public void deleteClientUserTicketByChannelId(Long channelId) {
        clientUserTicketRepo.delete("channelId", channelId);
    }

    public Page<ClientUserCoupon> fetchCouponByCouponIds(AdminCouponQueryDto dto, List<Long> couponIds, List<Long> userIds) {
        if (CollectionUtils.isEmpty(couponIds)) {
            return dto.empty();
        }
        String query = "couponId in :couponIds";
        Map<String, Object> params = new HashMap<>();
        params.put("couponIds", couponIds);
        if (CollectionUtils.isNotEmpty(userIds)) {
            query += " and userId in :userIds";
            params.put("userIds", userIds);
        }
        if (dto.getStartDate() != null) {
            query += " and createAt >= :startDate";
            params.put("startDate", dto.getStartDate().atStartOfDay());
        }
        if (dto.getEndDate() != null) {
            query += " and createAt <= :endDate";
            params.put("endDate", dto.getEndDate().plusDays(1).atStartOfDay());
        }
        if (dto.getStatus() != null) {
            if (dto.getStatus() == CouponStatusEnum.EXPIRED) {
                query += " and expired = :expired";
                params.put("expired", IsEnum.TRUE);
            } else if (dto.getStatus() == CouponStatusEnum.USED) {
                query += " and used = :used";
                params.put("used", IsEnum.TRUE);
            } else if (dto.getStatus() == CouponStatusEnum.REFUND) {
                query += " and refund = :refund";
                params.put("refund", IsEnum.TRUE);
            } else if (dto.getStatus() == CouponStatusEnum.UNUSED) {
                query += " and used = :used and expired = :expired and refund = :refund";
                params.put("used", IsEnum.FALSE);
                params.put("expired", IsEnum.FALSE);
                params.put("refund", IsEnum.FALSE);
            }
        }
        return clientUserCouponRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public List<ClientUser> fetchUsersByPhone(String phone) {
        return userRepo.find("phone like concat('%', ?1, '%')", phone).list();
    }

    public List<ClientUserCoupon> fetchCouponByIdAndUserId(Long couponId, Long userId) {
        return clientUserCouponRepo.find("couponId = ?1 and userId = ?2 and gift = 0", couponId, userId).list();
    }

}
