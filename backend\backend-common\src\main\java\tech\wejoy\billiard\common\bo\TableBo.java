package tech.wejoy.billiard.common.bo;

import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.enums.TableStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "桌台")
public class TableBo {

    @Schema(description = "桌台ID")
    private Long id;

    @Schema(description = "桌台名称")
    private String name;

    @Schema(description = "桌台描述")
    private String description;

    @Schema(description = "桌台图片")
    private String headImage;

    @Schema(description = "桌台等级")
    private Integer tableLevel;

    @Schema(description = "桌台时间段")
    private List<TimeSlotBo> timeSlots;

    @Schema(description = "桌台状态")
    private TableStatusEnum status;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "押金")
    private BigDecimal deposit;

    private Long userId;

    public static TableBo from(ClubTable table) {
        TableBo copy = BeanUtils.copy(table, TableBo.class);
        if (StringUtils.isNotBlank(table.getTimeSlots())) {
            copy.setTimeSlots(JsonUtils.toList(table.getTimeSlots(), TimeSlotBo.class));
        }
        return copy;
    }

    public ClubTable to() {
        ClubTable copy = BeanUtils.copy(this, ClubTable.class);
        if (this.getTimeSlots() != null) {
            copy.setTimeSlots(JsonUtils.toJson(this.getTimeSlots()));
        }
        return copy;
    }

}
