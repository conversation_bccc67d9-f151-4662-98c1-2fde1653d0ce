package tech.wejoy.billiard.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum BaseConfigEnum {

    CUSTOMER_PHONE("customer.phone", "客服电话"),
    CUSTOMER_WECHAT("customer.wechat", "客服微信"),
    CUSTOMER_NAME("customer.name", "客服名称"),

    CLUB_FILTER_CITY("club.filter.city", "门店筛选城市"),
    CLUB_DEPOSIT("club.deposit", "门店押金"),
    ;

    private final String key;

    private final String label;

}
