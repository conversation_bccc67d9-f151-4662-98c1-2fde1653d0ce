package tech.wejoy.billiard.common.third.douyin.request;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProductRequest extends DouyinRequest {

    private String cursor;

    private Integer count = 50;

    private Integer status;

    private String accountId;

    private Integer goodsCreatorType = 1;

    private Boolean queryAllPoi;

    private Integer goodsQueryType;

}
