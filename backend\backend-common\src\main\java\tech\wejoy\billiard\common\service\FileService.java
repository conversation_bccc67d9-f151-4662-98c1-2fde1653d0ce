package tech.wejoy.billiard.common.service;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.congeer.utils.BeanUtils;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import net.coobird.thumbnailator.Thumbnails;
import org.jboss.resteasy.reactive.multipart.FileUpload;
import tech.wejoy.billiard.common.bo.FileBo;
import tech.wejoy.billiard.common.dto.MultipartDto;
import tech.wejoy.billiard.common.entity.AliyunOss;
import tech.wejoy.billiard.common.entity.FileInfo;
import tech.wejoy.billiard.common.enums.FileTypeEnum;
import tech.wejoy.billiard.common.manager.FileManager;
import tech.wejoy.billiard.common.third.ThirdServiceHolder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.UUID;

@ApplicationScoped
@RequiredArgsConstructor
public class FileService {

    private final FileManager fileManager;

    public FileBo createFile(MultipartDto dto) {
        FileUpload file = dto.getFile();
        String filename = dto.getFile().fileName();
        int beginIndex = filename.lastIndexOf(".");
        String suffix = "";
        if (beginIndex != -1) {
            suffix = filename.substring(beginIndex);
        }
        String saveName = "images/" + dto.getLink().name().toLowerCase() + "/" + UUID.randomUUID().toString().replaceAll("-", "") + suffix;
        FileInfo fileInfo = new FileInfo();
        fileInfo.setName(filename);
        fileInfo.setObject(saveName);
        fileInfo.setUrl(ThirdServiceHolder.ossConfig().getDomain() + saveName);
        fileInfo.setSeq(0);
        fileInfo.setType(dto.getType());
        fileInfo.setLink(dto.getLink());
        fileManager.save(fileInfo);
        try {
            File realFile = file.uploadedFile().toFile();
            InputStream inputStream = new FileInputStream(realFile);
            if (dto.getType() == FileTypeEnum.IMAGE) {
                BufferedImage read = ImageIO.read(realFile);
                int width = read.getWidth();
                if (width > 1024) {
                    width = 1024;
                }
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                Thumbnails.of(realFile)
                        .width(width)
                        .outputFormat("jpg")
                        .toOutputStream(outputStream);
                inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            }
            uploadBytes(saveName, inputStream);
            return BeanUtils.copy(fileInfo, FileBo.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void deleteById(Long id) {
        FileInfo file = fileManager.findById(id);
        if (file == null) {
            return;
        }
        fileManager.deleteById(file.getId());
        deleteOssObject(file.getObject());
    }

    private void uploadBytes(String name, InputStream content) {
        OSS ossClient = ThirdServiceHolder.ossClient();
        AliyunOss aliyunOss = ThirdServiceHolder.ossConfig();
        ObjectMetadata metadata = new ObjectMetadata();
        ossClient.putObject(aliyunOss.getBucketName(), name, content, metadata);
    }

    private void deleteOssObject(String name) {
        OSS ossClient = ThirdServiceHolder.ossClient();
        AliyunOss aliyunOss = ThirdServiceHolder.ossConfig();
        ossClient.deleteObject(aliyunOss.getBucketName(), name);
    }

}
