package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.TableStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class ClubTable extends BaseEntity {

    private Long tenantId;

    private Long clubId;

    private String name;

    @Column(columnDefinition = "text")
    private String description;

    @Column(columnDefinition = "text")
    private String headImage;

    private Integer tableLevel;

    private TableStatusEnum status;

    private LocalDateTime endTime;

    @Column(columnDefinition = "text")
    private String timeSlots;

    private Integer seq;

    private BigDecimal deposit;

    private Long userId;

}
