package tech.wejoy.billiard.api.api;

import com.congeer.core.bean.Page;
import com.congeer.core.exception.BaseException;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.api.service.WxUserService;
import tech.wejoy.billiard.common.bo.*;
import tech.wejoy.billiard.common.dto.*;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.service.AssistantService;

import java.io.File;
import java.util.List;

@Path("/assistant")
@Tag(name = "助教")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class AssistantApi {

    private final AssistantService assistantService;

    private final WxUserService wxUserService;

    @GET
    @Path("/list")
    @Operation(summary = "助教列表")
    public Page<AssistantBo> list(PageRequest dto) {
        return assistantService.list(dto);
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "助教详情")
    public AssistantBo detail(@PathParam("id") Long id) {
        return assistantService.detail(id);
    }

    @GET
    @Path("/{id}/code")
    @Operation(summary = "获取助教码")
    @Produces("image/jpeg")
    public Response getQrcode(@PathParam("id") Long id) {
        File entity = wxUserService.getAssistantQrcode(id);
        return Response.ok(entity)
                .header("Content-Disposition", "attachment; filename=" + entity.getName())
                .build();
    }

    @GET
    @Path("/club/request")
    @Operation(summary = "俱乐部助教申请列表")
    public List<AssistantClubRequestBo> clubRequest() {
        return assistantService.clubRequestByUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @POST
    @Path("/club/request")
    @Operation(summary = "俱乐部助教申请")
    public void clubRequest(AssistantClubRequestDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        assistantService.clubRequest(dto);
    }

    @POST
    @Path("/club/request/{id}/cancel")
    @Operation(summary = "取消俱乐部助教申请")
    public void cancelClubRequest(@PathParam("id") Long id) {
        assistantService.cancelClubRequest(id, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @POST
    @Path("/club/{id}/cancel")
    @Operation(summary = "取消俱乐部助教申请")
    public void cancelClubPass(@PathParam("id") Long id) {
        assistantService.cancelClub(id, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @POST
    @Path("/time/check")
    @Operation(summary = "检查时间")
    @Authorized
    public boolean checkTime(StartAssistantTimeDto dto) {
        return assistantService.checkTime(dto);
    }

    @POST
    @Path("/price/cal")
    @Operation(summary = "计算价格")
    @Authorized
    public PriceCalBo calPrice(StartAssistantTimeDto dto) {
        return assistantService.calPrice(dto);
    }


    @POST
    @Path("/prepare")
    @Operation(summary = "预约准备信息")
    @Authorized
    public AssistantPrepareBo startPrepare(AssistantPrepareDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return assistantService.startPrepare(dto);
    }

    @POST
    @Path("/start")
    @Operation(summary = "预约助教")
    @Authorized
    public AssistantResultBo start(AssistantStartDto dto) {
        if (dto.getPayType() == null || dto.getPayType() == OrderPayTypeEnum.NONE) {
            throw new BaseException("请选择支付方式");
        }
        if (dto.getAssistantId() == null) {
            throw new BaseException("请选择助教");
        }
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return assistantService.start(dto);
    }

    @GET
    @Path("/start/status")
    @Operation(summary = "开始状态")
    @Authorized
    public AssistantResultBo startStatus(@QueryParam("orderNo") String orderNo) {
        return assistantService.startStatus(orderNo);
    }

    @POST
    @Path("/apply")
    @Operation(summary = "申请助教")
    @Authorized
    public void apply(ApplyAssistantDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        dto.setPhone(SecurityHolder.<Long, WxUserBo>session().getUser().getPhone());
        assistantService.apply(dto);
    }

    @GET
    @Path("/order/list")
    @Operation(summary = "订单列表")
    @Authorized
    public Page<AssistantOrderBo> pageOrder(@BeanParam AssistantOrderQueryDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return assistantService.pageOrder(dto);
    }

    @GET
    @Path("/order/todo")
    @Operation(summary = "助教未完成订单列表")
    @Authorized
    public Page<AssistantOrderBo> pageTodo(@BeanParam AssistantOrderQueryDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return assistantService.pageTodo(dto);
    }

    @GET
    @Path("/order/done")
    @Operation(summary = "助教完成订单列表")
    @Authorized
    public Page<AssistantOrderBo> pageDone(@BeanParam AssistantOrderQueryDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return assistantService.pageDone(dto);
    }

    @POST
    @Path("/order/{no}/finish")
    @Operation(summary = "完成订单")
    @Authorized
    public OrderFinishResultBo finishOrder(@PathParam("no") String orderNo) {
        return assistantService.finishOrder(orderNo);
    }

    @POST
    @Path("/order/{no}/start/{code}")
    @Operation(summary = "开始订单")
    @Authorized
    public OrderStartResultBo startOrder(@PathParam("no") String orderNo, @PathParam("code") String code) {
        return assistantService.startOrder(orderNo, code);
    }

    @POST
    @Path("/order/{no}/cancel")
    @Operation(summary = "取消订单")
    @Authorized
    public void cancelOrder(@PathParam("no") String orderNo) {
        assistantService.cancelOrder(orderNo);
    }

    @GET
    @Path("/order/{no}")
    @Operation(summary = "订单详情")
    @Authorized
    public AssistantOrderDetailBo orderDetail(@PathParam("no") String orderNo) {

        return assistantService.orderDetail(orderNo, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

    @GET
    @Path("/club/{id}")
    @Operation(summary = "俱乐部助教列表")
    public List<AssistantBo> clubAssistant(@PathParam("id") Long clubId) {
        return assistantService.clubAssistant(clubId);
    }

}
