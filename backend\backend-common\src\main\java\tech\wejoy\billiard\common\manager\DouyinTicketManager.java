package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.google.common.collect.Lists;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import tech.wejoy.billiard.common.dto.DealQueryDto;
import tech.wejoy.billiard.common.dto.ShopQueryDto;
import tech.wejoy.billiard.common.entity.DouyinAccount;
import tech.wejoy.billiard.common.entity.DouyinDeal;
import tech.wejoy.billiard.common.entity.DouyinShop;
import tech.wejoy.billiard.common.entity.DouyinTicket;
import tech.wejoy.billiard.common.repo.DouyinAccountRepo;
import tech.wejoy.billiard.common.repo.DouyinDealRepo;
import tech.wejoy.billiard.common.repo.DouyinShopRepo;
import tech.wejoy.billiard.common.repo.DouyinTicketRepo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class DouyinTicketManager {

    private final DouyinAccountRepo douyinAccountRepo;

    private final DouyinDealRepo douyinDealRepo;

    private final DouyinTicketRepo douyinTicketRepo;

    private final DouyinShopRepo douyinShopRepo;

    public DouyinShop fetchDouyinShopByClubId(Long clubId) {
        return douyinShopRepo.find("clubId", clubId).firstResult();
    }

    public List<DouyinShop> fetchDouyinShopsByClubId(Long clubId) {
        return douyinShopRepo.find("clubId", clubId).list();
    }

    public DouyinAccount fetchDouyinAccountById(Long id) {
        return douyinAccountRepo.findById(id);
    }

    public void saveDouyinTicket(DouyinTicket douyinTicket) {
        douyinTicketRepo.save(douyinTicket);
    }

    public DouyinDeal getDouyinDealBySkuId(String skuId) {
        return douyinDealRepo.find("skuId", skuId).firstResult();
    }

    public List<DouyinDeal> fetchDouyinDealByIds(List<Long> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Lists.newArrayList();
        }
        return douyinDealRepo.find("id in ?1", dealIds).list();
    }

    public DouyinTicket fetchDouyinTicketById(Long channelId) {
        return douyinTicketRepo.findById(channelId);
    }

    public void saveDouyinShop(DouyinShop shop) {
        douyinShopRepo.save(shop);
    }

    public List<DouyinShop> fetchDouyinShopByAccount(Long accountId) {
        return douyinShopRepo.find("accountId", accountId).list();
    }

    public void saveDouyinDeal(DouyinDeal douyinDeal) {
        douyinDealRepo.save(douyinDeal);
    }

    public List<DouyinDeal> fetchDouyinDealBySkuIds(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Lists.newArrayList();
        }
        return douyinDealRepo.find("skuId in ?1", skuIds).list();
    }

    public DouyinDeal fetchDouyinDealById(Long id) {
        return douyinDealRepo.findById(id);
    }

    public List<DouyinTicket> fetchDouyinTicketByCodes(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)) {
            return Lists.newArrayList();
        }
        return douyinTicketRepo.find("code in ?1", codes).list();
    }

    public DouyinTicket fetchDouyinTicketByCode(String code, String encryptedData) {
        if (StringUtils.isNotBlank(code)) {
            return douyinTicketRepo.find("code = ?1", code).firstResult();
        } else if (StringUtils.isNotBlank(encryptedData)) {
            return douyinTicketRepo.find("encryptedData = ?1", encryptedData).firstResult();
        } else {
            return null;
        }
    }

    public List<DouyinTicket> fetchDouyinTicketByIds(List<Long> channelIds) {
        if (CollectionUtils.isEmpty(channelIds)) {
            return Lists.newArrayList();
        }
        return douyinTicketRepo.find("id in ?1", channelIds).list();
    }

    public List<DouyinDeal> fetchDouyinDealByAccountId(Long accountId) {
        return douyinDealRepo.find("accountId", accountId).list();
    }

    public DouyinShop fetchDouyinShop(Long shopId) {
        return douyinShopRepo.findById(shopId);
    }

    public DouyinTicket fetchDouyinTicketBySkuId(String skuId) {
        return douyinTicketRepo.find("skuId", skuId).firstResult();
    }

    public List<DouyinAccount> fetchDouyinAccount() {
        return douyinAccountRepo.listAll();
    }

    public Page<DouyinShop> fetchDouyinShopPage(ShopQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(dto.getName())) {
            query += " and name like concat('%', :name, '%')";
            params.put("name", dto.getName());
        }
        return douyinShopRepo.page(dto.toPage(), query, Sort.by("id"), params);
    }

    public Page<DouyinDeal> fetchDouyinDealPage(DealQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(dto.getName())) {
            query += " and name like concat('%', :name, '%')";
            params.put("name", dto.getName());
        }
        if (dto.getDealIds() != null) {
            query += " and id in :dealIds";
            params.put("dealIds", dto.getDealIds());
        }
        return douyinDealRepo.page(dto.toPage(), query, Sort.by("id"), params);
    }

}
