package tech.wejoy.billiard.common.bo;

import com.congeer.utils.BeanUtils;
import com.congeer.utils.JsonUtils;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.entity.ClubInfo;
import tech.wejoy.billiard.common.enums.ClubStatusEnum;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Accessors(chain = true)
@Schema(description = "门店")
public class ApiClubBo {

    @Schema(description = "门店ID")
    private Long id;

    @Schema(description = "门店名称")
    private String name;

    @Schema(description = "门店描述")
    private String description;

    @Schema(description = "门店头像")
    private String headImage;

    @Schema(description = "门店图片")
    private List<String> images;

    @Schema(description = "门店地址")
    private String address;

    @Schema(description = "门店电话")
    private String phone;

    @Schema(description = "门店状态")
    private ClubStatusEnum status;

    @Schema(description = "开放时间")
    private LocalDateTime openAt;

    @Schema(description = "门店标签")
    private List<String> tags;

    @Schema(description = "城市编码")
    private String district;

    @Schema(description = "门店经度")
    private Double latitude;

    @Schema(description = "门店纬度")
    private Double longitude;

    @Schema(description = "距离")
    private Double distance;

    @Schema(description = "桌台数量")
    private Integer tableCount;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "桌台")
    private List<TableBo> tables;

    @Schema(description = "优惠券")
    private List<CouponBo> coupons;

    @Schema(description = "营业时间")
    private List<TimeBo> businessHours;

    @Schema(description = "约球")
    private List<MatchBo> matches;

    @Schema(description = "助教")
    private List<AssistantBo> assistants;

    @Schema(description = "会员提示")
    private IsEnum memberTips;

    public void setBusinessHours(String businessHours) {
        if (StringUtils.isBlank(businessHours)) {
            return;
        }
        this.businessHours = JsonUtils.toList(businessHours, TimeBo.class);
    }

    public static ApiClubBo from(ClubInfo clubInfo) {
        ApiClubBo copy = BeanUtils.copy(clubInfo, ApiClubBo.class);
        copy.setBusinessHours(clubInfo.getBusinessHours());
        return copy;
    }

    public static ApiClubBo optionFrom(ClubInfo clubInfo) {
        ApiClubBo copy = new ApiClubBo();
        copy.setId(clubInfo.getId());
        copy.setName(clubInfo.getName());
        return copy;
    }

}
