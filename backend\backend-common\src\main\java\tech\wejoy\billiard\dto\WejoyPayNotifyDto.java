package tech.wejoy.billiard.dto;

import lombok.Data;
import tech.wejoy.billiard.third.wejoy.request.TradeStatusEnum;

import java.time.LocalDateTime;

/**
 * DTO for WeJoy payment notification
 */
@Data
public class WejoyPayNotifyDto {
    private String tradeNo;
    private String requestId;
    private String description;
    private Integer amount;
    private TradeStatusEnum status;
    private String payer;
    private LocalDateTime createTime;
    private LocalDateTime payTime;
    private LocalDateTime expireTime;
    private String refundStatus;
    private Integer refundAmount;
    private LocalDateTime refundTime;
}
