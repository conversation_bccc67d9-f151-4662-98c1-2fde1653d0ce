"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/competition/detail/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/detail/index!./src/pages/competition/detail/index.tsx":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/detail/index!./src/pages/competition/detail/index.tsx ***!
  \******************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SafeArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/safearea.taro-BJpvFsMf.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js */ "./node_modules/@babel/runtime/helpers/esm/objectSpread2.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/ConfigProvider/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_nutui_nutui_react_taro_dist_esm_ConfigProvider_style_css__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/ConfigProvider */ "./node_modules/@nutui/nutui-react-taro/dist/esm/configprovider.taro-DpK4IiCE.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog_style_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/Dialog/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/Dialog */ "./node_modules/@nutui/nutui-react-taro/dist/esm/dialog.taro-1Vukbvap.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/bussiness/competition */ "./src/api/bussiness/competition.ts");
/* harmony import */ var _api_bussiness__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/bussiness */ "./src/api/bussiness/index.ts");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _components_bussiness_Card_Competition__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/bussiness/Card/Competition */ "./src/components/bussiness/Card/Competition.tsx");
/* harmony import */ var _components_bussiness_Title__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/bussiness/Title */ "./src/components/bussiness/Title/index.tsx");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_9__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_10__);
/* harmony import */ var _components_bussiness_Posters_competition__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/bussiness/Posters/competition */ "./src/components/bussiness/Posters/competition.tsx");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! dayjs */ "./node_modules/dayjs/dayjs.min.js");
/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_12__);
/* harmony import */ var _components_bussiness_Form_Button__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/bussiness/Form/Button */ "./src/components/bussiness/Form/Button.tsx");
/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! formik */ "./node_modules/formik/dist/formik.esm.js");
/* harmony import */ var yup__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! yup */ "./node_modules/yup/index.esm.js");
/* harmony import */ var _components_bussiness_Form__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/bussiness/Form */ "./src/components/bussiness/Form/index.tsx");
/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/hooks */ "./src/hooks/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");



























var COMPETION_STATUS = /*#__PURE__*/function (COMPETION_STATUS) {
  COMPETION_STATUS[COMPETION_STATUS["NONE"] = 0] = "NONE";
  COMPETION_STATUS[COMPETION_STATUS["SIGN_UP"] = 1] = "SIGN_UP";
  COMPETION_STATUS[COMPETION_STATUS["PREPARE"] = 2] = "PREPARE";
  COMPETION_STATUS[COMPETION_STATUS["START"] = 3] = "START";
  COMPETION_STATUS[COMPETION_STATUS["AWARDS"] = 4] = "AWARDS";
  COMPETION_STATUS[COMPETION_STATUS["END"] = 5] = "END";
  return COMPETION_STATUS;
}(COMPETION_STATUS || {});
var items = [{
  name: "赛事规程",
  path: "/pages/competition/rules/index"
}, {
  name: "赛事日程",
  path: "/pages/competition/schedule/index"
}, {
  name: "比分直播",
  path: "/pages/competition/live/index",
  render: function render(item, detailData) {
    var onClick = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(function () {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().navigateTo({
        url: "".concat(item.path, "?id=").concat(detailData.id)
      });
    }, [item, detailData]);
    if ([COMPETION_STATUS.START, COMPETION_STATUS.AWARDS, COMPETION_STATUS.END].includes(detailData === null || detailData === void 0 ? void 0 : detailData.status)) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
        className: "p-3 w-full rounded-lg bg-bgt font-bold text-center",
        onClick: onClick,
        children: item.name
      });
    }
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.Fragment, {});
  }
}, {
  name: "晋级成绩",
  path: "/pages/competition/score/index",
  render: function render(item, detailData) {
    var onClick = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(function () {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().navigateTo({
        url: "".concat(item.path, "?id=").concat(detailData.id)
      });
    }, [item, detailData]);
    if ([COMPETION_STATUS.START, COMPETION_STATUS.AWARDS, COMPETION_STATUS.END].includes(detailData === null || detailData === void 0 ? void 0 : detailData.status)) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
        className: "p-3 w-full rounded-lg bg-bgt font-bold text-center",
        onClick: onClick,
        children: item.name
      });
    }
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.Fragment, {});
  }
}, {
  name: "比赛榜单",
  path: "/pages/competition/rank/index",
  render: function render(item, detailData) {
    var onClick = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(function () {
      _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().navigateTo({
        url: "".concat(item.path, "?id=").concat(detailData.id)
      });
    }, [item, detailData]);
    if ([COMPETION_STATUS.START, COMPETION_STATUS.AWARDS, COMPETION_STATUS.END].includes(detailData === null || detailData === void 0 ? void 0 : detailData.status)) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
        className: "p-3 w-full rounded-lg bg-bgt font-bold text-center",
        onClick: onClick,
        children: item.name
      });
    }
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.Fragment, {});
  }
}, {
  name: "生成海报",
  render: function render(item, detailData) {
    var _useState = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(false),
      _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_17__["default"])(_useState, 2),
      visible = _useState2[0],
      setVisible = _useState2[1];
    var content = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(function () {
      if (detailData) {
        return [{
          value: dayjs__WEBPACK_IMPORTED_MODULE_12___default()(detailData.startTime).format("YYYY-MM-DD HH:mm"),
          fontWeight: "bord",
          fonSize: 16,
          color: "#666",
          center: true
        }, {
          value: detailData.title,
          fontWeight: "bord",
          fonSize: 18,
          color: "#333",
          center: true
        }];
      }
      return [];
    }, [detailData]);
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.Fragment, {
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
        className: "p-3 w-full rounded-lg bg-bgt font-bold text-center",
        onClick: function onClick() {
          return setVisible(true);
        },
        children: item.name
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_nutui_nutui_react_taro_dist_esm_ConfigProvider__WEBPACK_IMPORTED_MODULE_18__.C, {
        theme: {
          "--nutui-gray-7": "#fff",
          "--nutui-gray-6": "#fff",
          "--nutui-dialog-header-font-weight": "600",
          "--nutui-dialog-header-font-size": "1.25rem",
          "--nutui-dialog-padding": "10rpx",
          "--nutui-dialog-content-max-height": "auto"
        },
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_nutui_nutui_react_taro_dist_esm_Dialog__WEBPACK_IMPORTED_MODULE_19__.B, {
          visible: visible,
          onClose: function onClose() {
            return setVisible(false);
          },
          hideCancelButton: true,
          hideConfirmButton: true,
          children: detailData && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Posters_competition__WEBPACK_IMPORTED_MODULE_11__["default"], {
            id: detailData.id,
            content: content,
            awards: detailData.awards
          })
        })
      })]
    });
  }
}, {
  name: "正赛抽签",
  render: function render(__item, detailData) {
    if (((detailData === null || detailData === void 0 ? void 0 : detailData.apply) || {}).hasOwnProperty("number")) {
      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxs)("div", {
        className: "p-3 w-full rounded-lg bg-bgt font-bold text-center",
        children: ["\u62BD\u7B7E\u7ED3\u679C ", detailData.apply.number.toString().padStart(2, "0")]
      });
    }
    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.Fragment, {});
  }
}];
var SignButton = function SignButton(props) {
  var item = props.item,
    onLoad = props.onLoad;
  var id = item.id;
  var ref = (0,react__WEBPACK_IMPORTED_MODULE_10__.useRef)(null);
  var formik = (0,formik__WEBPACK_IMPORTED_MODULE_20__.useFormik)({
    initialValues: {
      realName: "",
      idCard: "",
      phone: ""
    },
    validationSchema: yup__WEBPACK_IMPORTED_MODULE_14__.object({
      realName: yup__WEBPACK_IMPORTED_MODULE_14__.string().required(),
      idCard: yup__WEBPACK_IMPORTED_MODULE_14__.string().required("请输入身份证").length(18, "身份证号必须为18位").matches(/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, "无效的身份证号格式"),
      phone: yup__WEBPACK_IMPORTED_MODULE_14__.string().required("手机号不能为空").matches(/^1[3-9]\d{9}$/, "手机号格式错误")
    }),
    onSubmit: function onSubmit() {},
    validateOnChange: false
  });
  var onCancel = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(function () {
    var _ref$current;
    formik.resetForm();
    (_ref$current = ref.current) === null || _ref$current === void 0 || _ref$current.setVisible(false);
  }, [formik]);
  var onConfirm = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_21__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().mark(function _callee() {
    var errorObjects, params;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().wrap(function _callee$(_context) {
      while (1) switch (_context.prev = _context.next) {
        case 0:
          _context.next = 2;
          return formik.validateForm();
        case 2:
          errorObjects = _context.sent;
          if (!(errorObjects && Object.keys(errorObjects).length)) {
            _context.next = 5;
            break;
          }
          return _context.abrupt("return");
        case 5:
          _context.next = 7;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().showLoading({
            title: "报名中..."
          });
        case 7:
          params = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_23__["default"])({
            competitionId: id
          }, formik.values);
          _context.next = 10;
          return _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_3__["default"].signUp(params);
        case 10:
          _context.next = 12;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().hideLoading();
        case 12:
          _context.next = 14;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().showToast({
            title: "报名成功",
            icon: "success"
          });
        case 14:
          _context.t0 = onLoad;
          if (!_context.t0) {
            _context.next = 18;
            break;
          }
          _context.next = 18;
          return onLoad();
        case 18:
          onCancel();
        case 19:
        case "end":
          return _context.stop();
      }
    }, _callee);
  })), [formik.values, onCancel, onLoad]);
  var auth = (0,_hooks__WEBPACK_IMPORTED_MODULE_24__.useStore)(function (state) {
    return state.auth;
  });
  var getPhone = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_21__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().mark(function _callee2() {
    var _yield$api$user$getPh, data;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().wrap(function _callee2$(_context2) {
      while (1) switch (_context2.prev = _context2.next) {
        case 0:
          _context2.next = 2;
          return _api_bussiness__WEBPACK_IMPORTED_MODULE_4__["default"].user.getPhone();
        case 2:
          _yield$api$user$getPh = _context2.sent;
          data = _yield$api$user$getPh.data;
          formik.setFieldValue("phone", data.phone);
        case 5:
        case "end":
          return _context2.stop();
      }
    }, _callee2);
  })), [formik]);
  var onVisibleChange = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(function (visible) {
    var _auth$user;
    if (visible && (_auth$user = auth.user) !== null && _auth$user !== void 0 && _auth$user.hasPhone) getPhone();
  }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Form_Button__WEBPACK_IMPORTED_MODULE_13__["default"], {
    buttonText: "\u6BD4\u8D5B\u62A5\u540D",
    title: "\u6BD4\u8D5B\u62A5\u540D",
    className: "justify-center",
    onCancel: onCancel,
    onConfirm: onConfirm,
    onVisibleChange: onVisibleChange,
    ref: ref,
    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxs)("div", {
      className: "flex flex-col gap-3 px-3 text-white",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Form__WEBPACK_IMPORTED_MODULE_15__.TextInput, {
        error: formik.errors.realName,
        value: formik.values.realName,
        onConfirm: function onConfirm(value) {
          return formik.setFieldValue("realName", value);
        },
        name: "\u771F\u5B9E\u59D3\u540D",
        placeholder: "\u53C2\u8D5B\u9009\u624B\u7684\u59D3\u540D",
        type: "text"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Form__WEBPACK_IMPORTED_MODULE_15__.TextInput, {
        error: formik.errors.phone,
        value: formik.values.phone,
        onConfirm: function onConfirm(value) {
          return formik.setFieldValue("phone", value);
        },
        name: "\u624B\u673A\u53F7",
        placeholder: "\u53C2\u8D5B\u9009\u624B\u7684\u624B\u673A\u53F7",
        type: "text"
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Form__WEBPACK_IMPORTED_MODULE_15__.TextInput, {
        error: formik.errors.idCard,
        value: formik.values.idCard,
        onConfirm: function onConfirm(value) {
          return formik.setFieldValue("idCard", value);
        },
        name: "\u8EAB\u4EFD\u8BC1",
        placeholder: "\u53C2\u8D5B\u9009\u624B\u7684\u8EAB\u4EFD\u8BC1\u53F7\u7801",
        type: "text"
      })]
    })
  });
};
/* harmony default export */ __webpack_exports__["default"] = (function () {
  var router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_9__.useRouter)();
  var id = router.params.id;
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_10__.useState)(),
    _useState4 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_17__["default"])(_useState3, 2),
    detailData = _useState4[0],
    setDetailData = _useState4[1];
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_8__.menuRect)();
  var getCompetition = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_21__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().mark(function _callee3() {
    var _yield$competition$ge, data;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          _context3.next = 2;
          return _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_3__["default"].getDetail(id);
        case 2:
          _yield$competition$ge = _context3.sent;
          data = _yield$competition$ge.data;
          setDetailData(data);
          return _context3.abrupt("return", data);
        case 6:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  })), [setDetailData]);
  var onDraw = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_21__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().mark(function _callee4() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_22__["default"])().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          _context4.next = 2;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().showLoading({
            title: "抽签中..."
          });
        case 2:
          _context4.next = 4;
          return _api_bussiness_competition__WEBPACK_IMPORTED_MODULE_3__["default"].draw(id);
        case 4:
          _context4.next = 6;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().hideLoading();
        case 6:
          _context4.next = 8;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().showToast({
            title: "抽签成功",
            icon: "success"
          });
        case 8:
          _context4.next = 10;
          return getCompetition();
        case 10:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  })), [getCompetition]);
  (0,react__WEBPACK_IMPORTED_MODULE_10__.useEffect)(function () {
    getCompetition();
  }, []);
  var onItemClick = (0,react__WEBPACK_IMPORTED_MODULE_10__.useCallback)(function (item) {
    var path = item.path;
    var commonPath = "".concat(path, "?id=").concat(id);
    _tarojs_taro__WEBPACK_IMPORTED_MODULE_9___default().navigateTo({
      url: commonPath
    });
  }, []);
  var hasDraw = (0,react__WEBPACK_IMPORTED_MODULE_10__.useMemo)(function () {
    if (!detailData) return false;
    return !((detailData === null || detailData === void 0 ? void 0 : detailData.apply) || {}).hasOwnProperty("number") && [COMPETION_STATUS.SIGN_UP, COMPETION_STATUS.PREPARE].includes(detailData.status) && detailData.signUp;
  }, [detailData]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxs)("div", {
    className: "h-_100vh_ flex flex-col",
    style: {
      paddingTop: rect.bottom + 10
    },
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_5__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Title__WEBPACK_IMPORTED_MODULE_7__["default"], {
      name: "\u8D5B\u4E8B\u8BE6\u60C5"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxs)("div", {
      className: "flex flex-col gap-3 flex-1 overflow-hidden",
      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
        className: "px-3",
        children: detailData && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Card_Competition__WEBPACK_IMPORTED_MODULE_6__["default"], {
          item: detailData
        })
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
        className: "flex-1 overflow-y-auto",
        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
          className: "px-3 flex flex-col gap-3",
          children: items.map(function (item) {
            var render = item.render;
            if (render) {
              return render(item, detailData);
            }
            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)("div", {
              className: "p-3 w-full rounded-lg bg-bgt font-bold text-center",
              onClick: function onClick(event) {
                return onItemClick(item, event);
              },
              children: item.name
            });
          })
        })
      })]
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsxs)("div", {
      className: "px-3",
      children: [detailData && detailData.status === COMPETION_STATUS.SIGN_UP && !detailData.signUp && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(SignButton, {
        item: detailData,
        onLoad: getCompetition
      }), hasDraw && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_components_bussiness_Form_Button__WEBPACK_IMPORTED_MODULE_13__["default"], {
        buttonText: "\u62BD\u7B7E",
        title: "\u62BD\u7B7E",
        className: "justify-center",
        customizationClick: onDraw
      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_16__.jsx)(_nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_25__.S, {
        position: "bottom"
      })]
    })]
  });
});

/***/ }),

/***/ "./src/components/bussiness/Posters/competition.tsx":
/*!**********************************************************!*\
  !*** ./src/components/bussiness/Posters/competition.tsx ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _Order_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../Order/Button */ "./src/components/bussiness/Order/Button.tsx");
/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api */ "./src/api/index.ts");
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");







var DEFAULT_COVER = "https://oss.gorillaballclub.cn/images/big-logo-y.png";
var WIDTH = 280;
var HEIGHT = 400;
var BGCOLOR = "#FBC202";
var PADDING = 20;
var QRSIZE = 80;
var GAP = 8;
/* harmony default export */ __webpack_exports__["default"] = (function (props) {
  // 裁剪图片
  var ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  var tempFilePath = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();
  var content = props.content,
    awards = props.awards,
    id = props.id;
  var URI = "".concat(_api__WEBPACK_IMPORTED_MODULE_3__.BASE_URI, "/competition/").concat(id, "/code");
  var clip2Cover = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().mark(function _callee(ctx, img, width, height) {
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            ctx.setFillStyle('#fff');
            ctx.fillRect(0, 0, width, HEIGHT);
            ctx.save();
            ctx.beginPath();
            ctx.rect(0, 0, width, height / 2);
            ctx.clip();
            ctx.drawImage(img.path, 0, -height / 4, width, height);
            ctx.restore();
          case 8:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x, _x2, _x3, _x4) {
      return _ref.apply(this, arguments);
    };
  }(), []);
  var drawAwards = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(function (ctx, currentHeight) {
    if (Array.isArray(awards) && awards.length) {
      var len = awards.length;
      var width = 60;
      var height = 40;
      var startX = (WIDTH - GAP * (len - 1)) / 2 - width / 2 * len;
      awards.forEach(function (item, index) {
        // 均分距离 计算第一个的位置
        var x = startX + index * (width + GAP);
        ctx.font = "10px";
        ctx.setFontSize(10);
        ctx.setFillStyle("#000");
        // 奖金标题名称
        var titleWidth = (ctx === null || ctx === void 0 ? void 0 : ctx.measureText(item.title).width) || 0;
        ctx.fillText(item.title, x + (width - titleWidth) / 2, currentHeight);
        // 奖励详情
        var textValue = "\u73B0\u91D1".concat(item.bonus, "\u5143");
        var textValue2 = "".concat(item.planBonus, "\u5143\u95E8\u5E97\u5361");
        var fontSize = 8;
        ctx.font = "italic bold ".concat(fontSize, "px cursive");
        ctx.setFontSize(fontSize);
        var textValueWidth = ctx.measureText(textValue).width || 0;
        var textValue2Width = ctx.measureText(textValue2).width || 0;
        var textHeight = currentHeight + 20 + height / 2 - fontSize * 1.5;
        ctx.fillText(textValue, x + (width - textValueWidth) / 2, textHeight);
        ctx.fillText(textValue2, x + (width - textValue2Width) / 2, textHeight + fontSize * 1.5);
        // 当前奖励的边框
        ctx.setStrokeStyle(BGCOLOR);
        ctx.lineJoin = "round";
        ctx.strokeRect(x, currentHeight + 10, width, height);
      });
    }
  }, [awards]);
  var draw = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().mark(function _callee3() {
    var cover, coverHeight, currentHeight, QRCode, QRHeight, QRX;
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().wrap(function _callee3$(_context3) {
      while (1) switch (_context3.prev = _context3.next) {
        case 0:
          if (!ctx.current) {
            _context3.next = 17;
            break;
          }
          _context3.next = 3;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().getImageInfo({
            src: DEFAULT_COVER
          });
        case 3:
          cover = _context3.sent;
          coverHeight = cover.height * (WIDTH / cover.width); // 绘制封面
          _context3.next = 7;
          return clip2Cover(ctx.current, cover, WIDTH, coverHeight);
        case 7:
          currentHeight = coverHeight / 2 + PADDING;
          if (Array.isArray(content)) {
            content.forEach(function (item) {
              var _ctx$current, _ctx$current2;
              var fontWeight = item.fontWeight,
                value = item.value,
                fonSize = item.fonSize,
                color = item.color;
              (_ctx$current = ctx.current) === null || _ctx$current === void 0 || _ctx$current.setFontSize(fonSize);
              if (ctx.current) {
                ctx.current.font = "".concat(fonSize, "px");
                ctx.current.font += " ".concat(fontWeight || 'normal', " Arial");
              }
              (_ctx$current2 = ctx.current) === null || _ctx$current2 === void 0 || _ctx$current2.setFillStyle(color);
              // 垂直居中
              var lineHeight = currentHeight + fonSize * 1.5 / 2;
              if (item.center) {
                var _ctx$current3, _ctx$current4;
                var textWidth = ((_ctx$current3 = ctx.current) === null || _ctx$current3 === void 0 ? void 0 : _ctx$current3.measureText(value).width) || 0;
                var x = WIDTH / 2 - textWidth / 2;
                (_ctx$current4 = ctx.current) === null || _ctx$current4 === void 0 || _ctx$current4.fillText(value, x, lineHeight);
              } else {
                var _ctx$current5;
                (_ctx$current5 = ctx.current) === null || _ctx$current5 === void 0 || _ctx$current5.fillText(value, PADDING, lineHeight);
              }
              currentHeight += fonSize * 1.5;
            });
          }
          _context3.next = 11;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().getImageInfo({
            src: URI
          });
        case 11:
          QRCode = _context3.sent;
          QRHeight = QRCode.height * (QRSIZE / QRCode.width);
          QRX = WIDTH / 2 - QRSIZE / 2;
          ctx.current.drawImage(QRCode.path, QRX, currentHeight, QRSIZE, QRHeight);
          drawAwards(ctx.current, currentHeight + QRHeight + PADDING);
          ctx.current.draw(true, /*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().mark(function _callee2() {
            var result;
            return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().wrap(function _callee2$(_context2) {
              while (1) switch (_context2.prev = _context2.next) {
                case 0:
                  _context2.next = 2;
                  return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().canvasToTempFilePath({
                    canvasId: 'canvas'
                  });
                case 2:
                  result = _context2.sent;
                  tempFilePath.current = result.tempFilePath;
                case 4:
                case "end":
                  return _context2.stop();
              }
            }, _callee2);
          })));
        case 17:
        case "end":
          return _context3.stop();
      }
    }, _callee3);
  })), [clip2Cover, ctx.current, content]);
  var save = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_5__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().mark(function _callee4() {
    return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_6__["default"])().wrap(function _callee4$(_context4) {
      while (1) switch (_context4.prev = _context4.next) {
        case 0:
          if (!tempFilePath.current) {
            _context4.next = 3;
            break;
          }
          _context4.next = 3;
          return _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().showShareImageMenu({
            path: tempFilePath.current
          });
        case 3:
        case "end":
          return _context4.stop();
      }
    }, _callee4);
  })), []);
  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function () {
    ctx.current = _tarojs_taro__WEBPACK_IMPORTED_MODULE_0___default().createCanvasContext("canvas");
    draw();
  }, []);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.Fragment, {
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("div", {
      id: "poster",
      className: "flex justify-center overflow-hidden rounded-md mb-4",
      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)("canvas", {
        "canvas-id": "canvas",
        width: "".concat(WIDTH, "px"),
        height: "".concat(HEIGHT, "px")
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_4__.jsx)(_Order_Button__WEBPACK_IMPORTED_MODULE_2__["default"], {
      buttonText: "\u4FDD\u5B58\u81F3\u76F8\u518C",
      mode: _Order_Button__WEBPACK_IMPORTED_MODULE_2__.MODES.Dark,
      onClick: function onClick() {
        return save();
      }
    })]
  });
});

/***/ }),

/***/ "./src/pages/competition/detail/index.tsx":
/*!************************************************!*\
  !*** ./src/pages/competition/detail/index.tsx ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/detail/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/detail/index!./src/pages/competition/detail/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white","disableScroll":true};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/competition/detail/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_competition_detail_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/competition/detail/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map