{"version": 3, "file": "pages/coupons/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC3IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/coupons/index.tsx?670c", "webpack://frontend-client/._src_pages_coupons_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport { menuRect } from \"@/utils\";\nimport api from \"@/api/bussiness/index\";\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport clsx from \"clsx\";\nimport Back from \"@/components/bussiness/back\";\nimport Title from \"@/components/bussiness/Title\";\nimport Coupon from \"@/components/bussiness/Card/Coupon\";\nimport Taro from \"@tarojs/taro\";\nimport { Text } from \"@tarojs/components\";\nimport { jsx as _jsx, Fragment as _Fragment, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar tabs = [{\n  title: \"未使用\",\n  params: {\n    used: false\n  },\n  key: 1,\n  isDefault: true\n}, {\n  title: \"已使用\",\n  params: {\n    used: true\n  },\n  key: 2\n}, {\n  title: \"已过期\",\n  params: {\n    expired: true\n  },\n  key: 3\n}];\nexport default (function () {\n  var rect = menuRect();\n  var _useState = useState([]),\n    _useState2 = _slicedToArray(_useState, 2),\n    list = _useState2[0],\n    setList = _useState2[1];\n  var _useState3 = useState(function () {\n      var currentTab = tabs.find(function (item) {\n        return item.isDefault;\n      });\n      return currentTab ? currentTab.key : tabs[0].key;\n    }),\n    _useState4 = _slicedToArray(_useState3, 2),\n    currentKey = _useState4[0],\n    setCurrentKey = _useState4[1];\n  var params = useMemo(function () {\n    var currentTab = tabs.find(function (item) {\n      return item.key === currentKey;\n    });\n    if (currentTab) return currentTab.params;\n    return tabs[0].params;\n  }, [currentKey]);\n  var getList = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n    var _yield$api$user$getCo, data;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          setList([]);\n          _context.next = 3;\n          return api.user.getCoupons(params);\n        case 3:\n          _yield$api$user$getCo = _context.sent;\n          data = _yield$api$user$getCo.data;\n          setList(data);\n        case 6:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  })), [setList, params]);\n  useEffect(function () {\n    getList();\n  }, [params]);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    className: \"h-[100vh] flex flex-col\",\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u4F18\\u60E0\\u5238\"\n    }), /*#__PURE__*/_jsxs(\"div\", {\n      className: \"flex-1 overflow-hidden flex flex-col\",\n      children: [/*#__PURE__*/_jsx(\"div\", {\n        className: \"flex px-3\",\n        children: tabs.map(function (tabItem) {\n          return /*#__PURE__*/_jsx(\"div\", {\n            className: clsx(\"flex-1 transition-all flex justify-center py-2 text-sm\", {\n              \"text-primary font-semibold border-b border-primary\": currentKey === tabItem.key\n            }),\n            onClick: function onClick() {\n              return setCurrentKey(tabItem.key);\n            },\n            children: tabItem.title\n          });\n        })\n      }), /*#__PURE__*/_jsx(\"div\", {\n        className: \"p-3 flex flex-col gap-3 flex-1 overflow-y-auto\",\n        children: !!list.length ? /*#__PURE__*/_jsx(_Fragment, {\n          children: list.map(function (item) {\n            return /*#__PURE__*/_jsx(Coupon, {\n              coupon: item,\n              renderDesc: function renderDesc() {\n                return /*#__PURE__*/_jsx(Text, {\n                  className: \"text-xs text-primary\",\n                  children: item.clubs.map(function (item) {\n                    return item.name;\n                  }).join(\",\")\n                });\n              },\n              onUse: function onUse() {\n                Taro.navigateTo({\n                  url: \"/pages/venue/index?id=\".concat(item.clubs[0].id)\n                });\n                getList();\n              },\n              renderRight: function renderRight() {\n                return /*#__PURE__*/_jsx(_Fragment, {\n                  children: !params.used && !params.expired && /*#__PURE__*/_jsx(\"div\", {\n                    className: \"border-background/75 border rounded-sm px-2 p-1 font-semibold text-xs\",\n                    children: \"\\u53BB\\u4F7F\\u7528\"\n                  })\n                });\n              }\n            });\n          })\n        }) : /*#__PURE__*/_jsx(\"div\", {\n          className: \"flex flex-1 justify-center items-center\",\n          children: \"\\u6682\\u65E0\\u4F18\\u60E0\\u5238\"\n        })\n      }), /*#__PURE__*/_jsx(_SafeArea, {\n        position: \"bottom\"\n      })]\n    })]\n  });\n});", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/coupons/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/coupons/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}