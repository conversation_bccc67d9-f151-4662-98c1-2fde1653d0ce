package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClientUserQueryDto extends PageRequest {
    @QueryParam("phone")
    @Schema(description = "手机号")
    private String phone;
    @Schema(hidden = true)
    private List<Long> userIds;
    @Schema(hidden = true)
    private Long tenantId;
    @QueryParam("clubId")
    @Schema(description = "门店ID")
    private Long clubId;
}
