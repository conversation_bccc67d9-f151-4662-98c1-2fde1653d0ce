{"version": 3, "file": "pages/competition/live/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC1IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/competition/live/index.tsx?aad6", "webpack://frontend-client/._src_pages_competition_live_index.tsx"], "sourcesContent": ["import _objectSpread from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _toConsumableArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport competition from \"@/api/bussiness/competition\";\nimport Title from \"@/components/bussiness/Title\";\nimport Back from \"@/components/bussiness/back\";\nimport { chunk, menuRect } from \"@/utils\";\nimport { useRouter } from \"@tarojs/taro\";\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport Tab from \"@/components/ui/tab\";\nimport Match from \"@/components/bussiness/Match\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar STATUS_TYPES = /*#__PURE__*/function (STATUS_TYPES) {\n  STATUS_TYPES[STATUS_TYPES[\"NONE\"] = 0] = \"NONE\";\n  STATUS_TYPES[STATUS_TYPES[\"WAITING\"] = 1] = \"WAITING\";\n  STATUS_TYPES[STATUS_TYPES[\"ONGOING\"] = 2] = \"ONGOING\";\n  STATUS_TYPES[STATUS_TYPES[\"FINISHED\"] = 3] = \"FINISHED\";\n  return STATUS_TYPES;\n}(STATUS_TYPES || {});\nexport default (function () {\n  var rect = menuRect();\n  var router = useRouter();\n  var id = router.params.id;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    detailData = _useState2[0],\n    setDetailData = _useState2[1];\n  var _useState3 = useState(1),\n    _useState4 = _slicedToArray(_useState3, 2),\n    current = _useState4[0],\n    setCurrent = _useState4[1];\n  var getCompetition = useCallback(/*#__PURE__*/_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n    var _yield$competition$ge, data;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          _context.next = 2;\n          return competition.getDetail(id);\n        case 2:\n          _yield$competition$ge = _context.sent;\n          data = _yield$competition$ge.data;\n          setDetailData(data);\n          return _context.abrupt(\"return\", data);\n        case 6:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  })), [setDetailData]);\n  var getPlayers = useCallback(function (items) {\n    return items.reduce(function (result, item) {\n      var player1UserId = item.player1UserId,\n        player1Name = item.player1Name,\n        player1Number = item.player1Number,\n        player2Number = item.player2Number,\n        player2Name = item.player2Name,\n        player2UserId = item.player2UserId,\n        player2Score = item.player2Score,\n        player1Score = item.player1Score,\n        winner = item.winner;\n      return [].concat(_toConsumableArray(result), [{\n        player: {\n          nickname: player1Name,\n          userId: player1UserId,\n          number: player1Number\n        },\n        score: player1Score,\n        winner: winner\n      }, {\n        player: {\n          userId: player2UserId,\n          nickname: player2Name,\n          number: player2Number\n        },\n        score: player2Score,\n        winner: winner\n      }]);\n    }, []);\n  }, []);\n  var schedulesMatches = useMemo(function () {\n    var _detailData$schedules;\n    return (detailData === null || detailData === void 0 || (_detailData$schedules = detailData.schedules) === null || _detailData$schedules === void 0 ? void 0 : _detailData$schedules.map(function (schedule) {\n      var _detailData$matches;\n      return _objectSpread(_objectSpread({}, schedule), {}, {\n        matches: chunk(getPlayers(detailData === null || detailData === void 0 || (_detailData$matches = detailData.matches) === null || _detailData$matches === void 0 ? void 0 : _detailData$matches.filter(function (match) {\n          return match.round === schedule.round;\n        })), 2)\n      });\n    })) || [];\n  }, [detailData, getPlayers]);\n  useEffect(function () {\n    getCompetition();\n  }, []);\n  var tabs = useMemo(function () {\n    var filterSchedulesMatches = schedulesMatches.filter(function (item) {\n      return [STATUS_TYPES.ONGOING, STATUS_TYPES.FINISHED].includes(item.status);\n    });\n    return filterSchedulesMatches.map(function (schedule) {\n      console.log(schedule.matches);\n      return {\n        title: schedule.title,\n        route: \"\",\n        component: /*#__PURE__*/_jsx(\"div\", {\n          className: \"flex flex-col gap-3 px-3\",\n          children: schedule.matches.map(function (players) {\n            var winnerIds = players.map(function (item) {\n              return item.winner;\n            });\n            return /*#__PURE__*/_jsx(\"div\", {\n              children: /*#__PURE__*/_jsx(Match, {\n                players: players,\n                isLastMatch: true,\n                resultIds: winnerIds,\n                noNum: true\n              })\n            });\n          })\n        })\n      };\n    });\n  }, [schedulesMatches]);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: \"flex flex-col h-[100vh]\",\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u6BD4\\u5206\\u76F4\\u64AD\"\n    }), /*#__PURE__*/_jsx(Tab, {\n      route: tabs,\n      current: current,\n      onChange: function onChange(value) {\n        return setCurrent(value);\n      }\n    })]\n  });\n});", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/competition/live/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/competition/live/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}