package tech.wejoy.billiard.api.service;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Synchronization;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.api.queue.ClubDeviceSend;
import tech.wejoy.billiard.common.bo.RefundResultBo;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.entity.ClubTable;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.OrderManager;
import tech.wejoy.billiard.common.strategy.Payment;
import tech.wejoy.billiard.common.utils.PriceUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class OrderApiService {

    private final OrderManager orderManager;

    private final ClubDeviceSend clubDeviceSend;

    private final ClubManager clubManager;

    private final TransactionManager transactionManager;

    public void payOrder(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        OrderInfo payInfoOrder = JsonUtils.toObject(payInfo, OrderInfo.class);
        OrderInfo order = orderManager.fetchOrderByNo(payInfoOrder.getOrderNo());

        // 检查是否为抢台费订单
        if (order.getDescription() != null && order.getDescription().contains("TABLE_GRAB")) {
            // 处理抢台费订单支付成功逻辑
            handleTableGrabOrderPayment(order, payerTotal);
        } else {
            // 处理普通订单支付成功逻辑
            handleNormalOrderPayment(order, payerTotal, userId);
        }
    }

    private void handleTableGrabOrderPayment(OrderInfo grabOrder, BigDecimal payerTotal) {
        try {
            // 解析抢台费订单信息
            @SuppressWarnings("unchecked")
            Map<String, Object> grabInfo = JsonUtils.toObject(grabOrder.getDescription(), Map.class);
            String originalOrderNo = (String) grabInfo.get("originalOrderNo");
            String grabType = (String) grabInfo.get("grabType");

            // 获取原订单
            OrderInfo originalOrder = orderManager.fetchOrderByNo(originalOrderNo);
            if (originalOrder == null) {
                log.error("抢台费订单支付成功，但原订单不存在: {}", originalOrderNo);
                return;
            }

            // 更新抢台费订单状态
            grabOrder.setStatus(OrderStatusEnum.FINISH);
            grabOrder.setPayAmount(payerTotal);
            grabOrder.setPayTime(LocalDateTime.now());
            orderManager.save(grabOrder);

            // 处理原订单退款
            LocalDateTime endTime = LocalDateTime.now();
            if ("ALL".equals(grabType)) {
                // 全额抢台：原订单全额退款
                BigDecimal refundAmount = originalOrder.getTotalAmount();
                if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    RefundResultBo refund = Payment.getPayment(originalOrder.getPayType()).refund(originalOrder, refundAmount);
                    if (refund.isSuccess()) {
                        originalOrder.setRefundTime(endTime);
                        originalOrder.setRefundAmount(refund.getAmount());
                    } else {
                        log.error("抢台费订单支付成功，但原订单退款失败: {}, 退款金额: {}", originalOrderNo, refundAmount);
                    }
                }
            } else if ("HALF".equals(grabType)) {
                // 半价抢台：原订单部分退款
                ClubTable table = clubManager.fetchClubTableById(originalOrder.getTableId());
                BigDecimal originalRealPrice = PriceUtils.calculatePrice(table, originalOrder.getRealStartTime(), endTime);
                BigDecimal refundAmount = originalOrder.getTotalAmount().subtract(originalRealPrice.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP));
                if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                    RefundResultBo refund = Payment.getPayment(originalOrder.getPayType()).refund(originalOrder, refundAmount);
                    if (refund.isSuccess()) {
                        originalOrder.setRefundTime(endTime);
                        originalOrder.setRefundAmount(refund.getAmount());
                    } else {
                        log.error("抢台费订单支付成功，但原订单退款失败: {}, 退款金额: {}", originalOrderNo, refundAmount);
                    }
                }
            }

            // 更新原订单状态
            originalOrder.setStatus(OrderStatusEnum.FINISH);
            originalOrder.setRealEndTime(endTime);
            originalOrder.setEndTime(endTime);
            orderManager.save(originalOrder);

            // 更新桌台状态为空闲
            clubManager.updateTableIdle(originalOrder.getTableId());

            log.info("抢台费订单支付成功处理完成: 新订单={}, 原订单={}, 类型={}", grabOrder.getOrderNo(), originalOrderNo, grabType);

        } catch (Exception e) {
            log.error("处理抢台费订单支付成功时发生错误: {}", grabOrder.getOrderNo(), e);
        }
    }

    private void handleNormalOrderPayment(OrderInfo order, BigDecimal payerTotal, Long userId) {
        order.setStatus(OrderStatusEnum.PAID);
        order.setPayAmount(payerTotal);
        order.setPayTime(LocalDateTime.now());
        orderManager.save(order);
        clubManager.updateTableUsing(order.getTableId(), userId, order.getEndTime());
        if (LocalDateTime.now().plusSeconds(30).isAfter(order.getStartTime())) {
            try {
                Transaction transaction = transactionManager.getTransaction();
                transaction.registerSynchronization(new Synchronization() {
                    @Override
                    public void beforeCompletion() {

                    }

                    @Override
                    public void afterCompletion(int status) {
                        clubDeviceSend.send(order.getOrderNo(), DeviceOperationTypeEnum.START);
                    }
                });
            } catch (Exception e) {
                log.error("send start device error", e);
            }
        }
    }

}
