package tech.wejoy.billiard.api.service;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Synchronization;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.api.queue.ClubDeviceSend;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.OrderManager;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class OrderApiService {

    private final OrderManager orderManager;

    private final ClubDeviceSend clubDeviceSend;

    private final ClubManager clubManager;

    private final TransactionManager transactionManager;

    public void payOrder(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        OrderInfo payInfoOrder = JsonUtils.toObject(payInfo, OrderInfo.class);
        OrderInfo order = orderManager.fetchOrderByNo(payInfoOrder.getOrderNo());
        order.setStatus(OrderStatusEnum.PAID);
        order.setPayAmount(payerTotal);
        order.setPayTime(LocalDateTime.now());
        orderManager.save(order);
        clubManager.updateTableUsing(order.getTableId(), userId, order.getEndTime());
        if (LocalDateTime.now().plusSeconds(30).isAfter(order.getStartTime())) {
            try {
                Transaction transaction = transactionManager.getTransaction();
                transaction.registerSynchronization(new Synchronization() {
                    @Override
                    public void beforeCompletion() {

                    }

                    @Override
                    public void afterCompletion(int status) {
                        clubDeviceSend.send(order.getOrderNo(), DeviceOperationTypeEnum.START);
                    }
                });
            } catch (Exception e) {
                log.error("send start device error", e);
            }
        }
    }

}
