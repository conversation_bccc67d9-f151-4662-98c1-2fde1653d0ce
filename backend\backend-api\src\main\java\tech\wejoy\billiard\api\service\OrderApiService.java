package tech.wejoy.billiard.api.service;

import com.congeer.utils.JsonUtils;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Synchronization;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.api.queue.ClubDeviceSend;
import tech.wejoy.billiard.common.entity.BillInfo;
import tech.wejoy.billiard.common.entity.OrderInfo;
import tech.wejoy.billiard.common.enums.DeviceOperationTypeEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;
import tech.wejoy.billiard.common.manager.ClubManager;
import tech.wejoy.billiard.common.manager.OrderManager;
import tech.wejoy.billiard.common.service.OrderService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Slf4j
public class OrderApiService {

    private final OrderManager orderManager;

    private final ClubDeviceSend clubDeviceSend;

    private final ClubManager clubManager;

    private final TransactionManager transactionManager;

    private final OrderService orderService;

    public void payOrder(BillInfo bill) {
        String payInfo = bill.getPayInfo();
        Long userId = bill.getUserId();
        BigDecimal payerTotal = bill.getPayAmount();
        OrderInfo payInfoOrder = JsonUtils.toObject(payInfo, OrderInfo.class);
        OrderInfo order = orderManager.fetchOrderByNo(payInfoOrder.getOrderNo());

        // 检查是否为抢台费订单
        if (order.getDescription() != null && order.getDescription().contains("TABLE_GRAB")) {
            // 处理抢台费订单支付成功逻辑
            handleTableGrabOrderPayment(order, payerTotal);
        } else {
            // 处理普通订单支付成功逻辑
            handleNormalOrderPayment(order, payerTotal, userId);
        }
    }

    private void handleTableGrabOrderPayment(OrderInfo grabOrder, BigDecimal payerTotal) {
        try {
            // 解析抢台费订单信息
            @SuppressWarnings("unchecked")
            Map<String, Object> grabInfo = JsonUtils.toObject(grabOrder.getDescription(), Map.class);
            String originalOrderNo = (String) grabInfo.get("originalOrderNo");
            String grabType = (String) grabInfo.get("grabType");

            if (originalOrderNo == null || grabType == null) {
                log.error("抢台费订单信息不完整: {}", grabOrder.getOrderNo());
                return;
            }

            // 更新抢台费订单支付信息
            grabOrder.setPayAmount(payerTotal);
            grabOrder.setPayTime(LocalDateTime.now());
            orderManager.save(grabOrder);

            // 使用OrderService的统一处理方法
            orderService.handleTableGrabCompletion(grabOrder, originalOrderNo, grabType, LocalDateTime.now());

            log.info("抢台费订单支付成功处理完成: 新订单={}, 原订单={}, 类型={}", grabOrder.getOrderNo(), originalOrderNo, grabType);
        } catch (Exception e) {
            log.error("处理抢台费订单支付成功时发生错误: {}", grabOrder.getOrderNo(), e);
        }
    }

    private void handleNormalOrderPayment(OrderInfo order, BigDecimal payerTotal, Long userId) {
        order.setStatus(OrderStatusEnum.PAID);
        order.setPayAmount(payerTotal);
        order.setPayTime(LocalDateTime.now());
        orderManager.save(order);
        clubManager.updateTableUsing(order.getTableId(), userId, order.getEndTime());
        if (LocalDateTime.now().plusSeconds(30).isAfter(order.getStartTime())) {
            try {
                Transaction transaction = transactionManager.getTransaction();
                transaction.registerSynchronization(new Synchronization() {
                    @Override
                    public void beforeCompletion() {

                    }

                    @Override
                    public void afterCompletion(int status) {
                        clubDeviceSend.send(order.getOrderNo(), DeviceOperationTypeEnum.START);
                    }
                });
            } catch (Exception e) {
                log.error("send start device error", e);
            }
        }
    }

}
