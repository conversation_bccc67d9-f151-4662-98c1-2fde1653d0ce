package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class PrepayBo {

    @Schema(description = "是否成功")
    private boolean success;

    private String message;

    @Schema(description = "支付参数")
    private Object extra;

    @Schema(description = "支付金额")
    private BigDecimal amount;

}
