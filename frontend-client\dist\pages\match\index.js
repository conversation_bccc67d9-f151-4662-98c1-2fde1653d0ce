"use strict";
(wx["webpackJsonp"] = wx["webpackJsonp"] || []).push([["pages/match/index"],{

/***/ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/match/index!./src/pages/match/index.tsx":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/match/index!./src/pages/match/index.tsx ***!
  \****************************************************************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

/* unused harmony export MatchDetail */
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea_style_css__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea/style/css */ "./node_modules/@nutui/nutui-react-taro/dist/esm/SafeArea/style/css.js");
/* harmony import */ var _nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @nutui/nutui-react-taro/dist/esm/SafeArea */ "./node_modules/@nutui/nutui-react-taro/dist/esm/safearea.taro-BJpvFsMf.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js */ "./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js */ "./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js");
/* harmony import */ var C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js */ "./node_modules/@babel/runtime/helpers/esm/slicedToArray.js");
/* harmony import */ var _api_bussiness_match__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/api/bussiness/match */ "./src/api/bussiness/match.ts");
/* harmony import */ var _components_bussiness_back__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/bussiness/back */ "./src/components/bussiness/back.tsx");
/* harmony import */ var _components_bussiness_Title__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/bussiness/Title */ "./src/components/bussiness/Title/index.tsx");
/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ "./src/components/ui/button.tsx");
/* harmony import */ var _custom_match_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/custom/match-card */ "./src/custom/match-card.tsx");
/* harmony import */ var _hooks__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks */ "./src/hooks/index.ts");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/utils */ "./src/utils.ts");
/* harmony import */ var _tarojs_components__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tarojs/components */ "./node_modules/@tarojs/plugin-platform-weapp/dist/components-react.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tarojs/taro */ "./node_modules/@tarojs/taro/index.js");
/* harmony import */ var _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_tarojs_taro__WEBPACK_IMPORTED_MODULE_7__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_8__);
/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react/jsx-runtime */ "./node_modules/react/cjs/react-jsx-runtime.production.min.js");
















var MatchDetail = function MatchDetail() {
  var rect = (0,_utils__WEBPACK_IMPORTED_MODULE_6__.menuRect)();
  var router = (0,_tarojs_taro__WEBPACK_IMPORTED_MODULE_7__.useRouter)();
  var id = router.params.id;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_8__.useState)(null),
    _useState2 = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_10__["default"])(_useState, 2),
    data = _useState2[0],
    setData = _useState2[1];
  var _useStore = (0,_hooks__WEBPACK_IMPORTED_MODULE_11__.useStore)(function (state) {
      return state.auth;
    }),
    user = _useStore.user;
  var getData = /*#__PURE__*/function () {
    var _ref = (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_asyncToGenerator_js__WEBPACK_IMPORTED_MODULE_12__["default"])(/*#__PURE__*/(0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_13__["default"])().mark(function _callee() {
      var _yield$match$detail, data;
      return (0,C_Users_Illuxiza_GitWork_partime_billiaard_frontend_client_node_modules_babel_runtime_helpers_esm_regeneratorRuntime_js__WEBPACK_IMPORTED_MODULE_13__["default"])().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            if (id) {
              _context.next = 2;
              break;
            }
            return _context.abrupt("return");
          case 2:
            _context.next = 4;
            return _api_bussiness_match__WEBPACK_IMPORTED_MODULE_1__["default"].detail(parseInt(id));
          case 4:
            _yield$match$detail = _context.sent;
            data = _yield$match$detail.data;
            setData(data);
          case 7:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function getData() {
      return _ref.apply(this, arguments);
    };
  }();
  (0,react__WEBPACK_IMPORTED_MODULE_8__.useEffect)(function () {
    if (!id) {
      return;
    }
    getData();
  }, [id]);
  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsxs)(_tarojs_components__WEBPACK_IMPORTED_MODULE_14__.View, {
    style: {
      paddingTop: rect.bottom + 5
    },
    className: "h-_100vh_ flex flex-col p-3",
    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_bussiness_back__WEBPACK_IMPORTED_MODULE_2__["default"], {}), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_bussiness_Title__WEBPACK_IMPORTED_MODULE_3__["default"], {
      name: "\u7EA6\u7403\u8BE6\u60C5"
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      className: "flex-1",
      children: data && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_custom_match_card__WEBPACK_IMPORTED_MODULE_5__.MatchCard, {
        data: data
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)("div", {
      className: "w-full",
      children: user && data && data.status === 1 && user.id !== data.userId && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {
        variant: data.request ? "destructive" : "default",
        className: "w-full h-10",
        onClick: function onClick() {
          _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().showLoading({
            title: "",
            mask: true
          });
          if (data.request) {
            _api_bussiness_match__WEBPACK_IMPORTED_MODULE_1__["default"].cancel(id).then(function (resp) {
              if (resp.statusCode != 200 && resp.statusCode != 204) {
                _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().showToast({
                  title: resp.data.message,
                  icon: "none"
                });
                return;
              }
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().showToast({
                title: "取消成功",
                icon: "none"
              });
              getData();
            });
            return;
          }
          _api_bussiness_match__WEBPACK_IMPORTED_MODULE_1__["default"].request(id).then(function (resp) {
            if (resp.statusCode != 200 && resp.statusCode != 204) {
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().hideLoading();
              _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().showToast({
                title: resp.data.message,
                icon: "none"
              });
              return;
            }
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().hideLoading();
            _tarojs_taro__WEBPACK_IMPORTED_MODULE_7___default().showToast({
              title: "应约成功",
              icon: "none"
            });
            getData();
          });
        },
        children: !(data !== null && data !== void 0 && data.request) ? "我要应约" : "取消应约"
      })
    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_9__.jsx)(_nutui_nutui_react_taro_dist_esm_SafeArea__WEBPACK_IMPORTED_MODULE_15__.S, {
      position: "bottom"
    })]
  });
};
/* harmony default export */ __webpack_exports__["default"] = (MatchDetail);

/***/ }),

/***/ "./src/pages/match/index.tsx":
/*!***********************************!*\
  !*** ./src/pages/match/index.tsx ***!
  \***********************************/
/***/ (function(__unused_webpack_module, __unused_webpack___webpack_exports__, __webpack_require__) {

/* harmony import */ var _tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tarojs/runtime */ "./node_modules/@tarojs/runtime/dist/dsl/common.js");
/* harmony import */ var _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_match_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! !!../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/match/index!./index.tsx */ "./node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/match/index!./src/pages/match/index.tsx");


var config = {"navigationStyle":"custom","navigationBarTitleText":"猩猩球社","navigationBarBackgroundColor":"#171717","navigationBarTextStyle":"white"};



var taroOption = (0,_tarojs_runtime__WEBPACK_IMPORTED_MODULE_1__.createPageConfig)(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_match_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"], 'pages/match/index', {root:{cn:[]}}, config || {})
if (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_match_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"] && _node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_match_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors) {
  taroOption.behaviors = (taroOption.behaviors || []).concat(_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_match_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"].behaviors)
}
var inst = Page(taroOption)



/* unused harmony default export */ var __WEBPACK_DEFAULT_EXPORT__ = (_node_modules_tarojs_taro_loader_lib_entry_cache_js_name_pages_match_index_index_tsx__WEBPACK_IMPORTED_MODULE_0__["default"]);


/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["taro","vendors","common"], function() { return __webpack_exec__("./src/pages/match/index.tsx"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ }
]);
//# sourceMappingURL=index.js.map