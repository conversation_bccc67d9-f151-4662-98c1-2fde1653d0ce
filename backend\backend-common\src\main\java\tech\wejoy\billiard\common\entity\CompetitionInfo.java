package tech.wejoy.billiard.common.entity;


import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.CompetitionLevelEnum;
import tech.wejoy.billiard.common.enums.CompetitionStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class CompetitionInfo extends BaseEntity {

    @Column(columnDefinition = "text")
    private String title;

    private Long clubId;

    private String city;

    private Long tenantId;

    private CompetitionLevelEnum level;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime signUpStartTime;

    private LocalDateTime signUpEndTime;

    private CompetitionStatusEnum status;

    private BigDecimal entryFee;

    private Integer maxPlayer;

    private BigDecimal bonus;

}
