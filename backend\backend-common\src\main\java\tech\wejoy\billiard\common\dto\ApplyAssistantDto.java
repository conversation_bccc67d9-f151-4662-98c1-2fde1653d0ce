package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.GenderEnum;

import java.time.LocalDate;
import java.util.List;

@Data
public class ApplyAssistantDto {

    @Schema(hidden = true)
    private Long userId;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "标签")
    private List<String> tags;

    @Schema(description = "性别")
    private GenderEnum gender;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "生日")
    private LocalDate birth;

    @Schema(description = "入职时间")
    private LocalDate startWork;

    private String realName;

    private String idCard;

}
