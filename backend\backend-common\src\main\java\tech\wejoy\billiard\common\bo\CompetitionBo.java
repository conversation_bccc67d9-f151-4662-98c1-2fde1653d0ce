package tech.wejoy.billiard.common.bo;

import jakarta.persistence.Column;
import lombok.Data;
import tech.wejoy.billiard.common.enums.CompetitionStatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CompetitionBo {

    private Long id;

    @Column(columnDefinition = "text")
    private String title;

    private Long clubId;

    private ApiClubBo club;

    private String city;

    private Long tenantId;

    private Integer level;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private LocalDateTime signUpStartTime;

    private LocalDateTime signUpEndTime;

    private CompetitionStatusEnum status;

    private BigDecimal entryFee;

    private Integer maxPlayer;

    private BigDecimal bonus;

    private List<CompetitionAwardBo> awards;

    private List<CompetitionMatchBo> matches;

    private List<CompetitionScheduleBo> schedules;

    private boolean signUp;

    private Long signUpCount;

    private CompetitionApplyBo apply;

    private List<CompetitionApplyBo> ranks;

}
