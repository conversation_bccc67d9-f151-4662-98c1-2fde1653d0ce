package tech.wejoy.billiard.common.dto;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderPayTypeEnum;
import tech.wejoy.billiard.common.enums.StartFromEnum;

import java.time.LocalDateTime;

@Data
public class StartTableDto {

    @Schema(hidden = true)
    private Long userId;

    @Schema(description = "桌号")
    private Long tableId;

    @Schema(description = "来源")
    private StartFromEnum from;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    private OrderPayTypeEnum payType;

    private Object extra;

}
