package tech.wejoy.billiard.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.AdminAssistantBo;
import tech.wejoy.billiard.bo.AssistantApplyBo;
import tech.wejoy.billiard.bo.AssistantClubRequestBo;
import tech.wejoy.billiard.bo.AssistantOrderBo;
import tech.wejoy.billiard.dto.*;
import tech.wejoy.billiard.service.AssistantService;

import java.util.List;

@Path("/assistant")
@Tag(name = "助教接口")
@Produces(MediaType.APPLICATION_JSON)
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class AssistantApi {

    private final AssistantService assistantService;

    @GET
    @Path("/list")
    public Page<AdminAssistantBo> list(@BeanParam AdminAssistantQueryDto dto) {
        return assistantService.adminList(dto);
    }

    @GET
    @Path("/{id}")
    public AdminAssistantBo detail(@PathParam("id") Long id) {
        return assistantService.adminDetail(id);
    }

    @PUT
    @Path("/{id}")
    public void update(@PathParam("id") Long id, AssistantUpdateDto dto) {
        dto.setId(id);
        assistantService.update(dto);
    }

    @GET
    @Path("/apply/list")
    public Page<AssistantApplyBo> applyList(@BeanParam AdminAssistantApplyQueryDto dto) {
        return assistantService.adminApplyList(dto);
    }

    @POST
    @Path("/apply/{id}/pass")
    public void pass(@PathParam("id") Long id) {
        assistantService.pass(id);
    }

    @POST
    @Path("/apply/{id}/reject")
    public void reject(@PathParam("id") Long id) {
        assistantService.reject(id);
    }

    @GET
    @Path("/order/list")
    public Page<AssistantOrderBo> orderList(@BeanParam AdminAssistantOrderQueryDto dto) {
        return assistantService.adminOrderList(dto);
    }

    @GET
    @Path("/order/{id}")
    public AssistantOrderBo orderDetail(@PathParam("id") Long id) {
        return assistantService.adminOrderDetail(id);
    }

    @GET
    @Path("/requests")
    public List<AssistantClubRequestBo> requestList(@BeanParam ClubRequestDto dto) {
        return assistantService.adminRequestList(dto);
    }

    @GET
    @Path("/passes")
    public List<AssistantClubRequestBo> passList(@BeanParam ClubRequestDto dto) {
        return assistantService.adminPassList(dto);
    }

    @POST
    @Path("/request/{id}/pass")
    public void passRequest(@PathParam("id") Long id) {
        assistantService.passRequest(id);
    }

    @POST
    @Path("/request/{id}/reject")
    public void rejectRequest(@PathParam("id") Long id) {
        assistantService.rejectRequest(id);
    }

    @POST
    @Path("/pass/{id}/cancel")
    public void cancelPass(@PathParam("id") Long id) {
        assistantService.cancelPass(id);
    }

}
