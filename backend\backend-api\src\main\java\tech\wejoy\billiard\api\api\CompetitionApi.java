package tech.wejoy.billiard.api.api;

import com.congeer.core.bean.Page;
import com.congeer.security.core.bean.SessionInfo;
import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.api.service.WxUserService;
import tech.wejoy.billiard.common.bo.CompetitionBo;
import tech.wejoy.billiard.common.dto.CompetitionQueryDto;
import tech.wejoy.billiard.common.dto.SignUpDto;
import tech.wejoy.billiard.common.service.CompetitionService;

import java.io.File;

@Path("/competition")
@Tag(name = "比赛接口")
@Produces(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor
public class CompetitionApi {

    private final CompetitionService competitionService;

    private final WxUserService wxUserService;

    @GET
    @Path("/list")
    @Operation(summary = "比赛列表")
    public Page<CompetitionBo> list(@BeanParam CompetitionQueryDto dto) {
        return competitionService.list(dto);
    }

    @GET
    @Path("/my")
    @Operation(summary = "我的比赛")
    @Authorized
    public Page<CompetitionBo> my(@BeanParam CompetitionQueryDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return competitionService.myList(dto);
    }

    @GET
    @Path("/{id}/code")
    @Operation(summary = "获取助教码")
    @Produces("image/jpeg")
    public Response getQrcode(@PathParam("id") Long id) {
        File entity = wxUserService.getCompetitionQrcode(id);
        return Response.ok(entity)
                .header("Content-Disposition", "attachment; filename=" + entity.getName())
                .build();
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "比赛详情")
    public CompetitionBo detail(@PathParam("id") Long id) {
        SessionInfo<Long, WxUserBo> session = SecurityHolder.<Long, WxUserBo>session();
        if (session == null) {
            return competitionService.detail(id, null);
        }
        return competitionService.detail(id, session.getUserId());
    }

    @POST
    @Path("/sign")
    @Operation(summary = "报名比赛")
    @Authorized
    public void sign(SignUpDto dto) {
        Long userId = SecurityHolder.<Long, WxUserBo>session().getUserId();
        dto.setUserId(userId);
        competitionService.sign(dto);
    }

    @POST
    @Path("/{id}/draw")
    @Operation(summary = "抽签")
    @Authorized
    public void draw(@PathParam("id") Long id) {
        competitionService.draw(id, SecurityHolder.<Long, WxUserBo>session().getUserId());
    }

}
