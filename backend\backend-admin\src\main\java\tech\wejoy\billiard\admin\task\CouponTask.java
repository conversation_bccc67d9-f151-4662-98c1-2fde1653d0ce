package tech.wejoy.billiard.admin.task;

import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.service.CouponService;

@ApplicationScoped
@RequiredArgsConstructor
public class CouponTask {

    private final CouponService couponService;

    @Scheduled(cron = "0 0 * * * ?")
    void expireCoupon() {
        couponService.expireCoupon();
    }


}
