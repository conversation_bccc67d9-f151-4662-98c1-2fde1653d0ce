package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class ClubGiftProfitBo {

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "俱乐部ID")
    private Long clubId;

    @Schema(description = "俱乐部名称")
    private String clubName;

    @Schema(description = "用户ID")
    private Long userId;

    private Long operatorId;

    private Long giftId;

    private String operatorName;

    @Schema(description = "用户电话")
    private String userPhone;

    @Schema(description = "支付金额")
    private BigDecimal payAmount;

    @Schema(description = "服务费")
    private BigDecimal serviceFee;

    @Schema(description = "净收入")
    private BigDecimal profitAmount;

}
