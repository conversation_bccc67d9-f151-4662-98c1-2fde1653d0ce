package tech.wejoy.billiard.common.manager;

import com.congeer.core.bean.Page;
import com.congeer.web.bean.request.PageRequest;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.bo.BannerBo;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.BannerTypeEnum;
import tech.wejoy.billiard.common.repo.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@Transactional(rollbackOn = Exception.class)
@RequiredArgsConstructor
public class ConfigManager {

    private final AliyunOssRepo aliyunOssRepo;

    private final WxMiniappRepo wxMiniappRepo;

    private final AqaraConfigRepo aqaraConfigRepo;

    private final SmyooConfigRepo smyooConfigRepo;

    private final FeedbackRepo feedbackRepo;

    private final BannerInfoRepo bannerInfoRepo;

    private final BaseConfigRepo baseConfigRepo;

    private final MeituanConfigRepo meituanConfigRepo;

    private final DouyinConfigRepo douyinConfigRepo;

    private final WejoyConfigRepo wejoyConfigRepo;

    public List<BannerInfo> getBannerListByType(BannerTypeEnum type) {
        return bannerInfoRepo.find("type = ?1 and status = 1", Sort.by("seq"), type).list();
    }

    public WxMiniapp getWxMiniapp(String env) {
        return wxMiniappRepo.find("name", env).firstResult();
    }

    public AqaraConfig getAqara(String env) {
        return aqaraConfigRepo.find("env", env).firstResult();
    }

    public AliyunOss getAliyunOss() {
        return aliyunOssRepo.listAll().stream().findFirst().orElse(null);
    }

    public void saveFeedback(Feedback entity) {
        feedbackRepo.save(entity);
    }

    public Page<Feedback> listFeedback(String title, String content, List<Long> userId, PageRequest page) {
        if (userId != null && userId.isEmpty()) {
            return page.empty();
        }
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (title != null) {
            query += " and title like concat('%', :title, '%')";
            params.put("title", "%" + title + "%");
        }
        if (content != null) {
            query += " and content like concat('%', :content, '%')";
            params.put("content", "%" + content + "%");
        }
        if (userId != null) {
            query += " and userId in :userId";
            params.put("userId", userId);
        }
        long total = feedbackRepo.count(query, params);
        if (total == 0) {
            return page.empty();
        }
        List<Feedback> list = feedbackRepo.find(query, Sort.descending("createAt"), params).page(page.getCurrent(), page.getSize()).list();
        return page.of(total, list);
    }

    public List<BaseConfig> fetchBaseConfig() {
        return baseConfigRepo.listAll();
    }

    public BaseConfig fetchBaseConfig(String key) {
        return baseConfigRepo.find("key", key).firstResult();
    }

    public MeituanConfig getMeituan(String env) {
        return meituanConfigRepo.find("env", env).firstResult();
    }

    public DouyinConfig getDouyin(String env) {
        return douyinConfigRepo.find("env", env).firstResult();
    }

    public void saveBanner(BannerInfo banner) {
        bannerInfoRepo.save(banner);
    }

    public Page<BannerBo> fetchBannerByType(List<BannerTypeEnum> type, PageRequest dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (type != null && !type.isEmpty()) {
            query += " and type in :type";
            params.put("type", type);
        }
        long total = bannerInfoRepo.count(query, params);
        if (total == 0) {
            return dto.empty();
        }
        List<BannerInfo> list = bannerInfoRepo.find(query, Sort.ascending("type", "seq"), params).page(dto.getCurrent(), dto.getSize()).list();
        return dto.of(total, list).convert(BannerBo::from);
    }

    public void deleteBanner(Long id) {
        bannerInfoRepo.deleteById(id);
    }

    public void saveBaseConfig(BaseConfig baseConfig) {
        baseConfigRepo.save(baseConfig);
    }

    public SmyooConfig getSmyoo(String env) {
        return smyooConfigRepo.find("env", env).firstResult();
    }

    public Feedback getFeedback(Long id) {
        return feedbackRepo.findById(id);
    }

    public BannerInfo getBanner(Long id) {
        return bannerInfoRepo.findById(id);
    }

    public WejoyConfig getWejoy(String env) {
        return wejoyConfigRepo.find("env", env).firstResult();
    }

    public void saveWejoyConfig(WejoyConfig wejoyConfig) {
        wejoyConfigRepo.save(wejoyConfig);
    }

    public List<WejoyConfig> listWejoyConfig() {
        return wejoyConfigRepo.listAll();
    }
}
