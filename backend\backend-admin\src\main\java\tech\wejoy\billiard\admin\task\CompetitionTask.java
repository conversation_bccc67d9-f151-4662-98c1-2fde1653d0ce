package tech.wejoy.billiard.admin.task;

import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.entity.CompetitionInfo;
import tech.wejoy.billiard.common.enums.CompetitionStatusEnum;
import tech.wejoy.billiard.common.manager.CompetitionManager;
import tech.wejoy.billiard.common.service.CompetitionService;

import java.time.LocalDateTime;
import java.util.List;

@ApplicationScoped
@RequiredArgsConstructor
public class CompetitionTask {

    private final CompetitionService competitionService;

    private final CompetitionManager competitionManager;

    @Scheduled(cron = "0 */5 * * * ?")
    @Transactional(rollbackOn = Exception.class)
    void changeCompetitionStatus() {
        List<CompetitionInfo> createList = competitionManager.fetchCompetitionByStatus(CompetitionStatusEnum.CREATE);
        for (CompetitionInfo competition : createList) {
            if (competition.getSignUpStartTime().isBefore(LocalDateTime.now())) {
                competition.setStatus(CompetitionStatusEnum.SIGN_UP);
                competitionManager.saveCompetition(competition);
            }
        }
        List<CompetitionInfo> signUpList = competitionManager.fetchCompetitionByStatus(CompetitionStatusEnum.SIGN_UP);
        for (CompetitionInfo competition : signUpList) {
            if (competition.getSignUpEndTime().isBefore(LocalDateTime.now())) {
                competition.setStatus(CompetitionStatusEnum.PREPARE);
                competitionManager.saveCompetition(competition);
            }
        }
    }

}
