package tech.wejoy.billiard.common.dto;

import com.congeer.web.bean.request.PageRequest;
import jakarta.ws.rs.QueryParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class OrderQueryDto extends PageRequest {

    @Schema(description = "订单状态")
    @QueryParam("status")
    private OrderStatusEnum status;

    @Schema(description = "店铺")
    @QueryParam("clubId")
    private Long clubId;

    @Schema(hidden = true)
    private Long userId;

}
