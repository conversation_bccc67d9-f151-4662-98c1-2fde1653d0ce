package tech.wejoy.billiard.admin.task;


import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.RequiredArgsConstructor;
import tech.wejoy.billiard.common.service.MatchService;

import java.time.LocalDateTime;

@ApplicationScoped
@RequiredArgsConstructor
public class MatchTask {

    private final MatchService matchService;

    @Scheduled(cron = "0 0 * * * ?")
    void profitRecord() {
        LocalDateTime now = LocalDateTime.now();
        matchService.updateMatchStatus(now);
    }

}
