package tech.wejoy.billiard.common.bo;

import lombok.Data;
import tech.wejoy.billiard.common.enums.AssistantResultEnum;
import tech.wejoy.billiard.common.enums.OrderStatusEnum;

import java.time.LocalDateTime;

@Data
public class AssistantResultBo {

    private Long assistantId;

    private AssistantResultEnum result;

    private String message;

    private String orderNo;

    private OrderStatusEnum status;

    private Object extra;

    private LocalDateTime startTime;

}
