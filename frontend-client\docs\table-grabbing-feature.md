# 抢台费功能前端实现

## 📋 功能概述

抢台费功能允许用户通过支付费用来提前结束他人的订单并获得球桌使用权。前端实现包括：

1. **抢台费开关检查** - 根据门店设置显示/隐藏抢台费按钮
2. **用户体验优化** - 支付确认、进度提示、成功反馈
3. **功能说明** - 帮助用户理解抢台费机制

## 🔧 技术实现

### 1. 关键逻辑修改

#### 支付方式显示条件优化
**修改前**: 只要不是订单拥有者就显示支付方式
```typescript
(!confirmationData.order || confirmationData.order.userId !== user?.id)
```

**修改后**: 精确控制支付方式显示条件
```typescript
((!confirmationData.order) ||                              // 新建订单场景
 (confirmationData.order.userId === user?.id) ||           // 订单拥有者场景
 (confirmationData.order.userId !== user?.id &&            // 抢台费场景：
  confirmationData.order.status === ORDERTYPES.USING &&    // - 不是拥有者
  confirmationData.club.enableTableGrabbing))              // - 订单使用中
                                                            // - 门店开启抢台费
```

这个修改确保了：
- ✅ 新建订单时正常显示支付方式
- ✅ 订单拥有者可以看到支付方式（用于正常支付）
- ✅ 非拥有者只有在订单使用中且门店开启抢台费时才能看到支付方式（用于抢台费）

### 2. 类型定义更新

#### `types/business.d.ts`
```typescript
interface IVenue {
  // ... 其他字段
  enableTableGrabbing?: boolean; // 抢台费开关
}
```

#### `pages/confirmation/index.tsx`
```typescript
interface IConfirmationData {
  club: { 
    // ... 其他字段
    enableTableGrabbing?: boolean; // 抢台费开关
  };
}
```

### 2. 组件功能增强

#### ForceButton 组件 (全额抢台费)
- ✅ 添加 `enableTableGrabbing` 属性检查
- ✅ 支付前确认对话框
- ✅ 加载状态和错误处理
- ✅ 支付成功提示

#### AverageButton 组件 (半价抢台费)
- ✅ 添加 `enableTableGrabbing` 属性检查
- ✅ 支付前确认对话框
- ✅ 加载状态和错误处理
- ✅ 支付成功提示

#### TableGrabbingInfo 组件 (功能说明)
- ✅ 抢台费功能介绍
- ✅ 支付方式说明
- ✅ 注意事项提醒

### 3. API 调用优化

#### `api/bussiness/order.ts`
```typescript
async otherFinishOrder(orderNo: string, params: { type: number; payType: number }) {
  return await http(`/order/${orderNo}/finish/other`, {
    method: "POST",
    data: params,
  });
}
```

## 🎯 用户体验流程

### 抢台费支付流程
```
用户点击抢台费按钮 
→ 显示确认对话框 
→ 用户确认支付 
→ 显示加载状态 
→ 调用支付接口 
→ 处理支付结果 
→ 显示成功/失败提示 
→ 刷新页面状态
```

### 功能可见性控制

#### 支付方式显示逻辑
```
(!confirmationData.order) ||                              // 没有订单（新建订单）
(confirmationData.order.userId === user?.id) ||           // 是订单拥有者
(confirmationData.order.userId !== user?.id &&            // 不是订单拥有者
 confirmationData.order.status === ORDERTYPES.USING &&    // 且订单使用中
 confirmationData.club.enableTableGrabbing)               // 且门店开启抢台费
→ 显示支付方式组件
```

#### 抢台费按钮显示逻辑
```
!isOwner &&                                               // 非订单拥有者
status === ORDERTYPES.USING &&                           // 订单使用中
enableTableGrabbing                                       // 门店开启抢台费
→ 显示抢台费按钮
```

## 📱 界面展示

### 抢台费按钮
- **全额抢台费**: 深色按钮，文字"支付全部"
- **半价抢台费**: 浅色按钮，文字"AA支付"

### 确认对话框
- **全额抢台费**: "您将支付全额抢台费，原订单用户将获得全额退款。确认继续吗？"
- **半价抢台费**: "您将支付半价抢台费，原订单用户将获得部分退款。确认继续吗？"

### 功能说明卡片
- 显示抢台费功能简介
- 提供"了解详情"链接
- 点击后弹出详细说明对话框

## 🔒 安全考虑

1. **支付前确认** - 防止误操作
2. **手机号验证** - 确保用户身份
3. **错误处理** - 支付失败时的回滚机制
4. **状态同步** - 支付成功后及时更新页面状态

## 🚀 部署说明

### 前端构建
```bash
cd frontend-client
npm install
npm run build
```

### 配置检查
确保后端API返回门店的 `enableTableGrabbing` 字段：
- `/club/table/prepare` 接口
- 在 `club` 对象中包含 `enableTableGrabbing` 字段

## 🧪 测试建议

### 功能测试
1. 测试抢台费开关控制按钮显示/隐藏
2. 测试支付确认对话框
3. 测试支付成功/失败流程
4. 测试功能说明对话框

### 用户体验测试
1. 确认提示信息清晰易懂
2. 验证加载状态显示正常
3. 检查错误提示友好
4. 测试页面状态更新及时

## 📝 注意事项

1. **兼容性**: 确保在不支持抢台费的门店中按钮正确隐藏
2. **性能**: 避免频繁的API调用和状态更新
3. **用户体验**: 提供清晰的操作反馈和错误提示
4. **数据一致性**: 支付成功后及时同步订单状态
