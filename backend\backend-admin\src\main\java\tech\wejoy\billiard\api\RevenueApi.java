package tech.wejoy.billiard.api;

import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.bo.RevenueBo;
import tech.wejoy.billiard.dto.ClubDatePeriodDto;
import tech.wejoy.billiard.service.BusinessService;

import java.util.List;

@Slf4j
@Path("/revenue")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "营收接口")
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class RevenueApi {

    private final BusinessService businessService;

    @GET
    @Path("/today")
    @Permission({"business_today:read", "business_today:write", "admin_today"})
    @Operation(summary = "今日营收")
    public RevenueBo today(@QueryParam("tenantId") Long tenantId, @QueryParam("clubId") Long clubId) {
        return businessService.todayRevenue(tenantId, clubId);
    }

    @GET
    @Path("/list")
    @Permission({"business_report:read", "business_report:write"})
    @Operation(summary = "营收报表")
    public List<RevenueBo> list(@BeanParam ClubDatePeriodDto dto) {
        return businessService.listRevenue(dto.getTenantId(), dto.getClubId(), dto.getStartDate(), dto.getEndDate());
    }

}
