package tech.wejoy.billiard.admin.api;

import com.congeer.core.bean.Page;
import com.congeer.security.quarkus.annotation.Authorized;
import com.congeer.security.quarkus.annotation.Permission;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.admin.bo.ProfitListBo;
import tech.wejoy.billiard.admin.bo.ProfitStatsBo;
import tech.wejoy.billiard.admin.bo.WithdrawRecordBo;
import tech.wejoy.billiard.admin.dto.*;
import tech.wejoy.billiard.admin.service.BusinessService;
import tech.wejoy.billiard.common.bo.WithdrawAccountBo;
import tech.wejoy.billiard.common.bo.WithdrawConfigBo;
import tech.wejoy.billiard.common.dto.DatePeriodDto;
import tech.wejoy.billiard.common.dto.WithdrawDto;

import java.time.LocalDate;

@Slf4j
@Path("/profit")
@Produces(MediaType.APPLICATION_JSON)
@Tag(name = "净收接口")
@Authorized({"default", "business"})
@RequiredArgsConstructor
public class ProfitApi {

    private final BusinessService businessService;

    @GET
    @Path("/stats")
    @Operation(summary = "获取净收统计")
    @Permission({"business_report:read", "business_report:write"})
    public ProfitStatsBo stats(@BeanParam ClubDatePeriodDto dto) {
        return businessService.stats(dto.getTenantId(), dto.getClubId(), dto.getStartDate(), dto.getEndDate());
    }

    @GET
    @Path("/list")
    @Operation(summary = "获取净收详情列表")
    @Permission({"business_profit:read", "business_profit:write"})
    public ProfitListBo list(@BeanParam ClubQueryDatePeriodDto dto) {
        return businessService.listProfit(dto.getOrderNo(), dto.getType(), dto.getTenantId(), dto.getClubId(), dto.getStartDate(), dto.getEndDate());
    }

    @GET
    @Path("/config/withdraw")
    @Operation(summary = "提现设置")
    @Permission({"business_withdraw:read", "business_withdraw:write"})
    public WithdrawConfigBo withdraw() {
        return businessService.withdrawConfig();
    }

    @GET
    @Path("/withdraw/account")
    @Operation(summary = "获取提现账户金额")
    @Permission({"business_withdraw:read", "business_withdraw:write"})
    public WithdrawAccountBo withdrawAccount() {
        return businessService.withdrawAccount();
    }

    @POST
    @Path("/config/withdraw")
    @Operation(summary = "更新提现设置")
    @Permission({"business_withdraw:write"})
    public void saveWithdraw(WithdrawDto dto) {
        businessService.saveWithdrawConfig(dto);
    }

    @GET
    @Path("/withdraw/list")
    @Operation(summary = "提现记录列表")
    @Permission({"business_withdraw:read", "business_withdraw:write", "admin_withdraw_list"})
    public Page<WithdrawRecordBo> withdrawList(@BeanParam WithdrawRecordQueryDto dto) {
        return businessService.withdrawList(dto);
    }

    @GET
    @Path("/withdraw/config/list")
    @Operation(summary = "提现配置列表")
    public Page<WithdrawConfigBo> withdrawConfigList(@BeanParam WithdrawConfigQueryDto dto) {
        return businessService.withdrawConfigList(dto);
    }

    @PUT
    @Path("/withdraw/config/status")
    @Operation(summary = "修改提现配置状态")
    public void changeWithdrawConfigStatus(WithdrawStatusDto dto) {
        businessService.changeWithdrawConfigStatus(dto);
    }

    @PUT
    @Path("/withdraw/status")
    @Operation(summary = "修改提现状态")
    public void changeWithdrawStatus(WithdrawStatusDto dto) {
        businessService.changeWithdrawStatus(dto);
    }

    @POST
    @Path("/check")
    @Operation(summary = "净收确认")
    @Authorized
    public void check(DatePeriodDto dto) {
        if (dto.getStartDate() == null || dto.getEndDate() == null) {
            throw new IllegalArgumentException("日期不能为空");
        }
        while (!dto.getStartDate().isAfter(dto.getEndDate()) && dto.getStartDate().isBefore(LocalDate.now())) {
            businessService.checkProfit(dto.getStartDate());
            dto.setStartDate(dto.getStartDate().plusDays(1));
        }
    }

    @POST
    @Path("/withdraw")
    @Operation(summary = "提现")
    @Authorized
    public void autoWithdraw() {
        businessService.autoWithdraw();
    }

}
