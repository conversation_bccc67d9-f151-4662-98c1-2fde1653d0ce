package tech.wejoy.billiard.common.third.meituan.response;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
public class ReceiptConsumeResponse extends MeituanResponse {

    private String orderId;
    private String receiptCode;
    private String flowId;
    private BigDecimal dealPrice;
    @JsonAlias("merchantAmount")
    private BigDecimal merchantAmount;

    private String sourceInfo;

}
