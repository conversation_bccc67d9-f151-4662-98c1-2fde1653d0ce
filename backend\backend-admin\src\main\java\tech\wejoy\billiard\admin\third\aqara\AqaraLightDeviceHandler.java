package tech.wejoy.billiard.admin.third.aqara;

import lombok.extern.slf4j.Slf4j;
import tech.wejoy.billiard.common.entity.TenantDevice;

@Slf4j
public class AqaraLightDeviceHandler extends AqaraDeviceHandler {


    @Override
    public boolean doStart(TenantDevice device) {
        return operator(device, "1");
    }

    @Override
    public boolean doStop(TenantDevice device) {
        return operator(device, "0");
    }

    @Override
    protected boolean doWarn(TenantDevice device) {
        blink(device);
        return true;
    }

    private void blink(TenantDevice device) {
        try {
            doStop(device);
            Thread.sleep(500);
            doStart(device);
            Thread.sleep(500);
        } catch (Exception e) {
            log.error("warn error : {}", e.getMessage(), e);
        } finally {
            doStart(device);
        }
    }

}
