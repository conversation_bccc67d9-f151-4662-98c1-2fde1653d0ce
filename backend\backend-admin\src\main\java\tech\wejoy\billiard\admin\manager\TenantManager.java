package tech.wejoy.billiard.admin.manager;

import com.congeer.core.bean.Page;
import com.congeer.utils.BeanUtils;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import tech.wejoy.billiard.admin.bo.TenantBo;
import tech.wejoy.billiard.admin.dto.TenantQueryDto;
import tech.wejoy.billiard.admin.dto.WithdrawConfigQueryDto;
import tech.wejoy.billiard.admin.dto.WithdrawRecordQueryDto;
import tech.wejoy.billiard.common.entity.*;
import tech.wejoy.billiard.common.enums.IsEnum;
import tech.wejoy.billiard.common.repo.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ApplicationScoped
@RequiredArgsConstructor
@Transactional(rollbackOn = Exception.class)
public class TenantManager {

    private final TenantInfoRepo tenantInfoRepo;

    private final TenantWithdrawConfigRepo tenantWithdrawConfigRepo;

    private final TenantWithdrawRecordRepo tenantWithdrawRecordRepo;

    private final TenantProfitAccountRepo tenantProfitAccountRepo;

    private final TenantProfitRecordRepo tenantProfitRecordRepo;

    public Page<TenantBo> page(TenantQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = BeanUtils.toMap(dto, "name");
        if (dto.getName() != null) {
            query += "and name like concat('%',:name,'%')";
        }
        long count = tenantInfoRepo.count(query, params);
        if (count == 0) {
            return dto.empty();
        }
        List<TenantInfo> list = tenantInfoRepo.find(query, params).range(dto.getOffset(), dto.getOffset() + dto.getSize()).list();
        return dto.of(count, BeanUtils.copyList(list, TenantBo.class));
    }

    public void save(TenantInfo info) {
        tenantInfoRepo.save(info);
    }

    public void changeStatus(Long id, IsEnum status) {
        tenantInfoRepo.update("set status = ?1 where id = ?2", status, id);
    }

    public List<TenantInfo> findByName(String name) {
        if (name == null || name.isEmpty()) {
            return tenantInfoRepo.listAll();
        }
        return tenantInfoRepo.find("status = 1 and name like concat('%',:name,'%')", Map.of("name", name)).list();
    }

    public TenantInfo fetchById(Long id) {
        return tenantInfoRepo.findById(id);
    }

    public TenantWithdrawConfig fetchWithdrawConfig(Long tenantId) {
        return tenantWithdrawConfigRepo.find("tenantId = ?1", tenantId).firstResult();
    }


    public TenantWithdrawConfig fetchWithdrawConfigById(Long id) {
        return tenantWithdrawConfigRepo.findById(id);
    }

    public void saveWithdrawConfig(TenantWithdrawConfig config) {
        tenantWithdrawConfigRepo.save(config);
    }

    public Page<TenantWithdrawRecord> pageWithdrawRecord(WithdrawRecordQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dto.getTenantId())) {
            query += " and tenantId in :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getStatus() != null && !dto.getStatus().isEmpty()) {
            query += " and status in :status";
            params.put("status", dto.getStatus());
        }
        if (dto.getStartDate() != null) {
            query += " and createAt >= :startDate";
            params.put("startDate", dto.getStartDate().atStartOfDay());
        }
        if (dto.getEndDate() != null) {
            query += " and createAt <= :endDate";
            params.put("endDate", dto.getEndDate().plusDays(1).atStartOfDay());
        }
        return tenantWithdrawRecordRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public void saveWithdrawRecord(TenantWithdrawRecord record) {
        tenantWithdrawRecordRepo.save(record);
    }

    public void saveProfitRecord(TenantProfitRecord record) {
        tenantProfitRecordRepo.save(record);
    }

    public TenantProfitAccount fetchProfitAccount(Long tenantId) {
        return tenantProfitAccountRepo.find("tenantId = ?1", tenantId).firstResult();
    }

    public void saveProfitAccount(TenantProfitAccount account) {
        tenantProfitAccountRepo.save(account);
    }

    public TenantProfitRecord fetchProfitRecord(Long tenantId, Long clubId, LocalDate date) {
        return tenantProfitRecordRepo.find("tenantId = ?1 and clubId = ?2 and date = ?3", tenantId, clubId, date).firstResult();
    }

    public void deleteProfitRecord(Long id) {
        tenantProfitRecordRepo.deleteById(id);
    }

    public List<TenantProfitAccount> fetchProfitAccounts() {
        return tenantProfitAccountRepo.listAll();
    }

    public List<TenantWithdrawConfig> fetchWithdrawConfigs() {
        return tenantWithdrawConfigRepo.listAll();
    }

    public List<TenantProfitRecord> fetchProfitRecord(Long tenantId, Long clubId, LocalDate start, LocalDate end) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (tenantId != null) {
            query += " and tenantId = :tenantId";
            params.put("tenantId", tenantId);
        }
        if (clubId != null) {
            query += " and clubId = :clubId";
            params.put("clubId", clubId);
        }
        if (start != null) {
            query += " and date >= :start";
            params.put("start", start);
        }
        if (end != null) {
            query += " and date <= :end";
            params.put("end", end);
        }
        return tenantProfitRecordRepo.find(query, Sort.descending("date"), params).list();
    }

    public Page<TenantWithdrawConfig> pageWithdrawConfig(WithdrawConfigQueryDto dto) {
        String query = "1=1";
        Map<String, Object> params = new HashMap<>();
        if (CollectionUtils.isNotEmpty(dto.getTenantId())) {
            query += "and tenantId in :tenantId";
            params.put("tenantId", dto.getTenantId());
        }
        if (dto.getStatus() != null && !dto.getStatus().isEmpty()) {
            query += "and status in :status";
            params.put("status", dto.getStatus());
        }
        return tenantWithdrawConfigRepo.page(dto.toPage(), query, Sort.descending("createAt"), params);
    }

    public TenantWithdrawRecord fetchWithdrawRecord(Long id) {
        return tenantWithdrawRecordRepo.findById(id);
    }

    public List<TenantProfitAccount> fetchProfitAccountByTenantIds(List<Long> tenantIds) {
        return tenantProfitAccountRepo.find("tenantId in :tenantId", Map.of("tenantId", tenantIds)).list();
    }

}
