{"version": 3, "file": "pages/assistant/club/index.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AC/EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sources": ["webpack://frontend-client/./src/pages/assistant/club/index.tsx?eca5", "webpack://frontend-client/._src_pages_assistant_club_index.tsx"], "sourcesContent": ["import \"@nutui/nutui-react-taro/dist/esm/SafeArea/style/css\";\nimport _SafeArea from \"@nutui/nutui-react-taro/dist/esm/SafeArea\";\nimport _regeneratorRuntime from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\";\nimport _asyncToGenerator from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport _slicedToArray from \"C:/Users/<USER>/GitWork/partime/billiaard/frontend-client/node_modules/@babel/runtime/helpers/esm/slicedToArray.js\";\nimport Back from \"@/components/bussiness/back\";\nimport Title from \"@/components/bussiness/Title\";\nimport { menuRect } from \"@/utils\";\nimport { useRouter } from \"@tarojs/taro\";\nimport { useCallback, useEffect, useMemo, useState } from \"react\";\nimport api from '@/api/bussiness/assistant';\nimport VenueCard from \"@/components/bussiness/venueCard\";\nimport Taro from \"@tarojs/taro\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport default (function () {\n  var rect = menuRect();\n  var router = useRouter();\n  var id = router.params.id;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    detail = _useState2[0],\n    setDetail = _useState2[1];\n  var getDetail = useCallback(/*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(id) {\n      var _yield$api$getOne, data;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return api.getOne(id);\n          case 2:\n            _yield$api$getOne = _context.sent;\n            data = _yield$api$getOne.data;\n            setDetail(data);\n          case 5:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function (_x) {\n      return _ref.apply(this, arguments);\n    };\n  }(), [setDetail]);\n  useEffect(function () {\n    getDetail(id);\n  }, []);\n  var navigate = useCallback(function (url) {\n    return Taro.navigateTo({\n      url: url\n    });\n  }, []);\n  var list = useMemo(function () {\n    if (!detail) return [];\n    return detail.clubs;\n  }, [detail]);\n  return /*#__PURE__*/_jsxs(\"div\", {\n    className: \"flex flex-col h-[100vh]\",\n    style: {\n      paddingTop: rect.bottom + 10\n    },\n    children: [/*#__PURE__*/_jsx(Back, {}), /*#__PURE__*/_jsx(Title, {\n      name: \"\\u7B7E\\u7EA6\\u5546\\u5BB6\"\n    }), /*#__PURE__*/_jsx(\"div\", {\n      className: \"flex flex-col px-3 flex-1 space-y-2\",\n      children: list.map(function (item) {\n        return /*#__PURE__*/_jsx(\"div\", {\n          onClick: function onClick() {\n            return navigate(\"/pages/venue/index?id=\".concat(item.id));\n          },\n          children: /*#__PURE__*/_jsx(VenueCard, {\n            item: item\n          })\n        });\n      })\n    }), /*#__PURE__*/_jsx(_SafeArea, {\n      position: \"bottom\"\n    })]\n  });\n});", "import { createPageConfig } from '@tarojs/runtime'\nimport component from \"!!../../../../node_modules/@tarojs/taro-loader/lib/entry-cache.js?name=pages/assistant/club/index!./index.tsx\"\nvar config = {\"navigationStyle\":\"custom\",\"navigationBarTitleText\":\"猩猩球社\",\"navigationBarBackgroundColor\":\"#171717\",\"navigationBarTextStyle\":\"white\",\"disableScroll\":true};\n\n\n\nvar taroOption = createPageConfig(component, 'pages/assistant/club/index', {root:{cn:[]}}, config || {})\nif (component && component.behaviors) {\n  taroOption.behaviors = (taroOption.behaviors || []).concat(component.behaviors)\n}\nvar inst = Page(taroOption)\n\n\n\nexport default component\n"], "names": [], "sourceRoot": ""}