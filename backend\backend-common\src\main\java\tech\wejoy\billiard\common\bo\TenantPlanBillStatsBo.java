package tech.wejoy.billiard.common.bo;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.math.BigDecimal;

@Data
public class TenantPlanBillStatsBo {

    @Schema(description = "今日充值")
    private BigDecimal today = BigDecimal.ZERO;

    @Schema(description = "会员数")
    private Long memberCount = 0L;

    @Schema(description = "总金额")
    private BigDecimal total = BigDecimal.ZERO;

    @Schema(description = "余额")
    private BigDecimal balance = BigDecimal.ZERO;

}
