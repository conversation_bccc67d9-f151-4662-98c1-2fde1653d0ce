package tech.wejoy.billiard.common.entity;

import com.congeer.database.bean.BaseEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import tech.wejoy.billiard.common.enums.IsEnum;

import java.math.BigDecimal;


@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Accessors(chain = true)
public class TenantPlan extends BaseEntity {

    private Long tenantId;

    private String name;

    private BigDecimal totalAmount;

    private BigDecimal payAmount;

    @Column(columnDefinition = "text")
    private String remark;

    private IsEnum status;

    private IsEnum show;

    private Integer seq;

    private Integer paySeq;

    private IsEnum delete;

}
