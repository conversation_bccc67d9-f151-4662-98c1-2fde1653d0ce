package tech.wejoy.billiard.common.bo;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
public class WechatPayParamBo extends PayParamBo {

    private String timeStamp;
    private String nonceStr;
    private String packageStr;
    private String signType;
    private String paySign;

    public static WechatPayParamBo fromMap(Map<String, String> map) {
        WechatPayParamBo param = new WechatPayParamBo();
        param.setTimeStamp(map.get("timeStamp"));
        param.setNonceStr(map.get("nonceStr"));
        param.setPackageStr(map.get("package"));
        param.setSignType(map.getOrDefault("signType", "RSA"));
        param.setPaySign(map.get("paySign"));
        return param;
    }
}
