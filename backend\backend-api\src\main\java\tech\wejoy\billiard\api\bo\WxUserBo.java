package tech.wejoy.billiard.api.bo;

import com.congeer.security.core.bean.IUser;
import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import tech.wejoy.billiard.common.entity.ClientUser;
import tech.wejoy.billiard.common.enums.GenderEnum;

import java.io.Serial;

@Data
@Schema(description = "微信用户")
public class WxUserBo implements IUser<Long> {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID")
    private Long id;

    private String openId;

    @Schema(description = "昵称")
    private String nickname;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别")
    private GenderEnum gender;

    @Schema(description = "手机号")
    private String phone;

    private String sessionKey;

    public static WxUserBo from(ClientUser user) {
        WxUserBo bo = new WxUserBo();
        bo.setId(user.getId());
        bo.setOpenId(user.getOpenId());
        bo.setNickname(user.getNickname());
        bo.setAvatar(user.getAvatar());
        bo.setGender(user.getGender());
        bo.setPhone(user.getPhone());
        return bo;
    }


    @Override
    public WxUserBo clearSensitive() {
        WxUserBo userBo = new WxUserBo();
        userBo.setId(this.id);
        userBo.setNickname(this.nickname);
        userBo.setAvatar(this.avatar);
        userBo.setGender(this.gender);
        userBo.setPhone(this.phone);
        return userBo;
    }

}
