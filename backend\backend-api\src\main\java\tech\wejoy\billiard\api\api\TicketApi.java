package tech.wejoy.billiard.api.api;

import com.congeer.security.core.utils.SecurityHolder;
import com.congeer.security.quarkus.annotation.Authorized;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;
import tech.wejoy.billiard.api.bo.WxUserBo;
import tech.wejoy.billiard.common.bo.TicketBo;
import tech.wejoy.billiard.common.dto.TicketCheckDto;
import tech.wejoy.billiard.common.dto.TicketQueryDto;
import tech.wejoy.billiard.common.service.TicketService;

import java.util.List;

@Path("/ticket")
@Tag(name = "渠道券相关")
@Produces(MediaType.APPLICATION_JSON)
@Authorized
@RequiredArgsConstructor
public class TicketApi {

    private final TicketService ticketService;

    @GET
    @Path("/list")
    public List<TicketBo> list(@BeanParam TicketQueryDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return ticketService.list(dto);
    }

    @POST
    @Path("/check")
    public TicketBo check(TicketCheckDto dto) {
        dto.setUserId(SecurityHolder.<Long, WxUserBo>session().getUserId());
        return ticketService.check(dto);
    }

}
